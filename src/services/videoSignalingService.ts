import { HubConnectionBuilder, HubConnection, HubConnectionState } from "@microsoft/signalr";
import { apiBaseUrl } from "@/config/env";

export interface SignalMessage {
  type: 'offer' | 'answer' | 'ice-candidate';
  fromUser: string;
  toUser: string;
  payload: any;
  roomId: string;
}

export interface RoomUser {
  connectionId: string;
  userId: string;
  userName: string;
}

export interface VideoSignalingCallbacks {
  onUserJoined?: (user: RoomUser) => void;
  onUserLeft?: (user: { connectionId: string; userId: string; userName: string }) => void;
  onSignalReceived?: (signal: { type: string; fromUser: string; payload: any; senderName: string }) => void;
  onRoomUsers?: (users: RoomUser[]) => void;
  onError?: (error: string) => void;
  onConnected?: () => void;
  onDisconnected?: () => void;
}

class VideoSignalingService {
  private connection: HubConnection | null = null;
  private callbacks: VideoSignalingCallbacks = {};
  private currentRoomId: string | null = null;
  private isConnecting = false;

  constructor() {
    this.setupConnection();
  }

  private setupConnection() {
    // Get the API base URL and construct the SignalR hub URL
    const hubUrl = `${apiBaseUrl.replace('/api', '')}/videohub`;

    console.log('Video Signaling: Setting up connection to', hubUrl);

    this.connection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => {
          // Get JWT token from localStorage
          const token = localStorage.getItem('thunee_token');
          console.log('Video Signaling: Using token for auth:', token ? 'Token present' : 'No token');
          return token || '';
        }
      })
      .withAutomaticReconnect()
      .build();

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.connection) return;

    // Connection events
    this.connection.onclose((error) => {
      console.log('Video Signaling: Connection closed', error);
      this.callbacks.onDisconnected?.();
    });

    this.connection.onreconnecting((error) => {
      console.log('Video Signaling: Reconnecting...', error);
    });

    this.connection.onreconnected((connectionId) => {
      console.log('Video Signaling: Reconnected with ID', connectionId);
      this.callbacks.onConnected?.();
      
      // Rejoin room if we were in one
      if (this.currentRoomId) {
        this.joinRoom(this.currentRoomId, 'Reconnected User');
      }
    });

    // Hub events
    this.connection.on('UserJoined', (user: RoomUser) => {
      console.log('Video Signaling: User joined', user);
      this.callbacks.onUserJoined?.(user);
    });

    this.connection.on('UserLeft', (user: { connectionId: string; userId: string; userName: string }) => {
      console.log('Video Signaling: User left', user);
      this.callbacks.onUserLeft?.(user);
    });

    this.connection.on('RoomUsers', (users: RoomUser[]) => {
      console.log('Video Signaling: Room users', users);
      this.callbacks.onRoomUsers?.(users);
    });

    this.connection.on('ReceiveSignal', (signal: { type: string; fromUser: string; payload: any; senderName: string }) => {
      console.log('Video Signaling: Signal received', signal);
      this.callbacks.onSignalReceived?.(signal);
    });

    this.connection.on('Error', (error: string) => {
      console.error('Video Signaling: Hub error', error);
      this.callbacks.onError?.(error);
    });

    this.connection.on('RoomInfo', (info: { roomId: string; userCount: number; users: any[] }) => {
      console.log('Video Signaling: Room info', info);
    });
  }

  async connect(): Promise<void> {
    if (!this.connection) {
      this.setupConnection();
    }

    if (this.connection?.state === HubConnectionState.Connected) {
      console.log('Video Signaling: Already connected');
      return;
    }

    if (this.isConnecting) {
      console.log('Video Signaling: Connection already in progress');
      return;
    }

    try {
      this.isConnecting = true;
      console.log('Video Signaling: Connecting...');
      
      await this.connection!.start();
      
      console.log('Video Signaling: Connected successfully');
      this.callbacks.onConnected?.();
    } catch (error) {
      console.error('Video Signaling: Connection failed', error);
      this.callbacks.onError?.(`Connection failed: ${error}`);
      throw error;
    } finally {
      this.isConnecting = false;
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      try {
        if (this.currentRoomId) {
          await this.leaveRoom(this.currentRoomId);
        }
        
        await this.connection.stop();
        console.log('Video Signaling: Disconnected');
      } catch (error) {
        console.error('Video Signaling: Error during disconnect', error);
      }
    }
  }

  async joinRoom(roomId: string, userName: string): Promise<void> {
    if (!this.connection || this.connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to signaling server');
    }

    try {
      console.log(`Video Signaling: Joining room ${roomId} as ${userName}`);
      
      await this.connection.invoke('JoinRoom', {
        roomId,
        userName
      });
      
      this.currentRoomId = roomId;
      console.log(`Video Signaling: Successfully joined room ${roomId}`);
    } catch (error) {
      console.error('Video Signaling: Failed to join room', error);
      throw error;
    }
  }

  async leaveRoom(roomId: string): Promise<void> {
    if (!this.connection || this.connection.state !== HubConnectionState.Connected) {
      return;
    }

    try {
      console.log(`Video Signaling: Leaving room ${roomId}`);
      
      await this.connection.invoke('LeaveRoom', roomId);
      
      if (this.currentRoomId === roomId) {
        this.currentRoomId = null;
      }
      
      console.log(`Video Signaling: Successfully left room ${roomId}`);
    } catch (error) {
      console.error('Video Signaling: Failed to leave room', error);
      throw error;
    }
  }

  async sendSignal(message: SignalMessage): Promise<void> {
    if (!this.connection || this.connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to signaling server');
    }

    try {
      console.log('Video Signaling: Sending signal', message);
      
      await this.connection.invoke('SendSignal', message);
      
      console.log('Video Signaling: Signal sent successfully');
    } catch (error) {
      console.error('Video Signaling: Failed to send signal', error);
      throw error;
    }
  }

  async getRoomInfo(roomId: string): Promise<void> {
    if (!this.connection || this.connection.state !== HubConnectionState.Connected) {
      throw new Error('Not connected to signaling server');
    }

    try {
      await this.connection.invoke('GetRoomInfo', roomId);
    } catch (error) {
      console.error('Video Signaling: Failed to get room info', error);
      throw error;
    }
  }

  setCallbacks(callbacks: VideoSignalingCallbacks) {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  getConnectionId(): string | null {
    return this.connection?.connectionId || null;
  }

  getConnectionState(): HubConnectionState | null {
    return this.connection?.state || null;
  }

  getCurrentRoomId(): string | null {
    return this.currentRoomId;
  }
}

// Export a singleton instance
export const videoSignalingService = new VideoSignalingService();
export default videoSignalingService;
