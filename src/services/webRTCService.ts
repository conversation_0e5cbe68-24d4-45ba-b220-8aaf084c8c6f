import videoSignalingService, { SignalMessage, RoomUser } from './videoSignalingService';

export interface PeerConnection {
  id: string;
  name: string;
  connection: RTCPeerConnection;
  stream: MediaStream | null;
  pendingIceCandidates: RTCIceCandidate[];
}

export interface WebRTCCallbacks {
  onLocalStream?: (stream: MediaStream) => void;
  onRemoteStream?: (peerId: string, stream: MediaStream, name: string) => void;
  onPeerConnected?: (peerId: string, name: string) => void;
  onPeerDisconnected?: (peerId: string) => void;
  onError?: (error: string) => void;
}

class WebRTCService {
  private localStream: MediaStream | null = null;
  private peers: Map<string, PeerConnection> = new Map();
  private callbacks: WebRTCCallbacks = {};
  private isInitialized = false;
  private currentRoomId: string | null = null;
  private currentUserName: string | null = null;

  // WebRTC configuration with STUN servers
  private rtcConfiguration: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' }
    ],
    iceCandidatePoolSize: 10
  };

  constructor() {
    this.setupSignalingCallbacks();
  }

  private setupSignalingCallbacks() {
    videoSignalingService.setCallbacks({
      onUserJoined: (user: RoomUser) => {
        console.log('WebRTC: User joined, creating offer', user);
        // Always create offer to new users
        this.createOffer(user.connectionId, user.userName);
      },

      onUserLeft: (user) => {
        console.log('WebRTC: User left, cleaning up peer', user);
        this.removePeer(user.connectionId);
      },

      onRoomUsers: (users: RoomUser[]) => {
        console.log('WebRTC: Existing room users', users);
        // Create offers to all existing users when we join
        users.forEach(user => {
          console.log('WebRTC: Creating offer to existing user', user);
          this.createOffer(user.connectionId, user.userName);
        });
      },

      onSignalReceived: async (signal) => {
        console.log('WebRTC: Signal received', signal);
        await this.handleSignal(signal);
      },

      onError: (error) => {
        console.error('WebRTC: Signaling error', error);
        this.callbacks.onError?.(error);
      }
    });
  }

  async initialize(roomId: string, userName: string): Promise<void> {
    try {
      console.log(`WebRTC: Initializing for room ${roomId} as ${userName}`);
      
      this.currentRoomId = roomId;
      this.currentUserName = userName;

      // Get user media
      await this.getUserMedia();

      // Connect to signaling server
      await videoSignalingService.connect();

      // Join the room
      await videoSignalingService.joinRoom(roomId, userName);

      this.isInitialized = true;
      console.log('WebRTC: Initialization complete');
    } catch (error) {
      console.error('WebRTC: Initialization failed', error);
      this.callbacks.onError?.(`Initialization failed: ${error}`);
      throw error;
    }
  }

  private async getUserMedia(): Promise<void> {
    try {
      console.log('WebRTC: Requesting user media');

      // First try video + audio
      try {
        this.localStream = await navigator.mediaDevices.getUserMedia({
          video: {
            width: { ideal: 640 },
            height: { ideal: 480 },
            frameRate: { ideal: 30 }
          },
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
        console.log('WebRTC: Got video + audio stream');
      } catch (videoError) {
        console.warn('WebRTC: Video access failed, trying audio only:', videoError);

        // Try audio only if video fails (common when camera is in use)
        this.localStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });
        console.log('WebRTC: Got audio-only stream (camera may be in use by another tab)');
      }

      this.callbacks.onLocalStream?.(this.localStream);
    } catch (error) {
      console.error('WebRTC: Failed to get any media', error);

      // Create a silent audio track as fallback
      try {
        const audioContext = new AudioContext();
        const oscillator = audioContext.createOscillator();
        const destination = audioContext.createMediaStreamDestination();
        oscillator.connect(destination);
        oscillator.start();

        this.localStream = destination.stream;
        console.log('WebRTC: Created fallback silent audio stream');
        this.callbacks.onLocalStream?.(this.localStream);
      } catch (fallbackError) {
        console.error('WebRTC: Even fallback failed', fallbackError);
        throw new Error('Could not access any media devices');
      }
    }
  }

  private async createOffer(peerId: string, peerName: string): Promise<void> {
    try {
      console.log(`WebRTC: Creating offer for peer ${peerId} (${peerName})`);
      
      const peerConnection = this.createPeerConnection(peerId, peerName);
      
      // Add local stream to peer connection
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => {
          peerConnection.connection.addTrack(track, this.localStream!);
        });
      }

      // Create offer
      const offer = await peerConnection.connection.createOffer();
      await peerConnection.connection.setLocalDescription(offer);

      // Send offer via signaling
      await this.sendSignal({
        type: 'offer',
        fromUser: videoSignalingService.getConnectionId()!,
        toUser: peerId,
        payload: offer,
        roomId: this.currentRoomId!
      });

      console.log(`WebRTC: Offer sent to ${peerId}`);
    } catch (error) {
      console.error('WebRTC: Failed to create offer', error);
      this.callbacks.onError?.(`Failed to create offer: ${error}`);
    }
  }

  private async handleSignal(signal: { type: string; fromUser: string; payload: any; senderName: string }): Promise<void> {
    try {
      const { type, fromUser, payload, senderName } = signal;
      
      let peer = this.peers.get(fromUser);
      
      if (type === 'offer') {
        console.log(`WebRTC: Handling offer from ${fromUser} (${senderName})`);
        
        // Create peer connection if it doesn't exist
        if (!peer) {
          peer = this.createPeerConnection(fromUser, senderName);
        }

        // Add local stream
        if (this.localStream) {
          this.localStream.getTracks().forEach(track => {
            peer!.connection.addTrack(track, this.localStream!);
          });
        }

        // Set remote description
        await peer.connection.setRemoteDescription(new RTCSessionDescription(payload));

        // Process any queued ICE candidates
        await this.processQueuedIceCandidates(peer);

        // Create answer
        const answer = await peer.connection.createAnswer();
        await peer.connection.setLocalDescription(answer);

        // Send answer
        await this.sendSignal({
          type: 'answer',
          fromUser: videoSignalingService.getConnectionId()!,
          toUser: fromUser,
          payload: answer,
          roomId: this.currentRoomId!
        });

        console.log(`WebRTC: Answer sent to ${fromUser}`);
      } 
      else if (type === 'answer') {
        console.log(`WebRTC: Handling answer from ${fromUser}`);
        
        if (peer) {
          await peer.connection.setRemoteDescription(new RTCSessionDescription(payload));
          console.log(`WebRTC: Remote description set for ${fromUser}`);

          // Process any queued ICE candidates
          await this.processQueuedIceCandidates(peer);
        }
      }
      else if (type === 'ice-candidate') {
        console.log(`WebRTC: Handling ICE candidate from ${fromUser}`);

        if (peer && payload) {
          const candidate = new RTCIceCandidate(payload);

          // Check if remote description is set
          if (peer.connection.remoteDescription) {
            try {
              await peer.connection.addIceCandidate(candidate);
              console.log(`WebRTC: ICE candidate added for ${fromUser}`);
            } catch (error) {
              console.error(`WebRTC: Failed to add ICE candidate for ${fromUser}:`, error);
            }
          } else {
            // Queue the candidate for later
            console.log(`WebRTC: Queuing ICE candidate for ${fromUser} (no remote description yet)`);
            peer.pendingIceCandidates.push(candidate);
          }
        }
      }
    } catch (error) {
      console.error('WebRTC: Error handling signal', error);
      this.callbacks.onError?.(`Signal handling error: ${error}`);
    }
  }

  private createPeerConnection(peerId: string, peerName: string): PeerConnection {
    console.log(`WebRTC: Creating peer connection for ${peerId} (${peerName})`);
    
    const connection = new RTCPeerConnection(this.rtcConfiguration);
    
    const peer: PeerConnection = {
      id: peerId,
      name: peerName,
      connection,
      stream: null,
      pendingIceCandidates: []
    };

    // Handle ICE candidates
    connection.onicecandidate = (event) => {
      if (event.candidate) {
        console.log(`WebRTC: Sending ICE candidate to ${peerId}`);
        this.sendSignal({
          type: 'ice-candidate',
          fromUser: videoSignalingService.getConnectionId()!,
          toUser: peerId,
          payload: event.candidate,
          roomId: this.currentRoomId!
        }).catch(error => {
          console.error('WebRTC: Failed to send ICE candidate', error);
        });
      }
    };

    // Handle remote stream
    connection.ontrack = (event) => {
      console.log(`WebRTC: Received remote stream from ${peerId}`, event.streams[0]);
      peer.stream = event.streams[0];
      this.callbacks.onRemoteStream?.(peerId, event.streams[0], peerName);
    };

    // Handle connection state changes
    connection.onconnectionstatechange = () => {
      console.log(`WebRTC: Connection state changed for ${peerId}:`, connection.connectionState);
      
      if (connection.connectionState === 'connected') {
        this.callbacks.onPeerConnected?.(peerId, peerName);
      } else if (connection.connectionState === 'disconnected' || connection.connectionState === 'failed') {
        this.removePeer(peerId);
      }
    };

    this.peers.set(peerId, peer);
    return peer;
  }

  private async sendSignal(message: SignalMessage): Promise<void> {
    try {
      await videoSignalingService.sendSignal(message);
    } catch (error) {
      console.error('WebRTC: Failed to send signal', error);
      throw error;
    }
  }

  private async processQueuedIceCandidates(peer: PeerConnection): Promise<void> {
    console.log(`WebRTC: Processing ${peer.pendingIceCandidates.length} queued ICE candidates for ${peer.id}`);

    for (const candidate of peer.pendingIceCandidates) {
      try {
        await peer.connection.addIceCandidate(candidate);
        console.log(`WebRTC: Added queued ICE candidate for ${peer.id}`);
      } catch (error) {
        console.error(`WebRTC: Failed to add queued ICE candidate for ${peer.id}:`, error);
      }
    }

    // Clear the queue
    peer.pendingIceCandidates = [];
  }

  private removePeer(peerId: string): void {
    const peer = this.peers.get(peerId);
    if (peer) {
      console.log(`WebRTC: Removing peer ${peerId}`);
      peer.connection.close();
      this.peers.delete(peerId);
      this.callbacks.onPeerDisconnected?.(peerId);
    }
  }

  async cleanup(): Promise<void> {
    console.log('WebRTC: Cleaning up');
    
    // Close all peer connections
    this.peers.forEach(peer => {
      peer.connection.close();
    });
    this.peers.clear();

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    // Leave room and disconnect signaling
    if (this.currentRoomId) {
      await videoSignalingService.leaveRoom(this.currentRoomId);
    }

    this.isInitialized = false;
    this.currentRoomId = null;
    this.currentUserName = null;
  }

  // Media control methods
  toggleAudio(enabled: boolean): void {
    if (this.localStream) {
      this.localStream.getAudioTracks().forEach(track => {
        track.enabled = enabled;
      });
    }
  }

  toggleVideo(enabled: boolean): void {
    if (this.localStream) {
      this.localStream.getVideoTracks().forEach(track => {
        track.enabled = enabled;
      });
    }
  }

  setCallbacks(callbacks: WebRTCCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getPeers(): Map<string, PeerConnection> {
    return this.peers;
  }

  isReady(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const webRTCService = new WebRTCService();
export default webRTCService;
