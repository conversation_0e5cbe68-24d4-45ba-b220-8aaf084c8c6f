import { io, Socket } from 'socket.io-client';
import { socketUrl, isDevelopment } from '@/config/env';
import { apiService } from './api';

// Define the server URL - this should be your backend WebSocket server
// Get socket URL from environment variables with fallback logic
const getServerUrl = () => {
  // First try to get from environment variables
  if (socketUrl) {
    return socketUrl;
  }

  // Fallback to auto-detection for backward compatibility
  // Check if we're in development (localhost)
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    const savedPort = localStorage.getItem('thunee_server_port');
    return `http://localhost:${savedPort || '3001'}`;
  }

  // Production environment - use the same host and port as the frontend
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  const port = window.location.port;

  // For IIS deployment, the Node.js server will be on the same host and port
  return `${protocol}//${hostname}${port ? ':' + port : ''}`;
};

let SOCKET_SERVER_URL = getServerUrl();

// Log socket URL in development
if (isDevelopment) {
  console.log('🔌 Socket URL:', SOCKET_SERVER_URL);
}

class SocketService {
  private socket: Socket | null = null;
  private listeners: Map<string, Array<(data: any) => void>> = new Map();
  private persistentPlayerId: string | null = null;
  private currentGameId: string | null = null;
  private lastRejoinAttempt: number = 0;

  // Check if socket is connected
  isConnected(): boolean {
    return this.socket !== null && this.socket.connected;
  }

  // Connect to the WebSocket server
  connect(playerName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // If already connected with the same player name, just resolve
        if (this.socket && this.socket.connected && this.socket.io.opts.query?.playerName === playerName) {
          console.log('Already connected with the same player name');
          resolve();
          return;
        }

        // Close existing connection if any
        if (this.socket) {
          this.socket.close();
        }

        // Generate or retrieve persistent player ID
        if (!this.persistentPlayerId) {
          this.persistentPlayerId = this.getOrCreatePersistentPlayerId();
        }

        // Load current game ID from storage
        if (!this.currentGameId) {
          this.currentGameId = this.getCurrentGameId();
        }

        // Create new connection with both websocket and polling transports
        // Don't pass token in query - we'll authenticate after connection
        this.socket = io(SOCKET_SERVER_URL, {
          query: {
            playerName
          },
          transports: ['websocket', 'polling'],
          reconnectionAttempts: 10,
          reconnectionDelay: 1000,
          timeout: 20000,
          autoConnect: true,
          reconnection: true
        });

        this.socket.on('connect', async () => {
          console.log('Connected to WebSocket server');

          // Try to get token with retry logic to handle race conditions
          const currentToken = await this.getTokenWithRetry();

          // Authenticate with the server if we have a token
          if (currentToken) {
            try {
              console.log('Authenticating with server...');
              await this.authenticate(currentToken);
              console.log('Authentication successful');
            } catch (error) {
              console.error('Authentication failed:', error);
              reject(new Error('Authentication failed: ' + (error instanceof Error ? error.message : 'Unknown error')));
              return;
            }
          }

          // Try to rejoin if we have a persistent ID and game ID (after authentication)
          if (this.persistentPlayerId && this.currentGameId) {
            try {
              console.log('Attempting to rejoin game with persistent ID:', this.persistentPlayerId);
              const rejoinResult = await this.attemptRejoin(this.persistentPlayerId, this.currentGameId);
              if (rejoinResult.success) {
                console.log('Successfully rejoined game');
                resolve();
                return;
              } else {
                console.log('Rejoin failed:', rejoinResult.error);
                console.log('Keeping game ID for potential manual rejoin');
              }
            } catch (error) {
              console.error('Rejoin attempt failed:', error);
              console.log('Keeping game ID for potential manual rejoin');
            }
          }

          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('Connection error:', error);
          reject(error);
        });

        // Handle rejoin required event from server
        this.socket.on('rejoin_required', async (data: { persistentId: string, gameId: string }) => {
          console.log('Server requested rejoin:', data);

          // Use our existing persistent ID if we have one, otherwise use server's
          const rejoinPersistentId = this.persistentPlayerId || data.persistentId;
          console.log('Using persistent ID for rejoin:', rejoinPersistentId);

          // Update game ID
          this.currentGameId = data.gameId;
          localStorage.setItem('thunee_current_game_id', data.gameId);

          // Attempt to rejoin immediately (with rate limiting)
          const now = Date.now();
          if (now - this.lastRejoinAttempt < 2000) { // 2 second rate limit
            console.log('Rate limiting rejoin attempt');
            return;
          }
          this.lastRejoinAttempt = now;

          try {
            const rejoinResult = await this.attemptRejoin(rejoinPersistentId, data.gameId);
            if (rejoinResult.success) {
              console.log('Successfully rejoined after server request');
            } else {
              console.log('Failed to rejoin after server request, trying with server persistent ID');
              // If our persistent ID failed, try with the server's
              if (rejoinPersistentId !== data.persistentId) {
                const fallbackResult = await this.attemptRejoin(data.persistentId, data.gameId);
                if (fallbackResult.success) {
                  console.log('Successfully rejoined with server persistent ID');
                  // Update our persistent ID to match the server's
                  this.persistentPlayerId = data.persistentId;
                  localStorage.setItem('thunee_persistent_player_id', data.persistentId);
                }
              }
            }
          } catch (error) {
            console.error('Error during server-requested rejoin:', error);
          }
        });

        // Set up default event listeners
        this.setupDefaultListeners();

        // Add a timeout to reject if connection takes too long
        setTimeout(() => {
          if (this.socket && !this.socket.connected) {
            reject(new Error('Connection timeout'));
          }
        }, 20000);
      } catch (error) {
        console.error('Error initializing socket:', error);
        reject(error);
      }
    });
  }

  // Get token with retry logic to handle race conditions
  private async getTokenWithRetry(maxRetries: number = 3, delayMs: number = 100): Promise<string | null> {
    for (let i = 0; i < maxRetries; i++) {
      const token = apiService.getToken();
      if (token) {
        console.log(`Token retrieved successfully on attempt ${i + 1}`);
        return token;
      }

      if (i < maxRetries - 1) {
        console.log(`Token not available, retrying in ${delayMs}ms... (attempt ${i + 1}/${maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    console.log('Token not available after all retry attempts');
    return null;
  }

  // Authenticate with the server
  private authenticate(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      // Add timeout for authentication
      const authTimeout = setTimeout(() => {
        reject(new Error('Authentication timeout'));
      }, 10000); // 10 second timeout

      this.socket.emit('authenticate', { token }, (response: { success: boolean, user?: any, persistentId?: string, error?: string }) => {
        clearTimeout(authTimeout);

        if (response.success) {
          console.log('Authenticated successfully:', response.user);

          // Store the persistent ID provided by the server, but only if we don't already have one
          if (response.persistentId && !this.persistentPlayerId) {
            this.persistentPlayerId = response.persistentId;
            localStorage.setItem('thunee_persistent_player_id', response.persistentId);
            console.log('Received persistent ID from server:', response.persistentId);
          } else if (response.persistentId && this.persistentPlayerId) {
            console.log('Server provided persistent ID, but we already have one. Keeping existing:', this.persistentPlayerId);
          }

          resolve();
        } else {
          console.error('Authentication failed:', response.error);
          reject(new Error(response.error || 'Authentication failed'));
        }
      });
    });
  }

  // Disconnect from the WebSocket server
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      console.log('Disconnected from WebSocket server');
    }
  }

  // Create a new lobby
  createLobby(playerName: string, teamName?: string): Promise<{
    lobbyCode: string;
    partnerInviteCode: string;
    opponentInviteCode: string;
  }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('create_lobby', { playerName, teamName }, (response: {
        success: boolean,
        lobbyCode?: string,
        partnerInviteCode?: string,
        opponentInviteCode?: string,
        error?: string
      }) => {
        if (response.success && response.lobbyCode && response.partnerInviteCode && response.opponentInviteCode) {
          // Store the game ID for potential rejoin
          this.setCurrentGameId(response.lobbyCode);

          resolve({
            lobbyCode: response.lobbyCode,
            partnerInviteCode: response.partnerInviteCode,
            opponentInviteCode: response.opponentInviteCode
          });
        } else {
          reject(new Error(response.error || 'Failed to create lobby'));
        }
      });
    });
  }

  // Join an existing lobby
  joinLobby(lobbyCode: string, playerName: string): Promise<{ actualLobbyCode?: string, isInviteCode?: boolean }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('join_lobby', { lobbyCode, playerName }, (response: {
        success: boolean,
        actualLobbyCode?: string,
        isInviteCode?: boolean,
        error?: string
      }) => {
        if (response.success) {
          // Store the game ID for potential rejoin
          const gameId = response.actualLobbyCode || lobbyCode;
          this.setCurrentGameId(gameId);

          resolve({
            actualLobbyCode: response.actualLobbyCode,
            isInviteCode: response.isInviteCode
          });
        } else {
          reject(new Error(response.error || 'Failed to join lobby'));
        }
      });
    });
  }

  // Get or create persistent player ID
  private getOrCreatePersistentPlayerId(): string {
    let persistentId = localStorage.getItem('thunee_persistent_player_id');
    if (!persistentId) {
      // Try to get user ID from current token
      const currentUser = this.getCurrentUser();
      const userId = currentUser?.id || `temp_${Date.now()}`;
      persistentId = `player_${userId}_${Date.now()}`;
      localStorage.setItem('thunee_persistent_player_id', persistentId);
      console.log('Created new persistent ID:', persistentId);
    } else {
      console.log('Using existing persistent ID:', persistentId);
    }
    return persistentId;
  }

  // Get current user from API service
  private getCurrentUser(): any {
    try {
      // This would need to be implemented based on your auth service
      // For now, return null and let the server generate the ID
      return null;
    } catch (error) {
      return null;
    }
  }

  // Set current game ID for rejoin purposes
  setCurrentGameId(gameId: string): void {
    this.currentGameId = gameId;
    localStorage.setItem('thunee_current_game_id', gameId);
  }

  // Clear current game ID
  clearCurrentGameId(): void {
    this.currentGameId = null;
    localStorage.removeItem('thunee_current_game_id');
  }

  // Get current game ID from storage
  private getCurrentGameId(): string | null {
    if (!this.currentGameId) {
      this.currentGameId = localStorage.getItem('thunee_current_game_id');
    }
    return this.currentGameId;
  }

  // Attempt to rejoin a game
  private attemptRejoin(persistentId: string, gameId: string): Promise<{ success: boolean, error?: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('rejoinRequest', { persistentId, gameId }, (response: {
        success: boolean,
        error?: string,
        gameState?: any
      }) => {
        if (response.success) {
          console.log('Rejoin successful, game state:', response.gameState);

          // Properly restore game state by triggering the local event handler
          if (response.gameState) {
            console.log('Restoring game state from rejoin response');
            // Trigger the game_state_update handler directly since we can't emit to ourselves
            this.eventHandlers.get('game_state_update')?.forEach(handler => {
              try {
                handler(response.gameState);
              } catch (error) {
                console.error('Error in game_state_update handler:', error);
              }
            });
          }

          resolve({ success: true });
        } else {
          console.log('Rejoin failed:', response.error);
          resolve({ success: false, error: response.error });
        }
      });
    });
  }

  // Update team name
  updateTeamName(lobbyCode: string, teamNumber: 1 | 2, teamName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('update_team_name', { lobbyCode, teamNumber, teamName }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to update team name'));
        }
      });
    });
  }

  // Set team ready status
  setTeamReady(lobbyCode: string, ready: boolean): Promise<void> {
    console.log('socketService.setTeamReady called with:', { lobbyCode, ready });
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        console.error('Socket not connected in setTeamReady');
        reject(new Error('Socket not connected'));
        return;
      }

      console.log('Emitting set_team_ready event');
      this.socket.emit('set_team_ready', { lobbyCode, ready }, (response: { success: boolean, error?: string }) => {
        console.log('set_team_ready response:', response);
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to set team ready status'));
        }
      });
    });
  }

  // Switch team
  switchTeam(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('switch_team', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to switch team'));
        }
      });
    });
  }

  // Start the game
  startGame(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('start_game', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to start game'));
        }
      });
    });
  }

  // Find a match for the team
  findMatch(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('find_match', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to find match'));
        }
      });
    });
  }

  // Cancel finding a match
  cancelFindMatch(lobbyCode: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not connected'));
        return;
      }

      this.socket.emit('cancel_find_match', { lobbyCode }, (response: { success: boolean, error?: string }) => {
        if (response.success) {
          resolve();
        } else {
          reject(new Error(response.error || 'Failed to cancel match finding'));
        }
      });
    });
  }

  // Send a game action
  sendGameAction(action: string, data: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        const error = new Error('Socket not connected');
        console.error(error);
        return reject(error);
      }

      console.log(`Sending game action: ${action}`, data);

      // List of actions that should use the game_action event
      const gameActionEvents = ['send_chat_message', 'vote_timeframe', 'vote_trump_visibility', 'join_video_call', 'leave_video_call', 'video_signal'];

      // Check if this action should use the game_action event
      if (gameActionEvents.includes(action)) {
        // Use the generic game_action event
        this.socket.emit('game_action', { action, ...data }, (response: { success: boolean, error?: string }) => {
          if (response?.success) {
            console.log(`Game action ${action} sent successfully`);
            resolve();
          } else {
            console.error(`Failed to send game action ${action}:`, response?.error);
            reject(new Error(response?.error || `Failed to send ${action}`));
          }
        });
        return;
      }

      // For other actions, emit the action directly
      // Create a timeout ID that we can clear if the server responds
      let timeoutId: NodeJS.Timeout;

      this.socket.emit(action, data, (response: { success: boolean, error?: string }) => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        if (response?.success) {
          console.log(`Game action ${action} successful:`, response);
          resolve();
        } else {
          console.error(`Game action ${action} failed:`, response?.error);
          reject(new Error(response?.error || `Failed to execute ${action}`));
        }
      });

      // Add a timeout in case the server doesn't respond
      timeoutId = setTimeout(() => {
        console.error(`Game action ${action} timed out after 5 seconds`);
        reject(new Error(`${action} timeout`));
      }, 5000);
    });
  }

  // Add an event listener
  on(event: string, callback: (data: any) => void): void {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }

    // Store the callback in our listeners map
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)?.push(callback);

    // Add the listener to the socket
    this.socket.on(event, callback);
  }

  // Remove an event listener
  off(event: string, callback?: (data: any) => void): void {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }

    if (callback) {
      // Remove specific callback
      this.socket.off(event, callback);

      // Update our listeners map
      const callbacks = this.listeners.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index !== -1) {
          callbacks.splice(index, 1);
        }
      }
    } else {
      // Remove all callbacks for this event
      this.socket.off(event);
      this.listeners.delete(event);
    }
  }

  // Get the socket ID
  getSocketId(): string | null {
    return this.socket?.id || null;
  }

  // Get the persistent player ID
  getPersistentPlayerId(): string | null {
    if (!this.persistentPlayerId) {
      this.persistentPlayerId = this.getOrCreatePersistentPlayerId();
    }
    return this.persistentPlayerId;
  }

  // Check if player has an active game session
  hasActiveGameSession(): boolean {
    return !!(this.persistentPlayerId && this.currentGameId);
  }

  // Get current game info for reconnection
  getCurrentGameInfo(): { persistentId: string | null, gameId: string | null } {
    return {
      persistentId: this.persistentPlayerId,
      gameId: this.currentGameId
    };
  }

  // Update the server URL
  updateServerUrl(port: string): void {
    SOCKET_SERVER_URL = `http://localhost:${port}`;
    localStorage.setItem('thunee_server_port', port);
    console.log(`Server URL updated to: ${SOCKET_SERVER_URL}`);
  }

  // Send a custom event to the server
  sendCustomEvent(event: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        const error = new Error('Socket not connected');
        console.error(error);
        return reject(error);
      }

      console.log(`Sending custom event: ${event}`, data);

      // Create a timeout ID that we can clear if the server responds
      let timeoutId: NodeJS.Timeout;

      this.socket.emit(event, data, (response: any) => {
        // Clear the timeout since we got a response
        clearTimeout(timeoutId);

        if (response?.success) {
          console.log(`Custom event ${event} successful:`, response);
          resolve(response);
        } else {
          // Special handling for spectator-related events
          if (event === 'join_as_spectator' && response?.error === 'Game not found') {
            console.error(`Game not found. This could be because the game hasn't started yet or the code is incorrect.`);
            reject(new Error(`Game not found. Please check the game code and make sure the game has started.`));
          } else {
            console.error(`Custom event ${event} failed:`, response?.error);
            reject(new Error(response?.error || `Failed to execute ${event}`));
          }
        }
      });

      // Add a timeout in case the server doesn't respond
      timeoutId = setTimeout(() => {
        console.error(`Custom event ${event} timed out after 5 seconds`);
        reject(new Error(`${event} timeout`));
      }, 5000);
    });
  }

  // Set up default event listeners
  private setupDefaultListeners(): void {
    if (!this.socket) return;

    // Handle disconnection
    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected:', reason);

      // If the disconnection was not intentional, try to reconnect
      if (reason === 'io server disconnect' || reason === 'transport close' || reason === 'ping timeout') {
        console.log('Attempting to reconnect...');
        this.socket?.connect();
      }
    });

    // Handle player disconnection notifications
    this.socket.on('player_disconnected', (data) => {
      console.log('Player disconnected:', data);
      // This will be handled by the UI components
    });

    // Handle player reconnection notifications
    this.socket.on('player_reconnected', (data) => {
      console.log('Player reconnected:', data);
      // This will be handled by the UI components
    });

    // Handle disconnection countdown updates
    this.socket.on('disconnection_countdown', (data) => {
      console.log('Disconnection countdown:', data);
      // This will be handled by the UI components
    });

    // Handle game pause/resume events
    this.socket.on('game_paused', (data) => {
      console.log('Game paused:', data);
      // This will be handled by the UI components
    });

    this.socket.on('game_resumed', (data) => {
      console.log('Game resumed:', data);
      // This will be handled by the UI components
    });

    // Handle toast notifications
    this.socket.on('toast_notification', (data) => {
      console.log('Toast notification:', data);
      // This will be handled by the UI components
    });

    // Handle reconnection
    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected after ${attemptNumber} attempts`);
    });

    // Handle reconnection attempts
    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`Reconnection attempt ${attemptNumber}`);
    });

    // Handle reconnection errors
    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error);
    });

    // Handle reconnection failures
    this.socket.on('reconnect_failed', () => {
      console.error('Failed to reconnect after all attempts');
    });

    // Handle errors
    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });
  }
}

// Create a singleton instance
const socketService = new SocketService();

export default socketService;
