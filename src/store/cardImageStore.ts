import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from '@/services/api';
import type { GameSettings, UpdateGameSettings } from '@/services/api';

export interface CardImageSettings {
  // Base URL for card face images (without trailing slash)
  cardFaceBaseUrl: string;
  
  // URL for the card back image
  cardBackImageUrl: string;
  
  // Custom card face mappings (optional)
  // Format: {"6H": "/custom/6H.svg", "JS": "/custom/JS.svg", ...}
  customCardFaceMappings?: Record<string, string>;
}

interface CardImageStore {
  settings: CardImageSettings;
  isLoading: boolean;
  error: string | null;
  updateSettings: (newSettings: Partial<CardImageSettings>) => void;
  resetToDefaults: () => void;
  setCardFaceBaseUrl: (url: string) => void;
  setCardBackImageUrl: (url: string) => void;
  setCustomCardFaceMapping: (cardKey: string, url: string) => void;
  removeCustomCardFaceMapping: (cardKey: string) => void;
  clearCustomCardFaceMappings: () => void;
  // API methods
  loadSettingsFromAPI: () => Promise<void>;
  saveSettingsToAPI: (settings?: Partial<CardImageSettings>) => Promise<void>;
  resetSettingsToAPI: () => Promise<void>;
}

const defaultSettings: CardImageSettings = {
  cardFaceBaseUrl: '/CardFace',
  cardBackImageUrl: '/CardFace/card-back.svg',
  customCardFaceMappings: undefined,
};

// Helper function to convert API GameSettings to CardImageSettings
const convertAPIToCardImageSettings = (apiSettings: GameSettings): CardImageSettings => ({
  cardFaceBaseUrl: apiSettings.cardFaceBaseUrl,
  cardBackImageUrl: apiSettings.cardBackImageUrl,
  customCardFaceMappings: apiSettings.customCardFaceMappings,
});

// Helper function to convert CardImageSettings to API UpdateGameSettings
const convertCardImageSettingsToAPI = (settings: Partial<CardImageSettings>): UpdateGameSettings => ({
  cardFaceBaseUrl: settings.cardFaceBaseUrl,
  cardBackImageUrl: settings.cardBackImageUrl,
  customCardFaceMappings: settings.customCardFaceMappings,
});

export const useCardImageStore = create<CardImageStore>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isLoading: false,
      error: null,

      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),

      resetToDefaults: () =>
        set({ settings: { ...defaultSettings } }),

      setCardFaceBaseUrl: (url) =>
        set((state) => ({
          settings: {
            ...state.settings,
            cardFaceBaseUrl: url,
          },
        })),

      setCardBackImageUrl: (url) =>
        set((state) => ({
          settings: {
            ...state.settings,
            cardBackImageUrl: url,
          },
        })),

      setCustomCardFaceMapping: (cardKey, url) =>
        set((state) => ({
          settings: {
            ...state.settings,
            customCardFaceMappings: {
              ...state.settings.customCardFaceMappings,
              [cardKey]: url,
            },
          },
        })),

      removeCustomCardFaceMapping: (cardKey) =>
        set((state) => {
          const newMappings = { ...state.settings.customCardFaceMappings };
          delete newMappings[cardKey];
          return {
            settings: {
              ...state.settings,
              customCardFaceMappings: Object.keys(newMappings).length > 0 ? newMappings : undefined,
            },
          };
        }),

      clearCustomCardFaceMappings: () =>
        set((state) => ({
          settings: {
            ...state.settings,
            customCardFaceMappings: undefined,
          },
        })),

      // API methods
      loadSettingsFromAPI: async () => {
        set({ isLoading: true, error: null });
        try {
          const apiSettings = await apiService.getGameSettings();
          const cardImageSettings = convertAPIToCardImageSettings(apiSettings);
          set({ settings: cardImageSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to load card image settings from API:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to load card image settings',
            isLoading: false
          });
        }
      },

      saveSettingsToAPI: async (settings) => {
        set({ isLoading: true, error: null });
        try {
          const settingsToSave = settings || get().settings;
          const apiSettings = convertCardImageSettingsToAPI(settingsToSave);
          const updatedSettings = await apiService.updateGameSettings(apiSettings);
          const cardImageSettings = convertAPIToCardImageSettings(updatedSettings);
          set({ settings: cardImageSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to save card image settings to API:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to save card image settings',
            isLoading: false
          });
          throw error;
        }
      },

      resetSettingsToAPI: async () => {
        set({ isLoading: true, error: null });
        try {
          const resetSettings = await apiService.resetGameSettings();
          const cardImageSettings = convertAPIToCardImageSettings(resetSettings);
          set({ settings: cardImageSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to reset card image settings:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to reset card image settings',
            isLoading: false
          });
          throw error;
        }
      },
    }),
    {
      name: 'card-image-settings',
      partialize: (state) => ({ settings: state.settings }),
    }
  )
);
