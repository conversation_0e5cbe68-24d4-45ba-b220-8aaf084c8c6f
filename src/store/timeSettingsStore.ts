import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from '@/services/api';
import type { GameSettings, UpdateGameSettings } from '@/services/api';

export interface TimeSettings {
  // Play timeframe options (in seconds)
  playTimeframeOptions: number[];
  defaultPlayTimeframe: number;

  // Thunee calling durations (in seconds)
  thuneeCallingDurations: {
    trumper: number;
    firstRemaining: number;
    lastRemaining: number;
  };

  // Voting and UI timeouts (in seconds)
  votingTimeLimit: number;
  trumpDisplayDuration: number;

  // Trump visibility options
  trumpVisibilityOptions: ('short' | 'full')[];
  defaultTrumpVisibility: 'short' | 'full';

  // Animation speeds (in milliseconds)
  cardDealingSpeed: number;

  // Turn timer update interval (in milliseconds)
  timerUpdateInterval: number;
}

interface TimeSettingsStore {
  settings: TimeSettings;
  isLoading: boolean;
  error: string | null;
  updateSettings: (newSettings: Partial<TimeSettings>) => void;
  resetToDefaults: () => void;
  updatePlayTimeframeOptions: (options: number[]) => void;
  updateThuneeCallingDuration: (stage: keyof TimeSettings['thuneeCallingDurations'], duration: number) => void;
  // API methods
  loadSettingsFromAPI: () => Promise<void>;
  saveSettingsToAPI: (settings?: Partial<TimeSettings>) => Promise<void>;
  resetSettingsToAPI: () => Promise<void>;
}

const defaultSettings: TimeSettings = {
  playTimeframeOptions: [3, 4, 5, 6, 60],
  defaultPlayTimeframe: 3,
  thuneeCallingDurations: {
    trumper: 5,
    firstRemaining: 3,
    lastRemaining: 2,
  },
  votingTimeLimit: 15,
  trumpDisplayDuration: 10,
  trumpVisibilityOptions: ['short', 'full'],
  defaultTrumpVisibility: 'full',
  cardDealingSpeed: 300,
  timerUpdateInterval: 100,
};

// Helper function to convert API GameSettings to TimeSettings
const convertAPIToTimeSettings = (apiSettings: GameSettings): TimeSettings => ({
  playTimeframeOptions: apiSettings.playTimeframeOptions,
  defaultPlayTimeframe: apiSettings.defaultPlayTimeframe,
  thuneeCallingDurations: {
    trumper: apiSettings.trumperThuneeCallingDuration,
    firstRemaining: apiSettings.firstRemainingThuneeCallingDuration,
    lastRemaining: apiSettings.lastRemainingThuneeCallingDuration,
  },
  votingTimeLimit: apiSettings.votingTimeLimit,
  trumpDisplayDuration: apiSettings.trumpDisplayDuration,
  cardDealingSpeed: apiSettings.cardDealingSpeed,
  timerUpdateInterval: apiSettings.timerUpdateInterval,
});

// Helper function to convert TimeSettings to API UpdateGameSettings
const convertTimeSettingsToAPI = (settings: Partial<TimeSettings>): UpdateGameSettings => ({
  playTimeframeOptions: settings.playTimeframeOptions,
  defaultPlayTimeframe: settings.defaultPlayTimeframe,
  trumperThuneeCallingDuration: settings.thuneeCallingDurations?.trumper,
  firstRemainingThuneeCallingDuration: settings.thuneeCallingDurations?.firstRemaining,
  lastRemainingThuneeCallingDuration: settings.thuneeCallingDurations?.lastRemaining,
  votingTimeLimit: settings.votingTimeLimit,
  trumpDisplayDuration: settings.trumpDisplayDuration,
  cardDealingSpeed: settings.cardDealingSpeed,
  timerUpdateInterval: settings.timerUpdateInterval,
});

export const useTimeSettingsStore = create<TimeSettingsStore>()(
  persist(
    (set, get) => ({
      settings: defaultSettings,
      isLoading: false,
      error: null,

      updateSettings: (newSettings) =>
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        })),

      resetToDefaults: () =>
        set({ settings: { ...defaultSettings } }),

      updatePlayTimeframeOptions: (options) =>
        set((state) => ({
          settings: {
            ...state.settings,
            playTimeframeOptions: options,
            // If current default is not in new options, set to first option
            defaultPlayTimeframe: options.includes(state.settings.defaultPlayTimeframe)
              ? state.settings.defaultPlayTimeframe
              : options[0],
          },
        })),

      updateThuneeCallingDuration: (stage, duration) =>
        set((state) => ({
          settings: {
            ...state.settings,
            thuneeCallingDurations: {
              ...state.settings.thuneeCallingDurations,
              [stage]: duration,
            },
          },
        })),

      // API methods
      loadSettingsFromAPI: async () => {
        set({ isLoading: true, error: null });
        try {
          const apiSettings = await apiService.getGameSettings();
          const timeSettings = convertAPIToTimeSettings(apiSettings);
          set({ settings: timeSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to load settings from API:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to load settings',
            isLoading: false
          });
        }
      },

      saveSettingsToAPI: async (settingsToSave?: Partial<TimeSettings>) => {
        set({ isLoading: true, error: null });
        try {
          const currentSettings = get().settings;
          const settingsToUpdate = settingsToSave || currentSettings;
          const apiSettings = convertTimeSettingsToAPI(settingsToUpdate);

          const updatedApiSettings = await apiService.updateGameSettings(apiSettings);
          const updatedTimeSettings = convertAPIToTimeSettings(updatedApiSettings);

          set({ settings: updatedTimeSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to save settings to API:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to save settings',
            isLoading: false
          });
        }
      },

      resetSettingsToAPI: async () => {
        set({ isLoading: true, error: null });
        try {
          const resetApiSettings = await apiService.resetGameSettings();
          const resetTimeSettings = convertAPIToTimeSettings(resetApiSettings);
          set({ settings: resetTimeSettings, isLoading: false });
        } catch (error) {
          console.error('Failed to reset settings via API:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to reset settings',
            isLoading: false
          });
        }
      },
    }),
    {
      name: 'thunee-time-settings',
      version: 1,
    }
  )
);
