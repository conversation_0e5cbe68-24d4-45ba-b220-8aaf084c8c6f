import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiService } from "../../../src/services/api";

// Define the user type
export interface User {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  isAdmin?: boolean;
}

// Define the authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  tempRegistrationData: {
    username: string;
    email: string;
    password: string;
    otpSent: boolean;
  } | null;
}

// Define the authentication actions
export interface AuthActions {
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  verifyOtp: (otp: string) => Promise<void>;
  resendOtp: () => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  tempRegistrationData: null,
};

// Create the store with persistence
export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Login action
      login: async (username: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const authResponse = await apiService.login(username, password);

          // Ensure token is properly stored before updating state
          // Add a small delay to ensure localStorage write is complete
          await new Promise(resolve => setTimeout(resolve, 50));

          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          // Always show user-friendly message for login errors
          const loginError = new Error('Invalid username or password');
          set({
            error: loginError.message,
            isLoading: false,
          });
          throw loginError;
        }
      },

      // Register action
      register: async (username: string, email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const authResponse = await apiService.register(username, email, password);

          set({
            user: authResponse.user,
            isAuthenticated: true,
            isLoading: false,
            tempRegistrationData: null,
          });

          // Registration successful - user is now logged in
          return authResponse;
        } catch (error) {
          // Extract the actual error message from API or provide user-friendly fallback
          let errorMessage = 'Failed to register';
          if (error instanceof Error) {
            const apiError = error.message;
            if (apiError.includes('Username already exists')) {
              errorMessage = 'Username already exists';
            } else if (apiError.includes('Email already exists')) {
              errorMessage = 'Email already exists';
            } else {
              errorMessage = apiError;
            }
          }

          const registrationError = new Error(errorMessage);
          set({
            error: registrationError.message,
            isLoading: false,
          });
          throw registrationError;
        }
      },

      // Verify OTP action (kept for compatibility but simplified since API handles registration directly)
      verifyOtp: async (_otp: string) => {
        const { tempRegistrationData } = get();
        if (!tempRegistrationData) {
          throw new Error('No registration in progress');
        }

        set({ isLoading: true, error: null });
        try {
          // Since the API now handles registration directly, this is mainly for UI compatibility
          // In a real implementation, you might want to verify email here
          await new Promise(resolve => setTimeout(resolve, 500));

          set({
            user: {
              id: '1',
              username: tempRegistrationData.username,
              email: tempRegistrationData.email,
              isVerified: true,
            },
            isAuthenticated: true,
            tempRegistrationData: null,
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to verify OTP',
            isLoading: false,
          });
          throw error;
        }
      },

      // Resend OTP action
      resendOtp: async () => {
        const { tempRegistrationData } = get();
        if (!tempRegistrationData) {
          throw new Error('No registration in progress');
        }

        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Just update the OTP sent flag
          set({
            tempRegistrationData: {
              ...tempRegistrationData,
              otpSent: true,
            },
            isLoading: false,
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to resend OTP',
            isLoading: false,
          });
          throw error;
        }
      },

      // Logout action
      logout: async () => {
        try {
          await apiService.logout();
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set(initialState);
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'thunee-auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
