import { create } from 'zustand';
import socketService from '../services/socketService';
import videoService from '../services/videoService';

// This store is now a simplified version that just sets up the connection
// between the game socket events and our dedicated video service

export interface VideoCallState {
  isInitialized: boolean;
  error: string | null;
}

interface VideoCallActions {
  setError: (error: string | null) => void;
  initialize: () => void;
}

const initialState: VideoCallState = {
  isInitialized: false,
  error: null,
};

export const useVideoCallStore = create<VideoCallState & VideoCallActions>((set) => ({
  ...initialState,

  setError: (error: string | null) => {
    set({ error });
  },

  initialize: () => {
    set({ isInitialized: true });
  },
}));

// Set up socket event listeners for video call
export const setupVideoCallListeners = () => {
  console.log('Setting up video call listeners');

  // Clean up any existing listeners to prevent duplicates
  socketService.off('game_started');
  socketService.off('player_joined');
  socketService.off('player_left');

  // Listen for game started event to initialize video service
  socketService.on('game_started', (data: { gameId: string }) => {
    console.log('Game started, initializing video service:', data.gameId);

    // Set the video server port (default is 3002)
    const videoPort = localStorage.getItem('thunee_video_port') || '3002';
    videoService.updateServerUrl(videoPort);

    // Initialize the video call store
    useVideoCallStore.getState().initialize();
  });

  // Listen for player joined event
  socketService.on('player_joined', (data: { player: { id: string, name: string } }) => {
    console.log('Player joined game:', data.player);
  });

  // Listen for player left event
  socketService.on('player_left', (data: { playerId: string }) => {
    console.log('Player left game:', data.playerId);
  });

  console.log('Video call listeners set up successfully');
};
