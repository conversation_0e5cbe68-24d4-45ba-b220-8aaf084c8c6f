"use client";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X, Trophy, AlertTriangle, Clock } from "lucide-react";
import "../styles/ThuneeResultDisplay.css";

interface ThuneeResultData {
  success: boolean;
  thuneePlayerId: string;
  thuneePlayerName: string;
  thuneePlayerTeam: number;
  winningPlayerId?: string;
  winningPlayerName?: string;
  winningPlayerTeam?: number;
  isPartner?: boolean;
  ballsAwarded: number;
  winningTeam: number;
  ballScores: { team1: number; team2: number };
  handsPlayed?: any[];
  currentHand?: {
    id: number;
    cards: any[];
    winner: any;
    winningCard: any;
    winReason: string;
  };
  caughtInHand?: number;
  caughtByCard?: any;
  instantResult?: boolean;
  trumpSuit?: string;
  allHandsWon?: boolean;
  totalHands?: number;
}

interface ThuneeResultDisplayProps {
  isOpen: boolean;
  onClose: () => void;
  result: ThuneeResultData | null;
}

export default function ThuneeResultDisplay({ isOpen, onClose, result }: ThuneeResultDisplayProps) {
  const [showContinueButton, setShowContinueButton] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Show continue button after 5 seconds for instant results, 8 seconds for full results
      const delay = result?.instantResult ? 5000 : 8000;
      const timer = setTimeout(() => {
        setShowContinueButton(true);
      }, delay);

      return () => clearTimeout(timer);
    } else {
      setShowContinueButton(false);
    }
  }, [isOpen, result?.instantResult]);

  if (!isOpen || !result) return null;

  const formatCard = (card: any) => {
    if (!card) return '';
    return `${card.value} of ${card.suit}`;
  };

  const formatHand = (hand: any, index: number) => {
    if (!hand || !hand.cards) return null;
    
    return (
      <div key={index} className="border border-[#E1C760]/30 rounded-lg p-3 mb-2">
        <div className="flex justify-between items-center mb-2">
          <span className="text-[#E1C760] font-semibold">Hand {index + 1}</span>
          <Badge variant="outline" className="border-[#E1C760] text-[#E1C760]">
            Won by {hand.winner?.name || 'Unknown'}
          </Badge>
        </div>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {hand.cards?.map((card: any, cardIndex: number) => (
            <div key={cardIndex} className="text-gray-300">
              {card.playerName}: {formatCard(card)}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 thunee-result-container">
      <Card className="bg-black border-[#E1C760] max-w-2xl w-full max-h-[90vh] overflow-y-auto thunee-result-card">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            {result.success ? (
              <Trophy className="h-8 w-8 text-green-500" />
            ) : (
              <AlertTriangle className="h-8 w-8 text-red-500" />
            )}
            <CardTitle className={`text-2xl ${result.success ? 'text-green-500' : 'text-red-500'}`}>
              Thunee {result.success ? 'Success!' : 'Failed!'}
            </CardTitle>
          </div>
          
          {result.instantResult && (
            <div className="flex items-center justify-center gap-1 text-[#E1C760] text-sm">
              <Clock className="h-4 w-4" />
              <span>Instant Result</span>
            </div>
          )}
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Result Summary */}
          <div className="text-center space-y-2">
            <div className="text-lg text-white">
              {result.success ? (
                <>
                  <span className="text-[#E1C760] font-semibold">{result.thuneePlayerName}</span>
                  <span className="text-white"> (Team {result.thuneePlayerTeam}) won all {result.totalHands || 6} hands!</span>
                </>
              ) : (
                <>
                  <span className="text-[#E1C760] font-semibold">{result.thuneePlayerName}</span>
                  <span className="text-white"> (Team {result.thuneePlayerTeam}) was caught</span>
                  {result.caughtInHand && <span className="text-red-400"> in hand {result.caughtInHand}</span>}
                  {result.winningPlayerName && (
                    <>
                      <span className="text-white"> by </span>
                      <span className="text-[#E1C760] font-semibold">{result.winningPlayerName}</span>
                      <span className="text-white"> (Team {result.winningPlayerTeam})</span>
                    </>
                  )}
                </>
              )}
            </div>

            {result.caughtByCard && (
              <div className="text-sm text-gray-300">
                Winning card: <span className="text-[#E1C760]">{formatCard(result.caughtByCard)}</span>
              </div>
            )}

            {result.trumpSuit && (
              <div className="text-sm text-gray-300">
                Trump suit: <span className="text-[#E1C760]">{result.trumpSuit}</span>
              </div>
            )}
          </div>

          {/* Ball Award */}
          <div className="text-center bg-[#E1C760]/10 border border-[#E1C760]/30 rounded-lg p-4">
            <div className="text-[#E1C760] font-semibold text-lg">
              Team {result.winningTeam} awarded {result.ballsAwarded} balls
            </div>
            <div className="text-sm text-gray-300 mt-1">
              Team 1: {result.ballScores.team1} balls | Team 2: {result.ballScores.team2} balls
            </div>
          </div>

          {/* Hands Played */}
          {result.handsPlayed && result.handsPlayed.length > 0 && (
            <div>
              <h3 className="text-[#E1C760] font-semibold mb-3">
                Hands Played ({result.handsPlayed.length}/{result.totalHands || 6})
              </h3>
              <div className="max-h-60 overflow-y-auto space-y-2">
                {result.handsPlayed.map((hand, index) => formatHand(hand, index))}
              </div>
            </div>
          )}

          {/* Current Hand (for instant results) */}
          {result.currentHand && result.instantResult && (
            <div>
              <h3 className="text-red-400 font-semibold mb-3">
                Final Hand (Hand {result.caughtInHand})
              </h3>
              {formatHand(result.currentHand, (result.caughtInHand || 1) - 1)}
            </div>
          )}

          {/* Continue Button */}
          <div className="flex justify-center pt-4">
            {showContinueButton ? (
              <Button
                onClick={onClose}
                className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80 px-8"
              >
                Continue Game
              </Button>
            ) : (
              <div className="text-[#E1C760] text-sm flex items-center gap-2">
                <Clock className="h-4 w-4 animate-spin" />
                Continue button will appear shortly...
              </div>
            )}
          </div>
        </CardContent>

        {/* Close button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="absolute top-4 right-4 text-[#E1C760] hover:bg-[#E1C760]/10"
        >
          <X className="h-5 w-5" />
        </Button>
      </Card>
    </div>
  );
}
