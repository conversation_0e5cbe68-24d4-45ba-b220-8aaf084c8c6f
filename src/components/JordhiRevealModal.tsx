"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { getCardImagePath } from "@/utils/cardUtils";

interface JordhiRevealModalProps {
  isOpen: boolean;
  onClose: () => void;
  jordhiCall: {
    playerId: string;
    playerName: string;
    playerTeam: 1 | 2;
    value: number;
    jordhiSuit: string;
    jordhiCards: Array<{ suit: string, value: string }>;
  };
}

export default function JordhiRevealModal({
  isOpen,
  onClose,
  jordhiCall,
}: JordhiRevealModalProps) {
  const { teamNames } = useGameStore();

  const handleRevealCards = () => {
    // Send the reveal action to the server with revealCards=true
    socketService.sendGameAction("reveal_jordhi_cards", {
      jordhiCallId: jordhiCall.playerId,
      jordhiValue: jordhiCall.value,
      jordhiSuit: jordhiCall.jordhiSuit,
      jordhiCards: jordhiCall.jordhiCards,
      revealCards: true
    })
    .then(() => {
      console.log("Jordhi cards revealed successfully");
      onClose();
    })
    .catch(err => {
      console.error("Error revealing Jordhi cards:", err);
    });
  };

  const handleDontReveal = () => {
    // Send the reveal action to the server with revealCards=false
    socketService.sendGameAction("reveal_jordhi_cards", {
      jordhiCallId: jordhiCall.playerId,
      jordhiValue: jordhiCall.value,
      jordhiSuit: jordhiCall.jordhiSuit,
      jordhiCards: jordhiCall.jordhiCards,
      revealCards: false
    })
    .then(() => {
      console.log("Jordhi cards not revealed");
      onClose();
    })
    .catch(err => {
      console.error("Error processing Jordhi reveal choice:", err);
    });
  };

  // Format suit name for display
  const formatSuit = (suit: string) => {
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
        >
          <div
            className="absolute inset-0 bg-black/50"
            onClick={onClose}
          />
          <Card className="relative w-full  bg-black border-2 border-[#E1C760] p-4 z-10">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#E1C760]">Reveal Jordhi Cards?</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-[#E1C760] hover:text-white hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <p className="text-white">
                You've made a valid Jordhi call of <span className="font-bold text-[#E1C760]">{jordhiCall.value}</span> in <span className="font-bold text-[#E1C760]">{formatSuit(jordhiCall.jordhiSuit)}</span>.
              </p>

              <p className="text-white">
                Would you like to reveal your Jordhi cards to all players?
              </p>

              {/* Display the cards */}
              <div className="flex justify-center gap-2 my-4">
                {jordhiCall.jordhiCards.map((card, index) => (
                  <div key={index} className="w-20 h-28 relative">
                    <Card className="w-full h-full flex items-center justify-center bg-white border-2 border-[#E1C760]">
                      <img
                        src={getCardImagePath(card.value, card.suit)}
                        alt={`${card.value} of ${card.suit}`}
                        className="w-full h-full object-contain"
                      />
                    </Card>
                  </div>
                ))}
              </div>

              <div className="flex justify-center gap-4 mt-6">
                <Button
                  variant="outline"
                  onClick={handleDontReveal}
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                >
                  Don't Reveal
                </Button>
                <Button
                  onClick={handleRevealCards}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                >
                  Reveal Cards
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
