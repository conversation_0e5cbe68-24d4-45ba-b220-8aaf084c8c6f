// "use client";
// import { useState, useEffect } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { useGameStore } from "@/store/gameStore";
// import { useTimeSettingsStore } from "@/store/timeSettingsStore";
// import socketService from "@/services/socketService";
// import { Clock } from "lucide-react";

// interface PlayTimeframeVotingProps {
//   onVotingComplete: () => void;
// }

// type TimeOption = 3 | 4 | 5 | 6 | 60;
// type VoteResults = Record<TimeOption, number>;

// export default function PlayTimeframeVoting({
//   onVotingComplete,
// }: PlayTimeframeVotingProps) {
//   const { players } = useGameStore();
//   const { settings } = useTimeSettingsStore();
//   const [selectedOption, setSelectedOption] = useState<TimeOption | null>(null);
//   const [hasVoted, setHasVoted] = useState(false);
//   const [isSubmitting, setIsSubmitting] = useState(false);

//   // Initialize vote results based on available options
//   const [voteResults, setVoteResults] = useState<VoteResults>(() => {
//     const results: VoteResults = {} as VoteResults;
//     settings.playTimeframeOptions.forEach(option => {
//       results[option as TimeOption] = 0;
//     });
//     return results;
//   });

//   const [votingTimeLeft, setVotingTimeLeft] = useState(settings.votingTimeLimit);
//   const [playersVoted, setPlayersVoted] = useState<string[]>([]);
//   const [selectedTimeframe, setSelectedTimeframe] = useState<TimeOption | null>(null);
//   const [showResults, setShowResults] = useState(false);

//   // Time options from settings
//   const timeOptions: TimeOption[] = settings.playTimeframeOptions as TimeOption[];

//   // Handle option selection
//   const handleSelectOption = (option: TimeOption) => {
//     if (hasVoted || isSubmitting) return;
//     setSelectedOption(option);
//   };

//   // Handle vote submission
//   const handleSubmitVote = async () => {
//     if (!selectedOption || hasVoted || isSubmitting) return;

//     try {
//       setIsSubmitting(true);
//       await socketService.sendGameAction("vote_timeframe", {
//         timeframe: selectedOption,
//       });
//       setHasVoted(true);
//       setIsSubmitting(false);
//     } catch (error) {
//       console.error("Error submitting vote:", error);
//       setIsSubmitting(false);
//     }
//   };

//   // Listen for voting events from the server
//   useEffect(() => {
//     const handleVoteReceived = (data: {
//       playerId: string;
//       playerName: string;
//     }) => {
//       // Add player to the list of players who have voted
//       setPlayersVoted((prev) => [...prev, data.playerId]);
//     };

//     const handleVoteResults = (data: {
//       results: VoteResults;
//       selectedTimeframe: TimeOption;
//     }) => {
//       setVoteResults(data.results);
//       setSelectedTimeframe(data.selectedTimeframe);
//       setShowResults(true);

//       // After showing results for 3 seconds, complete the voting process
//       setTimeout(() => {
//         onVotingComplete();
//       }, 3000);
//     };

//     socketService.on("timeframe_vote_received", handleVoteReceived);
//     socketService.on("timeframe_vote_results", handleVoteResults);

//     return () => {
//       socketService.off("timeframe_vote_received", handleVoteReceived);
//       socketService.off("timeframe_vote_results", handleVoteResults);
//     };
//   }, [onVotingComplete]);

//   // Countdown timer for voting
//   useEffect(() => {
//     if (hasVoted || showResults) return;

//     const timer = setInterval(() => {
//       setVotingTimeLeft((prev) => {
//         if (prev <= 1) {
//           clearInterval(timer);
//           // If time runs out and player hasn't voted, auto-submit with default (3 seconds)
//           if (!hasVoted && !isSubmitting) {
//             handleSubmitVote();
//           }
//           return 0;
//         }
//         return prev - 1;
//       });
//     }, 1000);

//     return () => clearInterval(timer);
//   }, [hasVoted, isSubmitting, showResults]);

//   // Check if all players have voted
//   useEffect(() => {
//     if (playersVoted.length === players.length && players.length > 0) {
//       // All players have voted, server will send results
//       console.log("All players have voted");
//     }
//   }, [playersVoted, players]);

//   return (
//     <AnimatePresence>
//       <motion.div
//         initial={{ opacity: 0 }}
//         animate={{ opacity: 1 }}
//         exit={{ opacity: 0 }}
//         className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4"
//       >
//         <div className="bg-gray-900 border-2 border-[#E1C760] rounded-lg w-full max-w-[30rem] mx-auto p-4 max-h-[90vh] overflow-y-auto">

//           {!showResults ? (
//             <>
//               <div className="flex items-center justify-between mb-3">
//                 <h2 className="text-[#E1C760] text-lg sm:text-xl font-bold">
//                   Vote for Play Timeframe
//                 </h2>
//                 <div className="bg-black/70 text-[#E1C760] px-2 py-1 rounded-md text-sm">
//                   {votingTimeLeft}s
//                 </div>
//               </div>

//               <p className="text-white text-xs sm:text-sm mb-4">
//                 How many seconds should each player have to play a card? If a player doesn't play within the timeframe, the opposite team wins a ball.
//               </p>

//               {/* Mobile-optimized grid - single column for better mobile experience */}
//               <div className="grid grid-cols-2 gap-2 mb-4">
//                 {timeOptions.map((option) => (
//                   <button
//                     key={option}
//                     onClick={() => handleSelectOption(option)}
//                     disabled={hasVoted || isSubmitting}
//                     className={`p-3 rounded-lg flex flex-col items-center justify-center min-h-[120px] ${
//                       selectedOption === option
//                         ? "bg-[#E1C760] text-black"
//                         : "bg-gray-800 text-white hover:bg-gray-700 active:bg-gray-600"
//                     } ${
//                       hasVoted || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
//                     } transition-colors duration-200`}
//                   >
//                     <Clock className="mb-1" size={20} />
//                     <span className="text-xl font-bold">{option === 60 ? "1" : option}</span>
//                     <span className="text-xs">{option === 60 ? "minute" : "seconds"}</span>
//                   </button>
//                 ))}
//               </div>

//               <div className="flex justify-center mb-4">
//                 <button
//                   onClick={handleSubmitVote}
//                   disabled={!selectedOption || hasVoted || isSubmitting}
//                   className={`w-full py-3 rounded-lg font-bold text-sm transition-colors duration-200 ${
//                     selectedOption && !hasVoted && !isSubmitting
//                       ? "bg-[#E1C760] text-black active:bg-[#d4b854]"
//                       : "bg-gray-700 text-gray-400 cursor-not-allowed"
//                   }`}
//                 >
//                   {isSubmitting
//                     ? "Submitting..."
//                     : hasVoted
//                     ? "Vote Submitted"
//                     : "Submit Vote"}
//                 </button>
//               </div>

//               {hasVoted && (
//                 <div className="text-center text-white text-sm">
//                   Waiting for other players to vote...
//                   <div className="mt-2 flex justify-center">
//                     <div className="w-6 h-6 border-2 border-[#E1C760] border-t-transparent rounded-full animate-spin"></div>
//                   </div>
//                 </div>
//               )}
//             </>
//           ) : (
//             <>
//               <h2 className="text-[#E1C760] text-lg sm:text-xl font-bold mb-4 text-center">
//                 Voting Results
//               </h2>

//               <div className="grid grid-cols-2 gap-2 mb-4">
//                 {timeOptions.map((option) => (
//                   <div
//                     key={option}
//                     className={`p-3 rounded-lg flex flex-col items-center justify-center min-h-[80px] ${
//                       selectedTimeframe === option
//                         ? "bg-[#E1C760] text-black"
//                         : "bg-gray-800 text-white"
//                     }`}
//                   >
//                     <Clock className="mb-1" size={20} />
//                     <span className="text-xl font-bold">{option === 60 ? "1" : option}</span>
//                     <span className="text-xs">{option === 60 ? "minute" : "seconds"}</span>
//                     <div className="mt-1 text-xs">
//                       {voteResults[option]} vote{voteResults[option] !== 1 ? "s" : ""}
//                     </div>
//                   </div>
//                 ))}
//               </div>

//               <div className="text-center text-white">
//                 <p className="mb-2 text-sm">
//                   Selected timeframe: <span className="text-[#E1C760] font-bold">
//                     {selectedTimeframe === 60 ? "1 minute" : `${selectedTimeframe} seconds`}
//                   </span>
//                 </p>
//                 <p className="text-xs text-gray-400">
//                   Game will start in a moment...
//                 </p>
//               </div>
//             </>
//           )}
//         </div>
//       </motion.div>
//     </AnimatePresence>
//   );
// }
"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { useTimeSettingsStore } from "@/store/timeSettingsStore";
import socketService from "@/services/socketService";
import { Clock } from "lucide-react";

interface PlayTimeframeVotingProps {
  onVotingComplete: () => void;
}

type TimeOption = 3 | 4 | 5 | 6 | 60;
type VoteResults = Record<TimeOption, number>;

export default function PlayTimeframeVoting({
  onVotingComplete,
}: PlayTimeframeVotingProps) {
  const { players } = useGameStore();
  const { settings } = useTimeSettingsStore();
  const [selectedOption, setSelectedOption] = useState<TimeOption | null>(null);
  const [hasVoted, setHasVoted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize vote results based on available options
  const [voteResults, setVoteResults] = useState<VoteResults>(() => {
    const results: VoteResults = {} as VoteResults;
    settings.playTimeframeOptions.forEach(option => {
      results[option as TimeOption] = 0;
    });
    return results;
  });

  const [votingTimeLeft, setVotingTimeLeft] = useState(settings.votingTimeLimit);
  const [playersVoted, setPlayersVoted] = useState<string[]>([]);
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeOption | null>(null);
  const [showResults, setShowResults] = useState(false);

  // Time options from settings
  const timeOptions: TimeOption[] = settings.playTimeframeOptions as TimeOption[];

  // Handle option selection
  const handleSelectOption = (option: TimeOption) => {
    if (hasVoted || isSubmitting) return;
    setSelectedOption(option);
  };

  // Handle vote submission
  const handleSubmitVote = async () => {
    if (!selectedOption || hasVoted || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await socketService.sendGameAction("vote_timeframe", {
        timeframe: selectedOption,
      });
      setHasVoted(true);
      setIsSubmitting(false);
    } catch (error) {
      console.error("Error submitting vote:", error);
      setIsSubmitting(false);
    }
  };

  // Listen for voting events from the server
  useEffect(() => {
    const handleVoteReceived = (data: {
      playerId: string;
      playerName: string;
    }) => {
      // Add player to the list of players who have voted
      setPlayersVoted((prev) => [...prev, data.playerId]);
    };

    const handleVoteResults = (data: {
      results: VoteResults;
      selectedTimeframe: TimeOption;
    }) => {
      setVoteResults(data.results);
      setSelectedTimeframe(data.selectedTimeframe);
      setShowResults(true);

      // After showing results for 3 seconds, complete the voting process
      setTimeout(() => {
        onVotingComplete();
      }, 3000);
    };

    socketService.on("timeframe_vote_received", handleVoteReceived);
    socketService.on("timeframe_vote_results", handleVoteResults);

    return () => {
      socketService.off("timeframe_vote_received", handleVoteReceived);
      socketService.off("timeframe_vote_results", handleVoteResults);
    };
  }, [onVotingComplete]);

  // Countdown timer for voting
  useEffect(() => {
    if (hasVoted || showResults) return;

    const timer = setInterval(() => {
      setVotingTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // If time runs out and player hasn't voted, auto-submit with default (3 seconds)
          if (!hasVoted && !isSubmitting) {
            handleSubmitVote();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [hasVoted, isSubmitting, showResults]);

  // Check if all players have voted
  useEffect(() => {
    if (playersVoted.length === players.length && players.length > 0) {
      // All players have voted, server will send results
      console.log("All players have voted");
    }
  }, [playersVoted, players]);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-2 sm:p-4"
      >
        <div className="bg-gray-900 border-2 border-[#E1C760] rounded-lg w-full max-w-[95vw] max-h-[95vh] mx-auto p-3 overflow-y-auto
                        sm:max-w-[30rem] sm:p-4 sm:max-h-[90vh]">

          {!showResults ? (
            <>
              <div className="flex items-center justify-between mb-2 sm:mb-3">
                <h2 className="text-[#E1C760] text-base font-bold
                              sm:text-lg sm:font-bold
                              md:text-xl">
                  Vote for Play Timeframe
                </h2>
                <div className="bg-black/70 text-[#E1C760] px-1.5 py-0.5 rounded-md text-xs
                               sm:px-2 sm:py-1 sm:text-sm">
                  {votingTimeLeft}s
                </div>
              </div>

              <p className="text-white text-xs mb-2 leading-tight
                           sm:text-sm sm:mb-4 sm:leading-normal">
                How many seconds should each player have to play a card? If a player doesn't play within the timeframe, the opposite team wins a ball.
              </p>

              {/* Responsive grid - optimized for mobile landscape */}
              <div className="grid grid-cols-2 gap-1.5 mb-2
                            sm:gap-2 sm:mb-4
                            md:grid-cols-2">
                {timeOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleSelectOption(option)}
                    disabled={hasVoted || isSubmitting}
                    className={`p-2 rounded-lg flex flex-col items-center justify-center min-h-[60px] text-xs
                               sm:p-3 sm:min-h-[80px] sm:text-sm
                               md:min-h-[120px] md:text-base ${
                      selectedOption === option
                        ? "bg-[#E1C760] text-black"
                        : "bg-gray-800 text-white hover:bg-gray-700 active:bg-gray-600"
                    } ${
                      hasVoted || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                    } transition-colors duration-200`}
                  >
                    <Clock className="mb-0.5 sm:mb-1" size={16} />
                    <span className="text-base font-bold sm:text-lg md:text-xl">
                      {option === 60 ? "1" : option}
                    </span>
                    <span className="text-[0.6rem] sm:text-xs">
                      {option === 60 ? "minute" : "seconds"}
                    </span>
                  </button>
                ))}
              </div>

              <div className="flex justify-center mb-2 sm:mb-4">
                <button
                  onClick={handleSubmitVote}
                  disabled={!selectedOption || hasVoted || isSubmitting}
                  className={`w-full py-2 rounded-lg font-bold text-xs transition-colors duration-200
                             sm:py-3 sm:text-sm ${
                    selectedOption && !hasVoted && !isSubmitting
                      ? "bg-[#E1C760] text-black active:bg-[#d4b854]"
                      : "bg-gray-700 text-gray-400 cursor-not-allowed"
                  }`}
                >
                  {isSubmitting
                    ? "Submitting..."
                    : hasVoted
                    ? "Vote Submitted"
                    : "Submit Vote"}
                </button>
              </div>

              {hasVoted && (
                <div className="text-center text-white text-xs sm:text-sm">
                  Waiting for other players to vote...
                  <div className="mt-1 flex justify-center sm:mt-2">
                    <div className="w-4 h-4 border-2 border-[#E1C760] border-t-transparent rounded-full animate-spin
                                   sm:w-6 sm:h-6"></div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <h2 className="text-[#E1C760] text-base font-bold mb-2 text-center
                            sm:text-lg sm:mb-3
                            md:text-xl md:mb-4">
                Voting Results
              </h2>

              <div className="grid grid-cols-2 gap-1.5 mb-2
                            sm:gap-2 sm:mb-4
                            md:grid-cols-2">
                {timeOptions.map((option) => (
                  <div
                    key={option}
                    className={`p-2 rounded-lg flex flex-col items-center justify-center min-h-[50px] text-xs
                               sm:p-3 sm:min-h-[60px] sm:text-sm
                               md:min-h-[80px] md:text-base ${
                      selectedTimeframe === option
                        ? "bg-[#E1C760] text-black"
                        : "bg-gray-800 text-white"
                    }`}
                  >
                    <Clock className="mb-0.5 sm:mb-1" size={14} />
                    <span className="text-base font-bold sm:text-lg md:text-xl">
                      {option === 60 ? "1" : option}
                    </span>
                    <span className="text-[0.6rem] sm:text-xs">
                      {option === 60 ? "minute" : "seconds"}
                    </span>
                    <div className="mt-0.5 text-[0.6rem] sm:mt-1 sm:text-xs">
                      {voteResults[option]} vote{voteResults[option] !== 1 ? "s" : ""}
                    </div>
                  </div>
                ))}
              </div>

              <div className="text-center text-white">
                <p className="mb-1 text-xs sm:mb-2 sm:text-sm">
                  Selected timeframe: <span className="text-[#E1C760] font-bold">
                    {selectedTimeframe === 60 ? "1 minute" : `${selectedTimeframe} seconds`}
                  </span>
                </p>
                <p className="text-[0.6rem] text-gray-400 sm:text-xs">
                  Game will start in a moment...
                </p>
              </div>
            </>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}