"use client";
import { motion } from "framer-motion";
import { useState } from "react";

interface BidOptionsProps {
  availableBids: number[];
  onBidSelect: (bid: number) => void;
  selectedBid: number | null;
}

export default function BidOptions({
  availableBids,
  onBidSelect,
  selectedBid,
}: BidOptionsProps) {
  // Debug state to track if component is rendering
  const [isRendered, setIsRendered] = useState(true);

  console.log("BidOptions rendering with bids:", availableBids);

  if (!isRendered || availableBids.length === 0) {
    console.log("No bids available or component not rendered");
    return (
      <div className="p-4 bg-black/30 rounded-lg text-center">
        <p className="text-[#E1C760]">No bid options available</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-5 gap-2 mb-4">
        {availableBids.map((bid) => (
          <motion.button
            key={bid}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onBidSelect(bid)}
            className={`py-3 rounded-lg text-lg font-bold ${
              selectedBid === bid
                ? "bg-[#E1C760] text-black"
                : "bg-gray-700 text-[#E1C760] hover:bg-gray-600"
            }`}
          >
            {bid}
          </motion.button>
        ))}
      </div>
    </div>
  );
}
