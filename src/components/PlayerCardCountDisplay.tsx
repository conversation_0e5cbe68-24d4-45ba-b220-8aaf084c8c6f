// "use client";
// import { useGameStore } from "@/store/gameStore";
// import { motion, AnimatePresence } from "framer-motion";
// import { useEffect, useState } from "react";
// import { getCardBackImagePath } from "@/utils/cardUtils";
// import socketService from "@/services/socketService";

// export default function PlayerCardCountDisplay() {
//   const { players, hands, currentBall, gameStarted, gameEnded } = useGameStore();
//   const [currentPlayerId, setCurrentPlayerId] = useState<string | null>(null);
//   const [topLeftPlayerCards, setTopLeftPlayerCards] = useState<number>(6);
//   const [topRightPlayerCards, setTopRightPlayerCards] = useState<number>(6);
//   const [leftPlayerCards, setLeftPlayerCards] = useState<number>(6);

//   // Get current player ID
//   useEffect(() => {
//     const id = socketService.getSocketId();
//     setCurrentPlayerId(id);
//   }, []);

//   // Reset card counts when a new ball starts
//   useEffect(() => {
//     setTopLeftPlayerCards(6);
//     setTopRightPlayerCards(6);
//     setLeftPlayerCards(6);
//   }, [currentBall]);

//   // Update card counts based on completed hands only
//   useEffect(() => {
//     if (!currentPlayerId || players.length < 3) return;

//     // Calculate remaining cards based on unique completed hands
//     // Use a Set to get unique hand IDs to avoid counting duplicates
//     const uniqueHandIds = new Set(hands.map(hand => hand.id));
//     const completedHands = uniqueHandIds.size;
//     const remainingCards = Math.max(0, 6 - completedHands);

//     // All players have the same number of cards remaining
//     setTopLeftPlayerCards(remainingCards);
//     setTopRightPlayerCards(remainingCards);
//     setLeftPlayerCards(remainingCards);

//     console.log('Card count update:', {
//       completedHands,
//       remainingCards,
//       totalHands: hands.length,
//       uniqueHandIds: Array.from(uniqueHandIds)
//     });
//   }, [hands, players, currentPlayerId]);

//   // Render a realistic card stack like in the reference image
//   const renderCardStack = (cardCount: number, stackType: 'top-left' | 'top-right' | 'left') => {
//     if (cardCount <= 0) return null;

//     return (
//       <div className="relative">
//         {/* Card stack container */}
//         <div className="relative" style={{ width: "60px", height: "84px" }}>
//           <AnimatePresence>
//             {Array.from({ length: cardCount }, (_, index) => (
//               <motion.div
//                 key={`${stackType}-${index}`}
//                 initial={{ opacity: 1, scale: 1 }}
//                 exit={{
//                   opacity: 0,
//                   scale: 0.8,
//                   x: stackType === 'left' ? -20 : stackType === 'top-right' ? 20 : 0,
//                   y: stackType === 'top-left' || stackType === 'top-right' ? -20 : -10
//                 }}
//                 transition={{ duration: 0.3, ease: "easeInOut" }}
//                 className="absolute rounded-lg shadow-md"
//                 style={{
//                   left: `${index * 20}px`, // Very tight stacking like real cards
//                   top: `${index * 0}px`,
//                   zIndex: index,
//                   width: "90px",
//                   height: "124px",
//                   backgroundColor: "#ffffff",
//                   border: "1px solid #e5e7eb",
//                 }}
//               >
//                 <img
//                   src={getCardBackImagePath()}
//                   alt="Card Back"
//                   className="w-full h-full object-cover rounded-lg"
//                   style={{
//                     filter: index < cardCount - 1 ? 'brightness(0.95)' : 'brightness(1)', // Slightly darken lower cards
//                   }}
//                 />
//               </motion.div>
//             ))}
//           </AnimatePresence>

//           {/* Card count indicator */}
//           {/* <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
//             <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full font-medium">
//               {cardCount}
//             </span>
//           </div> */}
//         </div>
//       </div>
//     );
//   };

//   // Debug logging
//   console.log('PlayerCardCountDisplay - Debug Info:', {
//     gameStarted,
//     gameEnded,
//     playersLength: players.length,
//     currentPlayerId,
//     topLeftPlayerCards,
//     topRightPlayerCards,
//     leftPlayerCards,
//     players: players.map(p => ({ id: p.id, name: p.name, position: p.position }))
//   });

//   // Show when we have enough players and game is not ended
//   // Remove gameStarted requirement to show cards during setup
//   if (gameEnded || players.length < 3) {
//     console.log('PlayerCardCountDisplay - Not showing due to conditions:', {
//       gameStarted,
//       gameEnded,
//       playersLength: players.length
//     });
//     return null;
//   }

//   return (
//     <div className="fixed inset-0 z-30 pointer-events-none">
//       {/* Top left - Partner player cards */}
//       <div className="absolute left-6 -rotate-90"style={{ top: '50%' }}>
//         {renderCardStack(topLeftPlayerCards, 'top-left')}
//       </div>

//       {/* Top right - Opposing team player cards */}
//       {/* <div className="absolute top-80 right-28"> */}
//       <div className="absolute right-28 rotate-90" style={{ top: '40%' }}>
//         {renderCardStack(topRightPlayerCards, 'top-right')}
//       </div>

//       {/* Left middle - Other opposing team player cards */}
//       <div className="absolute left-1/2  transform -translate-y-1/2"style={{ top: '20%',left: '45%' }}>
//         {renderCardStack(leftPlayerCards, 'left')}
//       </div>
//     </div>
//   );
// }
"use client";
import { useGameStore } from "@/store/gameStore";
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";
import { getCardBackImagePath } from "@/utils/cardUtils";
import socketService from "@/services/socketService";

export default function PlayerCardCountDisplay() {
  const { players, hands, currentBall, gameStarted, gameEnded } = useGameStore();
  const [currentPlayerId, setCurrentPlayerId] = useState<string | null>(null);
  const [topLeftPlayerCards, setTopLeftPlayerCards] = useState<number>(6);
  const [topRightPlayerCards, setTopRightPlayerCards] = useState<number>(6);
  const [leftPlayerCards, setLeftPlayerCards] = useState<number>(6);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // md breakpoint
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Get current player ID
  useEffect(() => {
    const id = socketService.getSocketId();
    setCurrentPlayerId(id);
  }, []);

  // Reset card counts when a new ball starts
  useEffect(() => {
    setTopLeftPlayerCards(6);
    setTopRightPlayerCards(6);
    setLeftPlayerCards(6);
  }, [currentBall]);

  // Update card counts based on completed hands only
  useEffect(() => {
    if (!currentPlayerId || players.length < 3) return;

    // Calculate remaining cards based on unique completed hands
    // Use a Set to get unique hand IDs to avoid counting duplicates
    const uniqueHandIds = new Set(hands.map(hand => hand.id));
    const completedHands = uniqueHandIds.size;
    const remainingCards = Math.max(0, 6 - completedHands);

    // All players have the same number of cards remaining
    setTopLeftPlayerCards(remainingCards);
    setTopRightPlayerCards(remainingCards);
    setLeftPlayerCards(remainingCards);

    console.log('Card count update:', {
      completedHands,
      remainingCards,
      totalHands: hands.length,
      uniqueHandIds: Array.from(uniqueHandIds)
    });
  }, [hands, players, currentPlayerId]);

  // Render a realistic card stack like in the reference image
  const renderCardStack = (cardCount: number, stackType: 'top-left' | 'top-right' | 'left') => {
    if (cardCount <= 0) return null;

    // Mobile-specific dimensions
    const cardDimensions = isMobile ? {
      width: "40px",
      height: "60px",
      stackOffset: 8 // Reduced stacking offset for mobile
    } : {
      width: "90px",
      height: "124px",
      stackOffset: 20
    };

    const containerDimensions = isMobile ? {
      width: "32px",
      height: "50px"
    } : {
      width: "60px",
      height: "84px"
    };

    return (
      <div className="relative">
        {/* Card stack container */}
        <div className="relative" style={containerDimensions}>
          <AnimatePresence>
            {Array.from({ length: cardCount }, (_, index) => (
              <motion.div
                key={`${stackType}-${index}`}
                initial={{ opacity: 1, scale: 1 }}
                exit={{
                  opacity: 0,
                  scale: 0.8,
                  x: stackType === 'left' ? -20 : stackType === 'top-right' ? 20 : 0,
                  y: stackType === 'top-left' || stackType === 'top-right' ? -20 : -10
                }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="absolute rounded-lg shadow-md"
                style={{
                  left: `${index * cardDimensions.stackOffset}px`, // Responsive stacking
                  top: `${index * 0}px`,
                  zIndex: index,
                  width: cardDimensions.width,
                  height: cardDimensions.height,
                  backgroundColor: "#ffffff",
                  border: "1px solid #e5e7eb",
                }}
              >
                <img
                  src={getCardBackImagePath()}
                  alt="Card Back"
                  className="w-full h-full object-cover rounded-lg"
                  style={{
                    filter: index < cardCount - 1 ? 'brightness(0.95)' : 'brightness(1)', // Slightly darken lower cards
                  }}
                />
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Card count indicator */}
          {/* <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
            <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full font-medium">
              {cardCount}
            </span>
          </div> */}
        </div>
      </div>
    );
  };

  // Debug logging
  console.log('PlayerCardCountDisplay - Debug Info:', {
    gameStarted,
    gameEnded,
    playersLength: players.length,
    currentPlayerId,
    topLeftPlayerCards,
    topRightPlayerCards,
    leftPlayerCards,
    isMobile,
    players: players.map(p => ({ id: p.id, name: p.name, position: p.position }))
  });

  // Show when we have enough players and game is not ended
  // Remove gameStarted requirement to show cards during setup
  if (gameEnded || players.length < 3) {
    console.log('PlayerCardCountDisplay - Not showing due to conditions:', {
      gameStarted,
      gameEnded,
      playersLength: players.length
    });
    return null;
  }

  return (
    <div className="fixed inset-0 z-30 pointer-events-none">
      {/* Top left - Partner player cards */}
      <div className={`absolute ${isMobile ? 'left-2' : 'left-6'} -rotate-90`} style={{ top: '50%' }}>
        {renderCardStack(topLeftPlayerCards, 'top-left')}
      </div>

      {/* Top right - Opposing team player cards */}
      <div className={`absolute ${isMobile ? 'right-12' : 'right-28'} rotate-90`} style={{ top: '40%' }}>
        {renderCardStack(topRightPlayerCards, 'top-right')}
      </div>

      {/* Left middle - Other opposing team player cards */}
      <div className="absolute left-1/2 transform -translate-y-1/2" style={{ top: '20%', left: '45%' }}>
        {renderCardStack(leftPlayerCards, 'left')}
      </div>
    </div>
  );
}