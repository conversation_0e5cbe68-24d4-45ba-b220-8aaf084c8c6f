"use client";
import { useState, KeyboardEvent } from "react";
import { useChatStore } from "@/store/chatStore";
import { Send } from "lucide-react";

export default function ChatInput() {
  const [message, setMessage] = useState("");
  const { sendMessage } = useChatStore();

  const handleSendMessage = () => {
    if (message.trim()) {
      sendMessage(message);
      setMessage("");
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex items-center gap-2">
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Type a message..."
        className="flex-1 bg-gray-800 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#E1C760]/50"
      />
      <button
        onClick={handleSendMessage}
        disabled={!message.trim()}
        className={`p-2 rounded-full ${
          message.trim()
            ? "bg-[#E1C760] text-black"
            : "bg-gray-700 text-gray-400"
        }`}
      >
        <Send size={20} />
      </button>
    </div>
  );
}
