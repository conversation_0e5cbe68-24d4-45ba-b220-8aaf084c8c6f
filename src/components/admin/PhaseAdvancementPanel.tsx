"use client";
import { useState } from "react";
import { Play, CheckCircle, AlertCircle, Trophy, Users, Clock, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { apiBaseUrl } from "@/config/env";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  phase: string;
  status: string;
}

interface AdminLobbyView {
  lobbyId: string;
  lobbyCode: string;
  phase: string;
  matchStatus: string;
  matchScheduledAt?: string;
  bestOfGames: number;
  requiredWins: number;
  completedAt?: string;
  team1Name: string;
  team2Name: string;
  gamesPlayed: number;
  team1Wins: number;
  team2Wins: number;
  winnerTeamName?: string;
  winnerTeamId?: string;
}

interface PhaseAdvancementPanelProps {
  competitionId: string;
  competition: Competition;
  adminLobbies: AdminLobbyView[];
  onAdvancement: () => void;
}

export default function PhaseAdvancementPanel({
  competitionId,
  competition,
  adminLobbies,
  onAdvancement
}: PhaseAdvancementPanelProps) {
  const { user } = useAuthStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [winners, setWinners] = useState<any[]>([]);
  const [isLoadingWinners, setIsLoadingWinners] = useState(false);

  const getNextPhase = (currentPhase: string): string => {
    const phaseMap: Record<string, string> = {
      "Top32": "Top16",
      "Top16": "Top8",
      "Top8": "Top4",
      "Top4": "Final",
      "Final": "Completed"
    };
    return phaseMap[currentPhase] || currentPhase;
  };

  const completedMatches = adminLobbies.filter(lobby => lobby.matchStatus === "Completed");
  const incompleteMatches = adminLobbies.filter(lobby => lobby.matchStatus !== "Completed");
  const canAdvance = incompleteMatches.length === 0 && adminLobbies.length > 0;

  const loadPhaseWinners = async () => {
    try {
      setIsLoadingWinners(true);
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/${competition.phase}/winners`, {
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setWinners(data.data || []);
      }
    } catch (error) {
      console.error("Error loading phase winners:", error);
    } finally {
      setIsLoadingWinners(false);
    }
  };

  const handleAdvancePhase = async () => {
    if (!canAdvance) {
      toast.error("Cannot advance phase. Some matches are still incomplete.");
      return;
    }

    try {
      setIsProcessing(true);
      const nextPhase = getNextPhase(competition.phase);

      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/advance`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          newPhase: nextPhase
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Phase advanced successfully to ${nextPhase}!`);
        setShowConfirmDialog(false);
        onAdvancement();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to advance phase");
      }
    } catch (error) {
      console.error("Error advancing phase:", error);
      toast.error("Failed to advance phase");
    } finally {
      setIsProcessing(false);
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Top32": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top16": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Top8": return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Top4": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Final": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "text-green-400";
      case "InProgress": return "text-blue-400";
      case "Scheduled": return "text-yellow-400";
      default: return "text-gray-400";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#E1C760]">Phase Advancement</h2>
          <p className="text-gray-400">Manage progression from {competition.phase} to {getNextPhase(competition.phase)}</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={getPhaseColor(competition.phase)}>
            Current: {competition.phase}
          </Badge>
          <ArrowRight className="h-4 w-4 text-gray-400" />
          <Badge className={getPhaseColor(getNextPhase(competition.phase))}>
            Next: {getNextPhase(competition.phase)}
          </Badge>
        </div>
      </div>

      {/* Phase Status Overview */}
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardHeader>
          <CardTitle className="text-[#E1C760]">Phase Status Overview</CardTitle>
          <CardDescription className="text-gray-400">
            Current phase completion status and advancement readiness
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-white">{adminLobbies.length}</p>
              <p className="text-sm text-gray-400">Total Matches</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{completedMatches.length}</p>
              <p className="text-sm text-gray-400">Completed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-400">{incompleteMatches.length}</p>
              <p className="text-sm text-gray-400">Remaining</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-[#E1C760]">
                {adminLobbies.length > 0 ? Math.round((completedMatches.length / adminLobbies.length) * 100) : 0}%
              </p>
              <p className="text-sm text-gray-400">Complete</p>
            </div>
          </div>

          {/* Advancement Status */}
          {canAdvance ? (
            <Alert className="border-green-500/30 bg-green-500/10">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <AlertDescription className="text-green-400">
                All matches completed! Ready to advance to {getNextPhase(competition.phase)} phase.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert className="border-yellow-500/30 bg-yellow-500/10">
              <AlertCircle className="h-4 w-4 text-yellow-400" />
              <AlertDescription className="text-yellow-400">
                {incompleteMatches.length} matches still need to be completed before advancing.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Match Status List */}
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardHeader>
          <CardTitle className="text-[#E1C760]">Match Status</CardTitle>
          <CardDescription className="text-gray-400">
            Overview of all matches in the current phase
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {adminLobbies.map((lobby) => (
              <div
                key={lobby.lobbyId}
                className={`flex items-center justify-between p-4 rounded-lg border ${
                  lobby.matchStatus === "Completed"
                    ? "border-green-500/30 bg-green-500/5"
                    : "border-gray-600 bg-gray-800/30"
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {lobby.matchStatus === "Completed" ? (
                      <CheckCircle className="h-5 w-5 text-green-400" />
                    ) : (
                      <Clock className="h-5 w-5 text-yellow-400" />
                    )}
                    <span className="font-medium text-white">
                      {lobby.team1Name} vs {lobby.team2Name}
                    </span>
                  </div>
                  
                  <Badge variant="outline" className={getMatchStatusColor(lobby.matchStatus)}>
                    {lobby.matchStatus}
                  </Badge>
                </div>

                <div className="flex items-center gap-4 text-sm">
                  <span className="text-gray-400">
                    {lobby.team1Wins}-{lobby.team2Wins} (Best of {lobby.bestOfGames})
                  </span>
                  {lobby.winnerTeamName && (
                    <span className="text-[#E1C760] font-medium">
                      Winner: {lobby.winnerTeamName}
                    </span>
                  )}
                  <span className="text-gray-500 font-mono">
                    {lobby.lobbyCode}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {adminLobbies.length === 0 && (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Matches Created</h3>
              <p className="text-gray-400">
                Create knockout lobbies first before advancing the phase.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Advancement Actions */}
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardHeader>
          <CardTitle className="text-[#E1C760]">Phase Advancement Actions</CardTitle>
          <CardDescription className="text-gray-400">
            Advance the competition to the next phase when ready
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-white">Advance to {getNextPhase(competition.phase)}</h4>
              <p className="text-sm text-gray-400">
                Move winning teams to the next phase and eliminate losers
              </p>
            </div>
            
            <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
              <DialogTrigger asChild>
                <Button
                  disabled={!canAdvance || competition.phase === "Final"}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                  onClick={loadPhaseWinners}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Advance Phase
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-[#1A1A1A] border-[#333333] text-white max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="text-[#E1C760]">
                    Confirm Phase Advancement
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <Alert className="border-yellow-500/30 bg-yellow-500/10">
                    <AlertCircle className="h-4 w-4 text-yellow-400" />
                    <AlertDescription className="text-yellow-400">
                      This action will advance the competition from {competition.phase} to {getNextPhase(competition.phase)}.
                      Winning teams will be moved to the next phase and losing teams will be eliminated.
                    </AlertDescription>
                  </Alert>

                  <div className="bg-gray-800/50 rounded-lg p-4">
                    <h4 className="font-medium text-white mb-3">Phase Summary:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-400">Total Matches:</p>
                        <p className="text-white font-medium">{adminLobbies.length}</p>
                      </div>
                      <div>
                        <p className="text-gray-400">Completed:</p>
                        <p className="text-green-400 font-medium">{completedMatches.length}</p>
                      </div>
                      <div>
                        <p className="text-gray-400">Winners Advancing:</p>
                        <p className="text-[#E1C760] font-medium">{completedMatches.length}</p>
                      </div>
                      <div>
                        <p className="text-gray-400">Teams Eliminated:</p>
                        <p className="text-red-400 font-medium">{completedMatches.length}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-3 justify-end">
                    <Button
                      variant="outline"
                      onClick={() => setShowConfirmDialog(false)}
                      className="border-gray-600 text-gray-300 hover:bg-gray-800"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAdvancePhase}
                      disabled={isProcessing}
                      className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                    >
                      {isProcessing ? "Advancing..." : `Advance to ${getNextPhase(competition.phase)}`}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {!canAdvance && (
            <div className="text-sm text-gray-400">
              Complete all matches before advancing the phase.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
