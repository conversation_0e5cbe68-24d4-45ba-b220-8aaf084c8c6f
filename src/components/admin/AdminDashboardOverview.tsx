"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Trophy, Users, Clock, CheckCircle, AlertCircle, Play, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  phase: string;
  status: string;
  currentTeams: number;
  maxTeams: number;
  startDate: string;
  endDate?: string;
}

interface CompetitionStats {
  totalCompetitions: number;
  activeCompetitions: number;
  knockoutCompetitions: number;
  totalTeams: number;
  activeMatches: number;
  completedMatches: number;
}

export default function AdminDashboardOverview() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [stats, setStats] = useState<CompetitionStats>({
    totalCompetitions: 0,
    activeCompetitions: 0,
    knockoutCompetitions: 0,
    totalTeams: 0,
    activeMatches: 0,
    completedMatches: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
      return;
    }
    
    loadDashboardData();
  }, [isAuthenticated, navigate]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Load competitions
      const competitionsData = await apiService.getCompetitions();
      setCompetitions(competitionsData);
      
      // Calculate stats
      const activeComps = competitionsData.filter(c => c.status === 'active');
      const knockoutComps = competitionsData.filter(c => 
        ['Top32', 'Top16', 'Top8', 'Top4', 'Final'].includes(c.phase)
      );
      
      setStats({
        totalCompetitions: competitionsData.length,
        activeCompetitions: activeComps.length,
        knockoutCompetitions: knockoutComps.length,
        totalTeams: competitionsData.reduce((sum, c) => sum + c.currentTeams, 0),
        activeMatches: 0, // Would need API endpoint for this
        completedMatches: 0 // Would need API endpoint for this
      });
      
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setIsLoading(false);
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Leaderboard": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Top32": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top16": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Top8": return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Top4": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Final": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "upcoming": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "completed": return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      default: return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
    }
  };

  const isKnockoutPhase = (phase: string): boolean => {
    return ["Top32", "Top16", "Top8", "Top4", "Final"].includes(phase);
  };

  const getNextAction = (competition: Competition) => {
    if (competition.phase === "Leaderboard") {
      return "Start Knockout";
    } else if (isKnockoutPhase(competition.phase)) {
      return "Manage Phase";
    }
    return "View Details";
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-[#E1C760]">Admin Dashboard</h1>
            <p className="text-gray-400">Manage competitions and knockout tournaments</p>
          </div>
          
          <Button
            onClick={() => navigate("/admin/competitions/create")}
            className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
          >
            <Trophy className="h-4 w-4 mr-2" />
            Create Competition
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Competitions</p>
                  <p className="text-2xl font-bold text-white">{stats.totalCompetitions}</p>
                </div>
                <Trophy className="h-8 w-8 text-[#E1C760]" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Active Competitions</p>
                  <p className="text-2xl font-bold text-green-400">{stats.activeCompetitions}</p>
                </div>
                <Play className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Knockout Phase</p>
                  <p className="text-2xl font-bold text-purple-400">{stats.knockoutCompetitions}</p>
                </div>
                <Settings className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Teams</p>
                  <p className="text-2xl font-bold text-blue-400">{stats.totalTeams}</p>
                </div>
                <Users className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Competitions */}
        <Card className="bg-black/50 border-[#E1C760]/30 mb-8">
          <CardHeader>
            <CardTitle className="text-[#E1C760]">Active Competitions</CardTitle>
            <CardDescription className="text-gray-400">
              Competitions currently running or ready for management
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {competitions.filter(c => c.status === 'active').map((competition) => (
                <div
                  key={competition.id}
                  className="flex items-center justify-between p-4 border border-gray-600 bg-gray-800/30 rounded-lg hover:border-[#E1C760]/50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <Trophy className="h-6 w-6 text-[#E1C760]" />
                    <div>
                      <h3 className="font-medium text-white">{competition.name}</h3>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getPhaseColor(competition.phase)}>
                          {competition.phase}
                        </Badge>
                        <Badge className={getStatusColor(competition.status)}>
                          {competition.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-sm text-gray-400">Teams</p>
                      <p className="font-medium text-white">
                        {competition.currentTeams}/{competition.maxTeams}
                      </p>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate(`/admin/competitions/${competition.id}`)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-800"
                      >
                        View Details
                      </Button>
                      
                      {isKnockoutPhase(competition.phase) ? (
                        <Button
                          size="sm"
                          onClick={() => navigate(`/admin/competitions/${competition.id}/knockout`)}
                          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Manage Knockout
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          onClick={() => navigate(`/admin/competitions/${competition.id}/phase-management`)}
                          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          {getNextAction(competition)}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {competitions.filter(c => c.status === 'active').length === 0 && (
              <div className="text-center py-12">
                <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No Active Competitions</h3>
                <p className="text-gray-400 mb-4">
                  Create a new competition to get started with tournament management.
                </p>
                <Button
                  onClick={() => navigate("/admin/competitions/create")}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                >
                  <Trophy className="h-4 w-4 mr-2" />
                  Create Competition
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* All Competitions */}
        <Card className="bg-black/50 border-[#E1C760]/30">
          <CardHeader>
            <CardTitle className="text-[#E1C760]">All Competitions</CardTitle>
            <CardDescription className="text-gray-400">
              Complete list of all competitions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {competitions.map((competition) => (
                <div
                  key={competition.id}
                  className="flex items-center justify-between p-3 border border-gray-700 bg-gray-900/30 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <Trophy className="h-5 w-5 text-[#E1C760]" />
                    <div>
                      <p className="font-medium text-white">{competition.name}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getPhaseColor(competition.phase)} variant="outline">
                          {competition.phase}
                        </Badge>
                        <Badge className={getStatusColor(competition.status)} variant="outline">
                          {competition.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="text-right text-sm">
                      <p className="text-gray-400">
                        {competition.currentTeams} teams
                      </p>
                      <p className="text-gray-500">
                        {new Date(competition.startDate).toLocaleDateString()}
                      </p>
                    </div>
                    
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => navigate(`/admin/competitions/${competition.id}`)}
                      className="text-[#E1C760] hover:bg-[#E1C760]/10"
                    >
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
