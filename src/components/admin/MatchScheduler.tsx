"use client";
import { useState } from "react";
import { Calendar, Clock, Bell, Save, AlertCircle, CheckCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { apiBaseUrl } from "@/config/env";
import { toast } from "sonner";

interface AdminLobbyView {
  lobbyId: string;
  lobbyCode: string;
  phase: string;
  matchStatus: string;
  matchScheduledAt?: string;
  bestOfGames: number;
  requiredWins: number;
  completedAt?: string;
  notificationSentAt?: string;
  team1Name: string;
  team2Name: string;
  team1Player1: string;
  team1Player2?: string;
  team2Player1: string;
  team2Player2?: string;
  gamesPlayed: number;
  team1Wins: number;
  team2Wins: number;
}

interface MatchSchedulerProps {
  competitionId: string;
  adminLobbies: AdminLobbyView[];
  onScheduleUpdated: () => void;
}

export default function MatchScheduler({
  competitionId,
  adminLobbies,
  onScheduleUpdated
}: MatchSchedulerProps) {
  const { user } = useAuthStore();
  const [selectedLobby, setSelectedLobby] = useState<AdminLobbyView | null>(null);
  const [scheduledDate, setScheduledDate] = useState<string>("");
  const [scheduledTime, setScheduledTime] = useState<string>("");
  const [customMessage, setCustomMessage] = useState<string>("");
  const [isScheduling, setIsScheduling] = useState(false);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);

  const unscheduledLobbies = adminLobbies.filter(lobby => 
    !lobby.matchScheduledAt && lobby.matchStatus !== "Completed"
  );
  const scheduledLobbies = adminLobbies.filter(lobby => 
    lobby.matchScheduledAt && lobby.matchStatus !== "Completed"
  );

  const handleScheduleMatch = async () => {
    if (!selectedLobby || !scheduledDate || !scheduledTime) {
      toast.error("Please select date and time for the match");
      return;
    }

    try {
      setIsScheduling(true);
      
      const scheduledAt = new Date(`${scheduledDate}T${scheduledTime}`).toISOString();

      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/lobbies/${selectedLobby.lobbyId}/schedule`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          scheduledAt,
          customMessage,
          sendImmediateNotification: true
        })
      });

      if (response.ok) {
        toast.success("Match scheduled successfully!");
        setShowScheduleDialog(false);
        setSelectedLobby(null);
        setScheduledDate("");
        setScheduledTime("");
        setCustomMessage("");
        onScheduleUpdated();
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to schedule match");
      }
    } catch (error) {
      console.error("Error scheduling match:", error);
      toast.error("Failed to schedule match");
    } finally {
      setIsScheduling(false);
    }
  };

  const handleSendReminder = async (lobbyId: string) => {
    try {
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/lobbies/${lobbyId}/notify?notificationType=MatchReminder`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        toast.success("Match reminder sent successfully!");
        onScheduleUpdated();
      } else {
        toast.error("Failed to send reminder");
      }
    } catch (error) {
      console.error("Error sending reminder:", error);
      toast.error("Failed to send reminder");
    }
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const isUpcoming = (dateTimeString: string) => {
    return new Date(dateTimeString) > new Date();
  };

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case "Pending": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Scheduled": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "InProgress": return "bg-green-500/20 text-green-400 border-green-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-[#E1C760]">Match Scheduler</h2>
          <p className="text-gray-400">Schedule matches and send notifications to players</p>
        </div>
        
        <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
          <DialogTrigger asChild>
            <Button 
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
              disabled={unscheduledLobbies.length === 0}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Match
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-[#1A1A1A] border-[#333333] text-white">
            <DialogHeader>
              <DialogTitle className="text-[#E1C760]">Schedule Match</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="lobby">Select Match</Label>
                <select
                  className="w-full p-2 bg-[#2A2A2A] border border-[#444444] rounded-md text-white"
                  value={selectedLobby?.lobbyId || ""}
                  onChange={(e) => {
                    const lobby = unscheduledLobbies.find(l => l.lobbyId === e.target.value);
                    setSelectedLobby(lobby || null);
                  }}
                >
                  <option value="">Select a match to schedule</option>
                  {unscheduledLobbies.map((lobby) => (
                    <option key={lobby.lobbyId} value={lobby.lobbyId}>
                      {lobby.team1Name} vs {lobby.team2Name} ({lobby.lobbyCode})
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="date">Match Date</Label>
                  <Input
                    id="date"
                    type="date"
                    value={scheduledDate}
                    onChange={(e) => setScheduledDate(e.target.value)}
                    className="bg-[#2A2A2A] border-[#444444]"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                
                <div>
                  <Label htmlFor="time">Match Time</Label>
                  <Input
                    id="time"
                    type="time"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                    className="bg-[#2A2A2A] border-[#444444]"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="message">Custom Message (Optional)</Label>
                <Textarea
                  id="message"
                  placeholder="Add a custom message for the players..."
                  value={customMessage}
                  onChange={(e) => setCustomMessage(e.target.value)}
                  className="bg-[#2A2A2A] border-[#444444] resize-none"
                  rows={3}
                />
              </div>

              <Button
                onClick={handleScheduleMatch}
                disabled={isScheduling || !selectedLobby || !scheduledDate || !scheduledTime}
                className="w-full bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
              >
                {isScheduling ? "Scheduling..." : "Schedule Match & Send Notifications"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Unscheduled Matches */}
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardHeader>
          <CardTitle className="text-[#E1C760]">Unscheduled Matches</CardTitle>
          <CardDescription className="text-gray-400">
            Matches that need to be scheduled
          </CardDescription>
        </CardHeader>
        <CardContent>
          {unscheduledLobbies.length > 0 ? (
            <div className="space-y-3">
              {unscheduledLobbies.map((lobby) => (
                <div
                  key={lobby.lobbyId}
                  className="flex items-center justify-between p-4 border border-yellow-500/30 bg-yellow-500/5 rounded-lg"
                >
                  <div className="flex items-center gap-4">
                    <AlertCircle className="h-5 w-5 text-yellow-400" />
                    <div>
                      <p className="font-medium text-white">
                        {lobby.team1Name} vs {lobby.team2Name}
                      </p>
                      <p className="text-sm text-gray-400">
                        Best of {lobby.bestOfGames} • Code: {lobby.lobbyCode}
                      </p>
                    </div>
                  </div>
                  
                  <Button
                    size="sm"
                    onClick={() => {
                      setSelectedLobby(lobby);
                      setShowScheduleDialog(true);
                    }}
                    className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">All Matches Scheduled</h3>
              <p className="text-gray-400">
                All matches have been scheduled or completed.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Scheduled Matches */}
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardHeader>
          <CardTitle className="text-[#E1C760]">Scheduled Matches</CardTitle>
          <CardDescription className="text-gray-400">
            Matches with scheduled dates and times
          </CardDescription>
        </CardHeader>
        <CardContent>
          {scheduledLobbies.length > 0 ? (
            <div className="space-y-3">
              {scheduledLobbies.map((lobby) => {
                const { date, time } = formatDateTime(lobby.matchScheduledAt!);
                const upcoming = isUpcoming(lobby.matchScheduledAt!);
                
                return (
                  <div
                    key={lobby.lobbyId}
                    className={`flex items-center justify-between p-4 rounded-lg border ${
                      upcoming
                        ? "border-blue-500/30 bg-blue-500/5"
                        : "border-gray-600 bg-gray-800/30"
                    }`}
                  >
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Clock className={`h-5 w-5 ${upcoming ? 'text-blue-400' : 'text-gray-400'}`} />
                        <div>
                          <p className="font-medium text-white">
                            {lobby.team1Name} vs {lobby.team2Name}
                          </p>
                          <p className="text-sm text-gray-400">
                            {date} at {time} • Best of {lobby.bestOfGames}
                          </p>
                        </div>
                      </div>
                      
                      <Badge className={getMatchStatusColor(lobby.matchStatus)}>
                        {lobby.matchStatus}
                      </Badge>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500 font-mono">
                        {lobby.lobbyCode}
                      </span>
                      
                      {upcoming && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSendReminder(lobby.lobbyId)}
                          className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                        >
                          <Bell className="h-4 w-4 mr-2" />
                          Send Reminder
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">No Scheduled Matches</h3>
              <p className="text-gray-400">
                Schedule matches to set specific dates and times for gameplay.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
