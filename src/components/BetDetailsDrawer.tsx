// components/BetDetailsDrawer.tsx
import { <PERSON>er, Drawer<PERSON>ontent, DrawerTrigger } from "@/components/ui/drawer";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface BetDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

export default function BetDetailsDrawer({
  open,
  onOpenChange,
  children,
}: BetDetailsProps) {
  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent>
        <div className="fixed inset-x-0 bottom-0">
          <div className="w-full max-w-xl mx-auto bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218] p-1 rounded-t-lg">
            <div className="bg-white p-4 space-y-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-black font-medium">Single (x3)</h3>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">Stake</label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded px-2 h-8"
                          placeholder="R100"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">
                        Total Stakes
                      </label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <div className="w-full border border-gray-300 rounded px-2 h-8 flex items-center bg-gray-50">
                          R300
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-black font-medium">Multiple (x1)</h3>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">Stake</label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <input
                          type="text"
                          className="w-full border border-gray-300 rounded px-2 h-8"
                          placeholder="R100"
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <label className="text-black/70 text-sm">
                        Potential Payout
                      </label>
                      <div className="flex items-center">
                        <div className="w-2 h-8 bg-[#CB9218] mr-2" />
                        <div className="w-full border border-gray-300 rounded px-2 h-8 flex items-center bg-gray-50">
                          R585.95
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-sm text-black/70">293/50</div>
                </div>
              </div>

              <button
                className={cn(
                  "w-full h-12 font-medium mt-4",
                  "bg-gradient-to-b from-[#FEDD18] via-[#E4AF18] to-[#CB9218]",
                  "hover:from-[#E4AF18] hover:via-[#CB9218] hover:to-[#B27918]",
                  "text-black rounded-lg"
                )}
              >
                Submit R400.00
              </button>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
