"use client";
import { useState } from "react";
import { useGameStore } from "@/store/gameStore";
import { Button } from "../ui/button";
import TrumpVisibilityVoting from "../TrumpVisibilityVoting";
import TrumpDisplay from "../TrumpDisplay";

/**
 * Test component for TrumpVisibilityVoting
 * This component allows us to manually test the TrumpVisibilityVoting behavior
 * and see how it integrates with the TrumpDisplay component
 */
export default function TrumpVisibilityVotingTest() {
  const { updateGameState, trumpSuit, trumpVisibilityMode, playedCards } = useGameStore();
  const [showVoting, setShowVoting] = useState(false);
  const [testCard] = useState({
    id: "test-card-1",
    value: "A",
    suit: "hearts",
    image: "/CardFaces/AH.svg",
    points: 11,
    playedBy: "test-player-1"
  });

  const handleSetTrump = (suit: string) => {
    updateGameState({ trumpSuit: suit });
  };

  const handlePlayTestCard = () => {
    updateGameState({ 
      playedCards: [testCard],
      currentHand: 1,
      currentBall: 1
    });
  };

  const handleClearCards = () => {
    updateGameState({ playedCards: [] });
  };

  const handleStartVoting = () => {
    setShowVoting(true);
  };

  const handleVotingComplete = () => {
    setShowVoting(false);
    console.log("Trump visibility voting completed!");
  };

  const handleSetVisibilityMode = (mode: 'short' | 'full') => {
    updateGameState({ trumpVisibilityMode: mode });
  };

  const handleResetGame = () => {
    updateGameState({
      trumpSuit: null,
      trumpVisibilityMode: null,
      playedCards: [],
      currentHand: 1,
      currentBall: 1
    });
  };

  return (
    <div className="p-6 bg-gray-900 min-h-screen text-white">
      <h1 className="text-2xl font-bold mb-6">Trump Visibility Voting Test</h1>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Current State</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p><strong>Trump Suit:</strong> {trumpSuit || "None"}</p>
            <p><strong>Trump Visibility Mode:</strong> {trumpVisibilityMode || "None"}</p>
            <p><strong>Played Cards:</strong> {playedCards.length}</p>
          </div>
          <div>
            <p><strong>Show Voting:</strong> {showVoting ? "Yes" : "No"}</p>
            <ul className="list-disc pl-5">
              {playedCards.map((card, index) => (
                <li key={index}>
                  {card.value} of {card.suit} (played by {(card as any).playedBy})
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Test Controls</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Trump Setup</h3>
            <div className="space-y-2">
              <Button onClick={() => handleSetTrump("hearts")} className="w-full">
                Set Trump: Hearts
              </Button>
              <Button onClick={() => handleSetTrump("diamonds")} className="w-full">
                Set Trump: Diamonds
              </Button>
              <Button onClick={() => handleSetTrump("clubs")} className="w-full">
                Set Trump: Clubs
              </Button>
              <Button onClick={() => handleSetTrump("spades")} className="w-full">
                Set Trump: Spades
              </Button>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">Game Actions</h3>
            <div className="space-y-2">
              <Button onClick={handlePlayTestCard} className="w-full">
                Play Test Card
              </Button>
              <Button onClick={handleClearCards} className="w-full">
                Clear Played Cards
              </Button>
              <Button onClick={handleStartVoting} className="w-full">
                Start Trump Visibility Voting
              </Button>
              <Button onClick={handleResetGame} className="w-full" variant="destructive">
                Reset Game
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Manual Visibility Mode</h2>
        <div className="space-x-2">
          <Button 
            onClick={() => handleSetVisibilityMode('short')}
            variant={trumpVisibilityMode === 'short' ? 'default' : 'outline'}
          >
            Short (8 seconds)
          </Button>
          <Button 
            onClick={() => handleSetVisibilityMode('full')}
            variant={trumpVisibilityMode === 'full' ? 'default' : 'outline'}
          >
            Full Hand
          </Button>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Instructions</h2>
        <ol className="list-decimal pl-5 space-y-2">
          <li>Set a trump suit using one of the "Set Trump" buttons</li>
          <li>Click "Start Trump Visibility Voting" to test the voting component</li>
          <li>Vote for either "8 Seconds" or "Full Hand" visibility</li>
          <li>After voting completes, click "Play Test Card" to trigger trump display</li>
          <li>Observe how the trump display behaves based on the voting result</li>
          <li>Test both voting options to see the different behaviors</li>
          <li>Use "Manual Visibility Mode" buttons to test without voting</li>
        </ol>
      </div>

      {/* Trump Visibility Voting Component */}
      {showVoting && (
        <TrumpVisibilityVoting onVotingComplete={handleVotingComplete} />
      )}

      {/* Trump Display Component */}
      <TrumpDisplay />
    </div>
  );
}
