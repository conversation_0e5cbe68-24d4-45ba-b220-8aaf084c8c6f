"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import "../styles/UnderChoppedModal.css";

export default function UnderChoppedModal() {
  const { players, teamNames, updateGameState, underChoppedModalOpen, currentPlayerId } = useGameStore();
  const [selectedPlayer, setSelectedPlayer] = useState<string | null>(null);
  const [selectedHand, setSelectedHand] = useState<number | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Reset state when modal opens
  useEffect(() => {
    if (underChoppedModalOpen) {
      setSelectedPlayer(null);
      setSelectedHand(null);
      setErrorMessage(null);
      setIsProcessing(false);
    }
  }, [underChoppedModalOpen]);

  // Close the modal
  const onClose = () => {
    updateGameState({ underChoppedModalOpen: false });
  };

  // Get the current player's team
  const currentPlayer = players.find(p => p.id === currentPlayerId);
  const currentTeam = currentPlayer?.team || 1;

  // Get players from the opposing team
  const opposingTeam = currentTeam === 1 ? 2 : 1;
  const opposingTeamPlayers = players.filter(p => p.team === opposingTeam);

  // Generate hand numbers (1-6 for Thunee)
  const handNumbers = Array.from({ length: 6 }, (_, i) => i + 1);

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedPlayer || !selectedHand) {
      setErrorMessage("Please select both a player and a hand number");
      return;
    }

    try {
      setIsProcessing(true);
      setErrorMessage(null);

      // Send the 4-ball action to the server
      await socketService.sendGameAction("four_eight_ball_selection", {
        ballType: "4 ball",
        option: "Under chopped",
        accusedPlayerId: selectedPlayer,
        handNumber: selectedHand
      });

      // Close the modal after successful submission
      onClose();
    } catch (error) {
      console.error("Error submitting 4-ball claim:", error);
      setErrorMessage(error instanceof Error ? error.message : "Failed to submit claim");
      setIsProcessing(false);
    }
  };

  return (
    <AnimatePresence>
      {underChoppedModalOpen && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 under-chopped-modal-container"
        >
          <div
            className="absolute inset-0 bg-black/50"
            onClick={onClose}
          />
          <Card className="relative w-full bg-black border-2 border-[#E1C760] p-4 z-10 under-chopped-modal-card max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-[#E1C760]">4-Ball: Under Chopped</h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-[#E1C760] hover:text-white hover:bg-gray-800"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <p className="text-gray-400 text-center mb-4 text-sm italic">
              Select a player from the opposing team who played a lower trump card when they had a higher trump card in hand.
              Also select the hand number where this occurred.
            </p>

            <div className="bg-gray-900 p-3 rounded-md mb-4 text-xs text-gray-300">
              <h4 className="text-[#E1C760] text-sm mb-1">Under Chopped Rules:</h4>
              <ul className="list-disc pl-4 space-y-1">
                <li><span className="text-red-400">Invalid:</span> If trump was the lead suit</li>
                <li><span className="text-red-400">Invalid:</span> If player played the highest trump in the hand</li>
                <li><span className="text-red-400">Invalid:</span> If player did not play a trump card</li>
                <li><span className="text-red-400">Invalid:</span> If player only had trump cards left</li>
                <li><span className="text-green-400">Valid:</span> If player had other suits but chose to play a lower trump</li>
              </ul>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-[#E1C760] font-semibold mb-2">Select Player</h3>
                <div className="grid grid-cols-2 gap-2">
                  {opposingTeamPlayers.map((player) => (
                    <Button
                      key={player.id}
                      onClick={() => setSelectedPlayer(player.id)}
                      className={`border ${
                        selectedPlayer === player.id
                          ? "bg-[#E1C760] text-black"
                          : "bg-black text-[#E1C760] border-[#E1C760]"
                      }`}
                    >
                      {player.name}
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="text-[#E1C760] font-semibold mb-2">Select Hand Number</h3>
                <div className="grid grid-cols-3 gap-2">
                  {handNumbers.map((num) => (
                    <Button
                      key={num}
                      onClick={() => setSelectedHand(num)}
                      className={`border ${
                        selectedHand === num
                          ? "bg-[#E1C760] text-black"
                          : "bg-black text-[#E1C760] border-[#E1C760]"
                      }`}
                    >
                      Hand #{num}
                    </Button>
                  ))}
                </div>
              </div>

              {errorMessage && (
                <div className="bg-red-900/50 border border-red-500 text-red-200 p-2 rounded-md text-sm">
                  {errorMessage}
                </div>
              )}

              <div className="flex justify-end mt-4">
                <Button
                  onClick={onClose}
                  className="bg-gray-800 text-white hover:bg-gray-700 mr-2"
                  disabled={isProcessing}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-[#E1C760] text-black hover:bg-[#c9b052]"
                  disabled={isProcessing || !selectedPlayer || !selectedHand}
                >
                  {isProcessing ? "Submitting..." : "Submit 4-Ball Claim"}
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
