import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, <PERSON>, PhoneOff, Mic, MicOff, Camera, CameraOff, Users } from "lucide-react";
import webRTCService, { PeerConnection } from "../services/webRTCService";
import { useGameStore } from "../store/gameStore";
import { useLobbyStore } from "../store/lobbyStore";
import { useAuthStore } from "../store/authStore";

interface RemotePeer {
  id: string;
  name: string;
  stream: MediaStream | null;
}

export default function WebRTCVideoCall() {
  const [isOpen, setIsOpen] = useState(false);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remotePeers, setRemotePeers] = useState<Map<string, RemotePeer>>(new Map());
  const [error, setError] = useState<string | null>(null);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const { players } = useGameStore();
  const { lobbyCode } = useLobbyStore();
  const { user } = useAuthStore();

  // Initialize WebRTC service callbacks
  useEffect(() => {
    webRTCService.setCallbacks({
      onLocalStream: (stream) => {
        console.log('WebRTC Video Call: Local stream received');
        setLocalStream(stream);
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
      },
      
      onRemoteStream: (peerId, stream, name) => {
        console.log(`WebRTC Video Call: Remote stream from ${name} (${peerId})`);
        setRemotePeers(prev => {
          const newPeers = new Map(prev);
          newPeers.set(peerId, { id: peerId, name, stream });
          return newPeers;
        });
      },
      
      onPeerConnected: (peerId, name) => {
        console.log(`WebRTC Video Call: Peer connected ${name} (${peerId})`);
      },
      
      onPeerDisconnected: (peerId) => {
        console.log(`WebRTC Video Call: Peer disconnected ${peerId}`);
        setRemotePeers(prev => {
          const newPeers = new Map(prev);
          newPeers.delete(peerId);
          return newPeers;
        });
      },
      
      onError: (error) => {
        console.error('WebRTC Video Call: Error', error);
        setError(error);
        setIsConnecting(false);
      }
    });

    return () => {
      // Cleanup on unmount
      handleEndCall();
    };
  }, []);

  // Update local video element when stream changes
  useEffect(() => {
    if (localVideoRef.current && localStream) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  const handleStartCall = async () => {
    if (!lobbyCode || !user?.username) {
      setError('Missing lobby code or user information');
      return;
    }

    try {
      setIsConnecting(true);
      setError(null);
      
      console.log('WebRTC Video Call: Starting call');
      await webRTCService.initialize(lobbyCode, user.username);
      
      setIsConnected(true);
      setIsOpen(true);
      console.log('WebRTC Video Call: Call started successfully');
    } catch (error) {
      console.error('WebRTC Video Call: Failed to start call', error);
      setError(`Failed to start call: ${error}`);
    } finally {
      setIsConnecting(false);
    }
  };

  const handleEndCall = async () => {
    try {
      console.log('WebRTC Video Call: Ending call');
      await webRTCService.cleanup();
      
      setIsOpen(false);
      setIsConnected(false);
      setLocalStream(null);
      setRemotePeers(new Map());
      setError(null);
      
      console.log('WebRTC Video Call: Call ended');
    } catch (error) {
      console.error('WebRTC Video Call: Error ending call', error);
    }
  };

  const toggleAudio = () => {
    const newState = !isAudioEnabled;
    setIsAudioEnabled(newState);
    webRTCService.toggleAudio(newState);
  };

  const toggleVideo = () => {
    const newState = !isVideoEnabled;
    setIsVideoEnabled(newState);
    webRTCService.toggleVideo(newState);
  };

  const getPlayerName = (socketId: string): string => {
    const player = players.find(p => p.socketId === socketId);
    return player?.name || socketId;
  };

  // Convert Map to Array for rendering
  const remotePeersArray = Array.from(remotePeers.values());

  if (!isOpen && !isConnected) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <motion.button
          onClick={handleStartCall}
          disabled={isConnecting}
          className="bg-[#edcf5d] hover:bg-[#d4b84a] text-black p-3 rounded-full shadow-lg transition-colors disabled:opacity-50"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isConnecting ? (
            <div className="w-6 h-6 border-2 border-black border-t-transparent rounded-full animate-spin" />
          ) : (
            <Video className="w-6 h-6" />
          )}
        </motion.button>
        
        {error && (
          <div className="absolute bottom-full right-0 mb-2 p-2 bg-red-600 text-white text-sm rounded whitespace-nowrap">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          className="fixed bottom-20 right-4 w-80 bg-black/90 border-2 border-[#edcf5d] rounded-lg overflow-hidden z-50"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-3 bg-[#edcf5d] text-black">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span className="font-semibold text-sm">Video Call</span>
              <span className="text-xs opacity-75">({remotePeersArray.length + 1})</span>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="hover:bg-black/10 p-1 rounded transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-2 bg-red-600 text-white text-xs">
              {error}
            </div>
          )}

          {/* Video Grid */}
          <div className="p-3 space-y-2">
            {/* Local Video */}
            <div className="relative aspect-video bg-neutral-800 rounded overflow-hidden">
              <video
                ref={localVideoRef}
                autoPlay
                muted
                playsInline
                className={`w-full h-full object-cover ${!isVideoEnabled ? 'invisible' : ''}`}
              />
              
              {!isVideoEnabled && (
                <div className="absolute inset-0 flex items-center justify-center bg-neutral-800">
                  <div className="w-12 h-12 rounded-full bg-[#edcf5d] flex items-center justify-center">
                    <span className="text-black font-semibold">
                      {user?.username?.charAt(0).toUpperCase() || 'Y'}
                    </span>
                  </div>
                </div>
              )}
              
              <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                You {!isAudioEnabled && '(muted)'}
              </div>
            </div>

            {/* Remote Videos */}
            {remotePeersArray.map((peer) => (
              <div key={peer.id} className="relative aspect-video bg-neutral-800 rounded overflow-hidden">
                {peer.stream && peer.stream.getVideoTracks().length > 0 ? (
                  <video
                    autoPlay
                    playsInline
                    className="w-full h-full object-cover"
                    ref={(el) => {
                      if (el && peer.stream) {
                        el.srcObject = peer.stream;
                      }
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="w-12 h-12 rounded-full bg-[#edcf5d] flex items-center justify-center">
                      <span className="text-black font-semibold">
                        {peer.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    {peer.stream && peer.stream.getAudioTracks().length > 0 && (
                      <div className="absolute top-2 right-2 bg-green-600 w-3 h-3 rounded-full"></div>
                    )}
                  </div>
                )}

                <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {peer.name}
                  {peer.stream && peer.stream.getVideoTracks().length === 0 && peer.stream.getAudioTracks().length > 0 && (
                    <span className="ml-1 text-yellow-300">(Audio Only)</span>
                  )}
                </div>
              </div>
            ))}

            {/* Placeholder for missing players */}
            {Array.from({ length: Math.max(0, 3 - remotePeersArray.length) }).map((_, i) => (
              <div key={`placeholder-${i}`} className="aspect-video bg-neutral-800 rounded flex items-center justify-center">
                <span className="text-neutral-500 text-xs">Waiting for player...</span>
              </div>
            ))}
          </div>

          {/* Controls */}
          <div className="flex items-center justify-center gap-2 p-3 bg-neutral-900">
            <button
              onClick={toggleAudio}
              className={`p-2 rounded-full transition-colors ${
                isAudioEnabled 
                  ? 'bg-neutral-700 hover:bg-neutral-600 text-white' 
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
            </button>
            
            <button
              onClick={toggleVideo}
              className={`p-2 rounded-full transition-colors ${
                isVideoEnabled 
                  ? 'bg-neutral-700 hover:bg-neutral-600 text-white' 
                  : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
            >
              {isVideoEnabled ? <Camera className="w-4 h-4" /> : <CameraOff className="w-4 h-4" />}
            </button>
            
            <button
              onClick={handleEndCall}
              className="p-2 rounded-full bg-red-600 hover:bg-red-700 text-white transition-colors"
            >
              <PhoneOff className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
