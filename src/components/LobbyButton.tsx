import React from "react";
import { But<PERSON> } from "@/components/ui/button";

interface LobbyButtonProps {
  svgComponent: React.ReactNode;
  buttonText: string;
  onClick?: () => void;
  centered?: boolean;
  disabled?: boolean;
}

export default function LobbyButton({
  svgComponent,
  buttonText,
  onClick,
  centered = false,
  disabled = false
}: LobbyButtonProps) {
  return (
    <div className="relative w-full mb-6">
      <div className="w-full">
        {svgComponent}
      </div>
      <div className={`flex ${centered ? 'justify-center' : 'justify-end mr-[6vw]'}`}>
        <Button
          onClick={onClick}
          className={`bg-transparent ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gold/20'} text-[#E1C760] text-lg py-2 px-4 mt-2 border border-[#E1C760] w-[160px]`}
          disabled={disabled}
        >
          {buttonText}
        </Button>
      </div>
    </div>
  );
}
