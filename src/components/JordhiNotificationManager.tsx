"use client";
import { useEffect, useState } from "react";
import { useGameStore } from "@/store/gameStore";
import JordhiNotification from "./JordhiNotification";
import { playSound, SOUNDS } from "@/utils/soundUtils";

type JordhiCall = {
  id: string;
  playerName: string;
  playerTeam: 1 | 2;
  value: number;
};

export default function JordhiNotificationManager() {
  const [notifications, setNotifications] = useState<JordhiCall[]>([]);
  const { jordhiCalls } = useGameStore();

  // Listen for changes to jordhiCalls and add new notifications
  useEffect(() => {
    if (jordhiCalls.length > 0) {
      const lastCall = jordhiCalls[jordhiCalls.length - 1];

      // Only add if it's a new call (not already in notifications)
      if (lastCall) {
        const newId = `${lastCall.playerId}-${Date.now()}`;

        // Play sound for new Jordhi call
        playSound(SOUNDS.JORDHI_CALL, 0.7);

        setNotifications(prev => [
          ...prev,
          {
            id: newId,
            playerName: lastCall.playerName,
            playerTeam: lastCall.playerTeam,
            value: lastCall.value
          }
        ]);

        // Remove notification after 8 seconds (longer than the notification itself)
        setTimeout(() => {
          setNotifications(prev => prev.filter(n => n.id !== newId));
        }, 8000);
      }
    }
  }, [jordhiCalls]);

  return (
    <>
      {notifications.map((notification) => (
        <JordhiNotification
          key={notification.id}
          playerName={notification.playerName}
          playerTeam={notification.playerTeam}
          value={notification.value}
          autoHideDuration={5000}
        />
      ))}
    </>
  );
}
