/**
 * Disconnection Notifications Component
 * Displays toast notifications and UI updates for player disconnections/reconnections
 */

import React, { useEffect, useState } from 'react';
import {
  disconnectionHandler,
  DisconnectionData,
  ReconnectionData,
  CountdownData,
  GamePauseData,
  GameResumeData,
  ToastNotification
} from '@/utils/disconnectionHandler';
import socketService from '@/services/socketService';

interface Toast {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  message: string;
  duration: number;
  timestamp: number;
}

export const DisconnectionNotifications: React.FC = () => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [disconnectedPlayers, setDisconnectedPlayers] = useState<DisconnectionData[]>([]);
  const [isGamePaused, setIsGamePaused] = useState(false);
  const [countdowns, setCountdowns] = useState<Map<string, number>>(new Map());

  useEffect(() => {
    console.log('DisconnectionNotifications: Component mounted, setting up direct socket event listeners');

    // Set up direct socket event listeners
    socketService.on('player_disconnected', (data: DisconnectionData) => {
      console.log('DisconnectionNotifications: Received player_disconnected event:', data);
      handlePlayerDisconnected(data);
    });

    socketService.on('player_reconnected', (data: ReconnectionData) => {
      console.log('DisconnectionNotifications: Received player_reconnected event:', data);
      handlePlayerReconnected(data);
    });

    socketService.on('disconnection_countdown', (data: CountdownData) => {
      console.log('DisconnectionNotifications: Received disconnection_countdown event:', data);
      handleCountdownUpdate(data);
    });

    socketService.on('game_paused', (data: GamePauseData) => {
      console.log('DisconnectionNotifications: Received game_paused event:', data);
      handleGamePaused(data);
    });

    socketService.on('game_resumed', (data: GameResumeData) => {
      console.log('DisconnectionNotifications: Received game_resumed event:', data);
      handleGameResumed(data);
    });

    socketService.on('toast_notification', (data: ToastNotification) => {
      console.log('DisconnectionNotifications: Received toast_notification event:', data);
      handleToastNotification(data);
    });

    console.log('DisconnectionNotifications: Direct socket event listeners set up successfully');

    // Cleanup on unmount
    return () => {
      console.log('DisconnectionNotifications: Component unmounting, removing socket listeners');
      socketService.off('player_disconnected');
      socketService.off('player_reconnected');
      socketService.off('disconnection_countdown');
      socketService.off('game_paused');
      socketService.off('game_resumed');
      socketService.off('toast_notification');
    };
  }, []);

  // Auto-remove toasts after their duration
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setToasts(prevToasts => 
        prevToasts.filter(toast => now - toast.timestamp < toast.duration)
      );
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handlePlayerDisconnected = (data: DisconnectionData) => {
    console.log('DisconnectionNotifications: handlePlayerDisconnected called with:', data);
    setDisconnectedPlayers(prev => [...prev, data]);

    addToast({
      type: 'warning',
      message: `${data.playerName} disconnected. Waiting ${data.timeoutSeconds || 300} seconds to reconnect.`,
      duration: 5000
    });
  };

  const handlePlayerReconnected = (data: ReconnectionData) => {
    setDisconnectedPlayers(prev => prev.filter(p => p.playerId !== data.playerId));
    setCountdowns(prev => {
      const newCountdowns = new Map(prev);
      newCountdowns.delete(data.playerId);
      return newCountdowns;
    });
    
    addToast({
      type: 'success',
      message: `${data.playerName} reconnected successfully!`,
      duration: 3000
    });
  };

  const handleCountdownUpdate = (data: CountdownData) => {
    setCountdowns(prev => {
      const newCountdowns = new Map(prev);
      newCountdowns.set(data.playerId, data.timeRemaining);
      return newCountdowns;
    });
  };

  const handleGamePaused = (data: GamePauseData) => {
    console.log('DisconnectionNotifications: handleGamePaused called with:', data);
    setIsGamePaused(true);

    addToast({
      type: 'warning',
      message: `Game paused: ${data.disconnectedPlayer.name} disconnected`,
      duration: 8000
    });
  };

  const handleGameResumed = (data: GameResumeData) => {
    setIsGamePaused(false);
    
    addToast({
      type: 'success',
      message: `Game resumed: ${data.reconnectedPlayer.name} reconnected`,
      duration: 3000
    });
  };

  const handleToastNotification = (data: ToastNotification) => {
    addToast({
      type: data.type,
      message: data.message,
      duration: data.duration || 5000
    });
  };

  const addToast = (toast: Omit<Toast, 'id' | 'timestamp'>) => {
    const newToast: Toast = {
      ...toast,
      id: `toast_${Date.now()}_${Math.random()}`,
      timestamp: Date.now()
    };
    
    setToasts(prev => [...prev, newToast]);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const getToastStyles = (type: string) => {
    const baseStyles = "p-4 rounded-lg shadow-lg max-w-sm mb-2";
    
    switch (type) {
      case 'success':
        return `${baseStyles} bg-green-500 text-white`;
      case 'warning':
        return `${baseStyles} bg-yellow-500 text-white`;
      case 'error':
        return `${baseStyles} bg-red-500 text-white`;
      case 'info':
      default:
        return `${baseStyles} bg-blue-500 text-white`;
    }
  };

  return (
    <>
      {/* Game Pause Overlay */}
      {isGamePaused && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-yellow-500 p-8 rounded-lg shadow-xl max-w-md text-center">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto mb-4 bg-yellow-500 rounded-full flex items-center justify-center">
                <span className="text-2xl">⏸️</span>
              </div>
              <h3 className="text-2xl font-bold text-yellow-400 mb-2">Game Paused</h3>
              <p className="text-gray-300 mb-4">
                The game is paused due to a player disconnection.
              </p>
              {disconnectedPlayers.length > 0 && (
                <div className="bg-gray-800 rounded-lg p-4 mb-4">
                  <h4 className="text-yellow-400 font-semibold mb-2">Waiting for:</h4>
                  {disconnectedPlayers.map(player => (
                    <div key={player.playerId} className="text-white mb-2">
                      <div className="font-medium">{player.playerName}</div>
                      {countdowns.has(player.playerId) && (
                        <div className="text-yellow-400 text-lg font-bold">
                          {countdowns.get(player.playerId)}s remaining
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
              <p className="text-gray-400 text-sm">
                Players cannot play cards until all players reconnect.
              </p>
            </div>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
            </div>
          </div>
        </div>
      )}

      {/* Disconnected Players Status - Only show when game is NOT paused */}
      {disconnectedPlayers.length > 0 && !isGamePaused && (
        <div className="fixed top-4 left-4 bg-yellow-900 border border-yellow-500 p-4 rounded-lg shadow-lg z-50 max-w-xs">
          <div className="flex items-center mb-2">
            <span className="text-yellow-400 mr-2">⚠️</span>
            <h4 className="font-bold text-yellow-400">Player Disconnected</h4>
          </div>
          {disconnectedPlayers.map(player => (
            <div key={player.playerId} className="text-yellow-200 mb-2">
              <div className="font-medium">{player.playerName}</div>
              {countdowns.has(player.playerId) && (
                <div className="text-yellow-400 font-bold text-lg">
                  {countdowns.get(player.playerId)}s to reconnect
                </div>
              )}
            </div>
          ))}
          <div className="text-yellow-300 text-xs mt-2">
            Game will continue if they reconnect in time
          </div>
        </div>
      )}

      {/* Toast Notifications */}
      <div className="fixed top-4 right-4 space-y-2 z-50">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            className={getToastStyles(toast.type)}
            style={{ 
              transform: `translateY(${index * 10}px)`,
              transition: 'all 0.3s ease-in-out'
            }}
          >
            <div className="flex justify-between items-start">
              <p className="text-sm font-medium pr-2">{toast.message}</p>
              <button
                onClick={() => removeToast(toast.id)}
                className="text-white hover:text-gray-200 font-bold text-lg leading-none"
              >
                ×
              </button>
            </div>
            
            {/* Progress bar */}
            <div className="mt-2 w-full bg-white bg-opacity-30 rounded-full h-1">
              <div
                className="bg-white h-1 rounded-full transition-all duration-1000 ease-linear"
                style={{
                  width: `${Math.max(0, 100 - ((Date.now() - toast.timestamp) / toast.duration) * 100)}%`
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default DisconnectionNotifications;
