"use client";

import { useState } from "react";
import { Send } from "lucide-react";
import { motion } from "framer-motion";
import { 
  Drawer, 
  DrawerContent, 
  DrawerTrigger,
} from "@/components/ui/drawer";

type Message = {
  id: number;
  user: string;
  text: string;
  timestamp: string;
  isCurrentUser: boolean;
};

const dummyMessages: Message[] = [
  {
    id: 1,
    user: "Player123",
    text: "Good luck everyone!",
    timestamp: "12:01",
    isCurrentUser: false,
  },
  {
    id: 2,
    user: "GameMaster",
    text: "Welcome to the game! Remember to play responsibly.",
    timestamp: "12:02",
    isCurrentUser: false,
  },
  {
    id: 3,
    user: "You",
    text: "Thanks for the welcome!",
    timestamp: "12:03",
    isCurrentUser: true,
  },
  {
    id: 4,
    user: "CardShark88",
    text: "Anyone want to join my private table?",
    timestamp: "12:04",
    isCurrentUser: false,
  },
  {
    id: 5,
    user: "LuckyDraw",
    text: "Just won 500 chips! 🎉",
    timestamp: "12:05",
    isCurrentUser: false,
  },
];

interface ChatDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

export default function ChatDrawer({
  open,
  onOpenChange,
  children,
}: ChatDrawerProps) {
  const [messages, setMessages] = useState<Message[]>(dummyMessages);
  const [newMessage, setNewMessage] = useState("");

  const handleSendMessage = () => {
    if (newMessage.trim() === "") return;
    
    const message: Message = {
      id: messages.length + 1,
      user: "You",
      text: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isCurrentUser: true,
    };
    
    setMessages([...messages, message]);
    setNewMessage("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>{children}</DrawerTrigger>
      <DrawerContent className="bg-[#2a2a2a] border-t-2 border-[#a07a4a] rounded-t-xl max-h-[80vh]">
        <div className="max-w-xl mx-auto w-full">
          <div className="flex flex-col h-[70vh]">
            {/* Header */}
            <div className="p-4 border-b border-neutral-800 flex justify-between items-center">
              <h2 className="text-[#edcf5d] font-semibold">Live Chat</h2>
              <div className="flex items-center gap-2">
                <span className="bg-green-500 w-2 h-2 rounded-full"></span>
                <span className="text-green-500 text-xs">42 Online</span>
              </div>
            </div>
            
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.isCurrentUser ? 'justify-end' : 'justify-start'}`}
                >
                  <div 
                    className={`max-w-[80%] rounded-lg p-3 ${
                      message.isCurrentUser 
                        ? 'bg-gradient-to-r from-[#a07a4a] to-[#edcf5d] text-black' 
                        : 'bg-neutral-800 text-white'
                    }`}
                  >
                    <div className="flex justify-between items-center mb-1">
                      <span className={`font-medium text-xs ${message.isCurrentUser ? 'text-black' : 'text-[#edcf5d]'}`}>
                        {message.user}
                      </span>
                      <span className="text-xs opacity-70">{message.timestamp}</span>
                    </div>
                    <p className="text-sm">{message.text}</p>
                  </div>
                </motion.div>
              ))}
            </div>
            
            {/* Input */}
            <div className="p-4 border-t border-neutral-800">
              <div className="flex items-center gap-2 bg-neutral-800 rounded-full px-4 py-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  className="flex-1 bg-transparent outline-none text-white"
                />
                <button 
                  onClick={handleSendMessage}
                  className="text-[#edcf5d] p-1 rounded-full hover:bg-neutral-700"
                >
                  <Send size={18} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}