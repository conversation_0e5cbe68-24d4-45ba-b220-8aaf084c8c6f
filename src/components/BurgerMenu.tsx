"use client";
import { useState } from "react";
import { Menu } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import { Button } from "@/components/ui/button";

type MenuItem = {
  title: string;
  icon?: React.ReactNode;
  isToggle?: boolean;
  isToggleOn?: boolean;
};

export default function BurgerMenu() {
  const [selectedItem, setSelectedItem] = useState<string>("Game Rules");
  const [open, setOpen] = useState(false);
  const [toggleStates, setToggleStates] = useState([true, true]);
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuthStore();

  // All menu items
  const allMenuItems: MenuItem[] = [
    {
      title: "Sound",
      isToggle: true,
      isToggleOn: toggleStates[0],
      icon: <span className={`text-xl ${toggleStates[0] ? 'text-green-500' : 'text-red-500'}`}>🔊</span>
    },
    {
      title: "Music",
      isToggle: true,
      isToggleOn: toggleStates[1],
      icon: <span className={`text-xl ${toggleStates[1] ? 'text-green-500' : 'text-red-500'}`}>🎵</span>
    },
    {
      title: "Game Rules",
      icon: <span className="text-white text-xl">🎮</span>
    },
    {
      title: "Bet History",
      icon: <span className="text-white text-xl">📊</span>
    },
    {
      title: "Players In Game",
      icon: <span className="text-white text-xl">👥</span>
    },
    {
      title: "Promotions",
      icon: <span className="text-white text-xl">🏆</span>
    },
    {
      title: "Chat",
      icon: <span className="text-white text-xl">💬</span>
    },
    {
      title: "Language",
      icon: <span className="text-white text-xl">🌐</span>
    },
    {
      title: "Time Settings",
      icon: <span className="text-white text-xl">⏱️</span>
    },
    {
      title: "Card Images",
      icon: <span className="text-white text-xl">🃏</span>
    },
    {
      title: "Admin Control",
      icon: <span className="text-white text-xl">⚙️</span>
    },
    {
      title: "Exit",
      icon: <span className="text-white text-xl">🚪</span>
    },
  ];

  // Filter menu items based on authentication status
  const menuItems = allMenuItems.filter(item => {
    // Hide these items if user is not authenticated
    if (!isAuthenticated && (
      item.title === "Bet History" ||
      item.title === "Chat" ||
      item.title === "Players In Game"
    )) {
      return false;
    }
    // Show Admin Control to all authenticated users (admin controls are now accessible to everyone)
    if (item.title === "Admin Control" && !isAuthenticated) {
      return false;
    }
    return true;
  });

  // Game Rules sections from GameRules component
  const gameRulesSections = [
    {
      title: "HOW THE GAME IS PLAYED",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Game Overview</h4>
          <p className="mb-2">Thunee is a trick-taking card game played with 4 people, divided into two teams of 2 players each.</p>
          <p className="mb-4">Your partner sits opposite you at the table and your opponents are to the left and right of you.</p>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Team Roles</h4>
          <p className="mb-2">There are two main roles in the game:</p>
          <ul className="list-disc pl-5 space-y-1 mb-3">
            <li><strong>Scoring Team:</strong> Your aim is to win 105 points worth of cards.</li>
            <li><strong>Trumping Team:</strong> Your aim is to stop your opponents from counting to 105 points.</li>
          </ul>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Game Flow</h4>
          <ol className="list-decimal pl-5 space-y-1 mb-3">
            <li>Determine the dealer (first player to receive a black Jack)</li>
            <li>The dealer offers the deck to the player on their left to cut</li>
            <li>The dealer distributes 4 cards to each player</li>
            <li>The player to the dealer's right selects the trump suit</li>
            <li>Bidding phase occurs (optional)</li>
            <li>The dealer distributes 2 more cards to each player</li>
            <li>Players play tricks, following suit when possible</li>
            <li>After all 6 tricks are played, points are tallied</li>
            <li>If the scoring team reaches 105+ points, they win a ball; otherwise, the trumping team wins</li>
          </ol>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Winning the Game</h4>
          <p>By achieving your team's aim, you will be awarded a 'ball' (game point). The first team to win 13 balls wins the game.</p>
        </>
      )
    },
    {
      title: "POINT VALUES OF THE CARDS",
      content: (
        <>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/JS.svg" alt="Jack" className="w-8 h-12" />
              </div>
              <span>Jack = 30 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/9S.svg" alt="Nine" className="w-8 h-12" />
              </div>
              <span>Nine = 20 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/AS.svg" alt="Ace" className="w-8 h-12" />
              </div>
              <span>Ace = 11 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/TS.svg" alt="Ten" className="w-8 h-12" />
              </div>
              <span>Ten = 10 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/KS.svg" alt="King" className="w-8 h-12" />
              </div>
              <span>King = 3 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                <img src="/CardFace/QS.svg" alt="Queen" className="w-8 h-12" />
              </div>
              <span>Queen = 2 points</span>
            </div>
          </div>
          <p className="font-bold">The most important rule is to follow suit, if a player doesn't have that suit, he/she may play any other card in his deck.</p>
        </>
      )
    },
    {
      title: "IMPORTANT FEATURES",
      content: (
        <>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Keeping Trump:</strong> The person to the right of the dealer must select trump from his/her first 4 cards which will be the highest card of the suit he/she has the most of.</li>
            <li><strong>Calling to trump:</strong> Calling is done midway through dealing when you receive your first 4 cards. Calling is done if you have a favourable hand and would like to trump. The thinking is that you can hold off the opposition team with your hand. The call which is generally increments of 10 is subtracted from the counting score. The maximum call is 104.</li>
            <li><strong>Trumpless:</strong> Both teams are to have at least 1 trump. If not would lead to a re-deal. The responsibility to call "no trump" is up to the counting team.</li>
            <li><strong>Chopping:</strong> A hand can be chopped (cut) if a player doesn't have the suit of the first card on the table. If you have the suit and decide to chop the hand and if you are caught you will lose 4 game points (4balled).</li>
          </ul>
        </>
      )
    },
    {
      title: "SPECIAL CALLS",
      content: (
        <>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Thunee:</strong> After receiving the 6 cards one can call "Thunee". When Thunee is called the first card played is trump and one has to win all 6 hands to gain 4 game points. If you don't you will lose 4 points (4 balled). If you were in a "calling" and won it, you as the Trumper get the first choice to call Thunee.</li>
            <li><strong>Jodhi:</strong> Jodhi cards are the Queen, King and Jack of the same suit. You and your partner can call Jodhi upon winning your first and third hand. Both teams' Jodhi's are offset, and the balance is used on the 105 counts.</li>
            <li><strong>Jodhi Combinations (non-trump):</strong> Queen and King = 20. Queen, King, and Jack = 30.</li>
            <li><strong>Jodhi Combinations (trump):</strong> Queen and King = 40. Queen, King, and Jack = 50.</li>
            <li><strong>Khanuck:</strong> Only directly related to Jodhi. You can call a Khanuck in the last hand if you are going to win that last hand and you believe that your opponent took a lower score than your Jodhi. If your Jodhi was 30 + (10 for taking the last hand) and your opponent took hands that have a total score less than your 40, then your Khanuck call is correct and you are awarded three balls.</li>
            <li><strong>Double/Two:</strong> When you have taken the first five hands and believe you will win the last hand you can call a double/two. Only the controlling card holder that secures the last hand can call double/two on the last hand. If you successfully win the last hand (all 6 hands) you will be awarded 2 balls after calling double/two.</li>
          </ul>
        </>
      )
    },
    {
      title: "TRICK-TAKING RULES",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Playing Tricks</h4>
          <p className="mb-2">After all players have received their 6 cards, the gameplay begins:</p>
          <ul className="list-disc pl-5 space-y-2 mb-4">
            <li>The player to the dealer's left leads the first trick by playing any card.</li>
            <li>Play proceeds counter-clockwise, with each player playing one card.</li>
            <li><strong>Following suit is mandatory</strong> - if you have a card of the suit that was led, you must play it.</li>
            <li>If you don't have the led suit, you may play any card, including a trump.</li>
            <li>The highest card of the led suit wins the trick, unless a trump is played.</li>
            <li>If any trumps are played, the highest trump wins the trick.</li>
          </ul>

          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Card Rankings</h4>
          <p className="mb-2"><strong>Trump suit ranking (highest to lowest):</strong> Jack, 9, Ace, 10, King, Queen</p>
          <p><strong>Non-trump suit ranking (highest to lowest):</strong> Jack, 9, Ace, 10, King, Queen</p>
        </>
      )
    }
  ];

  const [currentGameRuleSection, setCurrentGameRuleSection] = useState(0);

  const handleToggleClick = (index: number) => {
    const newToggleStates = [...toggleStates];
    newToggleStates[index] = !newToggleStates[index];
    setToggleStates(newToggleStates);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.title === "Exit") {
      setOpen(false); // Close the menu
      navigate("/"); // Navigate to home
    } else if (item.title === "Time Settings") {
      setOpen(false); // Close the menu
      navigate("/settings"); // Navigate to time settings
    } else if (item.title === "Card Images") {
      setOpen(false); // Close the menu
      navigate("/card-settings"); // Navigate to card image settings
    } else if (item.title === "Admin Control") {
      setOpen(false); // Close the menu
      navigate("/admin"); // Navigate to admin dashboard
    } else if (item.isToggle) {
      // Find the index in the original allMenuItems array for toggle handling
      const originalIndex = allMenuItems.findIndex(originalItem => originalItem.title === item.title);
      handleToggleClick(originalIndex);
    } else {
      setSelectedItem(item.title);
    }
  };

  return (
      <div className="relative">
        <Sheet open={open} onOpenChange={setOpen}>
          <div
              className={cn(
                  "fixed transition-all duration-300 ease-in-out top-0 left-0 z-[60] flex h-16 items-center px-4",
                  open
                      ? "bg-black border-b border-neutral-800 w-16"
                      : "bg-transparent w-16"
              )}
          >
            <SheetTrigger asChild>
              <button className="mr-6">
                <Menu className="h-6 w-6 text-white bg-blackglass" />
              </button>
            </SheetTrigger>
          </div>
          <SheetContent
              side="left"
              className="p-0 border-r border-neutral-800 bg-blackglass/90 backdrop-opacity-90 w-[80%] sm:max-w-[80%] md:max-w-[70%]"
          >
            <div className="flex h-full">
              {/* Menu Items Panel - made smaller */}
              <div className=" w-3/6 border-r border-neutral-800">
                <div className="flex flex-col pt-16">
                  {menuItems.map((item, index) => (
                      <motion.div
                          key={item.title}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className={`py-2 px-2 border-b border-neutral-800/30 ${selectedItem === item.title && !item.isToggle ? 'bg-neutral-800/20' : ''}`}
                      >
                        <button
                            onClick={() => handleItemClick(item)}
                            className="w-full flex items-center justify-between"
                        >
                          <div className="flex items-center gap-1">
                            {item.icon}
                            <span className="text-white text-xs whitespace-nowrap">{item.title}</span>
                          </div>

                          {item.isToggle && (
                              <div className={`w-7 h-3.5 rounded-full transition-colors ${toggleStates[index] ? 'bg-green-500' : 'bg-red-500'} flex items-center p-0.5`}>
                                <motion.div
                                    className="w-2 h-2 bg-white rounded-full"
                                    animate={{ x: toggleStates[index] ? 12 : 0 }}
                                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                                />
                              </div>
                          )}
                        </button>
                      </motion.div>
                  ))}
                </div>
              </div>

              {/* Content Panel - made bigger */}
              <div className="w-[100vw] py-6 px-6 overflow-y-auto">
                <AnimatePresence mode="wait">
                  {selectedItem === "Game Rules" && (
                      <motion.div
                          key="game-rules"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="space-y-4"
                      >
                        <div className="flex flex-col space-y-4">
                          {/* Section Navigation */}
                          <div className="flex flex-wrap gap-2 mb-4">
                            {gameRulesSections.map((section, index) => (
                              <Button
                                key={index}
                                variant="outline"
                                size="sm"
                                className={`text-xs transition-colors ${
                                  currentGameRuleSection === index
                                    ? "bg-[#E1C760] text-black hover:bg-[#E1C760]/90 hover:text-black border-[#E1C760]"
                                    : "bg-black text-[#E1C760] hover:bg-[#E1C760] hover:text-black border-[#E1C760]"
                                }`}
                                onClick={() => setCurrentGameRuleSection(index)}
                              >
                                {section.title}
                              </Button>
                            ))}
                          </div>

                          {/* Current Section Content */}
                          <div className="text-white text-sm">
                            <h2 className="text-[#E1C760] font-semibold mb-4 text-lg">
                              {gameRulesSections[currentGameRuleSection].title}
                            </h2>
                            <div className="space-y-2">
                              {gameRulesSections[currentGameRuleSection].content}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                  )}

                  {selectedItem === "Bet History" && isAuthenticated && (
                      <motion.div
                          key="bet-history"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                      >
                        <h2 className="text-white font-semibold mb-4">Bet History</h2>
                        <div className="text-xs text-neutral-400">
                          <p>Your recent betting activity will appear here.</p>
                          <div className="mt-4 p-3 border border-neutral-700 rounded">
                            <div className="mb-2 font-medium text-white">Winner of Next Hand</div>
                            <div className="text-neutral-300">5 Ball Bet</div>
                          </div>
                          <div className="mt-4 grid grid-cols-4 gap-2">
                            <div className="col-span-1">Call</div>
                            <div className="col-span-1">Bet</div>
                            <div className="col-span-2">Fold</div>
                          </div>
                        </div>
                      </motion.div>
                  )}

                  {selectedItem !== "Game rules" && selectedItem !== "Bet History" && (
                      <motion.div
                          key={selectedItem}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                      >
                        <h2 className="text-white font-semibold mb-4">{selectedItem}</h2>
                        <p className="text-xs text-neutral-400">
                          Content for {selectedItem} will be displayed here.
                        </p>
                      </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
  );
}