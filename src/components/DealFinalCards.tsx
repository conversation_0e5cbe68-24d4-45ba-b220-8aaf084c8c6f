"use client";
import { useState } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { Button } from "./ui/button";

interface DealFinalCardsProps {
  onDealComplete: () => void;
  onDealStart?: () => void; // New prop to trigger animation immediately
}

export default function DealFinalCards({ onDealComplete, onDealStart }: DealFinalCardsProps) {
  const { isDealer } = useGameStore();
  const [isDealing, setIsDealing] = useState(false);

  // Only the dealer should see this button
  if (!isDealer) {
    return null;
  }

  const handleDeal = async () => {
    try {
      setIsDealing(true);
      console.log("Dealing final 2 cards to each player...");

      // Trigger animation immediately when deal button is clicked
      if (onDealStart) {
        console.log("Triggering final cards deal animation immediately");
        onDealStart();
      }

      // Send deal final cards event to server
      await socketService.sendGameAction("deal_final_cards", {});

      // The server will handle the dealing animation and send events
      // We'll keep the button disabled until the dealing is complete
      // The onDealComplete callback will be triggered by the cards_dealt event in App.tsx

      // Add a timeout to reset the button if the server doesn't respond
      setTimeout(() => {
        if (isDealing) {
          console.log("Deal timeout reached, resetting button state");
          setIsDealing(false);
        }
      }, 10000); // 10 second timeout
    } catch (error) {
      console.error("Error dealing final cards:", error);
      setIsDealing(false);
    }
  };

  return (
    <div className="fixed bottom-24 left-0 right-0 flex justify-center items-center z-50">
      <Button
        onClick={handleDeal}
        disabled={isDealing}
        className="bg-[#E1C760] text-black px-16 py-6 rounded-md text-3xl font-bold shadow-lg border-4 border-black hover:bg-[#E1C760]/90 transition-colors"
      >
        {isDealing ? "DEALING..." : "DEAL"}
      </Button>
    </div>
  );
}
