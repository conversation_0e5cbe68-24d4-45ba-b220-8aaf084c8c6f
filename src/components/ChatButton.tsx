"use client";

import React from "react";
import { MessageCircle } from "lucide-react";
import { useChatStore } from "@/store/chatStore";

export default function ChatButton() {
  const { toggleChat, isOpen: isChatOpen } = useChatStore();

  return (
    <button
      onClick={toggleChat}
      className={`fixed top-4 right-4 z-40 flex items-center justify-center w-12 h-12 rounded-full shadow-lg transition-all duration-200 hover:scale-110 ${
        isChatOpen 
          ? "bg-gradient-to-r from-[#a07a4a] to-[#edcf5d] text-black" 
          : "bg-[#2a2a2a] text-[#edcf5d] border-2 border-[#edcf5d]"
      }`}
      title="Open Chat"
    >
      <MessageCircle size={20} />
    </button>
  );
}
