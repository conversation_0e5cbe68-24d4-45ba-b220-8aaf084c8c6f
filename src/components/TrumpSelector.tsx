"use client";
import { useState, useEffect } from "react";
import socketService from "@/services/socketService";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";

interface TrumpSelectorProps {
  isVisible: boolean;
  onTrumpSelected: () => void;
}

export default function TrumpSelector({ isVisible, onTrumpSelected }: TrumpSelectorProps) {
  const [isSelecting, setIsSelecting] = useState(false);
  const [canSelectTrump, setCanSelectTrump] = useState(false);
  const { hand, initialHand } = useGameStore();

  // Use initialHand if available, otherwise use hand
  const displayHand = initialHand.length > 0 ? initialHand : hand;

  // Listen for the your_turn_to_select_trump event
  useEffect(() => {
    const handleYourTurnToSelectTrump = () => {
      console.log("It's your turn to select trump");
      setCanSelectTrump(true);
    };

    socketService.on("your_turn_to_select_trump", handleYourTurnToSelectTrump);

    return () => {
      socketService.off("your_turn_to_select_trump", handleYourTurnToSelectTrump);
    };
  }, []);

  // Add useEffect for debugging
  useEffect(() => {
    console.log("TrumpSelector rendered with isVisible:", isVisible);
    console.log("Current hand:", displayHand);
    console.log("Can select trump:", canSelectTrump);
  }, [isVisible, displayHand, canSelectTrump]);

  if (!isVisible) {
    return null;
  }

  const handleSelectTrump = async (trumpCard: { value: string; suit: string } | string) => {
    if (!canSelectTrump && !isSelecting) {
      console.log("Cannot select trump yet - waiting for your turn");
      return;
    }

    try {
      setIsSelecting(true);

      if (typeof trumpCard === 'string') {
        // Handle "last_card" selection
        console.log(`Player selected last card as trump`);
        await socketService.sendGameAction("select_trump", {
          suit: trumpCard
        });
      } else {
        // Handle specific card selection
        console.log(`Player selected ${trumpCard.value} of ${trumpCard.suit} as trump`);
        await socketService.sendGameAction("select_trump", {
          suit: trumpCard.suit,
          value: trumpCard.value
        });
      }

      // Reset state
      setCanSelectTrump(false);

      // Show a brief animation
      setTimeout(() => {
        setIsSelecting(false);
        onTrumpSelected();
      }, 1000);
    } catch (error) {
      console.error("Error selecting trump:", error);
      setIsSelecting(false);
      // Re-enable selection on error
      setCanSelectTrump(true);
    }
  };

  // Use the player's actual hand for trump selection
  const trumpCards = displayHand.map(card => ({
    value: card.value,
    suit: card.suit
  }));

  console.log("Player's hand:", displayHand);

  return (
    <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-1 sm:p-2 md:p-4 lg:p-6 overflow-y-auto">
      <div className="bg-black border-2 sm:border-4 border-[#E1C760] rounded-lg p-2 sm:p-3 md:p-4 lg:p-6 w-full max-w-[98vw] sm:max-w-[95vw] md:max-w-[85vw] lg:max-w-[70vw] mx-auto shadow-[0_0_20px_rgba(225,199,96,0.5)] max-h-[98vh] flex flex-col">
        
        {/* Header - More compact on mobile landscape */}
        <div className="border-b-2 border-[#E1C760] bg-black px-2 sm:px-3 md:px-6 py-1 sm:py-2 md:py-3 text-center rounded-t-md flex-shrink-0">
          <span className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold text-[#E1C760] leading-tight">
            Select Trump Suit
          </span>
        </div>

        {/* Scrollable content area */}
        <div className="bg-black rounded-b-md flex-1 overflow-y-auto">
          <p className="text-white text-xs sm:text-sm md:text-base lg:text-lg mb-2 sm:mb-3 md:mb-4 text-center px-2">
            Choose a suit to be the trump for this game.
          </p>

          {/* Display player's cards - More compact layout */}
          <div className="mb-2 sm:mb-3 md:mb-4 lg:mb-6">
            <h3 className="text-[#E1C760] text-xs sm:text-sm md:text-base lg:text-lg font-bold mb-1 sm:mb-2 text-center">
              Your Cards
            </h3>
            <div className="flex justify-center gap-1 sm:gap-2 overflow-x-auto pb-2 max-w-full px-2">
              {displayHand.map(card => (
                <div key={card.id} className="w-8 h-12 sm:w-12 sm:h-16 md:w-16 md:h-24 lg:w-20 lg:h-28 border border-[#E1C760] sm:border-2 rounded overflow-hidden flex-shrink-0 shadow-md">
                  <img
                    src={getCardImagePath(card.value, card.suit)}
                    alt={`${card.value} of ${card.suit}`}
                    className="w-full h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Trump selection buttons - Responsive grid layout */}
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-4 lg:flex lg:flex-wrap gap-1 sm:gap-2 md:gap-3 lg:gap-4 justify-center px-2 mb-2 sm:mb-3 md:mb-4 lg:mb-6">
            {trumpCards.map((trumpCard, index) => (
              <button
                key={`trump-${trumpCard.value}-${trumpCard.suit}-${index}`}
                onClick={() => handleSelectTrump(trumpCard)}
                disabled={isSelecting || !canSelectTrump}
                className={`aspect-[5/7] w-full max-w-[4rem] sm:max-w-[5rem] md:max-w-[5rem] lg:max-w-[7rem] rounded border border-[#E1C760] sm:border-2 bg-black p-0.5 sm:p-1 text-xs font-medium transition-colors ${
                  canSelectTrump && !isSelecting
                    ? "hover:bg-[#E1C760]/20 cursor-pointer"
                    : "opacity-50 cursor-not-allowed"
                } flex flex-col items-center justify-center shadow-[0_0_10px_rgba(225,199,96,0.3)] mx-auto`}
              >
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={getCardImagePath(trumpCard.value, trumpCard.suit)}
                    alt={`${trumpCard.value} of ${trumpCard.suit}`}
                    className="w-full h-full object-contain rounded"
                  />
                </div>
              </button>
            ))}
          </div>

          {/* Last Card button - More compact on mobile */}
          <div className="px-2 mb-2 sm:mb-3 md:mb-4">
            <button
              onClick={() => handleSelectTrump("last_card")}
              disabled={isSelecting || !canSelectTrump}
              className={`w-full rounded border-2 border-[#E1C760] bg-black px-2 sm:px-3 py-1.5 sm:py-2 md:py-3 text-sm sm:text-base md:text-lg font-medium text-[#E1C760] transition-colors ${
                canSelectTrump && !isSelecting
                  ? "hover:bg-[#E1C760]/20 cursor-pointer"
                  : "opacity-50 cursor-not-allowed"
              } shadow-[0_0_10px_rgba(225,199,96,0.3)]`}
            >
              <span className="text-xs sm:text-sm md:text-base lg:text-lg font-bold text-[#E1C760] leading-tight">
                Last Card
              </span>
            </button>
          </div>

          {/* Status message - Compact on mobile */}
          {!canSelectTrump && !isSelecting && (
            <div className="px-2 pb-2 text-center text-white">
              <p className="text-xs sm:text-sm md:text-base">Waiting for your turn to select trump...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}