"use client";
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trophy, Users, Clock, CheckCircle } from "lucide-react";

interface BracketTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  points: number;
  bonusPoints: number;
}

interface BracketMatch {
  lobbyId: string;
  lobbyCode: string;
  team1?: BracketTeam;
  team2?: BracketTeam;
  winner?: BracketTeam;
  isCompleted: boolean;
  completedAt?: string;
}

interface Bracket {
  phase: string;
  matches: BracketMatch[];
}

interface CompetitionBracketProps {
  competitionId: string;
  brackets: Bracket[];
  isLoading?: boolean;
}

export default function CompetitionBracket({ competitionId, brackets, isLoading }: CompetitionBracketProps) {
  const [selectedPhase, setSelectedPhase] = useState<string>("Top16");

  const phases = ["Top16", "Top8", "Top4", "Final"];
  
  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Top16": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Top8": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top4": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Final": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getMatchesPerRow = (phase: string) => {
    switch (phase) {
      case "Top16": return 8;
      case "Top8": return 4;
      case "Top4": return 2;
      case "Final": return 1;
      default: return 1;
    }
  };

  const currentBracket = brackets.find(b => b.phase === selectedPhase);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading bracket...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Phase Selector */}
      <div className="flex flex-wrap gap-2 justify-center">
        {phases.map((phase) => (
          <button
            key={phase}
            onClick={() => setSelectedPhase(phase)}
            className={`px-4 py-2 rounded-lg border transition-colors ${
              selectedPhase === phase
                ? getPhaseColor(phase)
                : "bg-gray-800/50 text-gray-400 border-gray-600 hover:bg-gray-700/50"
            }`}
          >
            {phase}
          </button>
        ))}
      </div>

      {/* Bracket Display */}
      {currentBracket ? (
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-[#E1C760] mb-2">{selectedPhase} Phase</h3>
            <p className="text-gray-400">
              {currentBracket.matches.length} matches • {currentBracket.matches.filter(m => m.isCompleted).length} completed
            </p>
          </div>

          {/* Matches Grid */}
          <div className={`grid gap-4 ${
            getMatchesPerRow(selectedPhase) === 1 
              ? "grid-cols-1 max-w-md mx-auto"
              : getMatchesPerRow(selectedPhase) === 2
              ? "grid-cols-1 md:grid-cols-2 max-w-4xl mx-auto"
              : getMatchesPerRow(selectedPhase) === 4
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
              : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8"
          }`}>
            {currentBracket.matches.map((match, index) => (
              <MatchCard key={match.lobbyId} match={match} matchNumber={index + 1} />
            ))}
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <Trophy className="h-16 w-16 text-gray-600 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-400 mb-2">No Bracket Available</h3>
          <p className="text-gray-500">The {selectedPhase} bracket hasn't been created yet.</p>
        </div>
      )}
    </div>
  );
}

function MatchCard({ match, matchNumber }: { match: BracketMatch; matchNumber: number }) {
  return (
    <Card className="bg-black/50 border-[#E1C760]/30 relative">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm text-[#E1C760]">
            Match {matchNumber}
          </CardTitle>
          {match.isCompleted && (
            <CheckCircle className="h-4 w-4 text-green-400" />
          )}
        </div>
        <p className="text-xs text-gray-400">{match.lobbyCode}</p>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Team 1 */}
        <TeamDisplay 
          team={match.team1} 
          isWinner={match.winner?.id === match.team1?.id}
          isCompleted={match.isCompleted}
        />
        
        {/* VS Divider */}
        <div className="text-center">
          <span className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">VS</span>
        </div>
        
        {/* Team 2 */}
        <TeamDisplay 
          team={match.team2} 
          isWinner={match.winner?.id === match.team2?.id}
          isCompleted={match.isCompleted}
        />

        {/* Match Status */}
        <div className="text-center pt-2">
          {match.isCompleted ? (
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              Completed
            </Badge>
          ) : (
            <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function TeamDisplay({ 
  team, 
  isWinner, 
  isCompleted 
}: { 
  team?: BracketTeam; 
  isWinner: boolean; 
  isCompleted: boolean; 
}) {
  if (!team) {
    return (
      <div className="p-2 border border-dashed border-gray-600 rounded text-center">
        <p className="text-xs text-gray-500">TBD</p>
      </div>
    );
  }

  return (
    <div className={`p-2 border rounded transition-colors ${
      isWinner && isCompleted
        ? "border-green-500 bg-green-500/10"
        : "border-gray-600 bg-gray-800/30"
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <p className={`text-sm font-medium truncate ${
            isWinner && isCompleted ? "text-green-400" : "text-white"
          }`}>
            {team.teamName}
          </p>
          <div className="text-xs text-gray-400 space-y-1">
            <p className="truncate">{team.player1.username}</p>
            {team.player2 && (
              <p className="truncate">{team.player2.username}</p>
            )}
          </div>
        </div>
        {isWinner && isCompleted && (
          <Trophy className="h-4 w-4 text-green-400 ml-2 flex-shrink-0" />
        )}
      </div>
      
      {/* Team Stats */}
      <div className="flex justify-between text-xs text-gray-500 mt-1">
        <span>{team.points + team.bonusPoints} pts</span>
        <Users className="h-3 w-3" />
      </div>
    </div>
  );
}
