"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";

interface CardShufflingAndDealingProps {
  isVisible: boolean;
  onComplete: () => void;
  dealingPhase: "shuffle" | "dealFour" | "dealTwo" | "complete";
}

export default function CardShufflingAndDealing({
  isVisible,
  onComplete,
  dealingPhase,
}: CardShufflingAndDealingProps) {
  const { players } = useGameStore();
  const [shuffleComplete, setShuffleComplete] = useState(false);
  const [dealFourComplete, setDealFourComplete] = useState(false);
  const [dealTwoComplete, setDealTwoComplete] = useState(false);
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [cardsDealt, setCardsDealt] = useState(0);
  const [message, setMessage] = useState("Shuffling cards...");

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setShuffleComplete(false);
      setDealFourComplete(false);
      setDealTwoComplete(false);
      setCardsDealt(0);
      
      if (dealingPhase === "shuffle") {
        setMessage("Shuffling cards...");
      } else if (dealingPhase === "dealFour") {
        setMessage("Dealing first 4 cards to each player...");
      } else if (dealingPhase === "dealTwo") {
        setMessage("Dealing final 2 cards to each player...");
      }
    }
  }, [isVisible, dealingPhase]);

  // Listen for card dealt events
  useEffect(() => {
    const handleCardDealt = (event: CustomEvent) => {
      if (!isVisible) return;
      
      const { playerId } = event.detail;
      
      // Find the player index
      const playerIndex = players.findIndex(p => p.id === playerId);
      if (playerIndex !== -1) {
        setCurrentPlayerIndex(playerIndex);
      }
      
      // Increment cards dealt counter
      setCardsDealt(prev => prev + 1);
    };

    // Add event listener
    document.addEventListener("card_dealt", handleCardDealt as EventListener);
    
    return () => {
      document.removeEventListener("card_dealt", handleCardDealt as EventListener);
    };
  }, [isVisible, players]);

  // Listen for dealing complete events
  useEffect(() => {
    const handleDealingComplete = () => {
      if (!isVisible) return;
      
      if (dealingPhase === "shuffle") {
        setShuffleComplete(true);
        setMessage("Shuffling complete!");
      } else if (dealingPhase === "dealFour") {
        setDealFourComplete(true);
        setMessage("First 4 cards dealt to each player!");
      } else if (dealingPhase === "dealTwo") {
        setDealTwoComplete(true);
        setMessage("All cards dealt!");
      }
      
      // After a delay, call the onComplete callback
      setTimeout(() => {
        onComplete();
      }, 1500);
    };

    // Add event listener
    document.addEventListener("cards_dealing_complete", handleDealingComplete);
    
    return () => {
      document.removeEventListener("cards_dealing_complete", handleDealingComplete);
    };
  }, [isVisible, dealingPhase, onComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
      <h2 className="text-2xl font-bold text-[#E1C760] mb-4">
        {dealingPhase === "shuffle" ? "Shuffling Cards" : 
         dealingPhase === "dealFour" ? "Dealing First 4 Cards" :
         dealingPhase === "dealTwo" ? "Dealing Final 2 Cards" : "Dealing Complete"}
      </h2>
      
      {/* Shuffling Animation */}
      {dealingPhase === "shuffle" && (
        <div className="relative h-60 w-60 mb-8">
          <AnimatePresence>
            {Array.from({ length: 24 }).map((_, index) => (
              <motion.div
                key={`shuffle-card-${index}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  left: "calc(50% - 40px)",
                  top: "calc(50% - 56px)",
                }}
                initial={{ 
                  x: 0, 
                  y: 0, 
                  rotate: 0,
                  scale: 0.9 + (index * 0.005)
                }}
                animate={{ 
                  x: shuffleComplete ? 0 : [0, Math.random() * 40 - 20, 0],
                  y: shuffleComplete ? 0 : [0, Math.random() * 40 - 20, 0],
                  rotate: shuffleComplete ? 0 : [0, Math.random() * 10 - 5, 0],
                  scale: 0.9 + (index * 0.005)
                }}
                transition={{
                  duration: 0.5,
                  repeat: shuffleComplete ? 0 : Infinity,
                  repeatType: "loop",
                  delay: index * 0.02,
                }}
              />
            ))}
          </AnimatePresence>
        </div>
      )}
      
      {/* Dealing Animation */}
      {(dealingPhase === "dealFour" || dealingPhase === "dealTwo") && (
        <div className="relative h-80 w-80 mb-8">
          {/* Deck in the center */}
          <div 
            className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
            style={{
              backgroundImage: "url('/CardFaces/card-back.svg')",
              backgroundSize: "cover",
              backgroundPosition: "center",
              left: "calc(50% - 40px)",
              top: "calc(50% - 56px)",
              zIndex: 1
            }}
          />
          
          {/* Dealing animations to 4 positions */}
          <AnimatePresence>
            {cardsDealt > 0 && (
              <motion.div
                key={`deal-card-${cardsDealt}`}
                className="absolute w-20 h-28 bg-white rounded-md shadow-lg border-2 border-[#E1C760]"
                style={{
                  backgroundImage: "url('/CardFaces/card-back.svg')",
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                  left: "calc(50% - 40px)",
                  top: "calc(50% - 56px)",
                  zIndex: 10
                }}
                initial={{ x: 0, y: 0, rotate: 0 }}
                animate={{
                  x: currentPlayerIndex === 0 ? 0 : 
                     currentPlayerIndex === 1 ? 120 : 
                     currentPlayerIndex === 2 ? 0 : 
                     -120,
                  y: currentPlayerIndex === 0 ? -120 : 
                     currentPlayerIndex === 1 ? 0 : 
                     currentPlayerIndex === 2 ? 120 : 
                     0,
                  rotate: [0, Math.random() * 10 - 5],
                  opacity: [1, 1, 0]
                }}
                transition={{
                  duration: 0.5,
                  ease: "easeInOut"
                }}
                onAnimationComplete={() => {
                  // Animation completed
                }}
              />
            )}
          </AnimatePresence>
          
          {/* Player positions */}
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-900 text-white px-3 py-1 rounded-full text-sm">
            Top Player
          </div>
          <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 bg-red-900 text-white px-3 py-1 rounded-full text-sm">
            Right Player
          </div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-green-900 text-white px-3 py-1 rounded-full text-sm">
            Bottom Player
          </div>
          <div className="absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-yellow-900 text-white px-3 py-1 rounded-full text-sm">
            Left Player
          </div>
          
          {/* Card counters */}
          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 text-white text-xs">
            {dealingPhase === "dealFour" ? 
              `${Math.min(cardsDealt, 4)}/4` : 
              `${Math.min(cardsDealt, 2)}/2`}
          </div>
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2 text-white text-xs">
            {dealingPhase === "dealFour" ? 
              `${Math.min(Math.max(0, cardsDealt - 4), 4)}/4` : 
              `${Math.min(Math.max(0, cardsDealt - 2), 2)}/2`}
          </div>
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white text-xs">
            {dealingPhase === "dealFour" ? 
              `${Math.min(Math.max(0, cardsDealt - 8), 4)}/4` : 
              `${Math.min(Math.max(0, cardsDealt - 4), 2)}/2`}
          </div>
          <div className="absolute left-8 top-1/2 transform -translate-y-1/2 text-white text-xs">
            {dealingPhase === "dealFour" ? 
              `${Math.min(Math.max(0, cardsDealt - 12), 4)}/4` : 
              `${Math.min(Math.max(0, cardsDealt - 6), 2)}/2`}
          </div>
        </div>
      )}
      
      {/* Status message */}
      <div className="text-center text-white text-lg mt-4">
        {message}
      </div>
      
      {/* Progress indicator */}
      <div className="w-64 h-2 bg-gray-800 rounded-full mt-4 overflow-hidden">
        <motion.div 
          className="h-full bg-[#E1C760]"
          initial={{ width: "0%" }}
          animate={{ 
            width: dealingPhase === "shuffle" ? 
              (shuffleComplete ? "100%" : `${Math.min(cardsDealt * 4, 100)}%`) : 
              dealingPhase === "dealFour" ? 
              (dealFourComplete ? "100%" : `${Math.min(cardsDealt * 6.25, 100)}%`) :
              (dealTwoComplete ? "100%" : `${Math.min(cardsDealt * 12.5, 100)}%`)
          }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  );
}
