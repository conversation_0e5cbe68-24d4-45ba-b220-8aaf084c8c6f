"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";
import "../styles/BallResultsDisplay.css";

interface HandWon {
  handId: number;
  winner: {
    id: string;
    name: string;
    team: 1 | 2;
  };
  cards: any[];
}

interface BallResultsDisplayProps {
  isVisible: boolean;
  onClose: () => void;
  doubleCallerId?: string;
  doubleCallerTeam?: 1 | 2;
  handWinnerId?: string;
  handWinnerTeam?: 1 | 2;
  handsWonByOppositeTeam?: HandWon[];
  lastHand?: HandWon;
  outcome?: 'success' | 'teammate_won' | 'opponent_won' | 'opposing_team_won_previous_hands';
  ballsAwarded?: number;
  winningTeam?: 1 | 2;
  displayDuration?: number;
  onContinueGame?: () => void;
}

export default function BallResultsDisplay({
  isVisible,
  onClose,
  doubleCallerId,
  doubleCallerTeam,
  handWinnerId,
  handWinnerTeam,
  handsWonByOppositeTeam = [],
  lastHand,
  outcome,
  ballsAwarded = 0,
  winningTeam,
  displayDuration = 10000, // Default to 10 seconds
  onContinueGame,
}: BallResultsDisplayProps) {
  const { players, teamNames } = useGameStore();
  const [timeRemaining, setTimeRemaining] = useState(displayDuration / 1000);
  const [showContinueButton, setShowContinueButton] = useState(false);

  // Find the double caller and hand winner players
  const doubleCallerPlayer = players.find(p => p.id === doubleCallerId);
  const handWinnerPlayer = players.find(p => p.id === handWinnerId);

  // Set up timer for showing the continue button
  useEffect(() => {
    if (!isVisible) {
      setShowContinueButton(false);
      setTimeRemaining(displayDuration / 1000);
      return;
    }

    console.log("BallResultsDisplay is visible, starting timer");
    setShowContinueButton(false);

    // Start countdown
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          console.log("Timer reached 0, showing continue button");
          setShowContinueButton(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // After displayDuration, show the continue button
    const showContinueTimer = setTimeout(() => {
      console.log("Display duration reached, showing continue button");
      setShowContinueButton(true);
    }, displayDuration);

    return () => {
      clearInterval(timer);
      clearTimeout(showContinueTimer);
    };
  }, [isVisible, displayDuration]);

  // Generate appropriate message based on the outcome
  const getMessage = () => {
    if (!doubleCallerPlayer || !handWinnerPlayer) return "";

    const oppositeTeam = doubleCallerTeam === 1 ? 2 : 1;

    if (outcome === 'opposing_team_won_previous_hands' || handsWonByOppositeTeam.length > 0) {
      return `${teamNames[oppositeTeam]} has already won ${handsWonByOppositeTeam.length} hand(s) this ball. ${teamNames[oppositeTeam]} wins 4 balls!`;
    }

    if (outcome === 'success') {
      return `${doubleCallerPlayer.name} called Double and won the last hand! ${teamNames[doubleCallerTeam || 1]} wins 2 balls!`;
    } else if (outcome === 'teammate_won') {
      return `${doubleCallerPlayer.name} called Double but their teammate ${handWinnerPlayer.name} won the last hand. ${teamNames[oppositeTeam]} wins 4 balls!`;
    } else if (outcome === 'opponent_won') {
      return `${doubleCallerPlayer.name} called Double but ${handWinnerPlayer.name} from ${teamNames[handWinnerTeam || oppositeTeam]} won the last hand. ${teamNames[oppositeTeam]} wins 4 balls!`;
    }

    return "";
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="double-results-container"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="double-results-card-wrapper"
          >
            <div className="double-results-card">
              <div className="double-results-header">
                <h2 className="double-results-title">Double Call Results</h2>
                <div className="double-results-timer">
                  {timeRemaining}s
                </div>
              </div>

              <div className="double-results-content">
                <p className="double-results-message">{getMessage()}</p>

                {/* Display hands won by opposite team if any */}
                {handsWonByOppositeTeam.length > 0 && (
                  <div className="double-results-section">
                    <h3 className="double-results-section-title">
                      Hands Won by {teamNames[doubleCallerTeam === 1 ? 2 : 1]}:
                    </h3>
                    <div className="double-results-hands-grid">
                      {handsWonByOppositeTeam.map((hand) => (
                        <div key={hand.handId} className="double-results-hand-card">
                          <div className="double-results-hand-header">
                            <span>Hand #{hand.handId}</span>
                            <span className="double-results-hand-winner">Winner: {hand.winner.name}</span>
                          </div>
                          <div className="double-results-cards-container">
                            {hand.cards.map((card, index) => (
                              <div key={index} className="double-results-card-image">
                                <img
                                  src={getCardImagePath(card.value, card.suit)}
                                  alt={`${card.value} of ${card.suit}`}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Display last hand */}
                {lastHand && (
                  <div className="double-results-section">
                    <h3 className="double-results-section-title">Last Hand:</h3>
                    <div className="double-results-last-hand">
                      <div className="double-results-last-hand-header">
                        <span>Hand #{lastHand.handId}</span>
                        <span className={`double-results-winner-badge ${
                          lastHand.winner.team === 1 ? 'double-results-winner-badge-team1' : 'double-results-winner-badge-team2'
                        }`}>
                          Winner: {lastHand.winner.name} ({teamNames[lastHand.winner.team]})
                        </span>
                      </div>
                      <div className="double-results-last-hand-cards">
                        {lastHand.cards.map((card, index) => (
                          <div key={index} className="double-results-last-hand-card">
                            <img
                              src={getCardImagePath(card.value, card.suit)}
                              alt={`${card.value} of ${card.suit}`}
                              className={card.playedBy === lastHand.winner.id ? 'double-results-last-hand-card-winner' : 'double-results-last-hand-card-border'}
                            />
                            {card.playedBy === lastHand.winner.id && (
                              <div className="double-results-winner-checkmark">
                                ✓
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <div className="double-results-final">
                  <h3 className="double-results-final-title">Final Result</h3>
                  <p className="double-results-final-result">
                    {teamNames[winningTeam || 1]} awarded {ballsAwarded} ball{ballsAwarded !== 1 ? 's' : ''}
                  </p>
                  <p className="double-results-final-description">
                    {outcome === 'success'
                      ? 'Double call was successful'
                      : 'Double call was unsuccessful'}
                  </p>
                </div>

                {showContinueButton && (
                  <button
                    onClick={() => {
                      if (onContinueGame) {
                        onContinueGame();
                      }
                      onClose();
                    }}
                    className="double-results-continue-button"
                    style={{ animation: 'pulse 2s infinite' }}
                  >
                    Continue Game
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
