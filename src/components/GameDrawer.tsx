import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";

export default function GameDrawer() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDrawer = () => setIsOpen(!isOpen);

  return (
    <div className="absolute left-0 top-[30%] flex h-[190px]">
      {/* Static Label/Toggle */}
      <motion.div
        onClick={toggleDrawer}
        animate={{
          x: isOpen ? "calc(100vw - 35px)" : 0,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
        className="flex h-full w-[35px] items-center justify-center rounded-r-lg bg-blackglass cursor-pointer border border-[#a07a4a] z-50"
      >
        <span className="whitespace-nowrap text-xs font-semibold text-[#a07a4a] transform rotate-90">
          Betting Markets
        </span>
      </motion.div>

      {/* Sliding Drawer */}
      <motion.div
        initial={{ width: 0, opacity: 0 }}
        animate={{
          width: isOpen ? "calc(100vw - 35px)" : 0,
          opacity: isOpen ? 1 : 0,
          x: isOpen ? "-35px" : 0,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
        className="relative bg-gradient-to-b from-[#a07a4a] via-[#edcf5d] to-[#7a5a00] overflow-hidden border-y border-r border-[#a07a4a]"
      >
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full px-4">
          <div className="grid grid-cols-2 gap-4 max-w-[300px] mx-auto">
            {["Bet Per Round", "Outright", "Bet Per Hand", "Multipliers"].map(
              (text, index) => (
                <button
                  key={index}
                  className="relative h-[30px] w-full overflow-hidden rounded-lg bg-[#373737] text-xs text-[#FFD972] hover:bg-[#424242/0%] transition-colors"
                >
                  {text}
                </button>
              )
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
