"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { Eye, Clock } from "lucide-react";

interface TrumpVisibilityVotingProps {
  onVotingComplete: () => void;
}

type VisibilityOption = 'short' | 'full';
type VoteResults = Record<VisibilityOption, number>;

export default function TrumpVisibilityVoting({
  onVotingComplete,
}: TrumpVisibilityVotingProps) {
  const { players } = useGameStore();
  const [selectedOption, setSelectedOption] = useState<VisibilityOption | null>(null);
  const [hasVoted, setHasVoted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [votingTimeLeft, setVotingTimeLeft] = useState(15); // 15 seconds to vote
  const [playersVoted, setPlayersVoted] = useState<string[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [selectedMode, setSelectedMode] = useState<VisibilityOption>('full');

  // Initialize vote results
  const [voteResults, setVoteResults] = useState<VoteResults>({
    short: 0,
    full: 0,
  });

  // Available options
  const visibilityOptions: { mode: VisibilityOption; label: string; description: string }[] = [
    {
      mode: 'short',
      label: '8 Seconds',
      description: 'Trump disappears after 8 seconds'
    },
    {
      mode: 'full',
      label: 'Full Hand',
      description: 'Trump visible for entire hand'
    }
  ];

  // Handle option selection
  const handleSelectOption = (option: VisibilityOption) => {
    if (hasVoted || isSubmitting) return;
    setSelectedOption(option);
  };

  // Handle vote submission
  const handleSubmitVote = async () => {
    if (!selectedOption || hasVoted || isSubmitting) return;

    try {
      setIsSubmitting(true);
      await socketService.sendGameAction("vote_trump_visibility", {
        mode: selectedOption,
      });
      setHasVoted(true);
      setIsSubmitting(false);
    } catch (error) {
      console.error("Error submitting trump visibility vote:", error);
      setIsSubmitting(false);
    }
  };

  // Listen for voting events from the server
  useEffect(() => {
    const handleVoteReceived = (data: {
      playerId: string;
      playerName: string;
    }) => {
      // Add player to the list of players who have voted
      setPlayersVoted((prev) => [...prev, data.playerId]);
    };

    const handleVoteResults = (data: {
      results: VoteResults;
      selectedMode: VisibilityOption;
    }) => {
      setVoteResults(data.results);
      setSelectedMode(data.selectedMode);
      setShowResults(true);

      // After showing results for 3 seconds, complete the voting process
      setTimeout(() => {
        onVotingComplete();
      }, 3000);
    };

    socketService.on("trump_visibility_vote_received", handleVoteReceived);
    socketService.on("trump_visibility_vote_results", handleVoteResults);

    return () => {
      socketService.off("trump_visibility_vote_received", handleVoteReceived);
      socketService.off("trump_visibility_vote_results", handleVoteResults);
    };
  }, [onVotingComplete]);

  // Countdown timer for voting
  useEffect(() => {
    if (hasVoted || showResults) return;

    const timer = setInterval(() => {
      setVotingTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          // If time runs out and player hasn't voted, auto-submit with default (full)
          if (!hasVoted && !isSubmitting) {
            setSelectedOption('full');
            handleSubmitVote();
          }
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [hasVoted, isSubmitting, showResults]);

  // Auto-submit when option is selected
  useEffect(() => {
    if (selectedOption && !hasVoted && !isSubmitting) {
      handleSubmitVote();
    }
  }, [selectedOption, hasVoted, isSubmitting]);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
      >
        <div className="bg-gray-900 border-2 border-[#E1C760] rounded-lg p-6 max-w-md w-full mx-4">
          {!showResults ? (
            <>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-[#E1C760] text-xl font-bold">
                  Vote for Trump Display
                </h2>
                <div className="bg-black/70 text-[#E1C760] px-2 py-1 rounded-md text-sm">
                  {votingTimeLeft}s
                </div>
              </div>

              <p className="text-white text-sm mb-4">
                How long should the trump suit remain visible after the first card is played?
              </p>

              <div className="grid grid-cols-1 gap-3 mb-6">
                {visibilityOptions.map((option) => (
                  <button
                    key={option.mode}
                    onClick={() => handleSelectOption(option.mode)}
                    disabled={hasVoted || isSubmitting}
                    className={`p-4 rounded-lg flex items-center justify-between ${
                      selectedOption === option.mode
                        ? "bg-[#E1C760] text-black"
                        : "bg-gray-800 text-white hover:bg-gray-700"
                    } ${
                      hasVoted || isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <div className="flex items-center">
                      <Eye className="mr-3" size={24} />
                      <div className="text-left">
                        <div className="text-lg font-bold">{option.label}</div>
                        <div className="text-xs opacity-80">{option.description}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              <div className="text-center">
                <p className="text-gray-400 text-xs mb-2">
                  Players voted: {playersVoted.length}/{players.length}
                </p>
                {hasVoted && (
                  <p className="text-green-400 text-sm">
                    ✓ Vote submitted! Waiting for other players...
                  </p>
                )}
              </div>
            </>
          ) : (
            <div className="text-center">
              <h2 className="text-[#E1C760] text-xl font-bold mb-4">
                Voting Results
              </h2>
              
              <div className="mb-4">
                <div className="text-white text-lg mb-2">
                  Selected: <span className="text-[#E1C760] font-bold">
                    {selectedMode === 'short' ? '8 Seconds' : 'Full Hand'}
                  </span>
                </div>
                
                <div className="space-y-2">
                  {Object.entries(voteResults).map(([mode, votes]) => (
                    <div key={mode} className="flex justify-between text-sm">
                      <span className="text-gray-300">
                        {mode === 'short' ? '8 Seconds' : 'Full Hand'}:
                      </span>
                      <span className="text-white">{votes} votes</span>
                    </div>
                  ))}
                </div>
              </div>

              <p className="text-gray-400 text-sm">
                Starting game in a moment...
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
