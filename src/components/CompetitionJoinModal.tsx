import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Copy, Users, UserPlus, AlertCircle } from 'lucide-react';
import { apiService } from '@/services/api';
import { copyToClipboard } from '@/utils/clipboard';

interface CompetitionJoinModalProps {
  isOpen: boolean;
  onClose: () => void;
  competitionId: string;
  competitionName: string;
  onSuccess: () => void;
}

export default function CompetitionJoinModal({
  isOpen,
  onClose,
  competitionId,
  competitionName,
  onSuccess
}: CompetitionJoinModalProps) {
  const [activeTab, setActiveTab] = useState('create');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Create team state
  const [teamName, setTeamName] = useState('');
  const [createdTeam, setCreatedTeam] = useState<any>(null);
  
  // Join team state
  const [inviteCode, setInviteCode] = useState('');

  const handleCreateTeam = async () => {
    if (!teamName.trim()) {
      setError('Please enter a team name');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const team = await apiService.createCompetitionTeam(competitionId, teamName.trim());
      setCreatedTeam(team);
      setSuccess('Team created successfully! Share the invite code with your partner.');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create team');
    } finally {
      setLoading(false);
    }
  };

  const handleJoinTeam = async () => {
    if (!inviteCode.trim()) {
      setError('Please enter an invite code');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await apiService.joinCompetitionTeam(inviteCode.trim());
      setSuccess('Successfully joined the team!');
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to join team');
    } finally {
      setLoading(false);
    }
  };

  const copyInviteCode = async () => {
    if (createdTeam?.inviteCode) {
      try {
        await copyToClipboard(createdTeam.inviteCode);
        setSuccess('Invite code copied to clipboard!');
      } catch (err) {
        console.error('Failed to copy invite code to clipboard', err);
        setError(`Failed to copy code to clipboard. Please copy manually: ${createdTeam.inviteCode}`);
      }
    }
  };

  const handleClose = () => {
    setTeamName('');
    setInviteCode('');
    setCreatedTeam(null);
    setError(null);
    setSuccess(null);
    setActiveTab('create');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-[#1A1A1A] border-[#333333] text-white" aria-describedby="competition-join-description">
        <DialogHeader>
          <DialogTitle className="text-[#E1C760]">
            Join Competition: {competitionName}
          </DialogTitle>
          <div id="competition-join-description" className="sr-only">
            Modal to join a competition by creating a team or joining an existing team with an invite code.
          </div>
        </DialogHeader>

        {error && (
          <Alert className="border-red-500 bg-red-900/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-400">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-500 bg-green-900/20">
            <AlertDescription className="text-green-400">{success}</AlertDescription>
          </Alert>
        )}

        {!createdTeam ? (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-[#2A2A2A]">
              <TabsTrigger value="create" className="data-[state=active]:bg-[#E1C760] data-[state=active]:text-black">
                <Users className="h-4 w-4 mr-2" />
                Create Team
              </TabsTrigger>
              <TabsTrigger value="join" className="data-[state=active]:bg-[#E1C760] data-[state=active]:text-black">
                <UserPlus className="h-4 w-4 mr-2" />
                Join Team
              </TabsTrigger>
            </TabsList>

            <TabsContent value="create" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="teamName">Team Name</Label>
                <Input
                  id="teamName"
                  value={teamName}
                  onChange={(e) => setTeamName(e.target.value)}
                  placeholder="Enter your team name"
                  className="bg-[#2A2A2A] border-[#444444] text-white"
                  maxLength={50}
                />
              </div>
              <Button
                onClick={handleCreateTeam}
                disabled={loading || !teamName.trim()}
                className="w-full bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
              >
                {loading ? 'Creating...' : 'Create Team'}
              </Button>
              <p className="text-sm text-gray-400">
                Create a team and get an invite code to share with your partner.
              </p>
            </TabsContent>

            <TabsContent value="join" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="inviteCode">Invite Code</Label>
                <Input
                  id="inviteCode"
                  value={inviteCode}
                  onChange={(e) => setInviteCode(e.target.value.toUpperCase())}
                  placeholder="Enter invite code"
                  className="bg-[#2A2A2A] border-[#444444] text-white"
                  maxLength={10}
                />
              </div>
              <Button
                onClick={handleJoinTeam}
                disabled={loading || !inviteCode.trim()}
                className="w-full bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
              >
                {loading ? 'Joining...' : 'Join Team'}
              </Button>
              <p className="text-sm text-gray-400">
                Enter the invite code shared by your teammate.
              </p>
            </TabsContent>
          </Tabs>
        ) : (
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-[#E1C760] mb-2">
                Team Created Successfully!
              </h3>
              <p className="text-gray-300 mb-4">
                Share this invite code with your partner:
              </p>
              <div className="flex items-center space-x-2 bg-[#2A2A2A] p-3 rounded border">
                <code className="flex-1 text-center text-lg font-mono text-[#E1C760]">
                  {createdTeam.inviteCode}
                </code>
                <Button
                  onClick={copyInviteCode}
                  size="sm"
                  variant="outline"
                  className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={() => {
                  onSuccess();
                  onClose();
                }}
                className="flex-1 bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
              >
                Continue
              </Button>
              <Button
                onClick={handleClose}
                variant="outline"
                className="flex-1 border-[#444444] text-gray-300 hover:bg-[#2A2A2A]"
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
