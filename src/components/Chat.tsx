"use client";
import { useEffect, useRef, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useChatStore } from "@/store/chatStore";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { X, Send } from "lucide-react";

// Simple time formatter function
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

export default function Chat() {
  const { messages, isOpen, closeChat, sendMessage } = useChatStore();
  const { players } = useGameStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [onlineCount, setOnlineCount] = useState(0);
  const [newMessage, setNewMessage] = useState("");

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (isOpen && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isOpen]);

  // Update online count
  useEffect(() => {
    if (players) {
      setOnlineCount(players.length);
    }
  }, [players]);

  // Get player name by ID
  const getPlayerName = (playerId: string) => {
    const player = players.find((p) => p.id === playerId);
    return player ? player.name : "Unknown Player";
  };

  // Check if message is from current user
  const isCurrentUser = (senderId: string) => {
    return senderId === socketService.getSocketId();
  };

  const handleSendMessage = () => {
    if (newMessage.trim() === "") return;
    sendMessage(newMessage);
    setNewMessage("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/90 z-50 flex flex-col "
          style={{ height: "calc(100vh - 60px)" }}
        >
          {/* Chat header */}
          <div className="p-4 border-b border-neutral-800 flex justify-between items-center">
            <h2 className="text-[#edcf5d] font-semibold">Live Chat</h2>
            <div className="flex items-center gap-2">
              <span className="bg-green-500 w-2 h-2 rounded-full"></span>
              <span className="text-green-500 text-xs">{onlineCount} Online</span>
              <button
                onClick={closeChat}
                className="text-white/70 hover:text-white ml-2"
              >
                <X size={20} />
              </button>
            </div>
          </div>

          {/* Chat messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center text-gray-500 mt-8">
                No messages yet. Start the conversation!
              </div>
            ) : (
              messages.map((message) => {
                const isFromCurrentUser = isCurrentUser(message.senderId);
                return (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex ${isFromCurrentUser ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        isFromCurrentUser
                          ? 'bg-gradient-to-r from-[#a07a4a] to-[#edcf5d] text-black'
                          : 'bg-neutral-800 text-white'
                      }`}
                    >
                      <div className="flex justify-between items-center mb-1">
                        <span className={`font-medium text-xs ${isFromCurrentUser ? 'text-black' : 'text-[#edcf5d]'}`}>
                          {isFromCurrentUser
                            ? "You"
                            : message.senderName || getPlayerName(message.senderId)}
                        </span>
                        <span className="text-xs opacity-70">
                          {formatTime(message.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm">{message.text}</p>
                    </div>
                  </motion.div>
                );
              })
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-neutral-800">
            <div className="flex items-center gap-2 bg-neutral-800 rounded-full px-4 py-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type a message..."
                className="flex-1 bg-transparent outline-none text-white"
              />
              <button
                onClick={handleSendMessage}
                className="text-[#edcf5d] p-1 rounded-full hover:bg-neutral-700"
              >
                <Send size={18} />
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
