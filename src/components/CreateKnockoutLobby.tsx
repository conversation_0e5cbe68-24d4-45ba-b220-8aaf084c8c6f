"use client";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Users, Trophy, Play } from "lucide-react";
import { toast } from "sonner";
import { apiService } from "@/services/api";

interface CompetitionTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  points: number;
  bonusPoints: number;
  gamesPlayed: number;
}

interface CreateKnockoutLobbyProps {
  competitionId: string;
  phase: string;
  teams: CompetitionTeam[];
  onLobbyCreated: () => void;
}

export default function CreateKnockoutLobby({ 
  competitionId, 
  phase, 
  teams, 
  onLobbyCreated 
}: CreateKnockoutLobbyProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [isCreating, setIsCreating] = useState(false);

  const handleTeamToggle = (teamId: string) => {
    setSelectedTeams(prev => {
      if (prev.includes(teamId)) {
        return prev.filter(id => id !== teamId);
      } else if (prev.length < 2) {
        return [...prev, teamId];
      } else {
        // Replace the first selected team if already 2 selected
        return [prev[1], teamId];
      }
    });
  };

  const handleCreateLobby = async () => {
    if (selectedTeams.length !== 2) {
      toast.error("Please select exactly 2 teams for the match");
      return;
    }

    try {
      setIsCreating(true);

      const data = await apiService.createPhaseLobby(competitionId, phase, selectedTeams);
      toast.success(`Knockout lobby created: ${data.lobbyCode}. Email notifications sent to all players!`);
      setIsOpen(false);
      setSelectedTeams([]);
      onLobbyCreated();
    } catch (error) {
      console.error("Error creating lobby:", error);
      toast.error("Failed to create lobby");
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateAllLobbies = async () => {
    if (teams.length < 2) {
      toast.error("Need at least 2 teams to create lobbies");
      return;
    }

    try {
      setIsCreating(true);
      let lobbiesCreated = 0;

      // Create lobbies for pairs of teams
      for (let i = 0; i < teams.length; i += 2) {
        if (i + 1 < teams.length) {
          const teamIds = [teams[i].id, teams[i + 1].id];

          try {
            await apiService.createPhaseLobby(competitionId, phase, teamIds);
            lobbiesCreated++;
          } catch (error) {
            console.error("Error creating lobby for teams:", teamIds, error);
          }
        }
      }

      if (lobbiesCreated > 0) {
        toast.success(`Created ${lobbiesCreated} knockout lobbies. Email notifications sent to all players!`);
        setIsOpen(false);
        onLobbyCreated();
      } else {
        toast.error("Failed to create any lobbies");
      }
    } catch (error) {
      console.error("Error creating lobbies:", error);
      toast.error("Failed to create lobbies");
    } finally {
      setIsCreating(false);
    }
  };

  const getSelectedTeams = () => {
    return teams.filter(team => selectedTeams.includes(team.id));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80">
          <Plus className="h-4 w-4 mr-2" />
          Create Knockout Lobby
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-2xl bg-[#1A1A1A] border-[#333333] text-white">
        <DialogHeader>
          <DialogTitle className="text-[#E1C760]">
            Create Knockout Lobby - {phase} Phase
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Select 2 teams to create a knockout match lobby, or create all lobbies automatically.
            <br />
            <span className="text-blue-400">📧 Email notifications with lobby codes will be automatically sent to all players.</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="flex gap-4">
            <Button
              onClick={handleCreateAllLobbies}
              disabled={isCreating || teams.length < 2}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              <Trophy className="h-4 w-4 mr-2" />
              Create All Lobbies
            </Button>
            
            <div className="text-sm text-gray-400 flex items-center">
              Will create {Math.floor(teams.length / 2)} lobbies
            </div>
          </div>

          {/* Manual Team Selection */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-white">Manual Selection</h4>
            
            {/* Selected Teams Preview */}
            {selectedTeams.length > 0 && (
              <Card className="bg-black/50 border-[#E1C760]/30">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm text-[#E1C760]">Selected Match</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {getSelectedTeams().map((team, index) => (
                      <div key={team.id} className="flex items-center justify-between p-2 bg-gray-800/50 rounded">
                        <div>
                          <p className="font-medium text-white">{team.teamName}</p>
                          <p className="text-sm text-gray-400">
                            {team.player1.username}
                            {team.player2 && ` & ${team.player2.username}`}
                          </p>
                        </div>
                        <Badge className="bg-[#E1C760]/20 text-[#E1C760]">
                          {team.points + team.bonusPoints} pts
                        </Badge>
                      </div>
                    ))}
                    
                    {selectedTeams.length === 1 && (
                      <div className="p-2 border border-dashed border-gray-600 rounded text-center">
                        <p className="text-sm text-gray-500">Select second team</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Team Selection Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
              {teams.map((team) => (
                <div
                  key={team.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedTeams.includes(team.id)
                      ? "border-[#E1C760] bg-[#E1C760]/10"
                      : "border-gray-600 bg-gray-800/30 hover:border-gray-500"
                  }`}
                  onClick={() => handleTeamToggle(team.id)}
                >
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={selectedTeams.includes(team.id)}
                      onChange={() => handleTeamToggle(team.id)}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-white truncate">{team.teamName}</p>
                      <div className="text-sm text-gray-400">
                        <p className="truncate">{team.player1.username}</p>
                        {team.player2 && (
                          <p className="truncate">{team.player2.username}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-[#E1C760]">
                        {team.points + team.bonusPoints}
                      </p>
                      <p className="text-xs text-gray-500">pts</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="border-gray-500 bg-gray-500/10 text-gray-600 hover:bg-gray-500/20"
            >
              Cancel
            </Button>
            
            <Button
              onClick={handleCreateLobby}
              disabled={selectedTeams.length !== 2 || isCreating}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
            >
              <Play className="h-4 w-4 mr-2" />
              Create Match Lobby
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
