"use client";
import { useState, useEffect } from "react";
import CardShuffleAnimation from "./CardShuffleAnimation";
import ThuneeCardDealingAnimation from "./ThuneeCardDealingAnimation";

interface ThuneeCardAnimationsProps {
  isVisible: boolean;
  onComplete: () => void;
}

type AnimationPhase = "idle" | "shuffle" | "dealFour" | "dealTwo" | "complete";

export default function ThuneeCardAnimations({
  isVisible,
  onComplete,
}: ThuneeCardAnimationsProps) {
  const [phase, setPhase] = useState<AnimationPhase>("idle");

  // Reset state when component becomes visible
  useEffect(() => {
    if (isVisible) {
      setPhase("shuffle");
    } else {
      setPhase("idle");
    }
  }, [isVisible]);

  // Handle shuffle completion
  const handleShuffleComplete = () => {
    setPhase("dealFour");
  };

  // Handle first dealing phase completion
  const handleDealFourComplete = () => {
    // After dealing 4 cards, we need to wait for trump selection
    // For now, we'll just move to the next phase after a delay
    setPhase("dealTwo");
  };

  // Handle second dealing phase completion
  const handleDealTwoComplete = () => {
    setPhase("complete");
    // Call the onComplete callback
    onComplete();
  };

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  return (
    <>
      {/* Shuffle Animation */}
      <CardShuffleAnimation
        isVisible={phase === "shuffle"}
        onComplete={handleShuffleComplete}
        duration={5} // 5 seconds for shuffle animation
        shuffleType="cascade" // Default shuffle type
      />

      {/* Deal First 4 Cards Animation */}
      <ThuneeCardDealingAnimation
        isVisible={phase === "dealFour"}
        onComplete={handleDealFourComplete}
        dealingPhase="dealFour"
        dealSpeed={800} // 800ms per card - slower for better visibility
      />

      {/* Deal Final 2 Cards Animation */}
      <ThuneeCardDealingAnimation
        isVisible={phase === "dealTwo"}
        onComplete={handleDealTwoComplete}
        dealingPhase="dealTwo"
        dealSpeed={800} // 800ms per card - slower for better visibility
      />
    </>
  );
}
