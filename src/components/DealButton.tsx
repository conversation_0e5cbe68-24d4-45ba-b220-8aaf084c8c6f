"use client";
import { useEffect, useState } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";

interface DealButtonProps {
  onDealClick?: () => void;
}

export function DealButton({ onDealClick }: DealButtonProps) {
  const { dealCards, players, isDealer } = useGameStore();
  const [showButton, setShowButton] = useState(false);

  // Check if current player is the dealer using both methods
  useEffect(() => {
    const currentPlayerId = socketService.getSocketId();
    const dealer = players.find((p) => p.isDealer);
    const isCurrentPlayerDealer = dealer && dealer.id === currentPlayerId;

    // Log dealer information
    console.log("DealButton - Current player ID:", currentPlayerId);
    console.log(
      "DealButton - Current dealer:",
      dealer ? `${dealer.name} (${dealer.id})` : "None"
    );
    console.log(
      "DealButton - Is current player dealer (from players)?",
      isCurrentPlayerDealer
    );
    console.log(
      "DealButton - Is current player dealer (from state)?",
      isDealer
    );

    // Use either method to determine if we should show the button
    setShowButton(isDealer || isCurrentPlayerDealer);

    // If we're the dealer but the state doesn't reflect it, update the state
    if (isCurrentPlayerDealer && !isDealer) {
      console.log("Fixing dealer state inconsistency");
      const { updateGameState } = useGameStore.getState();
      updateGameState({ isDealer: true });
    }
  }, [players, isDealer]);

  const handleDeal = async () => {
    try {
      console.log("Deal button clicked");

      // Show the card dealing animation if provided
      if (onDealClick) {
        console.log("Showing card dealing animation");
        onDealClick();
      }

      // Deal the cards - this sends the deal_cards event to the server
      console.log("Calling dealCards() function");
      await dealCards();
      console.log(
        "Cards dealt successfully - server acknowledged the deal_cards event"
      );
    } catch (error) {
      console.error("Error dealing cards:", error);
    }
  };

  // If not the dealer, don't render anything
  if (!showButton) {
    return null;
  }

  return (
    <div className="fixed bottom-24 left-0 right-0 flex justify-center items-center z-50">
      <button
        onClick={handleDeal}
        className="bg-[#E1C760] text-black px-10 py-4 rounded-md text-2xl font-bold shadow-lg border-4 border-black hover:bg-[#E1C760]/90 transition-colors animate-pulse"
      >
        DEAL
      </button>
    </div>
  );
}

export default DealButton;
