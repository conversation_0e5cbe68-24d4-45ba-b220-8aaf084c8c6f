import React, { useState } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { useLobbyStore } from '@/store/lobbyStore';
import { useAuthStore } from '@/store/authStore';
import { Copy, Share2 } from 'lucide-react';
import { copyToClipboard, shareWithFallback } from '@/utils/clipboard';

export default function InviteCodes() {
  const { partnerInviteCode, opponentInviteCode, isHost, teamNames } = useLobbyStore();
  const { user } = useAuthStore();
  const [copiedPartner, setCopiedPartner] = useState(false);
  const [copiedOpponent, setCopiedOpponent] = useState(false);
  const [sharedPartner, setSharedPartner] = useState(false);
  const [sharedOpponent, setSharedOpponent] = useState(false);

  // If not host or no invite codes, don't show anything
  if (!isHost || !partnerInviteCode || !opponentInviteCode) {
    return null;
  }

  const copyCodeToClipboard = async (code: string, type: 'partner' | 'opponent') => {
    try {
      await copyToClipboard(code);

      // Use visual feedback instead of toast
      if (type === 'partner') {
        setCopiedPartner(true);
        setTimeout(() => setCopiedPartner(false), 2000);
      } else {
        setCopiedOpponent(true);
        setTimeout(() => setCopiedOpponent(false), 2000);
      }
    } catch (err) {
      // Error is already handled by the utility function
      console.error('Failed to copy code to clipboard', err);
    }
  };

  const shareInvite = async (code: string, type: 'partner' | 'opponent') => {
    try {
      const username = user?.username || 'A player';
      const teamName = type === 'partner' ? teamNames[1] : 'the opposing team';
      const baseUrl = window.location.origin;
      const shareText = `${username} invites you to join ${teamName} in a Thunee game! Join here: ${baseUrl} with code: ${code}`;

      await shareWithFallback(
        {
          title: 'Join Thunee Game',
          text: shareText,
        },
        shareText,
        { showAlert: false }
      );

      // Show visual feedback
      if (type === 'partner') {
        setSharedPartner(true);
        setTimeout(() => setSharedPartner(false), 2000);
      } else {
        setSharedOpponent(true);
        setTimeout(() => setSharedOpponent(false), 2000);
      }
    } catch (err) {
      console.error('Failed to share invite', err);
      // Error is already handled by the utility function
    }
  };

  return (
    <div className="mt-4 space-y-4">
      <h2 className="text-xl font-bold text-center text-[#E1C760]">Invite Codes</h2>

      <Card className="p-4 bg-black border-[#E1C760]">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-[#E1C760]">Partner Invite Code (Team 1)</h3>
            <div className="flex flex-col sm:flex-row items-center mt-1 space-y-2 sm:space-y-0 sm:space-x-2">
              <div className="w-full sm:flex-1 p-2 font-mono text-white bg-gray-800 rounded">{partnerInviteCode}</div>
              <div className="flex space-x-2 w-full sm:w-auto">
                <Button
                  onClick={() => copyCodeToClipboard(partnerInviteCode, 'partner')}
                  className={`${copiedPartner ? 'bg-green-600' : 'bg-[#E1C760] text-black'} hover:bg-[#c9b052] flex-1 sm:flex-none`}
                  size="sm"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copiedPartner ? 'Copied!' : 'Copy'}
                </Button>
                <Button
                  onClick={() => shareInvite(partnerInviteCode, 'partner')}
                  className={`${sharedPartner ? 'bg-green-600' : 'bg-[#E1C760] text-black'} hover:bg-[#c9b052] flex-1 sm:flex-none`}
                  size="sm"
                >
                  <Share2 className="h-4 w-4 mr-1" />
                  {sharedPartner ? 'Shared!' : 'Share'}
                </Button>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-[#E1C760]">Opponent Invite Code (Team 2)</h3>
            <div className="flex flex-col sm:flex-row items-center mt-1 space-y-2 sm:space-y-0 sm:space-x-2">
              <div className="w-full sm:flex-1 p-2 font-mono text-white bg-gray-800 rounded">{opponentInviteCode}</div>
              <div className="flex space-x-2 w-full sm:w-auto">
                <Button
                  onClick={() => copyCodeToClipboard(opponentInviteCode, 'opponent')}
                  className={`${copiedOpponent ? 'bg-green-600' : 'bg-[#E1C760] text-black'} hover:bg-[#c9b052] flex-1 sm:flex-none`}
                  size="sm"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  {copiedOpponent ? 'Copied!' : 'Copy'}
                </Button>
                <Button
                  onClick={() => shareInvite(opponentInviteCode, 'opponent')}
                  className={`${sharedOpponent ? 'bg-green-600' : 'bg-[#E1C760] text-black'} hover:bg-[#c9b052] flex-1 sm:flex-none`}
                  size="sm"
                >
                  <Share2 className="h-4 w-4 mr-1" />
                  {sharedOpponent ? 'Shared!' : 'Share'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <div className="text-sm text-center text-gray-300">
        <p>Share the Partner code with your teammate and the Opponent code with the opposing team.</p>
        <p>Use the Share button to send an invite link directly, or Copy to get just the code.</p>
        <p>Each team needs exactly 2 players to start the game.</p>
      </div>
    </div>
  );
}
