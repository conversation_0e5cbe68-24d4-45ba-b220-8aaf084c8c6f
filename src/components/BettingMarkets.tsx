import type React from "react";

const BettingMarkets: React.FC = () => {
  return (
    <div className="absolute left-16 bottom-0 flex h-[190px] w-[141px]">
      <div className="flex h-full w-[35px] items-center justify-center rounded-l-lg bg-black">
        <span className="whitespace-nowrap text-xs font-semibold text-[#FFD972] transform -rotate-90">
          Betting Markets
        </span>
      </div>
      <div className="relative flex-1 rounded-r-lg bg-gradient-to-br from-gray-400/50 via-black/50 to-gray-400/50">
        <div className="absolute left-1/2 top-1/2 w-[90px] -translate-x-1/2 -translate-y-1/2 space-y-3">
          {["Bet Per Round", "Outright", "Bet Per Hand", "Multipliers"].map(
            (text, index) => (
              <div
                key={index}
                className="relative h-[30px] w-full overflow-hidden rounded-lg bg-[#373737]"
              >
                <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[#E1B70F] opacity-50"></div>
                <span className="absolute inset-0 flex items-center justify-center text-[11px] font-semibold text-white">
                  {text}
                </span>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default BettingMarkets;
