import React, { useState, useEffect } from 'react';
import { HubConnectionBuilder, HubConnectionState } from '@microsoft/signalr';
import { apiBaseUrl } from '@/config/env';

export default function VideoCallTest() {
  const [connectionState, setConnectionState] = useState<string>('Disconnected');
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [connection, setConnection] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
    console.log(`[VideoCallTest] ${message}`);
  };

  const testConnection = async () => {
    try {
      setError(null);
      addLog('Starting connection test...');

      // Get token
      const token = localStorage.getItem('thunee_token');
      addLog(`Token available: ${token ? 'Yes' : 'No'}`);

      if (!token) {
        setError('No authentication token found. Please login first.');
        return;
      }

      // Build hub URL
      const hubUrl = `${apiBaseUrl.replace('/api', '')}/videohub`;
      addLog(`Connecting to: ${hubUrl}`);

      // Create connection
      const newConnection = new HubConnectionBuilder()
        .withUrl(hubUrl, {
          accessTokenFactory: () => {
            addLog('Token factory called');
            return token;
          }
        })
        .withAutomaticReconnect()
        .build();

      // Set up event handlers
      newConnection.onclose((error) => {
        addLog(`Connection closed: ${error?.message || 'No error'}`);
        setConnectionState('Disconnected');
      });

      newConnection.onreconnecting((error) => {
        addLog(`Reconnecting: ${error?.message || 'No error'}`);
        setConnectionState('Reconnecting');
      });

      newConnection.onreconnected((connectionId) => {
        addLog(`Reconnected with ID: ${connectionId}`);
        setConnectionState('Connected');
      });

      // Hub event handlers
      newConnection.on('UserJoined', (user) => {
        addLog(`User joined: ${JSON.stringify(user)}`);
      });

      newConnection.on('UserLeft', (user) => {
        addLog(`User left: ${JSON.stringify(user)}`);
      });

      newConnection.on('Error', (error) => {
        addLog(`Hub error: ${error}`);
        setError(error);
      });

      // Start connection
      setConnectionState('Connecting');
      await newConnection.start();
      
      addLog(`Connected successfully! Connection ID: ${newConnection.connectionId}`);
      setConnectionState('Connected');
      setConnection(newConnection);

    } catch (error: any) {
      addLog(`Connection failed: ${error.message}`);
      setError(error.message);
      setConnectionState('Failed');
    }
  };

  const testJoinRoom = async () => {
    if (!connection || connection.state !== HubConnectionState.Connected) {
      setError('Not connected to hub');
      return;
    }

    try {
      addLog('Attempting to join test room...');
      
      await connection.invoke('JoinRoom', {
        roomId: 'test-room-123',
        userName: 'Test User'
      });
      
      addLog('Successfully joined room!');
    } catch (error: any) {
      addLog(`Failed to join room: ${error.message}`);
      setError(error.message);
    }
  };

  const disconnect = async () => {
    if (connection) {
      try {
        await connection.stop();
        addLog('Disconnected successfully');
        setConnectionState('Disconnected');
        setConnection(null);
      } catch (error: any) {
        addLog(`Disconnect error: ${error.message}`);
      }
    }
  };

  const clearLogs = () => {
    setLogs([]);
    setError(null);
  };

  return (
    <div className="fixed top-4 right-4 w-96 bg-black/90 border border-[#edcf5d] rounded-lg p-4 text-white text-sm z-50">
      <h3 className="text-[#edcf5d] font-semibold mb-3">Video Call Connection Test</h3>
      
      {/* Connection Status */}
      <div className="mb-3">
        <span className="text-gray-300">Status: </span>
        <span className={`font-semibold ${
          connectionState === 'Connected' ? 'text-green-400' :
          connectionState === 'Connecting' || connectionState === 'Reconnecting' ? 'text-yellow-400' :
          connectionState === 'Failed' ? 'text-red-400' : 'text-gray-400'
        }`}>
          {connectionState}
        </span>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-3 p-2 bg-red-600/20 border border-red-600 rounded text-red-300 text-xs">
          {error}
        </div>
      )}

      {/* Controls */}
      <div className="flex gap-2 mb-3">
        <button
          onClick={testConnection}
          disabled={connectionState === 'Connecting'}
          className="px-3 py-1 bg-[#edcf5d] text-black rounded text-xs hover:bg-[#d4b84a] disabled:opacity-50"
        >
          Connect
        </button>
        
        <button
          onClick={testJoinRoom}
          disabled={connectionState !== 'Connected'}
          className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 disabled:opacity-50"
        >
          Join Room
        </button>
        
        <button
          onClick={disconnect}
          disabled={connectionState === 'Disconnected'}
          className="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 disabled:opacity-50"
        >
          Disconnect
        </button>
        
        <button
          onClick={clearLogs}
          className="px-3 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-700"
        >
          Clear
        </button>
      </div>

      {/* Logs */}
      <div className="bg-gray-900 rounded p-2 max-h-48 overflow-y-auto">
        <div className="text-gray-400 text-xs mb-1">Connection Logs:</div>
        {logs.length === 0 ? (
          <div className="text-gray-500 text-xs">No logs yet...</div>
        ) : (
          logs.map((log, index) => (
            <div key={index} className="text-xs text-gray-300 font-mono">
              {log}
            </div>
          ))
        )}
      </div>
    </div>
  );
}
