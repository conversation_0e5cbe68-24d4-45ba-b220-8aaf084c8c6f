"use client";
import { Mic, MicOff, Video, VideoOff, PhoneOff, MonitorSmartphone } from "lucide-react";
import { useVideoCallStore } from "@/store/videoCallStore";

export default function VideoCallControls() {
  const {
    isAudioEnabled,
    isVideoEnabled,
    isScreenSharing,
    toggleAudio,
    toggleVideo,
    toggleScreenShare,
    endCall,
  } = useVideoCallStore();

  return (
    <div className="flex items-center justify-center space-x-4 p-4 bg-neutral-900 rounded-lg">
      <button
        onClick={toggleAudio}
        className={`p-3 rounded-full ${
          isAudioEnabled ? "bg-neutral-700" : "bg-red-600"
        }`}
      >
        {isAudioEnabled ? (
          <Mic className="text-white" size={20} />
        ) : (
          <MicOff className="text-white" size={20} />
        )}
      </button>

      <button
        onClick={toggleVideo}
        className={`p-3 rounded-full ${
          isVideoEnabled ? "bg-neutral-700" : "bg-red-600"
        }`}
      >
        {isVideoEnabled ? (
          <Video className="text-white" size={20} />
        ) : (
          <VideoOff className="text-white" size={20} />
        )}
      </button>

      <button
        onClick={() => toggleScreenShare()}
        className={`p-3 rounded-full ${
          isScreenSharing ? "bg-[#edcf5d]" : "bg-neutral-700"
        }`}
      >
        <MonitorSmartphone
          className={isScreenSharing ? "text-black" : "text-white"}
          size={20}
        />
      </button>

      <button
        onClick={endCall}
        className="p-3 rounded-full bg-red-600"
      >
        <PhoneOff className="text-white" size={20} />
      </button>
    </div>
  );
}
