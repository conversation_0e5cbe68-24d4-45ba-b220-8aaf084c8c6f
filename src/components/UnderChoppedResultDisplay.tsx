"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { CheckCircle, XCircle, ChevronDown, ChevronUp } from "lucide-react";
import { getCardImagePath } from "@/utils/cardUtils";
import "../styles/FourBallResultDisplay.css";
import "../styles/UnderChoppedResultDisplay.css";

type UnderChoppedResultProps = {
  isOpen: boolean;
  onClose: () => void;
  result: {
    ballType: string;
    option: string;
    targetPlayer: string;
    targetPlayerName: string;
    targetTeam: 1 | 2;
    accuserId: string;
    accuserName: string;
    accuserTeam: 1 | 2;
    handNumber: number;
    trumpSuit: string;
    leadSuit?: string;
    cardPlayed: {
      suit: string;
      value: string;
    };
    handBeforePlaying: Array<{
      suit: string;
      value: string;
    }>;
    // All cards played in the selected hand by all players
    selectedHandCards?: Array<{
      suit: string;
      value: string;
      playedBy: string;
      playerName: string;
      playerTeam: 1 | 2;
      playOrder?: number; // Order in which the card was played (0 = lead card)
    }>;
    isValid: boolean;
    isPlayedCardTrump: boolean;
    hasHigherTrump: boolean;
    trumpCardsCount: number;
    hadOnlyTrumps?: boolean;
    hadLeadSuitCards?: boolean;
    isHighestTrumpPlayed?: boolean;
    invalidReason?: string;
    highestTrumpPlayed?: {
      suit: string;
      value: string;
    };
    winningTeam: 1 | 2;
    ballsAwarded: number;
    customTrumpRanking: string[];
  } | null;
};

export default function UnderChoppedResultDisplay({
  isOpen,
  onClose,
  result
}: UnderChoppedResultProps) {
  const { teamNames } = useGameStore();
  const [countdown, setCountdown] = useState(10);
  const [showHandCards, setShowHandCards] = useState(false);
  const [showSelectedHand, setShowSelectedHand] = useState(true); // Show selected hand by default

  // Auto-close after countdown
  useEffect(() => {
    if (!isOpen || !result) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [isOpen, onClose, result]);

  // Reset countdown when modal opens
  useEffect(() => {
    if (isOpen) {
      setCountdown(10);
      setShowHandCards(false);
      setShowSelectedHand(true); // Show selected hand by default

      // Log the result data for debugging
      if (result) {
        console.log('Under chopped result data:', result);
        console.log('Selected hand cards:', result.selectedHandCards);
        console.log('Player hand before playing:', result.handBeforePlaying);
      }
    }
  }, [isOpen, result]);

  if (!isOpen || !result) return null;

  // Function to format suit name
  const formatSuit = (suit: string | undefined) => {
    if (!suit) return 'Unknown';
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  // Get only trump cards from the hand
  const trumpCards = (result.handBeforePlaying && result.trumpSuit)
    ? result.handBeforePlaying.filter(card => card.suit === result.trumpSuit)
    : [];

  // Find the highest trump card in hand based on custom ranking
  const getHighestTrumpCard = () => {
    if (trumpCards.length === 0) return null;

    // Sort trump cards by rank (lowest index in customTrumpRanking is highest)
    // Default ranking if customTrumpRanking is not provided
    const ranking = result.customTrumpRanking || ['Q', 'K', '10', 'A', '9', 'J'];

    return [...trumpCards].sort((a, b) => {
      const rankA = ranking.indexOf(a.value);
      const rankB = ranking.indexOf(b.value);
      return rankA - rankB;
    })[0];
  };

  const highestTrumpCard = getHighestTrumpCard();

  return (
    <AnimatePresence>
      {isOpen && result && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="four-ball-container"
        >
          <div className="four-ball-backdrop" />
          <div className="four-ball-card">
            <div className="four-ball-header">
              <h2 className="four-ball-title">4-Ball Result</h2>
              <p className="four-ball-subtitle">
                {result.option} by {result.accuserName} ({teamNames[result.accuserTeam]})
              </p>
            </div>

            <div className="four-ball-content">
              <div className="four-ball-section">
                <h3 className="four-ball-section-title">Accusation Details</h3>
                <p className="four-ball-text">
                  Player: <span className="four-ball-text-bold">{result.targetPlayerName}</span> ({teamNames[result.targetTeam]})
                </p>
                <p className="four-ball-text">
                  Hand #: <span className="four-ball-text-bold">{result.handNumber}</span>
                </p>
                <p className="four-ball-text">
                  Trump Suit: <span className="four-ball-text-bold">{formatSuit(result.trumpSuit)}</span>
                </p>
                <p className="four-ball-text">
                  Card Played: <span className="four-ball-text-bold">
                    {result.cardPlayed?.value || 'Unknown'} of {formatSuit(result.cardPlayed?.suit)}
                  </span>
                </p>
                <div className="four-ball-status">
                  <p className="four-ball-status-text">Status:</p>
                  {result.isValid ? (
                    <div className="four-ball-status-valid">
                      <CheckCircle className="four-ball-status-icon" />
                      <span>Valid Claim</span>
                    </div>
                  ) : (
                    <div className="four-ball-status-invalid">
                      <XCircle className="four-ball-status-icon" />
                      <span>Invalid Claim</span>
                    </div>
                  )}
                </div>

                {/* Clickable button to show cards */}
                <div
                  className="mt-3 text-center"
                  onClick={() => setShowHandCards(!showHandCards)}
                >
                  <button
                    className="bg-gray-800 hover:bg-gray-700 text-[#E1C760] font-bold py-2 px-4 rounded flex items-center mx-auto"
                  >
                    {showHandCards ? (
                      <>
                        <ChevronUp className="h-4 w-4 mr-1" />
                        Hide Player's Hand
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-4 w-4 mr-1" />
                        View Player's Hand
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Hand cards section - toggleable */}
              <div className={`four-ball-hand-details ${showHandCards ? 'visible' : ''}`}>
                <div className="bg-gray-800 p-2 rounded-t-md mb-2">
                  <h3 className="four-ball-hand-title flex items-center">
                    <span className="text-[#E1C760] font-bold mr-2">{result.targetPlayerName}'s Hand</span>
                    <span className="text-sm text-gray-300">
                      (Before playing {result.cardPlayed.value} of {formatSuit(result.cardPlayed.suit)})
                    </span>
                  </h3>
                </div>

                {result.handBeforePlaying && result.handBeforePlaying.length > 0 ? (
                  <>
                    <div className="four-ball-hand-cards">
                      {result.handBeforePlaying.map((card, index) => (
                        <div
                          key={index}
                          className={`four-ball-card-wrapper ${
                            card.suit === result.trumpSuit ? 'four-ball-card-highlight' : ''
                          } ${card.suit === result.cardPlayed.suit && card.value === result.cardPlayed.value ? 'four-ball-card-accused' : ''}`}
                        >
                          <img
                            src={getCardImagePath(card.value, card.suit)}
                            alt={`${card.value} of ${card.suit}`}
                          />
                          {card.suit === result.cardPlayed.suit && card.value === result.cardPlayed.value && (
                            <div className="absolute bottom-0 right-0 bg-red-500 text-white text-xs px-1 rounded-tl">
                              Played
                            </div>
                          )}
                          {card.suit === result.trumpSuit &&
                           result.hasHigherTrump &&
                           result.customTrumpRanking.indexOf(card.value) < result.customTrumpRanking.indexOf(result.cardPlayed.value) && (
                            <div className="absolute top-0 right-0 bg-yellow-500 text-white text-xs px-1 rounded-bl">
                              Higher Trump
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Display additional information about the player's hand */}
                    <div className="mt-4 grid grid-cols-2 gap-2 text-sm">
                      <div className="bg-gray-800 p-2 rounded">
                        <span className="text-gray-400">Trump Cards:</span>
                        <span className="ml-2 text-white font-bold">{result.trumpCardsCount}</span>
                      </div>
                      <div className="bg-gray-800 p-2 rounded">
                        <span className="text-gray-400">Had Higher Trump:</span>
                        <span className={`ml-2 font-bold ${result.hasHigherTrump ? 'text-green-500' : 'text-red-500'}`}>
                          {result.hasHigherTrump ? 'Yes' : 'No'}
                        </span>
                      </div>
                      {result.hadOnlyTrumps !== undefined && (
                        <div className="bg-gray-800 p-2 rounded">
                          <span className="text-gray-400">Only Trump Cards:</span>
                          <span className={`ml-2 font-bold ${result.hadOnlyTrumps ? 'text-yellow-500' : 'text-green-500'}`}>
                            {result.hadOnlyTrumps ? 'Yes' : 'No'}
                          </span>
                        </div>
                      )}
                      {result.hadLeadSuitCards !== undefined && (
                        <div className="bg-gray-800 p-2 rounded">
                          <span className="text-gray-400">Had Lead Suit:</span>
                          <span className={`ml-2 font-bold ${result.hadLeadSuitCards ? 'text-green-500' : 'text-gray-500'}`}>
                            {result.hadLeadSuitCards ? 'Yes' : 'No'}
                          </span>
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <p className="four-ball-text mt-2 text-center text-yellow-400">
                    Player's hand details not available
                  </p>
                )}
              </div>

              {/* Selected Hand Display - toggleable */}
              <div className="four-ball-section">
                <div
                  className="four-ball-section-title flex justify-between items-center cursor-pointer bg-gray-800 p-2 rounded-t-md"
                  onClick={() => setShowSelectedHand(!showSelectedHand)}
                >
                  <h3 className="flex items-center">
                    <span className="text-[#E1C760] font-bold mr-2">Selected Hand #{result.handNumber}</span>
                    {result.leadSuit && (
                      <span className="text-sm text-gray-300">
                        (Lead: {formatSuit(result.leadSuit)}
                        {result.leadSuit === result.trumpSuit && (
                          <span className="text-yellow-400 ml-1">- Trump was led</span>
                        )}
                        )
                      </span>
                    )}
                  </h3>
                  {showSelectedHand ? (
                    <ChevronUp className="h-5 w-5 text-[#E1C760]" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-[#E1C760]" />
                  )}
                </div>

                {showSelectedHand && (
                  <div className="mt-2">
                    {result.selectedHandCards && result.selectedHandCards.length > 0 ? (
                      <>
                        <p className="four-ball-text mb-2">
                          Cards played in this hand:
                        </p>
                        <div className="four-ball-hand-cards">
                          {/* Sort cards by playOrder if available */}
                          {[...result.selectedHandCards]
                            .sort((a, b) => (a.playOrder !== undefined && b.playOrder !== undefined) ? a.playOrder - b.playOrder : 0)
                            .map((card, index) => (
                              <div
                                key={index}
                                className={`four-ball-card-wrapper ${
                                  card.suit === result.trumpSuit ? 'four-ball-card-highlight' : ''
                                } ${card.playedBy === result.targetPlayer ? 'four-ball-card-accused' : ''}`}
                              >
                                <img
                                  src={getCardImagePath(card.value, card.suit)}
                                  alt={`${card.value} of ${card.suit}`}
                                />
                                <div className="four-ball-card-player">
                                  {card.playerName}
                                </div>
                                {index === 0 && (
                                  <div className="absolute top-0 left-0 bg-blue-500 text-white text-xs px-1 rounded-br">
                                    Lead
                                  </div>
                                )}
                                {card.playedBy === result.targetPlayer && (
                                  <div className="absolute bottom-0 right-0 bg-red-500 text-white text-xs px-1 rounded-tl">
                                    Accused
                                  </div>
                                )}
                                {card.suit === result.trumpSuit && card.value === result.highestTrumpPlayed?.value && (
                                  <div className="absolute top-0 right-0 bg-green-500 text-white text-xs px-1 rounded-bl">
                                    Highest Trump
                                  </div>
                                )}
                              </div>
                          ))}
                        </div>

                        {/* Display lead suit information */}
                        {result.leadSuit && (
                          <p className="four-ball-text mt-2 text-center">
                            Lead Suit: <span className="four-ball-text-bold">{formatSuit(result.leadSuit)}</span>
                            {result.leadSuit === result.trumpSuit && (
                              <span className="text-yellow-400 ml-2">(Trump was led)</span>
                            )}
                          </p>
                        )}

                        {/* Display custom trump ranking */}
                        {result.customTrumpRanking && (
                          <p className="four-ball-text mt-2 text-center">
                            Trump Ranking: <span className="four-ball-text-bold">{result.customTrumpRanking.join(' > ')}</span>
                            <span className="text-xs block text-gray-400">(Higher rank on left)</span>
                          </p>
                        )}
                      </>
                    ) : (
                      <p className="four-ball-text mt-2 text-center text-yellow-400">
                        Hand details not available
                      </p>
                    )}
                  </div>
                )}

                {/* We've already handled the case where selectedHandCards is empty or null above */}
              </div>

              <div className="four-ball-section">
                <h3 className="four-ball-section-title">Result</h3>
                <p className="four-ball-text four-ball-text-bold" style={{ textAlign: 'center' }}>
                  {teamNames[result.winningTeam]} wins {result.ballsAwarded} balls
                </p>

                {/* Display the specific reason for validity/invalidity */}
                <div className="bg-gray-900 rounded-lg p-3 mt-2">
                  <h4 className="text-[#E1C760] text-sm mb-1">Ruling:</h4>
                  {result.isValid ? (
                    <div className="flex items-center text-green-400 mb-2">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      <span className="font-semibold">Valid 4-Ball Claim</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-red-400 mb-2">
                      <XCircle className="h-4 w-4 mr-1" />
                      <span className="font-semibold">Invalid 4-Ball Claim: {result.invalidReason}</span>
                    </div>
                  )}

                  <p className="four-ball-text mt-1">
                    {result.isValid && highestTrumpCard ? (
                      `${result.targetPlayerName} played a lower trump (${result.cardPlayed?.value || 'Unknown'}) when they had a higher trump (${highestTrumpCard.value}) and had other non-trump cards. ${teamNames[result.accuserTeam]} wins 4 balls.`
                    ) : result.invalidReason === 'Trump was the Lead Suit' ? (
                      `Trump (${result.trumpSuit}) was the lead suit in this hand. A player cannot be penalized for Under Chopping when Trump was led. ${teamNames[result.targetTeam]} wins 4 balls.`
                    ) : result.invalidReason === 'Highest Trump Played' ? (
                      `${result.targetPlayerName} played the highest trump card in the hand. A player cannot be penalized for playing the winning trump. ${teamNames[result.targetTeam]} wins 4 balls.`
                    ) : result.invalidReason === 'No Trump Played' ? (
                      `${result.targetPlayerName} did not play a trump card. You cannot be Under Chopped if you didn't play a trump. ${teamNames[result.targetTeam]} wins 4 balls.`
                    ) : result.invalidReason === 'Player Had Only Trumps' ? (
                      `${result.targetPlayerName} had only trump cards left in hand. The player had no other option—they were forced to play a trump. ${teamNames[result.targetTeam]} wins 4 balls.`
                    ) : result.trumpCardsCount === 0 ? (
                      `${result.targetPlayerName} had no other trump cards. ${teamNames[result.targetTeam]} wins 4 balls.`
                    ) : (
                      `${result.targetPlayerName} did not have a higher trump card than the one played. ${teamNames[result.targetTeam]} wins 4 balls.`
                    )}
                  </p>
                </div>

                {/* Display lead suit information if available */}
                {result.leadSuit && (
                  <p className="four-ball-text mt-2">
                    Lead Suit: <span className="four-ball-text-bold">{formatSuit(result.leadSuit)}</span>
                  </p>
                )}
              </div>
            </div>

            <div className="four-ball-footer">
              <button
                onClick={onClose}
                className="four-ball-button"
              >
                Continue ({countdown})
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
