import { useState } from "react";
import { ArrowLeft } from "lucide-react";
import socketService from "@/services/socketService";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface GameControlsProps {
  onShowRules?: () => void;
}

const GameControls = ({ onShowRules }: GameControlsProps) => {
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [openAccordion, setOpenAccordion] = useState(false); // Track open state

  const callOptions = ["Doubles", "Khanak", "Thunee"];
  const ballOptions = ["1 ball", "2 ball", "3 ball", "4 ball", "8 ball"];
  const fourEightBallOptions = ["4 ball"];
  const jordhiOptions = ["20", "20", "30", "40", "50"];

  const nestedOptions: Record<string, string[]> = {
    "4 ball": ["Under chopped", "Never follow suit", "Called incorrect Jodhi", "Partner caught you"],
    "8 ball": ["Partner caught you"],
  };

  const handleCallAction = async (option: string) => {
    console.log(`handleCallAction called with option: ${option}`);
    try {
      if (option === "Doubles") {
        console.log("Sending call_double action");
        await socketService.sendGameAction("call_double", {});
      } else if (option === "Khanak") {
        console.log("Sending call_khanuck action");
        await socketService.sendGameAction("call_khanuck", {});
      } else if (option === "Thunee") {
        console.log("Sending call_thunee action");
        await socketService.sendGameAction("call_thunee", {});
      }
      console.log(`Successfully sent ${option} action`);
    } catch (error) {
      console.error(`Error sending ${option} action:`, error);
    }
    setActiveSubmenu(null);
  };

  const handleBallAction = (option: string) => {
    socketService.sendGameAction("ball_selection", { ballOption: option });
    setActiveSubmenu(null);
  };

  const handleJordhiAction = (option: string) => {
    socketService.sendGameAction("jordhi_selection", { jordhiOption: option });
    setActiveSubmenu(null);
  };

  const handleFourEightBallAction = (ballType: string, option: string) => {
    const openModal = async (modalKey: string) => {
      const { useGameStore } = await import("@/store/gameStore");
      const currentState = useGameStore.getState();
      const socketId = socketService.getSocketId();

      if (!currentState.currentPlayerId && socketId) {
        useGameStore.getState().updateGameState({ currentPlayerId: socketId, [modalKey]: true });
      } else {
        useGameStore.getState().updateGameState({ [modalKey]: true });
      }
    };

    if (option === "Called incorrect Jodhi") {
      openModal("incorrectJordhiModalOpen");
    } else if (option === "Never follow suit") {
      openModal("neverFollowSuitModalOpen");
    } else if (option === "Under chopped") {
      openModal("underChoppedModalOpen");
    } else {
      socketService.sendGameAction("four_eight_ball_selection", { ballType, option });
    }

    setActiveSubmenu(null);
  };

  const renderSubmenuContent = () => {
    const menuButtonClass = "h-8 lg:h-10 w-full bg-gradient-to-b from-gray-600 to-gray-800 text-white text-xs lg:text-sm font-bold rounded-md lg:rounded-lg hover:from-gray-500 hover:to-gray-700 transition-all duration-200 shadow-sm lg:shadow-md border border-gray-500 mb-1.5 lg:mb-2";
    const actionButtonClass = "h-7 lg:h-9 w-full bg-gradient-to-b from-gray-700 to-gray-900 text-white text-xs lg:text-sm font-medium rounded lg:rounded-md hover:from-gray-600 hover:to-gray-800 transition-all duration-200 border border-gray-600 mb-1 lg:mb-1";
    const backButtonClass = "h-7 lg:h-9 w-full bg-gradient-to-b from-gray-700 to-gray-900 text-white text-xs lg:text-sm font-medium rounded lg:rounded-md hover:from-gray-600 hover:to-gray-800 flex items-center justify-center mb-1.5 lg:mb-2 border border-gray-600";
    const grayGradientStyle = {
      background: 'linear-gradient(90deg,rgba(47, 47, 47, 1) 0%, rgba(16, 16, 16, 1) 39%, rgba(3, 3, 3, 1) 94%)'
    };

    switch (activeSubmenu) {
      case "calls":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu(null)} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 lg:mr-2 h-3 lg:h-4 w-3 lg:w-4" /> Back
            </button>
            {callOptions.map((option, index) => (
              <button key={index} onClick={() => handleCallAction(option)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      case "ball":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu(null)} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 lg:mr-2 h-3 lg:h-4 w-3 lg:w-4" /> Back
            </button>
            {ballOptions.map((option, index) => (
              <button key={index} onClick={() => handleBallAction(option)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      case "4/8ball":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu(null)} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 lg:mr-2 h-3 lg:h-4 w-3 lg:w-4" /> Back
            </button>
            {fourEightBallOptions.map((option, index) => (
              <button key={index} onClick={() => setActiveSubmenu(`${option}-submenu`)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      case "4 ball-submenu":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu("4/8ball")} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 lg:mr-2 h-3 lg:h-4 w-3 lg:w-4" /> Back
            </button>
            {nestedOptions["4 ball"].map((option, index) => (
              <button key={index} onClick={() => handleFourEightBallAction("4 ball", option)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      case "8 ball-submenu":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu("4/8ball")} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 lg:mr-2 h-3 lg:h-4 w-3 lg:w-4" /> Back
            </button>
            {nestedOptions["8 ball"].map((option, index) => (
              <button key={index} onClick={() => handleFourEightBallAction("8 ball", option)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      case "jordhi":
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu(null)} className={backButtonClass} style={grayGradientStyle}>
              <ArrowLeft className="mr-1 h-3 w-3" /> Back
            </button>
            {jordhiOptions.map((option, index) => (
              <button key={index} onClick={() => handleJordhiAction(option)} className={actionButtonClass} style={grayGradientStyle}>
                {option}
              </button>
            ))}
          </div>
        );
      default:
        return (
          <div className="flex flex-col">
            <button onClick={() => setActiveSubmenu("calls")} className={menuButtonClass} style={grayGradientStyle}>
              Calls
            </button>
            <button onClick={() => setActiveSubmenu("4/8ball")} className={menuButtonClass} style={grayGradientStyle}>
              4/8 Ball
            </button>
            <button onClick={() => setActiveSubmenu("jordhi")} className={menuButtonClass} style={grayGradientStyle}>
              Jordhi
            </button>
          </div>
        );
    }
  };

  return (
    <div className="fixed bottom-0 right-2 lg:right-4 xl:right-8 z-10 w-44 lg:w-64 game-controls" style={{ zIndex: "30" }}>
      <div className="rounded-t-lg lg:rounded-xl border-2 border-b-0 lg:border-b-2 shadow-xl lg:shadow-2xl overflow-hidden" style={{ background: 'linear-gradient(90deg,rgba(47, 47, 47, 1) 0%, rgba(16, 16, 16, 1) 39%, rgba(3, 3, 3, 1) 94%)', borderColor: 'rgba(134, 102, 10, 1)' }}>
        <Accordion
          type="single"
          collapsible
          className="w-full"
          onValueChange={(value) => {
            setOpenAccordion(value === "calls");
            if (value !== "calls") setActiveSubmenu(null); // Reset submenu if closed
          }}
        >
          <AccordionItem value="calls" className="border-0">
            <AccordionTrigger
              className="h-9 lg:h-12 text-yellow-400 text-sm lg:text-lg font-bold flex items-center justify-center px-3 lg:px-4 py-0 hover:opacity-80 transition-all duration-200 border-b-2 border-gray-500 data-[state=open]:border-b-0"
              style={{ background: 'linear-gradient(90deg, rgba(47, 47, 47, 0.82) 0%, rgba(2, 3, 3, 0.99) 100%)' }}
            >
              Calls
            </AccordionTrigger>
            <AccordionContent
              className="p-2 lg:p-4 data-[state=open]:animate-accordion-down data-[state=closed]:animate-accordion-up"
              style={{ background: 'linear-gradient(0deg,rgba(134, 102, 10, 1) 0%, rgba(246, 208, 106, 1) 50%, rgba(134, 102, 10, 1) 100%)' }}
            >
              {renderSubmenuContent()}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
};

export default GameControls;