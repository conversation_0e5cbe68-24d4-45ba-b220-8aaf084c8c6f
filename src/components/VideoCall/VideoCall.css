.video-call {
  position: fixed;
  bottom: 60px;
  right: 20px;
  width: 320px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 2px solid #ffcc00;
  border-radius: 8px;
  color: white;
  z-index: 1000;
  overflow: hidden;
  transition: all 0.3s ease;
  max-height: 80vh;
}

.video-call.minimized {
  height: 40px;
  overflow: hidden;
}

.video-call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #000;
  border-bottom: 1px solid #ffcc00;
}

.video-call-header h3 {
  margin: 0;
  font-size: 16px;
  color: #ffcc00;
}

.video-controls {
  display: flex;
  gap: 8px;
}

.video-controls button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
}

.video-controls button:hover {
  background-color: rgba(255, 204, 0, 0.2);
}

.video-controls button.active {
  background-color: rgba(255, 0, 0, 0.3);
}

.video-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  padding: 8px;
  height: 320px;
}

.video-container {
  position: relative;
  background-color: #222;
  border-radius: 4px;
  overflow: hidden;
  aspect-ratio: 4/3;
}

.video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-container.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
}

.video-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px;
  font-size: 12px;
  text-align: center;
}

.waiting-for-video,
.waiting-for-player {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 12px;
  text-align: center;
}

.video-error {
  padding: 12px;
  background-color: rgba(255, 0, 0, 0.2);
  border-bottom: 1px solid #ff6666;
}

.video-error h3 {
  margin: 0 0 8px 0;
  color: #ff6666;
  font-size: 14px;
}

.video-error p {
  margin: 4px 0;
  font-size: 12px;
}

.video-off {
  filter: grayscale(100%) brightness(0.3);
}

.video-off-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

@media (max-width: 768px) {
  .video-call {
    width: 280px;
    bottom: 70px;
  }
  
  .video-grid {
    height: 280px;
  }
}
