import React, { useEffect, useRef, useState } from 'react';
import Peer from 'simple-peer';
import videoService from '../../services/videoService';
import { useGameStore } from '../../store/gameStore';
import './VideoCall.css';

interface VideoCallProps {
  gameId: string;
  userName: string;
}

interface PeerConnection {
  peerId: string;
  peer: Peer.Instance;
  stream?: MediaStream;
  name: string;
}

const VideoCall: React.FC<VideoCallProps> = ({ gameId, userName }) => {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [peers, setPeers] = useState<PeerConnection[]>([]);
  const [deviceError, setDeviceError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const peersRef = useRef<PeerConnection[]>([]);
  const { players } = useGameStore();

  // Initialize video call
  useEffect(() => {
    const initializeVideoCall = async () => {
      try {
        // Connect to video service
        await videoService.connect(userName);
        setIsConnected(true);
        
        // Get local media stream
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: true, 
          audio: true 
        });
        
        // Set local stream
        setLocalStream(stream);
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = stream;
        }
        
        // Join room
        await videoService.joinRoom(gameId);
        
        // Set up event listeners
        setupEventListeners(stream);
        
        return () => {
          // Clean up
          if (localStream) {
            localStream.getTracks().forEach(track => track.stop());
          }
          
          peers.forEach(peer => {
            peer.peer.destroy();
          });
          
          videoService.disconnect();
        };
      } catch (error: any) {
        console.error('Error initializing video call:', error);
        setDeviceError(error.message || 'Error accessing camera/microphone');
        
        // Try to connect to video service even if media access fails
        try {
          await videoService.connect(userName);
          await videoService.joinRoom(gameId);
          setIsConnected(true);
        } catch (serviceError) {
          console.error('Error connecting to video service:', serviceError);
        }
      }
    };
    
    initializeVideoCall();
    
    return () => {
      // Clean up
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
      
      peers.forEach(peer => {
        peer.peer.destroy();
      });
      
      if (isConnected) {
        videoService.leaveRoom(gameId);
        videoService.disconnect();
      }
    };
  }, [gameId, userName]);

  // Set up event listeners for video service
  const setupEventListeners = (stream: MediaStream) => {
    // When we receive the list of users already in the room
    videoService.on('room_users', ({ users }) => {
      console.log('Received room users:', users);
      
      // Create a peer connection for each existing user
      users.forEach(user => {
        const peer = createPeer(user.id, videoService.getSocketId()!, stream, user.name);
        peersRef.current.push({
          peerId: user.id,
          peer,
          name: user.name
        });
      });
      
      setPeers(peersRef.current);
    });
    
    // When a new user joins the room
    videoService.on('user_joined', ({ id, name }) => {
      console.log('User joined:', id, name);
      
      // Create a peer connection for the new user
      const peer = addPeer(id, videoService.getSocketId()!, stream, name);
      peersRef.current.push({
        peerId: id,
        peer,
        name
      });
      
      setPeers(prevPeers => [...prevPeers, {
        peerId: id,
        peer,
        name
      }]);
    });
    
    // When a user leaves the room
    videoService.on('user_left', ({ id }) => {
      console.log('User left:', id);
      
      // Remove the peer connection
      const peerObj = peersRef.current.find(p => p.peerId === id);
      if (peerObj) {
        peerObj.peer.destroy();
      }
      
      // Update peers list
      peersRef.current = peersRef.current.filter(p => p.peerId !== id);
      setPeers(prevPeers => prevPeers.filter(p => p.peerId !== id));
    });
    
    // When we receive a signal from another peer
    videoService.on('signal', ({ from, signal, name }) => {
      console.log('Received signal from:', from);
      
      // Find the peer connection
      const peerObj = peersRef.current.find(p => p.peerId === from);
      
      if (peerObj) {
        // Signal the peer
        peerObj.peer.signal(signal);
      } else {
        // Create a new peer connection if it doesn't exist
        const peer = addPeer(from, videoService.getSocketId()!, stream, name);
        peersRef.current.push({
          peerId: from,
          peer,
          name
        });
        
        // Signal the peer
        peer.signal(signal);
        
        setPeers(prevPeers => [...prevPeers, {
          peerId: from,
          peer,
          name
        }]);
      }
    });
  };

  // Create a peer connection (initiator)
  const createPeer = (userToSignal: string, callerId: string, stream: MediaStream, userName: string): Peer.Instance => {
    const peer = new Peer({
      initiator: true,
      trickle: false,
      stream
    });
    
    peer.on('signal', signal => {
      videoService.sendSignal(userToSignal, signal);
    });
    
    peer.on('stream', remoteStream => {
      // Add remote stream to peer object
      const peerObj = peersRef.current.find(p => p.peerId === userToSignal);
      if (peerObj) {
        peerObj.stream = remoteStream;
        setPeers([...peersRef.current]);
      }
    });
    
    return peer;
  };

  // Add a peer connection (receiver)
  const addPeer = (callerId: string, userId: string, stream: MediaStream, userName: string): Peer.Instance => {
    const peer = new Peer({
      initiator: false,
      trickle: false,
      stream
    });
    
    peer.on('signal', signal => {
      videoService.sendSignal(callerId, signal);
    });
    
    peer.on('stream', remoteStream => {
      // Add remote stream to peer object
      const peerObj = peersRef.current.find(p => p.peerId === callerId);
      if (peerObj) {
        peerObj.stream = remoteStream;
        setPeers([...peersRef.current]);
      }
    });
    
    return peer;
  };

  // Toggle mute
  const toggleMute = () => {
    if (localStream) {
      localStream.getAudioTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
      setIsMuted(!isMuted);
    }
  };

  // Toggle video
  const toggleVideo = () => {
    if (localStream) {
      localStream.getVideoTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
      setIsVideoOff(!isVideoOff);
    }
  };

  // Toggle minimize
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // Render video grid
  const renderVideoGrid = () => {
    if (isMinimized) {
      return null;
    }

    return (
      <div className="video-grid">
        {/* Local video */}
        <div className="video-container">
          <video
            ref={localVideoRef}
            autoPlay
            playsInline
            muted
            className={isVideoOff ? 'video-off' : ''}
          />
          <div className="video-label">{userName} (You)</div>
          {isVideoOff && <div className="video-off-indicator">Video Off</div>}
        </div>
        
        {/* Remote videos */}
        {peers.map(peer => (
          <div key={peer.peerId} className="video-container">
            {peer.stream ? (
              <RemoteVideo stream={peer.stream} />
            ) : (
              <div className="waiting-for-video">Waiting for video...</div>
            )}
            <div className="video-label">{peer.name}</div>
          </div>
        ))}
        
        {/* Placeholder videos if less than 4 participants */}
        {Array.from({ length: Math.max(0, 3 - peers.length) }).map((_, i) => (
          <div key={`placeholder-${i}`} className="video-container placeholder">
            <div className="waiting-for-player">Waiting for player...</div>
          </div>
        ))}
      </div>
    );
  };

  // Render error message
  const renderError = () => {
    if (!deviceError) return null;
    
    return (
      <div className="video-error">
        <h3>Limited Video Call Mode</h3>
        <p>Your browser has limited WebRTC support. You can see your own video, but not other players.</p>
        <p>Error: {deviceError}</p>
      </div>
    );
  };

  return (
    <div className={`video-call ${isMinimized ? 'minimized' : ''}`}>
      <div className="video-call-header">
        <h3>Video Call</h3>
        <div className="video-controls">
          <button onClick={toggleMute} className={isMuted ? 'active' : ''}>
            {isMuted ? '🔇' : '🔊'}
          </button>
          <button onClick={toggleVideo} className={isVideoOff ? 'active' : ''}>
            {isVideoOff ? '📵' : '📹'}
          </button>
          <button onClick={toggleMinimize}>
            {isMinimized ? '🔼' : '🔽'}
          </button>
        </div>
      </div>
      
      {renderError()}
      {renderVideoGrid()}
    </div>
  );
};

// Remote video component
const RemoteVideo: React.FC<{ stream: MediaStream }> = ({ stream }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.srcObject = stream;
    }
  }, [stream]);
  
  return <video ref={videoRef} autoPlay playsInline />;
};

export default VideoCall;
