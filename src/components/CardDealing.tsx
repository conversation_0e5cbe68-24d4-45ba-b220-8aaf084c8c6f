"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Player } from "@/store/lobbyStore";
import { useGameStore, Card as PlayingCard } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { getCardImagePath } from "@/utils/cardUtils";

interface PlayerCardState {
  playerId: string;
  cards: PlayingCard[];
  isDealer: boolean;
}

interface CardDealingProps {
  isVisible: boolean;
  onDealingComplete: () => void;
}

export default function CardDealing({
  isVisible,
  onDealingComplete,
}: CardDealingProps) {
  const { players, isDealer, teamNames } = useGameStore();
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [message, setMessage] = useState("Dealing cards...");
  const [playerCards, setPlayerCards] = useState<PlayerCardState[]>([]);
  const [currentCard, setCurrentCard] = useState<PlayingCard | null>(null);
  const [allPlayers, setAllPlayers] = useState<Player[]>(players);
  const [dealingComplete, setDealingComplete] = useState(false);
  const [cardsDealt, setCardsDealt] = useState(0);

  // Initialize player cards state
  useEffect(() => {
    if (isVisible && players.length > 0) {
      console.log(
        "Initial players in CardDealing:",
        players.map((p) => `${p.name} (${p.id}) - Team ${p.team}`)
      );

      // Sort players by ID to ensure consistent order across all clients
      const sortedPlayers = [...players].sort((a, b) =>
        a.id.localeCompare(b.id)
      );
      setAllPlayers(sortedPlayers);

      // Initialize player cards if not already done
      setPlayerCards(
        sortedPlayers.map((player) => ({
          playerId: player.id,
          cards: [],
          isDealer: player.isDealer || false,
        }))
      );

      // Reset dealing state
      setDealingComplete(false);
      setCardsDealt(0);
    }
  }, [isVisible, players]);

  // Listen for dealing card to player event
  useEffect(() => {
    const handleDealingCardTo = (data: { playerId: string }) => {
      if (!isVisible) return;

      // console.log("Dealing card to player:", data.playerId);

      // Find the player who will receive the card
      const player = allPlayers.find((p) => p.id === data.playerId);
      if (!player) {
        console.error("Player not found in allPlayers:", data.playerId);
        return;
      }

      // Update current player index
      const playerIndex = allPlayers.findIndex((p) => p.id === data.playerId);
      if (playerIndex === -1) {
        console.error("Player index not found in allPlayers:", data.playerId);
        return;
      }

      setCurrentPlayerIndex(playerIndex);
      setMessage(`Dealing to: ${player.name}`);
    };

    socketService.on("dealing_card_to", handleDealingCardTo);

    return () => {
      socketService.off("dealing_card_to", handleDealingCardTo);
    };
  }, [allPlayers, isVisible]);

  // Listen for card dealt events
  useEffect(() => {
    const handleCardDealt = (data: {
      playerId: string;
      card: PlayingCard;
      isDealer: boolean;
    }) => {
      if (!isVisible) {
        console.log("Card dealt event ignored - component not visible");
        return;
      }

      console.log("Card dealt event received in CardDealing component:", data);
      console.log(
        "Current players:",
        allPlayers.map((p) => `${p.name} (${p.id})`)
      );
      setIsAnimating(true);

      // Find the player who received the card
      const player = allPlayers.find((p) => p.id === data.playerId);
      if (!player) {
        console.error("Player not found in allPlayers:", data.playerId);
        console.log(
          "Available players:",
          allPlayers.map((p) => `${p.name} (${p.id})`)
        );
        return;
      }

      // Update current player index
      const playerIndex = allPlayers.findIndex((p) => p.id === data.playerId);
      if (playerIndex === -1) {
        console.error("Player index not found in allPlayers:", data.playerId);
        return;
      }
      setCurrentPlayerIndex(playerIndex);

      // Set the current card being dealt - this is the card that will be shown in the center
      setCurrentCard(data.card);

      // Update message to show which player is receiving the card
      setMessage(`Dealing to: ${player.name}`);

      // Increment cards dealt counter
      setCardsDealt((prev) => prev + 1);

      // After a delay, update the player's cards in their display area
      setTimeout(() => {
        console.log(`Adding card to player ${player.name}'s hand`);
        // Update the player cards state to show the card in the player's area
        setPlayerCards((prevCards) => {
          return prevCards.map((pc) => {
            if (pc.playerId === data.playerId) {
              return {
                ...pc,
                cards: [...pc.cards, data.card],
              };
            }
            return pc;
          });
        });

        setIsAnimating(false);
      }, 800);
    };

    console.log("Adding card_dealt event listener in CardDealing component");
    socketService.on("card_dealt", handleCardDealt);

    return () => {
      console.log(
        "Removing card_dealt event listener in CardDealing component"
      );
      socketService.off("card_dealt", handleCardDealt);
    };
  }, [allPlayers, isVisible]);

  // Listen for cards dealt event (all cards have been dealt)
  useEffect(() => {
    const handleCardsDealt = () => {
      if (!isVisible) return;

      console.log("All cards have been dealt");
      setMessage("All cards have been dealt!");
      setDealingComplete(true);

      // After a delay, hide the dealing UI
      setTimeout(() => {
        onDealingComplete();
      }, 2000);
    };

    socketService.on("cards_dealt", handleCardsDealt);

    return () => {
      socketService.off("cards_dealt", handleCardsDealt);
    };
  }, [isVisible, onDealingComplete]);

  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center p-4">
      <h2 className="text-xl font-bold text-[#E1C760] mb-2">Dealing Cards</h2>
      <p className="text-white text-center mb-3 max-w-xs text-xs">
        {dealingComplete
          ? "All cards have been dealt!"
          : `Dealing 6 cards to each player (${cardsDealt}/24 cards dealt)`}
      </p>

      <div className="mb-2 text-center">
        <p className="text-white text-sm font-semibold mb-1">
          Dealing to:{" "}
          <span className="text-[#E1C760]">
            {allPlayers[currentPlayerIndex]?.name || ""}
          </span>
        </p>
        <p className="text-[#E1C760] text-xs">
          Everyone sees this card being dealt
        </p>
      </div>

      {/* Current card being dealt - this is shown to all players */}
      <div className="relative h-32 w-24 mb-3">
        <AnimatePresence>
          {currentCard && isAnimating && (
            <motion.div
              key={`current-${currentCard.suit}-${
                currentCard.value
              }-${Date.now()}`}
              initial={{ y: -50, opacity: 0, rotateY: 180, scale: 0.8 }}
              animate={{ y: 0, opacity: 1, rotateY: 0, scale: 1 }}
              exit={{ y: 50, opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="absolute inset-0"
            >
              <div className="relative">
                <div className="border-2 border-[#E1C760] rounded-md overflow-hidden shadow-lg shadow-[#E1C760]/20">
                  <img
                    src={getCardImagePath(currentCard.value, currentCard.suit)}
                    alt={`${currentCard.value} of ${currentCard.suit}`}
                    className="w-full h-full object-contain"
                  />
                </div>

                {/* Player name label */}
                <div className="absolute -bottom-6 left-0 right-0 text-center">
                  <span className="bg-[#E1C760] text-black text-xs px-2 py-0.5 rounded-full font-medium">
                    {allPlayers[currentPlayerIndex]?.name || ""}
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Player grid with their cards - ensure consistent layout for 4 players */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-4 mb-4 w-full max-w-xs">
        {allPlayers.length === 4 && playerCards.length === 4 ? (
          allPlayers.map((player, index) => {
            // Find card state for this player
            const playerCardState = playerCards.find(
              (pc) => pc.playerId === player.id
            );

            if (!playerCardState) {
              return null;
            }

            return (
              <div
                key={player.id}
                className={`flex flex-col items-center p-2 rounded-lg ${
                  currentPlayerIndex === index
                    ? "bg-[#E1C760]/10 ring-1 ring-[#E1C760]"
                    : ""
                }`}
              >
                <div className="flex flex-col items-center mb-2">
                  <div
                    className={`w-10 h-10 rounded-full overflow-hidden border-2 ${
                      playerCardState.isDealer
                        ? "border-green-500"
                        : currentPlayerIndex === index
                        ? "border-[#E1C760]"
                        : "border-gray-600"
                    }`}
                  >
                    <img
                      src={player.avatar}
                      alt={player.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="mt-1 text-center">
                    <span
                      className={`block text-sm font-medium ${
                        currentPlayerIndex === index
                          ? "text-[#E1C760]"
                          : "text-white"
                      }`}
                    >
                      {player.name}
                    </span>
                    {playerCardState.isDealer && (
                      <span className="text-green-500 text-xs font-bold block">
                        DEALER
                      </span>
                    )}
                  </div>
                </div>

                {/* Player's cards - show stacked */}
                <div className="h-24 w-16 relative">
                  {playerCardState.cards.length > 0 ? (
                    <div className="relative h-full w-full">
                      {playerCardState.cards.map((card, cardIndex) => (
                        <motion.div
                          key={`${player.id}-card-${cardIndex}`}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{
                            duration: 0.3,
                            delay: cardIndex * 0.05,
                          }}
                          className="absolute border-2 border-[#E1C760] rounded-md overflow-hidden shadow-md"
                          style={{
                            top: `${cardIndex * 3}px`,
                            left: `${cardIndex * 2}px`,
                            zIndex: cardIndex,
                            width: "100%",
                            height: "100%",
                          }}
                        >
                          <img
                            src={getCardImagePath(card.value, card.suit)}
                            alt={`${card.value} of ${card.suit}`}
                            className="w-full h-full object-contain"
                          />
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="w-full h-full border border-gray-600 rounded-md flex items-center justify-center bg-black/30">
                      <span className="text-gray-400 text-xs">Waiting...</span>
                    </div>
                  )}
                </div>

                {/* Show team info */}
                <div className="mt-2 text-xs text-center">
                  <span
                    className={`px-2 py-1 rounded-full font-medium ${
                      player.team === 1
                        ? "bg-blue-900 text-blue-200"
                        : "bg-red-900 text-red-200"
                    }`}
                  >
                    {teamNames[player.team] || `Team ${player.team}`}
                  </span>
                </div>
              </div>
            );
          })
        ) : (
          <div className="col-span-2 text-center text-white">
            <p>Waiting for players...</p>
          </div>
        )}
      </div>

      {/* Status message */}
      <div className="text-center text-white text-sm mt-2">{message}</div>
    </div>
  );
}
