"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import "../styles/StandardBallResultsDisplay.css";

interface TimeoutBallResultsDisplayProps {
  isVisible: boolean;
  onClose: () => void;
  timedOutPlayer: {
    id: string;
    name: string;
    team: 1 | 2;
  };
  opposingTeam: 1 | 2;
  ballsAwarded: number;
  displayDuration?: number;
  onContinueGame?: () => void;
}

export default function TimeoutBallResultsDisplay({
  isVisible,
  onClose,
  timedOutPlayer,
  opposingTeam,
  ballsAwarded = 1,
  displayDuration = 3000, // Default to 3 seconds
  onContinueGame,
}: TimeoutBallResultsDisplayProps) {
  const { teamNames } = useGameStore();
  const [timeRemaining, setTimeRemaining] = useState(displayDuration / 1000);
  const [showContinueButton, setShowContinueButton] = useState(false);

  // Set up timer for showing the continue button
  useEffect(() => {
    if (!isVisible) {
      setShowContinueButton(false);
      setTimeRemaining(displayDuration / 1000);
      return;
    }

    console.log("TimeoutBallResultsDisplay is visible, starting timer");
    setShowContinueButton(false);

    // Start countdown
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          console.log("Timer reached 0, showing continue button");
          setShowContinueButton(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // After displayDuration, show the continue button
    const showContinueTimer = setTimeout(() => {
      console.log("Display duration reached, showing continue button");
      setShowContinueButton(true);
    }, displayDuration);

    return () => {
      clearInterval(timer);
      clearTimeout(showContinueTimer);
    };
  }, [isVisible, displayDuration]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="ball-results-container"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="ball-results-card-wrapper"
          >
            <div className="ball-results-card">
              <div className="ball-results-header">
                <h2 className="ball-results-title">Player Timeout</h2>
                <div className="ball-results-timer">
                  {timeRemaining}s
                </div>
              </div>

              <div>
                {/* Timeout Information */}
                <div className="ball-results-section">
                  <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                    <div style={{ fontSize: '1.25rem', marginBottom: '0.5rem' }}>
                      <span style={{ color: '#f87171', fontWeight: 'bold' }}>
                        {timedOutPlayer.name}
                      </span> didn't play within the time limit
                    </div>
                    <div style={{ fontSize: '1.125rem' }}>
                      The opposite team ({teamNames[opposingTeam]}) wins {ballsAwarded} ball
                    </div>
                  </div>
                </div>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <div className="ball-results-final">
                  <h3 className="ball-results-final-title">Final Result</h3>
                  <p className="ball-results-final-result">
                    {`${teamNames[opposingTeam]} awarded ${ballsAwarded} ball${ballsAwarded !== 1 ? 's' : ''}`}
                  </p>
                  <p className="ball-results-final-description">
                    When a player doesn't play within the time limit, the opposite team automatically wins a ball.
                  </p>
                </div>

                {showContinueButton && (
                  <button
                    onClick={() => {
                      if (onContinueGame) {
                        onContinueGame();
                      }
                      onClose();
                    }}
                    className="ball-results-continue-button"
                    style={{ animation: 'pulse 2s infinite' }}
                  >
                    Continue Game
                  </button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
