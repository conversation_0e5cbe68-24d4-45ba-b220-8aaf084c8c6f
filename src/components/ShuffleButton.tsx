"use client";
import { useState, useEffect } from "react";
import { useGameStore } from "@/store/gameStore";
import socketService from "@/services/socketService";
import { Button } from "./ui/button";
import ShuffleOptions, { ShuffleType } from "./ShuffleOptions";

interface ShuffleButtonProps {
  onShuffleComplete: () => void;
}

export default function ShuffleButton({ onShuffleComplete }: ShuffleButtonProps) {
  const { isDealer } = useGameStore();
  const [isShuffling, setIsShuffling] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  // Clean up event listeners when component unmounts
  useEffect(() => {
    return () => {
      // Clean up any shuffle_complete listeners that might be active
      socketService.off("shuffle_complete");
    };
  }, []);

  // Only the dealer should see this button
  if (!isDealer) {
    return null;
  }

  const handleShowOptions = () => {
    setShowOptions(true);
  };

  const handleCancelOptions = () => {
    setShowOptions(false);
  };

  const handleSelectShuffle = async (type: ShuffleType) => {
    setShowOptions(false);
    setIsShuffling(true);

    try {
      console.log(`Shuffling deck with ${type} shuffle...`);

      // Send shuffle event to server - this will trigger the animation for all players
      await socketService.sendGameAction("shuffle_deck", { shuffleType: type });

      // Listen for the shuffle_complete event to reset the button state
      const handleShuffleComplete = () => {
        console.log("Shuffle complete, resetting button state");
        setIsShuffling(false);
        socketService.off("shuffle_complete", handleShuffleComplete);
        onShuffleComplete();
      };

      socketService.on("shuffle_complete", handleShuffleComplete);
    } catch (error) {
      console.error("Error shuffling deck:", error);
      setIsShuffling(false);
    }
  };

  return (
    <>
      <div className="fixed bottom-24 left-0 right-0 flex justify-center items-center z-50">
        <Button
          onClick={handleShowOptions}
          disabled={isShuffling}
          className="dealer-button bg-[#E1C760] text-black px-10 py-4 rounded-md text-2xl font-bold shadow-lg border-4 border-black hover:bg-[#E1C760]/90 transition-colors"
        >
          {isShuffling ? "SHUFFLING..." : "SHUFFLE"}
        </Button>
      </div>

      <ShuffleOptions
        isOpen={showOptions}
        onSelectShuffle={handleSelectShuffle}
        onCancel={handleCancelOptions}
      />
    </>
  );
}
