// "use client";
// import { useGameStore } from "@/store/gameStore";
// import { motion } from "framer-motion";
// import { useEffect, useState, useRef } from "react";
// import { getCardImagePath } from "@/utils/cardUtils";

// export default function TrumpDisplay() {
//   const { trumpSuit, trumpCard, playedCards, currentHand, currentBall, trumpVisibilityMode, isLastCard } = useGameStore();
//   const [showTrump, setShowTrump] = useState(false);
//   const [temporaryReveal, setTemporaryReveal] = useState(false);
//   const timerRef = useRef<NodeJS.Timeout | null>(null);
//   const lastHandRef = useRef<number>(0);
//   const lastBallRef = useRef<number>(0);
//   const firstCardPlayedRef = useRef<boolean>(false);

//   // Debug logging
//   useEffect(() => {
//     console.log("TrumpDisplay state:", {
//       trumpSuit,
//       trumpCard,
//       isLastCard,
//       trumpVisibilityMode,
//       playedCards: playedCards.length,
//       currentHand,
//       currentBall,
//       showTrump,
//       temporaryReveal
//     });
//   }, [trumpSuit, trumpCard, isLastCard, trumpVisibilityMode, playedCards.length, currentHand, currentBall, showTrump, temporaryReveal]);

//   // Track when the first card of a ball is played
//   useEffect(() => {
//     // Reset first card played flag when a new ball starts
//     if (currentBall !== lastBallRef.current) {
//       console.log("New ball started - resetting first card played flag");
//       firstCardPlayedRef.current = false;
//       lastBallRef.current = currentBall;
//       lastHandRef.current = currentHand;
//     }

//     // Track the current hand without resetting the flag
//     if (currentHand !== lastHandRef.current) {
//       lastHandRef.current = currentHand;
//     }

//     // Show trump when first card is played in a ball
//     if (trumpSuit && playedCards.length >= 1 && !firstCardPlayedRef.current) {
//       console.log("🎯 First card of the ball played - revealing trump");
//       console.log("🎯 Trump visibility mode:", trumpVisibilityMode);
//       firstCardPlayedRef.current = true;
//       setTemporaryReveal(true);

//       // Clear any existing timer
//       if (timerRef.current) {
//         clearTimeout(timerRef.current);
//       }

//       // Set timer based on voting results - BOTH options show trump initially
//       if (trumpVisibilityMode === 'short') {
//         // Hide after 8 seconds for 'short' mode
//         console.log("🎯 Setting 8-second timer for trump display (short mode)");
//         timerRef.current = setTimeout(() => {
//           console.log("🎯 8 seconds passed - hiding trump (short mode)");
//           setTemporaryReveal(false);
//         }, 8000);
//       } else if (trumpVisibilityMode === 'full') {
//         // Keep visible for the entire hand (6 cards) for 'full' mode
//         console.log("🎯 Trump will remain visible for the full hand (full mode)");
//         // No timer set - will be hidden when hand completes
//       } else {
//         // Fallback to 8 seconds if no voting result yet
//         console.log("🎯 No voting result yet - using 8-second fallback");
//         timerRef.current = setTimeout(() => {
//           console.log("🎯 8 seconds passed - hiding trump (fallback)");
//           setTemporaryReveal(false);
//         }, 8000);
//       }
//     }
//   }, [trumpSuit, playedCards, currentHand, currentBall, trumpVisibilityMode]);

//   // Clean up timer on unmount
//   useEffect(() => {
//     return () => {
//       if (timerRef.current) {
//         clearTimeout(timerRef.current);
//       }
//     };
//   }, []);

//   // Determine if trump should be shown
//   useEffect(() => {
//     // Show trump if it's temporarily revealed OR if we have a trump suit and cards are played
//     if (trumpSuit && (temporaryReveal || (playedCards.length > 0 && trumpVisibilityMode))) {
//       setShowTrump(true);
//     } else {
//       setShowTrump(false);
//     }
//   }, [trumpSuit, temporaryReveal, playedCards.length, trumpVisibilityMode]);

//   // Hide trump when hand completes (for full hand mode)
//   useEffect(() => {
//     if (trumpVisibilityMode === 'full' && playedCards.length === 0 && temporaryReveal) {
//       // Hand completed, hide trump
//       console.log("Hand completed - hiding trump (full hand mode)");
//       setTemporaryReveal(false);
//     }
//   }, [playedCards.length, trumpVisibilityMode, temporaryReveal]);

//   // Show trump if we have a trump suit and should show it
//   if (!trumpSuit || !showTrump) {
//     return null;
//   }

//   return (
//     <>
     

//       <div className="fixed bottom-4 left-36 transform -translate-x-1/2 z-40 pointer-events-none" style={{ width: "-13vw" }}>
//         <motion.div
//           initial={{ scale: 0.8, opacity: 0 }}
//           animate={{ scale: 1, opacity: 1 }}
//           exit={{ scale: 0.8, opacity: 0 }}
//           className="bg-black/90 border-3 border-[#E1C760] rounded-lg p-4 flex flex-col items-center shadow-[0_0_15px_rgba(225,199,96,0.5)]"
//         >
//           <span className="text-[#E1C760] text-lg font-bold mb-2">TRUMP</span>
//           {isLastCard ? (
//             // Show "Last Card" for last card selection
//             <div className="flex flex-col items-center">
//               <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-2 border border-[#E1C760]">
//                 <span className="text-white text-xs font-bold text-center">LAST<br/>CARD</span>
//               </div>
//               <span className="text-white text-sm mt-2 font-medium">Last Card</span>
//             </div>
//           ) : trumpCard ? (
//             // Show the specific trump card
//             <div className="flex flex-col items-center">
//               <div className="w-16 h-24 border-2 border-[#E1C760] rounded overflow-hidden shadow-md">
//                 <img
//                   src={getCardImagePath(trumpCard.value, trumpCard.suit)}
//                   alt={`${trumpCard.value} of ${trumpCard.suit}`}
//                   className="w-full h-full object-contain"
//                 />
//               </div>
//               <span className="text-white text-sm mt-2 font-medium">{trumpCard.value} of {trumpCard.suit}</span>
//             </div>
//           ) : (
//             // Fallback to suit display
//             <div className="flex flex-col items-center">
//               <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-1.5 border border-[#E1C760]">
//                 <img
//                   src={`/SuitFaces/${trumpSuit.charAt(0).toUpperCase()}.svg`}
//                   alt={trumpSuit}
//                   className="w-full h-full"
//                 />
//               </div>
//               <span className="text-white text-sm mt-2 capitalize font-medium">{trumpSuit}</span>
//             </div>
//           )}
//           {showTrump && (
//             <span className="text-yellow-300 text-xs mt-1">
//               {trumpVisibilityMode === 'short'
//                 ? "(Visible for 8 seconds)"
//                 : trumpVisibilityMode === 'full'
//                   ? "(Visible for full hand)"
//                   : "(Visible for 8 seconds)"}
//             </span>
//           )}
//         </motion.div>
//       </div>
//     </>
//   );
// }
"use client";
import { useGameStore } from "@/store/gameStore";
import { motion } from "framer-motion";
import { useEffect, useState, useRef } from "react";
import { getCardImagePath } from "@/utils/cardUtils";

export default function TrumpDisplay() {
  const { trumpSuit, trumpCard, playedCards, currentHand, currentBall, trumpVisibilityMode, isLastCard } = useGameStore();
  const [showTrump, setShowTrump] = useState(false);
  const [temporaryReveal, setTemporaryReveal] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const lastHandRef = useRef<number>(0);
  const lastBallRef = useRef<number>(0);
  const firstCardPlayedRef = useRef<boolean>(false);

  // Debug logging
  useEffect(() => {
    console.log("TrumpDisplay state:", {
      trumpSuit,
      trumpCard,
      isLastCard,
      trumpVisibilityMode,
      playedCards: playedCards.length,
      currentHand,
      currentBall,
      showTrump,
      temporaryReveal
    });
  }, [trumpSuit, trumpCard, isLastCard, trumpVisibilityMode, playedCards.length, currentHand, currentBall, showTrump, temporaryReveal]);

  // Track when the first card of a ball is played
  useEffect(() => {
    // Reset first card played flag when a new ball starts
    if (currentBall !== lastBallRef.current) {
      console.log("New ball started - resetting first card played flag");
      firstCardPlayedRef.current = false;
      lastBallRef.current = currentBall;
      lastHandRef.current = currentHand;
    }

    // Track the current hand without resetting the flag
    if (currentHand !== lastHandRef.current) {
      lastHandRef.current = currentHand;
    }

    // Show trump when first card is played in a ball
    if (trumpSuit && playedCards.length >= 1 && !firstCardPlayedRef.current) {
      console.log("🎯 First card of the ball played - revealing trump");
      console.log("🎯 Trump visibility mode:", trumpVisibilityMode);
      firstCardPlayedRef.current = true;
      setTemporaryReveal(true);

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Set timer based on voting results - BOTH options show trump initially
      if (trumpVisibilityMode === 'short') {
        // Hide after 8 seconds for 'short' mode
        console.log("🎯 Setting 8-second timer for trump display (short mode)");
        timerRef.current = setTimeout(() => {
          console.log("🎯 8 seconds passed - hiding trump (short mode)");
          setTemporaryReveal(false);
        }, 8000);
      } else if (trumpVisibilityMode === 'full') {
        // Keep visible for the entire hand (6 cards) for 'full' mode
        console.log("🎯 Trump will remain visible for the full hand (full mode)");
        // No timer set - will be hidden when hand completes
      } else {
        // Fallback to 8 seconds if no voting result yet
        console.log("🎯 No voting result yet - using 8-second fallback");
        timerRef.current = setTimeout(() => {
          console.log("🎯 8 seconds passed - hiding trump (fallback)");
          setTemporaryReveal(false);
        }, 8000);
      }
    }
  }, [trumpSuit, playedCards, currentHand, currentBall, trumpVisibilityMode]);

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Determine if trump should be shown
  useEffect(() => {
    // Show trump if it's temporarily revealed OR if we have a trump suit and cards are played
    if (trumpSuit && (temporaryReveal || (playedCards.length > 0 && trumpVisibilityMode))) {
      setShowTrump(true);
    } else {
      setShowTrump(false);
    }
  }, [trumpSuit, temporaryReveal, playedCards.length, trumpVisibilityMode]);

  // Hide trump when hand completes (for full hand mode)
  useEffect(() => {
    if (trumpVisibilityMode === 'full' && playedCards.length === 0 && temporaryReveal) {
      // Hand completed, hide trump
      console.log("Hand completed - hiding trump (full hand mode)");
      setTemporaryReveal(false);
    }
  }, [playedCards.length, trumpVisibilityMode, temporaryReveal]);

  // Show trump if we have a trump suit and should show it
  if (!trumpSuit || !showTrump) {
    return null;
  }

  return (
    <>
      {/* Mobile-responsive trump display */}
      <div className="fixed bottom-2 left-2 z-40 pointer-events-none 
                      sm:bottom-4 sm:left-4 
                      md:bottom-4 md:left-36 md:transform md:-translate-x-1/2">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          className="bg-black/90 border-2 border-[#E1C760] rounded-lg p-2 flex flex-col items-center shadow-[0_0_15px_rgba(225,199,96,0.5)]
                     sm:border-3 sm:p-3
                     md:p-4"
        >
          <span className="text-[#E1C760] text-xs font-bold mb-1
                          sm:text-sm sm:mb-2
                          md:text-lg">
            TRUMP
          </span>
          {isLastCard ? (
            // Show "Last Card" for last card selection
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-1 border border-[#E1C760]
                            sm:w-12 sm:h-12 sm:p-1.5
                            md:w-16 md:h-16 md:p-2">
                <span className="text-white text-[0.6rem] font-bold text-center leading-tight
                               sm:text-xs
                               md:text-xs">
                  LAST<br/>CARD
                </span>
              </div>
              <span className="text-white text-xs mt-1 font-medium
                             sm:text-sm sm:mt-2
                             md:text-sm">
                Last Card
              </span>
            </div>
          ) : trumpCard ? (
            // Show the specific trump card
            <div className="flex flex-col items-center">
              <div className="w-10 h-14 border-2 border-[#E1C760] rounded overflow-hidden shadow-md
                            sm:w-12 sm:h-16
                            md:w-16 md:h-24">
                <img
                  src={getCardImagePath(trumpCard.value, trumpCard.suit)}
                  alt={`${trumpCard.value} of ${trumpCard.suit}`}
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-white text-xs mt-1 font-medium text-center
                             sm:text-sm sm:mt-2
                             md:text-sm">
                {trumpCard.value} of {trumpCard.suit}
              </span>
            </div>
          ) : (
            // Fallback to suit display
            <div className="flex flex-col items-center">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-700 to-green-900 flex items-center justify-center p-1 border border-[#E1C760]
                            sm:w-10 sm:h-10 sm:p-1.5
                            md:w-12 md:h-12">
                <img
                  src={`/SuitFaces/${trumpSuit.charAt(0).toUpperCase()}.svg`}
                  alt={trumpSuit}
                  className="w-full h-full"
                />
              </div>
              <span className="text-white text-xs mt-1 capitalize font-medium
                             sm:text-sm sm:mt-2
                             md:text-sm">
                {trumpSuit}
              </span>
            </div>
          )}
          {showTrump && (
            <span className="text-yellow-300 text-[0.6rem] mt-0.5 text-center
                           sm:text-xs sm:mt-1
                           md:text-xs">
              {trumpVisibilityMode === 'short'
                ? "(8 seconds)"
                : trumpVisibilityMode === 'full'
                  ? "(Full hand)"
                  : "(8 seconds)"}
            </span>
          )}
        </motion.div>
      </div>
    </>
  );
}