"use client";

import type React from "react";

import { useState } from "react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Trash2 } from "lucide-react";
import { BetPanel } from "./BetPanel";

interface BetslipProps {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function Betslip({
  children,
  open,
  onOpenChange,
}: BetslipProps) {
  const [bets, setBets] = useState([
    { id: 1, stake: 100, payout: 90 },
    { id: 2, stake: 100, payout: 90 },
    { id: 3, stake: 100, payout: 90 },
  ]);

  const [totalStake, setTotalStake] = useState(400);
  const [totalPayout, setTotalPayout] = useState(360);

  const removeBet = (id: number) => {
    setBets(bets.filter((bet) => bet.id !== id));
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent
        side="bottom"
        className="h-[80vh] bg-[#262626] border-t-2 border-[#a07a4a] p-0"
      >
        <div className="flex flex-col h-full">
          {/* Scrollable Bet Panels */}
          <ScrollArea className="flex-1 px-4">
            <div className="space-y-4 py-4">
              {bets.map((bet) => (
                <BetPanel
                  key={bet.id}
                  stake={bet.stake}
                  payout={bet.payout}
                  onClose={() => removeBet(bet.id)}
                />
              ))}
            </div>
          </ScrollArea>

          {/* Fixed Summary Panel */}
          <div className="bg-[#262626] border-t border-[#646464]/20 p-4">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <h2 className="text-white text-xl font-semibold">
                  Single (x{bets.length})
                </h2>
              </div>
              <div className="flex items-center gap-2">
                <button className="bg-gradient-to-b from-[#C4A962] to-[#907c47] text-white px-6 py-2 rounded-full font-semibold hover:from-[#d4b972] hover:to-[#a08c57] transition-colors">
                  Raise All
                </button>
                <button className="bg-[#208801] text-white px-6 py-2 rounded-full font-semibold hover:bg-[#2a9801] transition-colors">
                  Accept All
                </button>
                <button
                  className="p-2 text-[#ff2323] hover:bg-[#ff2323]/10 rounded-md transition-colors"
                  onClick={() => setBets([])}
                >
                  <Trash2 className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-[#646464]">Stake</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white">R100</p>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-[#646464]">Total Stakes</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white">R{totalStake}</p>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-[#646464]">Total Stake</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white">R{totalStake}</p>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-[#646464]">Potential Payout</p>
                <div className="bg-[#2d2d2d] rounded-md p-2">
                  <p className="text-white">R{totalPayout.toFixed(2)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
