"use client";
import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

type NotificationType = "success" | "error" | "info" | "warning";

interface Notification {
  title: string;
  message: string;
  type: NotificationType;
  duration?: number;
}

export default function Notification() {
  const [notification, setNotification] = useState<Notification | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleShowNotification = (event: CustomEvent<Notification>) => {
      const newNotification = event.detail;
      setNotification(newNotification);
      setIsVisible(true);

      // Auto-hide after duration
      const duration = newNotification.duration || 3000;
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, duration);

      return () => clearTimeout(timer);
    };

    // Add event listener
    window.addEventListener(
      "show-notification",
      handleShowNotification as EventListener
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "show-notification",
        handleShowNotification as EventListener
      );
    };
  }, []);

  // Get background color based on notification type
  const getBgColor = (type: NotificationType) => {
    switch (type) {
      case "success":
        return "bg-green-900/80 border-green-500";
      case "error":
        return "bg-red-900/80 border-red-500";
      case "warning":
        return "bg-yellow-900/80 border-yellow-500";
      case "info":
      default:
        return "bg-blue-900/80 border-blue-500";
    }
  };

  return (
    <AnimatePresence>
      {isVisible && notification && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-20 left-0 right-0 z-50 mx-auto w-11/12 max-w-md"
        >
          <div
            className={`rounded-lg border p-4 shadow-lg ${getBgColor(
              notification.type
            )}`}
          >
            <div className="flex items-start">
              <div className="flex-1">
                <h3 className="text-lg font-bold text-white">
                  {notification.title}
                </h3>
                <div className="text-white/90">
                  {notification.message.split('\n').map((line, index) => (
                    <p key={index}>{line}</p>
                  ))}
                </div>
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="text-white/70 hover:text-white"
              >
                ✕
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
