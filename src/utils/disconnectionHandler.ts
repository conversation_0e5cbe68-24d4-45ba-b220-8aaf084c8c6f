/**
 * Disconnection Handler Utility
 * Manages disconnection events and UI notifications for the Thunee game
 */

import socketService from '@/services/socketService';

export interface DisconnectionData {
  playerId: string;
  playerName: string;
  playerTeam: number;
  timeoutSeconds?: number;
  startTime?: number;
}

export interface ReconnectionData {
  playerId: string;
  playerName: string;
  playerTeam: number;
}

export interface CountdownData {
  playerId: string;
  playerName: string;
  timeRemaining: number;
}

export interface GamePauseData {
  reason: string;
  disconnectedPlayer: {
    name: string;
    team: number;
  };
}

export interface GameResumeData {
  reconnectedPlayer: {
    name: string;
    team: number;
  };
}

export interface ToastNotification {
  type: 'success' | 'warning' | 'error' | 'info';
  message: string;
  duration?: number;
}

export class DisconnectionHandler {
  private disconnectedPlayers: Map<string, DisconnectionData> = new Map();
  private countdownTimers: Map<string, NodeJS.Timeout> = new Map();
  private isGamePaused: boolean = false;

  // Event handlers that can be set by UI components
  public onPlayerDisconnected?: (data: DisconnectionData) => void;
  public onPlayerReconnected?: (data: ReconnectionData) => void;
  public onCountdownUpdate?: (data: CountdownData) => void;
  public onGamePaused?: (data: GamePauseData) => void;
  public onGameResumed?: (data: GameResumeData) => void;
  public onToastNotification?: (data: ToastNotification) => void;

  constructor() {
    this.setupEventListeners();
  }

  // Public method to reinitialize event listeners
  public initialize(): void {
    console.log('DisconnectionHandler: Reinitializing event listeners');
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    console.log('DisconnectionHandler: Setting up event listeners');

    // Listen for player disconnection events
    socketService.on('player_disconnected', (data: DisconnectionData) => {
      console.log('DisconnectionHandler: Player disconnected event received:', data);
      this.handlePlayerDisconnected(data);
    });

    // Listen for player reconnection events
    socketService.on('player_reconnected', (data: ReconnectionData) => {
      console.log('DisconnectionHandler: Player reconnected event received:', data);
      this.handlePlayerReconnected(data);
    });

    // Listen for countdown updates
    socketService.on('disconnection_countdown', (data: CountdownData) => {
      console.log('DisconnectionHandler: Disconnection countdown event received:', data);
      this.handleCountdownUpdate(data);
    });

    // Listen for game pause events
    socketService.on('game_paused', (data: GamePauseData) => {
      console.log('DisconnectionHandler: Game paused event received:', data);
      this.handleGamePaused(data);
    });

    // Listen for game resume events
    socketService.on('game_resumed', (data: GameResumeData) => {
      console.log('DisconnectionHandler: Game resumed event received:', data);
      this.handleGameResumed(data);
    });

    // Listen for toast notifications
    socketService.on('toast_notification', (data: ToastNotification) => {
      console.log('DisconnectionHandler: Toast notification event received:', data);
      this.handleToastNotification(data);
    });

    console.log('DisconnectionHandler: Event listeners setup complete');
  }

  private handlePlayerDisconnected(data: DisconnectionData): void {
    console.log('DisconnectionHandler: Handling player disconnected:', data);
    console.log('DisconnectionHandler: onPlayerDisconnected handler exists:', !!this.onPlayerDisconnected);

    // Store disconnected player info
    this.disconnectedPlayers.set(data.playerId, data);

    // Call the UI handler if set
    if (this.onPlayerDisconnected) {
      console.log('DisconnectionHandler: Calling onPlayerDisconnected handler');
      this.onPlayerDisconnected(data);
    } else {
      console.warn('DisconnectionHandler: No onPlayerDisconnected handler set');
    }

    // Show default toast if no custom handler
    if (!this.onToastNotification) {
      console.warn(`${data.playerName} disconnected. Waiting ${data.timeoutSeconds || 300} seconds to reconnect.`);
    }
  }

  private handlePlayerReconnected(data: ReconnectionData): void {
    // Remove from disconnected players
    this.disconnectedPlayers.delete(data.playerId);

    // Clear any countdown timer
    const timer = this.countdownTimers.get(data.playerId);
    if (timer) {
      clearInterval(timer);
      this.countdownTimers.delete(data.playerId);
    }

    // Call the UI handler if set
    if (this.onPlayerReconnected) {
      this.onPlayerReconnected(data);
    }

    // Show default toast if no custom handler
    if (!this.onToastNotification) {
      console.log(`${data.playerName} reconnected successfully!`);
    }
  }

  private handleCountdownUpdate(data: CountdownData): void {
    // Call the UI handler if set
    if (this.onCountdownUpdate) {
      this.onCountdownUpdate(data);
    }

    // Show countdown in console if no custom handler
    if (!this.onToastNotification) {
      console.log(`${data.playerName} has ${data.timeRemaining} seconds to reconnect`);
    }
  }

  private handleGamePaused(data: GamePauseData): void {
    console.log('DisconnectionHandler: Handling game paused:', data);
    console.log('DisconnectionHandler: onGamePaused handler exists:', !!this.onGamePaused);

    this.isGamePaused = true;

    // Call the UI handler if set
    if (this.onGamePaused) {
      console.log('DisconnectionHandler: Calling onGamePaused handler');
      this.onGamePaused(data);
    } else {
      console.warn('DisconnectionHandler: No onGamePaused handler set');
    }

    // Show default message if no custom handler
    if (!this.onToastNotification) {
      console.warn(`Game paused: ${data.disconnectedPlayer.name} disconnected`);
    }
  }

  private handleGameResumed(data: GameResumeData): void {
    this.isGamePaused = false;

    // Call the UI handler if set
    if (this.onGameResumed) {
      this.onGameResumed(data);
    }

    // Show default message if no custom handler
    if (!this.onToastNotification) {
      console.log(`Game resumed: ${data.reconnectedPlayer.name} reconnected`);
    }
  }

  private handleToastNotification(data: ToastNotification): void {
    // Call the UI handler if set
    if (this.onToastNotification) {
      this.onToastNotification(data);
    } else {
      // Fallback to console
      const logMethod = data.type === 'error' ? console.error : 
                       data.type === 'warning' ? console.warn : 
                       console.log;
      logMethod(`[${data.type.toUpperCase()}] ${data.message}`);
    }
  }

  // Public methods for UI components
  public getDisconnectedPlayers(): DisconnectionData[] {
    return Array.from(this.disconnectedPlayers.values());
  }

  public isPlayerDisconnected(playerId: string): boolean {
    return this.disconnectedPlayers.has(playerId);
  }

  public getGamePauseStatus(): boolean {
    return this.isGamePaused;
  }

  public cleanup(): void {
    // Clear all timers
    this.countdownTimers.forEach(timer => clearInterval(timer));
    this.countdownTimers.clear();
    
    // Clear disconnected players
    this.disconnectedPlayers.clear();
    
    // Reset game pause status
    this.isGamePaused = false;

    // Remove event listeners
    socketService.off('player_disconnected');
    socketService.off('player_reconnected');
    socketService.off('disconnection_countdown');
    socketService.off('game_paused');
    socketService.off('game_resumed');
    socketService.off('toast_notification');
  }

  // Attempt manual rejoin (for testing or manual triggers)
  public async attemptRejoin(gameId?: string): Promise<boolean> {
    try {
      const persistentId = socketService.getPersistentPlayerId();
      const currentGameId = gameId || localStorage.getItem('thunee_current_game_id');
      
      if (!persistentId || !currentGameId) {
        console.error('Missing persistent ID or game ID for rejoin attempt');
        return false;
      }

      const result = await socketService.sendCustomEvent('rejoinRequest', {
        persistentId,
        gameId: currentGameId
      });

      return result.success;
    } catch (error) {
      console.error('Manual rejoin attempt failed:', error);
      return false;
    }
  }
}

// Create a singleton instance
export const disconnectionHandler = new DisconnectionHandler();

// Export types for use in components
export type {
  DisconnectionData,
  ReconnectionData,
  CountdownData,
  GamePauseData,
  GameResumeData,
  ToastNotification
};
