/**
 * Utility functions for card operations
 */

import { useCardImageStore } from '@/store/cardImageStore';

// Cache for card image settings to avoid repeated store access
let cachedSettings: {
  cardFaceBaseUrl: string;
  cardBackImageUrl: string;
  customCardFaceMappings?: Record<string, string>;
} | null = null;

/**
 * Get current card image settings from store
 */
function getCardImageSettings() {
  // Use cached settings if available, otherwise get from store
  if (!cachedSettings) {
    const store = useCardImageStore.getState();
    cachedSettings = store.settings;
  }
  return cachedSettings;
}

/**
 * Clear cached settings (call this when settings are updated)
 */
export function clearCardImageCache(): void {
  cachedSettings = null;
}

/**
 * Generates the path to the SVG file for a card
 * @param value The card value (6, 9, 10, J, Q, K, A)
 * @param suit The card suit (hearts, diamonds, clubs, spades)
 * @returns The path to the SVG file
 */
export function getCardImagePath(value: string, suit: string): string {
  const settings = getCardImageSettings();

  // Convert suit to first letter uppercase
  const suitFirstLetter = suit.charAt(0).toUpperCase();

  // Map card values to their SVG file names
  let cardValue = value;
  if (value === '10') {
    cardValue = 'T'; // 10 is represented as T in the SVG filenames
  }

  // Create the card key for custom mappings
  const cardKey = `${cardValue}${suitFirstLetter}`;

  // Check if there's a custom mapping for this specific card
  if (settings.customCardFaceMappings && settings.customCardFaceMappings[cardKey]) {
    return settings.customCardFaceMappings[cardKey];
  }

  // Return the default path using the base URL
  return `${settings.cardFaceBaseUrl}/${cardKey}.svg`;
}

/**
 * Gets the path to the card back image
 * @returns The path to the card back SVG file
 */
export function getCardBackImagePath(): string {
  const settings = getCardImageSettings();
  return settings.cardBackImageUrl;
}
