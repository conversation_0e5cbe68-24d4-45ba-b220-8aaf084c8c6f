/**
 * Robust clipboard utility that works across different browsers and contexts
 */

export interface ClipboardOptions {
  fallbackMessage?: string;
  showAlert?: boolean;
}

/**
 * Copy text to clipboard with fallback support
 * @param text - Text to copy to clipboard
 * @param options - Configuration options
 * @returns Promise that resolves when copy is successful
 */
export async function copyToClipboard(
  text: string, 
  options: ClipboardOptions = {}
): Promise<void> {
  const { fallbackMessage, showAlert = true } = options;
  
  try {
    // Try modern clipboard API first (requires HTTPS or localhost)
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return;
    }
    
    // Fallback for older browsers or non-secure contexts
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // Make the textarea invisible but accessible
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.style.pointerEvents = 'none';
    textArea.setAttribute('readonly', '');
    
    document.body.appendChild(textArea);
    
    // Focus and select the text
    textArea.focus();
    textArea.select();
    textArea.setSelectionRange(0, text.length);
    
    // Try to copy using the deprecated execCommand
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    if (!successful) {
      throw new Error('Copy command failed');
    }
    
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    
    if (showAlert) {
      const message = fallbackMessage || `Failed to copy to clipboard. Please copy manually: ${text}`;
      alert(message);
    }
    
    throw error;
  }
}

/**
 * Share text using Web Share API with clipboard fallback
 * @param shareData - Data to share
 * @param fallbackText - Text to copy if sharing fails
 * @param options - Configuration options
 */
export async function shareWithFallback(
  shareData: { title?: string; text: string; url?: string },
  fallbackText?: string,
  options: ClipboardOptions = {}
): Promise<void> {
  try {
    // Try Web Share API first (mainly mobile)
    if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
      await navigator.share(shareData);
      return;
    }
    
    // Fallback to clipboard
    const textToShare = fallbackText || shareData.text;
    await copyToClipboard(textToShare, options);
    
  } catch (error) {
    console.error('Failed to share:', error);
    
    // Final fallback - try to copy the text
    const textToShare = fallbackText || shareData.text;
    await copyToClipboard(textToShare, {
      ...options,
      fallbackMessage: `Failed to share. Please copy manually: ${textToShare}`
    });
  }
}

/**
 * Check if clipboard functionality is available
 */
export function isClipboardSupported(): boolean {
  return !!(
    (navigator.clipboard && window.isSecureContext) ||
    document.queryCommandSupported?.('copy')
  );
}

/**
 * Check if Web Share API is available
 */
export function isWebShareSupported(): boolean {
  return !!(navigator.share && navigator.canShare);
}
