/**
 * Player position utilities for Thunee
 *
 * In Thunee, we use fixed positions 1-4 where:
 * - Position 1: Top left player
 * - Position 2: Top right player
 * - Position 3: Bottom left player (dealer)
 * - Position 4: Bottom right player
 *
 * Team 1 consists of positions 1 and 3
 * Team 2 consists of positions 2 and 4
 *
 * Play moves counter-clockwise, so the player to the right of position X
 * is position (X % 4) + 1
 */

export type PlayerPosition = 1 | 2 | 3 | 4;

/**
 * Get the position to the right of the given position (counter-clockwise)
 */
export function getPositionToRight(position: PlayerPosition): PlayerPosition {
  return ((position % 4) + 1) as PlayerPosition;
}

/**
 * Get the position to the left of the given position (clockwise)
 */
export function getPositionToLeft(position: PlayerPosition): PlayerPosition {
  return (position === 1 ? 4 : position - 1) as PlayerPosition;
}

/**
 * Get the position opposite to the given position
 */
export function getOppositePosition(position: PlayerPosition): PlayerPosition {
  return ((position + 1) % 4 + 1) as PlayerPosition;
}

/**
 * Get the team number (1 or 2) for a given position
 */
export function getTeamForPosition(position: PlayerPosition): 1 | 2 {
  // Team 1: positions 1 and 3
  // Team 2: positions 2 and 4
  return (position % 2 === 1) ? 1 : 2;
}

/**
 * Get the partner position for a given position
 */
export function getPartnerPosition(position: PlayerPosition): PlayerPosition {
  // Partners are 2 positions apart
  return ((position + 1) % 4 + 1) as PlayerPosition;
}

/**
 * Get the dealer position (always position 3)
 */
export function getDealerPosition(): PlayerPosition {
  return 3;
}

/**
 * Get the initial trumper position (always position 2, to the right of dealer at position 3)
 *
 * In Thunee, the player to the right of the dealer selects trump.
 * Since the dealer is always at position 3 (bottom left), the trumper
 * is always at position 2 (top right).
 */
export function getInitialTrumperPosition(): PlayerPosition {
  return 2;
}
