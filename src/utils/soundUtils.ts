// Utility functions for playing sounds in the game

// Play a sound effect
export const playSound = (soundUrl: string, volume: number = 1.0): void => {
  try {
    const audio = new Audio(soundUrl);
    audio.volume = volume;

    // Handle case where sound file doesn't exist
    audio.addEventListener('error', (e) => {
      console.warn(`Sound file not found: ${soundUrl}. Please add actual sound files to the public/sounds directory.`);
    });

    audio.play().catch(error => {
      console.warn('Error playing sound:', error, `- Sound file: ${soundUrl}`);
    });
  } catch (error) {
    console.warn('Error creating audio element:', error);
  }
};

// Sound URLs
export const SOUNDS = {
  JORDHI_CALL: '/sounds/jordhi-call.mp3',
  CARD_PLAYED: '/sounds/card-played.mp3',
  GAME_WIN: '/sounds/game-win.mp3',
  GAME_LOSE: '/sounds/game-lose.mp3',
  SUCCESS: '/sounds/game-win.mp3',  // Reuse game-win sound for success
  ERROR: '/sounds/game-lose.mp3',   // Reuse game-lose sound for error

  // Card animation sounds
  CARD_SHUFFLE: '/sounds/card-shuffle.mp3',
  CARD_CUT: '/sounds/card-cut.mp3',
  CARD_DEAL: '/sounds/card-deal.mp3',
  CARD_PLAY: '/sounds/card-play.mp3',
  CARD_REMOVE: '/sounds/card-remove.mp3',
};
