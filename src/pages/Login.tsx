"use client";
import { useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { ArrowR<PERSON>, AlertCircle, User, Lock, Eye, EyeOff } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import BurgerMenu from "@/components/BurgerMenu";
import { useAuthStore } from "@/store/authStore";
import GRCard from "@/assets/GRCard.png"; // Import the GRCard from assets

export default function Login() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { login, isLoading } = useAuthStore();
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username) {
      setErrorMessage("Please enter your username");
      return;
    }

    if (!password) {
      setErrorMessage("Please enter your password");
      return;
    }

    setErrorMessage(null);

    try {
      await login(username, password);
      navigate("/"); // Navigate to the lobby page after successful login
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : 'Failed to login');
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <div className="relative w-full p-3 sm:p-4 flex justify-center items-center">
        <h1 className="text-2xl sm:text-3xl font-bold text-[#E1C760]">Thunee</h1>
        <BurgerMenu />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 py-6 sm:p-4">
        {/* Mobile-optimized container */}
        <div className="w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl
                        landscape:max-w-[85%] landscape:md:max-w-2xl">

          {/* Card and Title - centered layout like the image */}
          <div className="flex flex-col items-center text-center mb-6 sm:mb-6 landscape:flex-row landscape:items-center landscape:justify-between landscape:text-left">
            {/* Card Image */}
            <div className="flex justify-center landscape:justify-start mb-6 landscape:mb-0">
              <img
  src={GRCard}
  alt="Thunee Card"
className="w-16 sm:w-24 md:w-20 lg:w-32 xl:w-36 h-auto"

/>
            </div>

            <h2 className="text-2xl sm:text-2xl font-bold text-[#E1C760] landscape:ml-4 landscape:text-right">
              Sign In
            </h2>
          </div>

          {errorMessage && (
            <Alert variant="destructive" className="mb-4 bg-red-900/50 border border-red-500">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">{errorMessage}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleLogin} className="space-y-5">
            <div className="space-y-2">
              <Label htmlFor="username" className="text-[#E1C760] text-sm sm:text-base">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 w-4 sm:h-5 sm:w-5 z-10" />
                <Input
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder=""
                  className="pl-10 h-12 sm:h-10 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-base sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50 focus-visible:outline-none"
                  disabled={isLoading}
                  autoComplete="username"
                  autoCapitalize="none"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-[#E1C760] text-sm sm:text-base">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] h-4 w-4 sm:h-5 sm:w-5 z-10" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder=""
                  className="pl-10 pr-12 h-12 sm:h-10 bg-transparent border border-[#E1C760] rounded-lg text-[#E1C760] text-base sm:text-base focus-visible:border-[#E1C760] focus-visible:ring-[#E1C760]/50 focus-visible:outline-none"
                  disabled={isLoading}
                  autoComplete="current-password"
                />
                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#E1C760] hover:text-[#E1C760]/80 p-1 min-h-[44px] min-w-[44px] flex items-center justify-center z-10"
                  disabled={isLoading}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 sm:h-5 sm:w-5" />
                  ) : (
                    <Eye className="h-4 w-4 sm:h-5 sm:w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Mobile-optimized button */}
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full h-12 sm:h-10 bg-[#E1C760] text-black hover:bg-[#E1C760]/90 flex items-center justify-center gap-2 text-base sm:text-base mt-6 font-medium"
            >
              {isLoading ? "Signing In..." : "Sign In"}
              {!isLoading && <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />}
            </Button>

            <div className="text-center mt-4 sm:mt-4">
              <p className="text-gray-400 text-sm sm:text-sm">
                Don't have an account?{" "}
                <Link to="/register" className="text-[#E1C760] hover:underline font-medium">
                  Sign Up
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}