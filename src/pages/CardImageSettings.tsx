"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Image, Upload, RotateCcw, Plus, Minus, Loader2, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useCardImageStore } from "@/store/cardImageStore";
import { clearCardImageCache } from "@/utils/cardUtils";
import { toast } from "sonner";

export default function CardImageSettings() {
  const navigate = useNavigate();
  const {
    settings,
    isLoading,
    error,
    updateSettings,
    resetToDefaults,
    setCardFaceBaseUrl,
    setCardBackImageUrl,
    setCustomCardFaceMapping,
    removeCustomCardFaceMapping,
    clearCustomCardFaceMappings,
    loadSettingsFromAPI,
    saveSettingsToAPI,
    resetSettingsToAPI
  } = useCardImageStore();

  // Local state for editing
  const [localSettings, setLocalSettings] = useState(settings);
  const [newCardKey, setNewCardKey] = useState("");
  const [newCardUrl, setNewCardUrl] = useState("");

  // Load settings from API on component mount
  useEffect(() => {
    loadSettingsFromAPI();
  }, [loadSettingsFromAPI]);

  // Update local settings when store settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleSave = async () => {
    try {
      await saveSettingsToAPI(localSettings);
      updateSettings(localSettings);
      clearCardImageCache(); // Clear cache so components use new settings
      toast.success("Card image settings saved successfully!");
    } catch (error) {
      toast.error("Failed to save card image settings");
    }
  };

  const handleReset = async () => {
    try {
      await resetSettingsToAPI();
      clearCardImageCache(); // Clear cache so components use new settings
      toast.success("Card image settings reset to defaults!");
    } catch (error) {
      toast.error("Failed to reset card image settings");
    }
  };

  const handleAddCustomMapping = () => {
    if (newCardKey && newCardUrl) {
      const newMappings = {
        ...localSettings.customCardFaceMappings,
        [newCardKey]: newCardUrl
      };
      setLocalSettings(prev => ({
        ...prev,
        customCardFaceMappings: newMappings
      }));
      setNewCardKey("");
      setNewCardUrl("");
    } else {
      toast.error("Please enter both card key and URL");
    }
  };

  const handleRemoveCustomMapping = (cardKey: string) => {
    const newMappings = { ...localSettings.customCardFaceMappings };
    delete newMappings[cardKey];
    setLocalSettings(prev => ({
      ...prev,
      customCardFaceMappings: Object.keys(newMappings).length > 0 ? newMappings : undefined
    }));
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center">
        <Card className="bg-red-900/20 border-red-500/30 max-w-md">
          <CardContent className="p-6 text-center">
            <p className="text-red-400 mb-4">Error loading card image settings: {error}</p>
            <Button onClick={() => loadSettingsFromAPI()} className="bg-red-600 hover:bg-red-700">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/")}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-[#E1C760]">Card Image Settings</h1>
              <p className="text-gray-400 mt-1">Configure card face and back images</p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={handleReset}
              variant="outline"
              className="border-red-500/30 text-red-400 hover:bg-red-500/10"
              disabled={isLoading}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>
            <Button
              onClick={handleSave}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Image className="h-4 w-4 mr-2" />
              )}
              Save Settings
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          {/* Card Face Base URL */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760] flex items-center gap-2">
                <Image className="h-5 w-5" />
                Card Face Base URL
              </CardTitle>
              <CardDescription className="text-gray-400">
                Base URL for card face images (without trailing slash)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Base URL</Label>
                <Input
                  type="text"
                  value={localSettings.cardFaceBaseUrl}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, cardFaceBaseUrl: e.target.value }))}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="/CardFace"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Card faces will be loaded from: {localSettings.cardFaceBaseUrl}/[CardValue][Suit].svg
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Card Back Image URL */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760] flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Card Back Image URL
              </CardTitle>
              <CardDescription className="text-gray-400">
                URL for the card back image
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Image URL</Label>
                <Input
                  type="text"
                  value={localSettings.cardBackImageUrl}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, cardBackImageUrl: e.target.value }))}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="/CardFace/card-back.svg"
                />
              </div>
            </CardContent>
          </Card>

          {/* Custom Card Face Mappings */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Custom Card Face Mappings</CardTitle>
              <CardDescription className="text-gray-400">
                Override specific card images with custom URLs
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add new mapping */}
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label className="text-white mb-2 block">Card Key (e.g., 6H, JS, AH)</Label>
                  <Input
                    type="text"
                    value={newCardKey}
                    onChange={(e) => setNewCardKey(e.target.value.toUpperCase())}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    placeholder="6H"
                  />
                </div>
                <div className="flex-1">
                  <Label className="text-white mb-2 block">Image URL</Label>
                  <Input
                    type="text"
                    value={newCardUrl}
                    onChange={(e) => setNewCardUrl(e.target.value)}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    placeholder="/custom/6H.svg"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleAddCustomMapping}
                    size="icon"
                    className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <Separator className="bg-[#E1C760]/20" />

              {/* Existing mappings */}
              <div className="space-y-2">
                <Label className="text-white">Current Custom Mappings</Label>
                {localSettings.customCardFaceMappings && Object.keys(localSettings.customCardFaceMappings).length > 0 ? (
                  <div className="space-y-2">
                    {Object.entries(localSettings.customCardFaceMappings).map(([cardKey, url]) => (
                      <div key={cardKey} className="flex items-center gap-2 bg-[#E1C760]/10 border border-[#E1C760]/30 rounded-lg px-3 py-2">
                        <span className="text-white font-mono text-sm">{cardKey}</span>
                        <span className="text-gray-400 text-sm">→</span>
                        <span className="text-gray-300 text-sm flex-1">{url}</span>
                        <Button
                          onClick={() => handleRemoveCustomMapping(cardKey)}
                          size="icon"
                          variant="ghost"
                          className="h-6 w-6 text-red-400 hover:bg-red-500/20"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No custom mappings configured</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
