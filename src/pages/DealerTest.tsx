import { useState } from "react";
import DealerDetermination from "@/components/DealerDetermination";
import { Button } from "@/components/ui/button";

// Sample players for testing
const samplePlayers = [
  {
    id: "1",
    name: "Player 1",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Player1",
    team: 1
  },
  {
    id: "2",
    name: "Player 2",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Player2",
    team: 1
  },
  {
    id: "3",
    name: "Player 3",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Player3",
    team: 2
  },
  {
    id: "4",
    name: "Player 4",
    avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=Player4",
    team: 2
  }
];

export default function DealerTest() {
  const [showDealerDetermination, setShowDealerDetermination] = useState(false);
  const [selectedDealer, setSelectedDealer] = useState<string | null>(null);

  const handleDealerSelected = (dealerId: string) => {
    setSelectedDealer(dealerId);
    setShowDealerDetermination(false);
  };

  return (
    <div className="h-screen bg-black flex flex-col items-center justify-center p-4">
      {!showDealerDetermination ? (
        <div className="text-center">
          <h1 className="text-2xl font-bold text-[#E1C760] mb-6">Dealer Determination Test</h1>
          
          {selectedDealer ? (
            <div className="mb-6">
              <p className="text-white mb-2">Selected Dealer:</p>
              <p className="text-[#E1C760] font-bold">
                {samplePlayers.find(p => p.id === selectedDealer)?.name || "Unknown"}
              </p>
            </div>
          ) : null}
          
          <Button 
            className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
            onClick={() => setShowDealerDetermination(true)}
          >
            Start Dealer Determination
          </Button>
        </div>
      ) : (
        <DealerDetermination 
          players={samplePlayers} 
          onDealerSelected={handleDealerSelected} 
        />
      )}
    </div>
  );
}
