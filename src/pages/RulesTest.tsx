import { useState } from "react";
import GameRules from "@/components/GameRules";
import { But<PERSON> } from "@/components/ui/button";

export default function RulesTest() {
  const [showRules, setShowRules] = useState(false);

  return (
    <div className="h-screen bg-black flex items-center justify-center">
      <Button 
        className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
        onClick={() => setShowRules(true)}
      >
        Show Game Rules
      </Button>
      
      {showRules && <GameRules onClose={() => setShowRules(false)} />}
    </div>
  );
}
