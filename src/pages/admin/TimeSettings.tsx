"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLef<PERSON>, Clock, Timer, Settings, RotateCcw, Plus, Minus, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useTimeSettingsStore } from "@/store/timeSettingsStore";
import { useAuthStore } from "@/store/authStore";
import { toast } from "sonner";

export default function AdminTimeSettings() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const {
    settings,
    isLoading,
    error,
    updateSettings,
    resetToDefaults,
    updatePlayTimeframeOptions,
    updateThuneeCallingDuration,
    loadSettingsFromAPI,
    saveSettingsToAPI,
    resetSettingsToAPI
  } = useTimeSettingsStore();

  // Local state for editing
  const [localSettings, setLocalSettings] = useState(settings);
  const [newTimeframeOption, setNewTimeframeOption] = useState("");

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load settings from API on component mount
  useEffect(() => {
    loadSettingsFromAPI();
  }, [loadSettingsFromAPI]);

  // Update local settings when store settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  if (!isAuthenticated) {
    return null;
  }

  const handleSave = async () => {
    try {
      await saveSettingsToAPI(localSettings);
      toast.success("Time settings saved successfully!");
    } catch (error) {
      toast.error("Failed to save settings. Please try again.");
    }
  };

  const handleReset = async () => {
    try {
      await resetSettingsToAPI();
      toast.success("Settings reset to defaults!");
    } catch (error) {
      toast.error("Failed to reset settings. Please try again.");
    }
  };

  const handleAddTimeframeOption = () => {
    const value = parseInt(newTimeframeOption);
    if (value && value > 0 && value <= 3600 && !localSettings.playTimeframeOptions.includes(value)) {
      const newOptions = [...localSettings.playTimeframeOptions, value].sort((a, b) => a - b);
      setLocalSettings(prev => ({
        ...prev,
        playTimeframeOptions: newOptions
      }));
      setNewTimeframeOption("");
    } else if (value > 3600) {
      toast.error("Timeframe cannot exceed 3600 seconds (1 hour)");
    } else if (value <= 0) {
      toast.error("Timeframe must be a positive number");
    } else if (localSettings.playTimeframeOptions.includes(value)) {
      toast.error("This timeframe option already exists");
    }
  };

  const handleRemoveTimeframeOption = (option: number) => {
    if (localSettings.playTimeframeOptions.length > 1) {
      const newOptions = localSettings.playTimeframeOptions.filter(opt => opt !== option);
      setLocalSettings(prev => ({
        ...prev,
        playTimeframeOptions: newOptions,
        defaultPlayTimeframe: newOptions.includes(prev.defaultPlayTimeframe)
          ? prev.defaultPlayTimeframe
          : newOptions[0]
      }));
    }
  };

  const formatTime = (seconds: number) => {
    if (seconds >= 60) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    }
    return `${seconds}s`;
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Loading Overlay */}
        {isLoading && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-black border border-[#E1C760]/30 rounded-lg p-6 flex items-center gap-3">
              <Loader2 className="h-6 w-6 text-[#E1C760] animate-spin" />
              <span className="text-[#E1C760]">Loading settings...</span>
            </div>
          </div>
        )}
        
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/admin")}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Clock className="h-6 w-6 text-[#E1C760]" />
            <h1 className="text-2xl font-bold text-[#E1C760]">Admin - Time Settings</h1>
          </div>
        </div>

        <div className="space-y-6">
          {/* Play Timeframe Settings */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760] flex items-center gap-2">
                <Timer className="h-5 w-5" />
                Play Timeframe Options
              </CardTitle>
              <CardDescription className="text-gray-400">
                Configure the time options available for players to vote on during gameplay
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Available Options (seconds)</Label>
                <div className="flex flex-wrap gap-2 mb-4">
                  {localSettings.playTimeframeOptions.map((option) => (
                    <div key={option} className="flex items-center gap-1 bg-[#E1C760]/10 border border-[#E1C760]/30 rounded-lg px-3 py-1">
                      <span className="text-[#E1C760]">{formatTime(option)}</span>
                      {localSettings.playTimeframeOptions.length > 1 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 text-red-400 hover:text-red-300 hover:bg-red-400/10"
                          onClick={() => handleRemoveTimeframeOption(option)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Add new option (seconds)"
                    value={newTimeframeOption}
                    onChange={(e) => setNewTimeframeOption(e.target.value)}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="1"
                    max="3600"
                  />
                  <Button
                    onClick={handleAddTimeframeOption}
                    className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                    disabled={!newTimeframeOption || parseInt(newTimeframeOption) <= 0}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <Label className="text-white mb-2 block">Default Timeframe</Label>
                <select
                  value={localSettings.defaultPlayTimeframe}
                  onChange={(e) => setLocalSettings(prev => ({ ...prev, defaultPlayTimeframe: parseInt(e.target.value) }))}
                  className="w-full bg-black border border-[#E1C760]/30 rounded-lg px-3 py-2 text-white"
                >
                  {localSettings.playTimeframeOptions.map((option) => (
                    <option key={option} value={option}>
                      {formatTime(option)}
                    </option>
                  ))}
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-end">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={isLoading}
              className="border-red-500 bg-red-500/10 text-red-600 hover:bg-red-500/20 hover:text-red-700 dark:border-red-500/50 dark:bg-red-500/5 dark:text-red-400 dark:hover:bg-red-500/15 dark:hover:text-red-300 disabled:opacity-50"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4 mr-2" />
              )}
              Reset to Defaults
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80 disabled:opacity-50"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
