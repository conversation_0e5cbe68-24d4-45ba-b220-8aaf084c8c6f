"use client";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, Trophy, Users, Calendar, DollarSign, Settings, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: string;
  maxTeams: number;
  currentTeams: number;
  entryFee: number;
  prizeFirst: number;
  prizeSecond: number;
  prizeThird: number;
  totalPrizePool: number;
  rules: string;
  isPublic: boolean;
  allowSpectators: boolean;
  maxGamesPerTeam: number;
  createdAt: string;
  updatedAt: string;
}

export default function CompetitionDetails() {
  const navigate = useNavigate();
  const { competitionId } = useParams<{ competitionId: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load competition details
  useEffect(() => {
    if (competitionId) {
      loadCompetition();
    }
  }, [competitionId]);

  if (!isAuthenticated) {
    return null;
  }

  const loadCompetition = async () => {
    try {
      setIsLoading(true);
      const competitionData = await apiService.getCompetitionById(competitionId!);
      setCompetition(competitionData);
    } catch (error) {
      console.error("Error loading competition:", error);
      toast.error("Failed to load competition details");
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'upcoming':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString() + " " + new Date(dateString).toLocaleTimeString();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-[#E1C760] animate-spin" />
      </div>
    );
  }

  if (!competition) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-400 mb-2">Competition Not Found</h2>
          <Button onClick={() => navigate("/admin/competitions")} className="bg-[#E1C760] text-black">
            Back to Competitions
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin/competitions")}
              className="text-[#E1C760] hover:bg-[#E1C760]/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-[#E1C760]" />
              <h1 className="text-2xl font-bold text-[#E1C760]">Competition Details</h1>
            </div>
          </div>
          <Badge className={getStatusColor(competition.status)}>
            {competition.status}
          </Badge>
        </div>

        {/* Competition Info */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Main Details */}
          <div className="lg:col-span-2">
            <Card className="bg-black/50 border-[#E1C760]/30">
              <CardHeader>
                <CardTitle className="text-[#E1C760] text-2xl">{competition.name}</CardTitle>
                <CardDescription className="text-gray-400 text-lg">
                  {competition.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Date Range */}
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-[#E1C760]" />
                  <span className="text-white">
                    {formatDate(competition.startDate)} - {formatDate(competition.endDate)}
                  </span>
                </div>

                {/* Teams Info */}
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-[#E1C760]" />
                  <span className="text-white">
                    {competition.currentTeams} / {competition.maxTeams} teams registered
                  </span>
                </div>

                {/* Entry Fee */}
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-[#E1C760]" />
                  <span className="text-white">Entry Fee: R{competition.entryFee}</span>
                </div>

                {/* Settings */}
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-[#E1C760]" />
                  <span className="text-white">
                    Max {competition.maxGamesPerTeam} games per team
                    {competition.isPublic && " • Public"}
                    {competition.allowSpectators && " • Spectators allowed"}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Prize Pool */}
          <div>
            <Card className="bg-black/50 border-[#E1C760]/30">
              <CardHeader>
                <CardTitle className="text-[#E1C760]">Prize Pool</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-[#E1C760]">R{competition.totalPrizePool}</div>
                  <div className="text-gray-400">Total Prize Pool</div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-400">1st Place:</span>
                    <span className="text-white font-semibold">R{competition.prizeFirst}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">2nd Place:</span>
                    <span className="text-white font-semibold">R{competition.prizeSecond}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">3rd Place:</span>
                    <span className="text-white font-semibold">R{competition.prizeThird}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Rules */}
        {competition.rules && (
          <Card className="bg-black/50 border-[#E1C760]/30 mb-8">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Rules & Regulations</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 whitespace-pre-wrap">{competition.rules}</p>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4">
          <Button
            onClick={() => navigate(`/admin/competitions/${competition.id}/teams`)}
            className="bg-blue-600 text-white hover:bg-blue-700 hover:text-white dark:bg-blue-600 dark:text-white dark:hover:bg-blue-700"
          >
            <Users className="h-4 w-4 mr-2" />
            View Teams
          </Button>
          <Button
            onClick={() => navigate(`/admin/competitions/${competition.id}/edit`)}
            className="bg-green-600 text-white hover:bg-green-700 hover:text-white dark:bg-green-600 dark:text-white dark:hover:bg-green-700"
          >
            <Settings className="h-4 w-4 mr-2" />
            Edit Competition
          </Button>
          <Button
            onClick={() => navigate(`/admin/competitions/${competition.id}/email`)}
            className="bg-purple-600 text-white hover:bg-purple-700 hover:text-white dark:bg-purple-600 dark:text-white dark:hover:bg-purple-700"
          >
            <DollarSign className="h-4 w-4 mr-2" />
            Send Email
          </Button>

          <Button
            onClick={() => navigate(`/admin/competitions/${competition.id}/phases`)}
            className="bg-orange-600 text-white hover:bg-orange-700 hover:text-white dark:bg-orange-600 dark:text-white dark:hover:bg-orange-700"
          >
            <Trophy className="h-4 w-4 mr-2" />
            Phase Management
          </Button>
        </div>

        {/* Metadata */}
        <div className="mt-8 text-sm text-gray-500">
          <p>Created: {formatDate(competition.createdAt)}</p>
          <p>Last Updated: {formatDate(competition.updatedAt)}</p>
        </div>
      </div>
    </div>
  );
}
