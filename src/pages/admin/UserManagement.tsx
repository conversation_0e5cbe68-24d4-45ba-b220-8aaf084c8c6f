"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Users, Edit, Trash2, Key, Search, Loader2, Shield, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface UserData {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  isActive: boolean;
  isAdmin: boolean;
  lastLoginAt: string | null;
  createdAt: string;
}

export default function UserManagement() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [users, setUsers] = useState<UserData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<UserData[]>([]);

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load users
  useEffect(() => {
    loadUsers();
  }, []);

  // Filter users based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(u => 
        u.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        u.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [users, searchTerm]);

  if (!isAuthenticated) {
    return null;
  }

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      const usersData = await apiService.getAllUsers();
      // Convert the API response to match our UserData interface
      const convertedUsers: UserData[] = usersData.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        isVerified: user.isVerified,
        isActive: user.isActive,
        isAdmin: user.isAdmin,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt
      }));
      setUsers(convertedUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string, username: string) => {
    if (userId === user?.id) {
      toast.error("You cannot delete your own account");
      return;
    }

    if (window.confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
      try {
        await apiService.deleteUser(userId);
        setUsers(prev => prev.filter(u => u.id !== userId));
        toast.success("User deleted successfully");
      } catch (error) {
        console.error("Error deleting user:", error);
        toast.error("Failed to delete user");
      }
    }
  };

  const handleChangePassword = async (userId: string, username: string) => {
    const newPassword = window.prompt(`Enter new password for user "${username}":`);
    if (newPassword && newPassword.length >= 6) {
      try {
        await apiService.changeUserPassword(userId, newPassword);
        toast.success("Password changed successfully");
      } catch (error) {
        console.error("Error changing password:", error);
        toast.error("Failed to change password");
      }
    } else if (newPassword !== null) {
      toast.error("Password must be at least 6 characters long");
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleDateString() + " " + new Date(dateString).toLocaleTimeString();
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin")}
              className="text-[#E1C760] hover:bg-[#E1C760]/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <Users className="h-6 w-6 text-[#E1C760]" />
              <h1 className="text-2xl font-bold text-[#E1C760]">User Management</h1>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search users by username or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-black/50 border-[#E1C760]/30 text-white"
            />
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 text-[#E1C760] animate-spin" />
          </div>
        ) : (
          <>
            {/* Users Table */}
            <Card className="bg-black/50 border-[#E1C760]/30">
              <CardHeader>
                <CardTitle className="text-[#E1C760]">Registered Users ({filteredUsers.length})</CardTitle>
                <CardDescription className="text-gray-400">
                  Manage user accounts, update details, and change passwords
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredUsers.map((userData) => (
                    <div key={userData.id} className="flex items-center justify-between p-4 bg-black/30 rounded-lg border border-[#E1C760]/20">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          {userData.isAdmin ? (
                            <Shield className="h-5 w-5 text-yellow-400" />
                          ) : (
                            <User className="h-5 w-5 text-gray-400" />
                          )}
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-semibold text-white">{userData.username}</span>
                              {userData.isAdmin && (
                                <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                                  Admin
                                </Badge>
                              )}
                              {!userData.isVerified && (
                                <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                                  Unverified
                                </Badge>
                              )}
                              {!userData.isActive && (
                                <Badge className="bg-gray-500/20 text-gray-400 border-gray-500/30">
                                  Inactive
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-400">{userData.email}</p>
                            <p className="text-xs text-gray-500">
                              Last login: {formatDate(userData.lastLoginAt)} | 
                              Joined: {formatDate(userData.createdAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/admin/users/${userData.id}/edit`)}
                          className="border-green-500 bg-green-500/10 text-green-600 hover:bg-green-500/20 hover:text-green-700 dark:border-green-500/50 dark:bg-green-500/5 dark:text-green-400 dark:hover:bg-green-500/15 dark:hover:text-green-300"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleChangePassword(userData.id, userData.username)}
                          className="border-blue-500 bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 hover:text-blue-700 dark:border-blue-500/50 dark:bg-blue-500/5 dark:text-blue-400 dark:hover:bg-blue-500/15 dark:hover:text-blue-300"
                        >
                          <Key className="h-4 w-4 mr-1" />
                          Password
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteUser(userData.id, userData.username)}
                          disabled={userData.id === user?.id}
                          className="border-red-500 bg-red-500/10 text-red-600 hover:bg-red-500/20 hover:text-red-700 dark:border-red-500/50 dark:bg-red-500/5 dark:text-red-400 dark:hover:bg-red-500/15 dark:hover:text-red-300 disabled:opacity-50"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Empty State */}
                {filteredUsers.length === 0 && !isLoading && (
                  <div className="text-center py-8">
                    <Users className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-400 mb-2">
                      {searchTerm ? "No users found" : "No users registered"}
                    </h3>
                    <p className="text-gray-500">
                      {searchTerm ? "Try adjusting your search terms." : "Users will appear here once they register."}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}
