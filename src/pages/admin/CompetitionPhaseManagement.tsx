"use client";
import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Trophy, Users, Play, Settings, Clock, CheckCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";
import CompetitionBracket from "@/components/CompetitionBracket";

interface Competition {
  id: string;
  name: string;
  phase: string;
  phaseEndDate?: string;
  maxGamesPerPhase?: number;
  status: string;
}

interface CompetitionTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  points: number;
  bonusPoints: number;
  gamesPlayed: number;
  phase: string;
  isEliminated: boolean;
  advancedToNextPhase: boolean;
}

export default function CompetitionPhaseManagement() {
  const { competitionId } = useParams<{ competitionId: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [teams, setTeams] = useState<CompetitionTeam[]>([]);
  const [eligibleTeams, setEligibleTeams] = useState<CompetitionTeam[]>([]);
  const [brackets, setBrackets] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
      return;
    }
    
    if (competitionId) {
      loadCompetitionData();
    }
  }, [competitionId, isAuthenticated, navigate]);

  const loadCompetitionData = async () => {
    try {
      setIsLoading(true);
      
      // Load competition details
      const competitionData = await apiService.getCompetitionById(competitionId!);
      setCompetition(competitionData);
      
      // Load current phase teams
      await loadPhaseTeams(competitionData.phase);
      
      // Load eligible teams for next phase
      await loadEligibleTeams();
      
      // Load brackets if in knockout phase
      if (isKnockoutPhase(competitionData.phase)) {
        await loadBrackets();
      }
      
    } catch (error) {
      console.error("Error loading competition data:", error);
      toast.error("Failed to load competition data");
    } finally {
      setIsLoading(false);
    }
  };

  const loadPhaseTeams = async (phase: string) => {
    try {
      const teams = await apiService.getPhaseTeams(competitionId!, phase);
      setTeams(teams);
    } catch (error) {
      console.error("Error loading phase teams:", error);
    }
  };

  const loadEligibleTeams = async () => {
    try {
      const teams = await apiService.getEligibleTeams(competitionId!);
      setEligibleTeams(teams);
    } catch (error) {
      console.error("Error loading eligible teams:", error);
    }
  };

  const loadBrackets = async () => {
    try {
      const brackets = await apiService.getPhaseBrackets(competitionId!);
      setBrackets(brackets);
    } catch (error) {
      console.error("Error loading brackets:", error);
    }
  };

  const handleAdvancePhase = async () => {
    if (!competition) return;

    try {
      setIsProcessing(true);

      const nextPhase = getNextPhase(competition.phase);

      await apiService.advanceCompetitionPhase(competitionId!, nextPhase);
      toast.success(`Competition advanced to ${nextPhase} phase. Email notifications sent to all teams!`);
      await loadCompetitionData();
    } catch (error) {
      console.error("Error advancing phase:", error);
      toast.error("Failed to advance phase");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProcessPhaseEnd = async () => {
    try {
      setIsProcessing(true);

      await apiService.processPhaseEnd(competitionId!);
      toast.success("Phase end processed successfully. Email notifications sent to all teams!");
      await loadCompetitionData();
    } catch (error) {
      console.error("Error processing phase end:", error);
      toast.error("Failed to process phase end");
    } finally {
      setIsProcessing(false);
    }
  };

  const getNextPhase = (currentPhase: string): string => {
    const phaseMap: Record<string, string> = {
      "Leaderboard": "Top32",
      "Top32": "Top16",
      "Top16": "Top8",
      "Top8": "Top4",
      "Top4": "Final",
      "Final": "Completed"
    };
    return phaseMap[currentPhase] || currentPhase;
  };

  const isKnockoutPhase = (phase: string): boolean => {
    return ["Top16", "Top8", "Top4", "Final"].includes(phase);
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Leaderboard": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Top32": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top16": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Top8": return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Top4": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Final": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading competition data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(`/admin/competitions/${competitionId}`)}
              className="text-[#E1C760] hover:bg-[#E1C760]/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-[#E1C760]" />
              <div>
                <h1 className="text-2xl font-bold text-[#E1C760]">Phase Management</h1>
                {competition && (
                  <p className="text-gray-400">{competition.name}</p>
                )}
              </div>
            </div>
          </div>
          
          {competition && (
            <Badge className={getPhaseColor(competition.phase)}>
              {competition.phase} Phase
            </Badge>
          )}
        </div>

        {competition && (
          <div className="space-y-6">
            {/* Phase Overview */}
            <Card className="bg-black/50 border-[#E1C760]/30">
              <CardHeader>
                <CardTitle className="text-[#E1C760]">Current Phase: {competition.phase}</CardTitle>
                <CardDescription className="text-gray-400">
                  Manage the current competition phase and advance to the next stage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-white">{teams.length}</p>
                    <p className="text-sm text-gray-400">Active Teams</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-[#E1C760]">{eligibleTeams.length}</p>
                    <p className="text-sm text-gray-400">Eligible for Next Phase</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-400">{getNextPhase(competition.phase)}</p>
                    <p className="text-sm text-gray-400">Next Phase</p>
                  </div>
                </div>

                {/* Email Notification Info */}
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-blue-400 mb-2">
                    <span className="text-lg">📧</span>
                    <h4 className="font-medium">Automatic Email Notifications</h4>
                  </div>
                  <p className="text-sm text-gray-300">
                    When you process phase end or create knockout lobbies, email notifications are automatically sent to all affected players with:
                  </p>
                  <ul className="text-sm text-gray-400 mt-2 ml-4 space-y-1">
                    <li>• Lobby codes for knockout matches</li>
                    <li>• Phase advancement congratulations</li>
                    <li>• Elimination notifications with encouragement</li>
                    <li>• Competition status updates</li>
                  </ul>
                </div>

                <div className="flex gap-4 justify-center">
                  <Button
                    onClick={handleProcessPhaseEnd}
                    disabled={isProcessing}
                    className="bg-blue-600 text-white hover:bg-blue-700"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Process Phase End
                  </Button>
                  
                  <Button
                    onClick={handleAdvancePhase}
                    disabled={isProcessing || competition.phase === "Final"}
                    className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Advance to {getNextPhase(competition.phase)}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Bracket View for Knockout Phases */}
            {isKnockoutPhase(competition.phase) && (
              <Card className="bg-black/50 border-[#E1C760]/30">
                <CardHeader>
                  <CardTitle className="text-[#E1C760]">Tournament Bracket</CardTitle>
                  <CardDescription className="text-gray-400">
                    View and manage knockout phase matches
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CompetitionBracket 
                    competitionId={competitionId!}
                    brackets={brackets}
                    isLoading={false}
                  />
                </CardContent>
              </Card>
            )}

            {/* Teams List */}
            <Card className="bg-black/50 border-[#E1C760]/30">
              <CardHeader>
                <CardTitle className="text-[#E1C760]">Phase Teams</CardTitle>
                <CardDescription className="text-gray-400">
                  Teams currently in the {competition.phase} phase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {teams.map((team) => (
                    <div
                      key={team.id}
                      className={`p-4 border rounded-lg ${
                        eligibleTeams.some(et => et.id === team.id)
                          ? "border-green-500 bg-green-500/10"
                          : team.isEliminated
                          ? "border-red-500 bg-red-500/10"
                          : "border-gray-600 bg-gray-800/30"
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-white">{team.teamName}</h4>
                        {eligibleTeams.some(et => et.id === team.id) && (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-400 space-y-1">
                        <p>{team.player1.username}</p>
                        {team.player2 && <p>{team.player2.username}</p>}
                      </div>
                      
                      <div className="flex justify-between text-sm mt-2">
                        <span className="text-[#E1C760]">{team.points + team.bonusPoints} pts</span>
                        <span className="text-gray-400">{team.gamesPlayed} games</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
