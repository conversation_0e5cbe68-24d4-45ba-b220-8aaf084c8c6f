"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Trophy, Plus, Edit, Trash2, Users, Mail, Eye, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface Competition {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: string;
  maxTeams: number;
  currentTeams: number;
  entryFee: number;
  prizeFirst: number;
  prizeSecond: number;
  prizeThird: number;
  totalPrizePool: number;
  isPublic: boolean;
  allowSpectators: boolean;
  maxGamesPerTeam: number;
  createdAt: string;
}

export default function CompetitionManagement() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [competitions, setCompetitions] = useState<Competition[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load competitions
  useEffect(() => {
    loadCompetitions();
  }, []);

  if (!isAuthenticated) {
    return null;
  }

  const loadCompetitions = async () => {
    try {
      setIsLoading(true);
      const competitionsData = await apiService.getAllCompetitions();
      // Convert the API response to match our Competition interface
      const convertedCompetitions: Competition[] = competitionsData.map(comp => ({
        id: comp.id,
        name: comp.name,
        description: comp.description,
        startDate: comp.startDate,
        endDate: comp.endDate,
        status: comp.status,
        maxTeams: comp.maxTeams,
        currentTeams: comp.currentTeams,
        entryFee: comp.entryFee,
        prizeFirst: comp.prizeFirst,
        prizeSecond: comp.prizeSecond,
        prizeThird: comp.prizeThird,
        totalPrizePool: comp.totalPrizePool,
        isPublic: comp.isPublic,
        allowSpectators: comp.allowSpectators,
        maxGamesPerTeam: comp.maxGamesPerTeam,
        createdAt: comp.createdAt
      }));
      setCompetitions(convertedCompetitions);
    } catch (error) {
      console.error("Error loading competitions:", error);
      toast.error("Failed to load competitions");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCompetition = async (competitionId: string) => {
    if (window.confirm("Are you sure you want to delete this competition? This action cannot be undone.")) {
      try {
        await apiService.deleteCompetition(competitionId);
        setCompetitions(prev => prev.filter(comp => comp.id !== competitionId));
        toast.success("Competition deleted successfully");
      } catch (error) {
        console.error("Error deleting competition:", error);
        toast.error("Failed to delete competition");
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'upcoming':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/admin")}
              className="text-[#E1C760] hover:bg-[#E1C760]/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-[#E1C760]" />
              <h1 className="text-2xl font-bold text-[#E1C760]">Competition Management</h1>
            </div>
          </div>
          <Button
            onClick={() => navigate("/admin/competitions/create")}
            className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Competition
          </Button>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 text-[#E1C760] animate-spin" />
          </div>
        ) : (
          <>
            {/* Competitions Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {competitions.map((competition) => (
                <Card key={competition.id} className="bg-black/50 border-[#E1C760]/30">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-[#E1C760] text-lg">{competition.name}</CardTitle>
                        <CardDescription className="text-gray-400 mt-1">
                          {competition.description}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(competition.status)}>
                        {competition.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Competition Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-400">Teams:</span>
                        <span className="text-white ml-2">{competition.currentTeams}/{competition.maxTeams}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Entry Fee:</span>
                        <span className="text-white ml-2">${competition.entryFee}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Prize Pool:</span>
                        <span className="text-white ml-2">${competition.totalPrizePool}</span>
                      </div>
                      <div>
                        <span className="text-gray-400">Max Games:</span>
                        <span className="text-white ml-2">{competition.maxGamesPerTeam}</span>
                      </div>
                    </div>

                    {/* Date Range */}
                    <div className="text-sm">
                      <span className="text-gray-400">Duration:</span>
                      <span className="text-white ml-2">
                        {new Date(competition.startDate).toLocaleDateString()} - {new Date(competition.endDate).toLocaleDateString()}
                      </span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/competitions/${competition.id}`)}
                        className="border-[#E1C760] bg-[#E1C760]/10 text-[#E1C760] hover:bg-[#E1C760]/20 hover:text-[#E1C760] dark:border-[#E1C760]/50 dark:bg-[#E1C760]/5 dark:text-[#E1C760] dark:hover:bg-[#E1C760]/15"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/competitions/${competition.id}/teams`)}
                        className="border-blue-500 bg-blue-500/10 text-blue-600 hover:bg-blue-500/20 hover:text-blue-700 dark:border-blue-500/50 dark:bg-blue-500/5 dark:text-blue-400 dark:hover:bg-blue-500/15 dark:hover:text-blue-300"
                      >
                        <Users className="h-4 w-4 mr-1" />
                        Teams
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/competitions/${competition.id}/edit`)}
                        className="border-green-500 bg-green-500/10 text-green-600 hover:bg-green-500/20 hover:text-green-700 dark:border-green-500/50 dark:bg-green-500/5 dark:text-green-400 dark:hover:bg-green-500/15 dark:hover:text-green-300"
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/admin/competitions/${competition.id}/email`)}
                        className="border-purple-500 bg-purple-500/10 text-purple-600 hover:bg-purple-500/20 hover:text-purple-700 dark:border-purple-500/50 dark:bg-purple-500/5 dark:text-purple-400 dark:hover:bg-purple-500/15 dark:hover:text-purple-300"
                      >
                        <Mail className="h-4 w-4 mr-1" />
                        Email
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteCompetition(competition.id)}
                        className="border-red-500 bg-red-500/10 text-red-600 hover:bg-red-500/20 hover:text-red-700 dark:border-red-500/50 dark:bg-red-500/5 dark:text-red-400 dark:hover:bg-red-500/15 dark:hover:text-red-300"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {competitions.length === 0 && (
              <div className="text-center py-12">
                <Trophy className="h-16 w-16 text-gray-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-400 mb-2">No Competitions Found</h3>
                <p className="text-gray-500 mb-4">Create your first competition to get started.</p>
                <Button
                  onClick={() => navigate("/admin/competitions/create")}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Competition
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
