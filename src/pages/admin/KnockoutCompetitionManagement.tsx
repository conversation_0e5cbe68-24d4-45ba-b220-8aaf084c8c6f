"use client";
import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { ArrowLeft, Trophy, Users, Play, Settings, Clock, CheckCircle, Plus, Calendar, Bell } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { apiBaseUrl } from "@/config/env";
import { toast } from "sonner";
import KnockoutLobbyManager from "@/components/admin/KnockoutLobbyManager";
import PhaseAdvancementPanel from "@/components/admin/PhaseAdvancementPanel";
import MatchScheduler from "@/components/admin/MatchScheduler";

interface Competition {
  id: string;
  name: string;
  phase: string;
  phaseEndDate?: string;
  maxGamesPerPhase?: number;
  status: string;
}

interface CompetitionTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  points: number;
  bonusPoints: number;
  gamesPlayed: number;
  phase: string;
  isEliminated: boolean;
  advancedToNextPhase: boolean;
}

interface AdminLobbyView {
  lobbyId: string;
  lobbyCode: string;
  phase: string;
  matchStatus: string;
  matchScheduledAt?: string;
  bestOfGames: number;
  requiredWins: number;
  completedAt?: string;
  notificationSentAt?: string;
  createdByAdmin: string;
  team1Id: string;
  team1Name: string;
  team1Player1: string;
  team1Player2?: string;
  team2Id: string;
  team2Name: string;
  team2Player1: string;
  team2Player2?: string;
  gamesPlayed: number;
  team1Wins: number;
  team2Wins: number;
  winnerTeamName?: string;
  winnerTeamId?: string;
}

export default function KnockoutCompetitionManagement() {
  const { competitionId } = useParams<{ competitionId: string }>();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  
  const [competition, setCompetition] = useState<Competition | null>(null);
  const [teams, setTeams] = useState<CompetitionTeam[]>([]);
  const [eligibleTeams, setEligibleTeams] = useState<CompetitionTeam[]>([]);
  const [adminLobbies, setAdminLobbies] = useState<AdminLobbyView[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'lobbies' | 'advancement' | 'scheduling'>('overview');

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
      return;
    }
    
    if (competitionId) {
      loadCompetitionData();
    }
  }, [competitionId, isAuthenticated, navigate]);

  const loadCompetitionData = async () => {
    try {
      setIsLoading(true);
      
      // Load competition details
      const competitionData = await apiService.getCompetitionById(competitionId!);
      setCompetition(competitionData);
      
      // Load current phase teams
      await loadPhaseTeams(competitionData.phase);
      
      // Load eligible teams for next phase
      await loadEligibleTeams();
      
      // Load admin lobby view if in knockout phase
      if (isKnockoutPhase(competitionData.phase)) {
        await loadAdminLobbies();
      }
      
    } catch (error) {
      console.error("Error loading competition data:", error);
      toast.error("Failed to load competition data");
    } finally {
      setIsLoading(false);
    }
  };

  const loadPhaseTeams = async (phase: string) => {
    try {
      const teams = await apiService.getPhaseTeams(competitionId!, phase);
      setTeams(teams);
    } catch (error) {
      console.error("Error loading phase teams:", error);
    }
  };

  const loadEligibleTeams = async () => {
    try {
      const teams = await apiService.getEligibleTeams(competitionId!);
      setEligibleTeams(teams);
    } catch (error) {
      console.error("Error loading eligible teams:", error);
    }
  };

  const loadAdminLobbies = async () => {
    try {
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/admin/lobbies`, {
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setAdminLobbies(data.data || []);
      }
    } catch (error) {
      console.error("Error loading admin lobbies:", error);
    }
  };

  const handleAdvanceToKnockout = async () => {
    if (!competition) return;

    try {
      setIsProcessing(true);
      
      // First advance to Top32 phase
      const response = await fetch(`${apiBaseUrl}/competitions/${competitionId}/phases/advance`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiService.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          newPhase: 'Top32'
        })
      });

      if (response.ok) {
        toast.success("Competition advanced to Top32 phase!");
        await loadCompetitionData();
        setActiveTab('lobbies');
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to advance phase");
      }
    } catch (error) {
      console.error("Error advancing to knockout:", error);
      toast.error("Failed to advance phase");
    } finally {
      setIsProcessing(false);
    }
  };

  const isKnockoutPhase = (phase: string): boolean => {
    return ["Top32", "Top16", "Top8", "Top4", "Final"].includes(phase);
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case "Leaderboard": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "Top32": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "Top16": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "Top8": return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      case "Top4": return "bg-red-500/20 text-red-400 border-red-500/30";
      case "Final": return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      default: return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getNextPhase = (currentPhase: string): string => {
    const phaseMap: Record<string, string> = {
      "Leaderboard": "Top32",
      "Top32": "Top16",
      "Top16": "Top8",
      "Top8": "Top4",
      "Top4": "Final",
      "Final": "Completed"
    };
    return phaseMap[currentPhase] || currentPhase;
  };

  if (!isAuthenticated) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#E1C760] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading competition data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(`/admin/competitions/${competitionId}`)}
              className="text-[#E1C760] hover:bg-[#E1C760]/10"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-[#E1C760]" />
              <div>
                <h1 className="text-2xl font-bold text-[#E1C760]">Knockout Competition Management</h1>
                {competition && (
                  <p className="text-gray-400">{competition.name}</p>
                )}
              </div>
            </div>
          </div>
          
          {competition && (
            <Badge className={getPhaseColor(competition.phase)}>
              {competition.phase} Phase
            </Badge>
          )}
        </div>

        {/* Navigation Tabs */}
        <div className="flex gap-2 mb-6 border-b border-gray-700">
          {['overview', 'lobbies', 'advancement', 'scheduling'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab as any)}
              className={`px-4 py-2 capitalize transition-colors ${
                activeTab === tab
                  ? 'text-[#E1C760] border-b-2 border-[#E1C760]'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {tab}
            </button>
          ))}
        </div>

        {competition && (
          <div className="space-y-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <>
                {/* Phase Overview */}
                <Card className="bg-black/50 border-[#E1C760]/30">
                  <CardHeader>
                    <CardTitle className="text-[#E1C760]">Current Phase: {competition.phase}</CardTitle>
                    <CardDescription className="text-gray-400">
                      Manage the current competition phase and advance to knockout stages
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-white">{teams.length}</p>
                        <p className="text-sm text-gray-400">Active Teams</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-[#E1C760]">{eligibleTeams.length}</p>
                        <p className="text-sm text-gray-400">Eligible for Next Phase</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-400">{getNextPhase(competition.phase)}</p>
                        <p className="text-sm text-gray-400">Next Phase</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-400">{adminLobbies.length}</p>
                        <p className="text-sm text-gray-400">Active Lobbies</p>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-4 justify-center">
                      {competition.phase === "Leaderboard" ? (
                        <Button
                          onClick={handleAdvanceToKnockout}
                          disabled={isProcessing || eligibleTeams.length < 32}
                          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Start Knockout Phase (Top32)
                        </Button>
                      ) : (
                        <Button
                          onClick={() => setActiveTab('advancement')}
                          className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Manage Phase Advancement
                        </Button>
                      )}
                      
                      {isKnockoutPhase(competition.phase) && (
                        <Button
                          onClick={() => setActiveTab('lobbies')}
                          className="bg-blue-600 text-white hover:bg-blue-700"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Manage Lobbies
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            {/* Lobbies Tab */}
            {activeTab === 'lobbies' && isKnockoutPhase(competition.phase) && (
              <KnockoutLobbyManager
                competitionId={competitionId!}
                competition={competition}
                teams={teams}
                adminLobbies={adminLobbies}
                onLobbiesUpdated={loadAdminLobbies}
              />
            )}

            {/* Advancement Tab */}
            {activeTab === 'advancement' && (
              <PhaseAdvancementPanel
                competitionId={competitionId!}
                competition={competition}
                adminLobbies={adminLobbies}
                onAdvancement={loadCompetitionData}
              />
            )}

            {/* Scheduling Tab */}
            {activeTab === 'scheduling' && isKnockoutPhase(competition.phase) && (
              <MatchScheduler
                competitionId={competitionId!}
                adminLobbies={adminLobbies}
                onScheduleUpdated={loadAdminLobbies}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}
