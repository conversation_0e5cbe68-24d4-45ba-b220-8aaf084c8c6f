/* NeverFollowSuitResultDisplay.css */

/* Mobile landscape orientation adjustments */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .four-ball-container {
    padding: 0.5rem !important;
    align-items: flex-start !important;
    justify-content: center !important;
    overflow-y: auto !important;
  }

  .four-ball-card {
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    max-width: 90vw !important;
    max-height: 85vh !important;
  }

  .four-ball-header {
    margin-bottom: 0.5rem !important;
  }

  .four-ball-title {
    font-size: 1rem !important;
  }

  .four-ball-subtitle {
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
  }

  .four-ball-content {
    gap: 0.75rem !important;
  }

  .four-ball-content > div {
    margin-bottom: 0.75rem !important;
  }

  .four-ball-content h3 {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
  }

  .four-ball-content p {
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
  }

  .four-ball-content .text-lg {
    font-size: 0.875rem !important;
  }

  .four-ball-content .text-sm {
    font-size: 0.625rem !important;
  }

  .four-ball-content .border {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .four-ball-content .grid-cols-2 {
    gap: 0.5rem !important;
  }

  .four-ball-content .grid-cols-2 > div {
    font-size: 0.625rem !important;
  }

  .four-ball-content button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    margin-top: 0.5rem !important;
  }

  /* Card images in result display */
  .four-ball-content img {
    max-width: 2rem !important;
    max-height: 3rem !important;
  }

  /* Expandable sections */
  .four-ball-content .space-y-2 > div {
    margin-bottom: 0.5rem !important;
  }

  /* Close button */
  .four-ball-card .absolute {
    top: 0.5rem !important;
    right: 0.5rem !important;
  }

  .four-ball-card .absolute button {
    padding: 0.25rem !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .four-ball-card .absolute button svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
}

/* Very short screens */
@media (max-height: 400px) and (orientation: landscape) {
  .four-ball-container {
    padding: 0.25rem !important;
  }

  .four-ball-card {
    padding: 0.5rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    max-height: 90vh !important;
  }

  .four-ball-title {
    font-size: 0.875rem !important;
  }

  .four-ball-subtitle {
    font-size: 0.625rem !important;
  }

  .four-ball-content {
    gap: 0.5rem !important;
  }

  .four-ball-content h3 {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
  }

  .four-ball-content p {
    font-size: 0.625rem !important;
    margin-bottom: 0.25rem !important;
  }

  .four-ball-content button {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.625rem !important;
  }

  .four-ball-content img {
    max-width: 1.5rem !important;
    max-height: 2.25rem !important;
  }
}
