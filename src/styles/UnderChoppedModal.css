/* UnderChoppedModal.css */

/* Mobile landscape orientation adjustments */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .under-chopped-modal-container {
    padding: 0.5rem !important;
    align-items: flex-start !important;
    justify-content: center !important;
    overflow-y: auto !important;
  }

  .under-chopped-modal-card {
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    max-width: 90vw !important;
    max-height: 85vh !important;
  }

  .under-chopped-modal-card h2 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
  }

  .under-chopped-modal-card p {
    font-size: 0.75rem !important;
    margin-bottom: 0.75rem !important;
    line-height: 1.2 !important;
  }

  .under-chopped-modal-card h3 {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
  }

  .under-chopped-modal-card h4 {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
  }

  .under-chopped-modal-card .space-y-4 {
    gap: 0.75rem !important;
  }

  .under-chopped-modal-card .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  .under-chopped-modal-card .grid-cols-2 {
    gap: 0.5rem !important;
  }

  .under-chopped-modal-card .grid-cols-3 {
    gap: 0.5rem !important;
  }

  .under-chopped-modal-card button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
    min-height: 2rem !important;
  }

  .under-chopped-modal-card .text-red-500 {
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
  }

  .under-chopped-modal-card .bg-gray-900 {
    padding: 0.5rem !important;
    margin-bottom: 0.75rem !important;
  }

  .under-chopped-modal-card .bg-gray-900 h4 {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
  }

  .under-chopped-modal-card .bg-gray-900 ul {
    font-size: 0.625rem !important;
    line-height: 1.2 !important;
  }

  .under-chopped-modal-card .bg-gray-900 li {
    margin-bottom: 0.125rem !important;
  }

  .under-chopped-modal-card .flex.justify-between.items-center {
    margin-bottom: 0.5rem !important;
  }

  .under-chopped-modal-card .flex.justify-between.items-center button {
    padding: 0.25rem !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .under-chopped-modal-card .flex.justify-between.items-center button svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
}

/* Very short screens */
@media (max-height: 400px) and (orientation: landscape) {
  .under-chopped-modal-container {
    padding: 0.25rem !important;
  }

  .under-chopped-modal-card {
    padding: 0.5rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    max-height: 90vh !important;
  }

  .under-chopped-modal-card h2 {
    font-size: 0.875rem !important;
    margin-bottom: 0.25rem !important;
  }

  .under-chopped-modal-card p {
    font-size: 0.625rem !important;
    margin-bottom: 0.5rem !important;
  }

  .under-chopped-modal-card h3 {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
  }

  .under-chopped-modal-card button {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.625rem !important;
    min-height: 1.75rem !important;
  }

  .under-chopped-modal-card .bg-gray-900 {
    padding: 0.375rem !important;
    margin-bottom: 0.5rem !important;
  }

  .under-chopped-modal-card .bg-gray-900 ul {
    font-size: 0.5rem !important;
  }
}
