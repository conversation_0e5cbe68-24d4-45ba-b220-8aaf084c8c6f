/* ThuneeResultDisplay.css */

/* Mobile landscape orientation adjustments */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .thunee-result-container {
    padding: 0.5rem !important;
    align-items: flex-start !important;
    justify-content: center !important;
    overflow-y: auto !important;
  }

  .thunee-result-card {
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    max-width: 90vw !important;
    max-height: 85vh !important;
  }

  .thunee-result-card .text-2xl {
    font-size: 1rem !important;
  }

  .thunee-result-card .text-lg {
    font-size: 0.875rem !important;
  }

  .thunee-result-card .text-sm {
    font-size: 0.625rem !important;
  }

  .thunee-result-card h3 {
    font-size: 0.875rem !important;
    margin-bottom: 0.5rem !important;
  }

  .thunee-result-card .space-y-4 {
    gap: 0.75rem !important;
  }

  .thunee-result-card .space-y-4 > * + * {
    margin-top: 0.75rem !important;
  }

  .thunee-result-card .space-y-2 {
    gap: 0.5rem !important;
  }

  .thunee-result-card .space-y-2 > * + * {
    margin-top: 0.5rem !important;
  }

  .thunee-result-card .border {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .thunee-result-card .grid-cols-2 {
    gap: 0.5rem !important;
  }

  .thunee-result-card .grid-cols-2 > div {
    font-size: 0.625rem !important;
  }

  .thunee-result-card button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .thunee-result-card .max-h-60 {
    max-height: 8rem !important;
  }

  /* Card header adjustments */
  .thunee-result-card .flex.items-center.justify-center.gap-2 {
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .thunee-result-card .flex.items-center.justify-center.gap-2 svg {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .thunee-result-card .flex.items-center.justify-center.gap-1 {
    gap: 0.25rem !important;
  }

  .thunee-result-card .flex.items-center.justify-center.gap-1 svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  /* Close button */
  .thunee-result-card .absolute {
    top: 0.5rem !important;
    right: 0.5rem !important;
  }

  .thunee-result-card .absolute button {
    padding: 0.25rem !important;
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .thunee-result-card .absolute button svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  /* Hand display adjustments */
  .thunee-result-card .border.border-\\[\\#E1C760\\]\\/30 {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .thunee-result-card .border.border-\\[\\#E1C760\\]\\/30 .flex.justify-between.items-center {
    margin-bottom: 0.25rem !important;
  }

  .thunee-result-card .border.border-\\[\\#E1C760\\]\\/30 .text-\\[\\#E1C760\\] {
    font-size: 0.75rem !important;
  }
}

/* Very short screens */
@media (max-height: 400px) and (orientation: landscape) {
  .thunee-result-container {
    padding: 0.25rem !important;
  }

  .thunee-result-card {
    padding: 0.5rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    max-height: 90vh !important;
  }

  .thunee-result-card .text-2xl {
    font-size: 0.875rem !important;
  }

  .thunee-result-card .text-lg {
    font-size: 0.75rem !important;
  }

  .thunee-result-card .text-sm {
    font-size: 0.5rem !important;
  }

  .thunee-result-card h3 {
    font-size: 0.75rem !important;
    margin-bottom: 0.25rem !important;
  }

  .thunee-result-card button {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.625rem !important;
  }

  .thunee-result-card .max-h-60 {
    max-height: 6rem !important;
  }

  .thunee-result-card .flex.items-center.justify-center.gap-2 svg {
    width: 1rem !important;
    height: 1rem !important;
  }

  .thunee-result-card .flex.items-center.justify-center.gap-1 svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }
}
