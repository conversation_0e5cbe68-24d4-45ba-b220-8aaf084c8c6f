/* Ensure the ball cards components can receive pointer events */
.player-team-ball-cards,
.opposite-team-ball-cards {
  pointer-events: auto;
  position: absolute;
  z-index: 10;
}

.ball-cards-title {
  color: #E1C760;
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
}

/* Player team ball cards (top right) */
.player-team-ball-cards {
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
}

/* Opposite team ball cards (left) */
.opposite-team-ball-cards {
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300px;
}

/* For backward compatibility */
.ball-cards-teams {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 65rem;
}

.ball-cards-team {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50%;
}

.team-name {
  color: rgb(237, 234, 16);
  font-size: 1rem;
  margin-bottom: -85px;
}

.team-score {
  color: #E1C760;
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.team-ball-cards {

  width: 100%;
  display: flex;
  justify-content: center;
}

.table-surface {
  position: relative;
  width: 500px;
  /* height: 500px; */
  /* overflow: hidden; */
  transform: scale(0.6);
  transform-origin: top center;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .player-team-ball-cards {
    width: 150px;
    top: 5px;
    right: 5px;
  }

  .opposite-team-ball-cards {
    width: 150px;
    top: 5px;
    left: 5px;
  }

  .table-surface {
    width: 200px;
    /* height: 200px; */
    transform: scale(0.4);
  }

  .team-name {
    color: rgb(237, 234, 16);
  font-size: 1rem;
  margin-bottom: -85px;
  }

  /* For backward compatibility */
  .ball-cards-teams {
    flex-direction: column;
    align-items: center;
    gap: 8rem;
  }

  .ball-cards-team {
    width: 100%;
    margin-bottom: 20px;
  }
}

/* Specific styles for mobile landscape */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .player-team-ball-cards {
    width: 80px;
    top: 2px;
    right: 2px;
  }

  .opposite-team-ball-cards {
    width: 80px;
    top: 2px;
    left: 2px;
  }

  .table-surface {
    width: 100px;
    /* height: 100px; */
    transform: scale(0.2);
  }

  .team-name {
    color: rgb(237, 234, 16);
    font-size: 1rem;
    margin-bottom: -85px;
  }

  /* Make cards smaller in landscape mode */
  .card {
    width: 120px;
    height: 180px;
  }
}

@media (max-width: 480px) {
  .player-team-ball-cards {
    width: 120px;
  }

  .opposite-team-ball-cards {
    width: 120px;
  }

  .table-surface {
    width: 150px;
    /* height: 150px; */
    transform: scale(0.3);
  }

  .team-name {
    color: rgb(237, 234, 16);
    font-size: 1rem;
    margin-bottom: -85px;
  }
}

@media (max-width: 1000px) and (min-width: 769px) {
  .player-team-ball-cards {
    width: 200px;
    top: 10px;
    right: 10px;
  }

  .opposite-team-ball-cards {
    width: 200px;
    top: 10px;
    left: 10px;
  }

  /* For backward compatibility */
  .ball-cards-teams {
    gap: 8rem;
  }
}
