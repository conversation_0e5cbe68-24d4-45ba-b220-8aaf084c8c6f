/* Responsive styles for Thunee game with focus on mobile landscape orientation */

/* Base styles for all screen sizes */
:root {
  --card-width-xs: 60px;
  --card-width-sm: 80px;
  --card-width-md: 100px;
  --card-width-lg: 120px;
  --header-height: 70px;
  --footer-height: 50px;
  --game-controls-height: 40px;
}

/* Small landscape phones */
@media (max-width: 640px) and (orientation: landscape) {
  :root {
    --card-width-xs: 50px;
    --card-width-sm: 60px;
    --card-width-md: 70px;
    --card-width-lg: 80px;
    --header-height: 60px;
    --footer-height: 40px;
    --game-controls-height: 36px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 60px !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 4px !important;
  }
  
  .player-avatar {
    width: 24px !important;
    height: 24px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.85);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.85);
  }
  
  /* Make bottom navigation more compact */
  .bottom-nav {
    height: 40px !important;
  }
  
  /* Adjust game controls */
  .game-controls {
    transform: scale(0.9);
    right: 5px !important;
  }
}

/* Medium landscape phones and small tablets */
@media (min-width: 641px) and (max-width: 768px) and (orientation: landscape) {
  :root {
    --card-width-xs: 55px;
    --card-width-sm: 70px;
    --card-width-md: 80px;
    --card-width-lg: 90px;
    --header-height: 65px;
    --footer-height: 45px;
    --game-controls-height: 38px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 65px !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 6px !important;
  }
  
  .player-avatar {
    width: 28px !important;
    height: 28px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.9);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.9);
  }
}

/* Large landscape phones and tablets */
@media (min-width: 769px) and (max-width: 1024px) and (orientation: landscape) {
  :root {
    --card-width-xs: 60px;
    --card-width-sm: 75px;
    --card-width-md: 90px;
    --card-width-lg: 100px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 70px !important;
  }
}

/* Specific height-based adjustments for very short screens */
@media (max-height: 450px) and (orientation: landscape) {
  :root {
    --card-width-xs: 45px;
    --card-width-sm: 55px;
    --card-width-md: 65px;
    --card-width-lg: 75px;
    --header-height: 50px;
    --footer-height: 36px;
    --game-controls-height: 32px;
  }
  
  /* Adjust main layout */
  .main-content {
    padding-top: 50px !important;
    grid-template-rows: 40% 60% !important;
  }
  
  /* Adjust player info section */
  .player-info-section {
    padding: 2px !important;
    font-size: 0.75rem !important;
  }
  
  .player-avatar {
    width: 20px !important;
    height: 20px !important;
  }
  
  /* Adjust card sizing */
  .player-card {
    transform: scale(0.8);
  }
  
  /* Adjust played cards area */
  .played-cards-area {
    transform: scale(0.8);
  }
  
  /* Make bottom navigation more compact */
  .bottom-nav {
    height: 36px !important;
  }
  
  /* Adjust game controls */
  .game-controls {
    transform: scale(0.85);
    /* bottom: 40px !important; */
  }
}

/* Utility classes for responsive design */
.landscape-hidden {
  display: none !important;
}

@media (orientation: landscape) {
  .landscape-hidden {
    display: none !important;
  }
  
  .landscape-visible {
    display: block !important;
  }
  
  .landscape-flex {
    display: flex !important;
  }
}

/* Fix for iOS Safari bottom bar */
@supports (-webkit-touch-callout: none) {
  .main-content {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

  .bottom-nav {
    padding-bottom: env(safe-area-inset-bottom, 0);
  }

}

/* DealerDetermination component styles */
.dealer-determination-container {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 50;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.dealer-determination-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #E1C760;
  margin-bottom: 0.5rem;
  text-align: center;
}

.dealer-determination-description {
  color: white;
  text-align: center;
  margin-bottom: 0.75rem;
  max-width: 20rem;
  font-size: 0.75rem;
}

.dealer-determination-dealing-info {
  margin-bottom: 0.5rem;
  text-align: center;
}

.dealer-determination-dealing-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.dealer-determination-dealing-subtext {
  color: #E1C760;
  font-size: 0.75rem;
}

.dealer-determination-current-card {
  position: relative;
  height: 8rem;
  width: 6rem;
  margin-bottom: 0.75rem;
}

.dealer-determination-card-label {
  position: absolute;
  bottom: -1.5rem;
  left: 0;
  right: 0;
  text-align: center;
}

.dealer-determination-card-label-text {
  background-color: #E1C760;
  color: black;
  font-size: 0.75rem;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
}

/* Mobile landscape orientation adjustments */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .dealer-determination-container {
    justify-content: flex-start;
    padding: 0.25rem;
    padding-top: 0.5rem;
    overflow-y: auto;
    max-height: 100vh;
  }

  .dealer-determination-title {
    font-size: 1rem;
    margin-bottom: 0.125rem;
  }

  .dealer-determination-description {
    margin-bottom: 0.25rem;
    max-width: 16rem;
    font-size: 9px;
    line-height: 1.2;
  }

  .dealer-determination-dealing-info {
    margin-bottom: 0.125rem;
  }

  .dealer-determination-dealing-text {
    font-size: 0.625rem;
    margin-bottom: 0;
  }

  .dealer-determination-dealing-subtext {
    font-size: 9px;
  }

  .dealer-determination-current-card {
    height: 3.5rem;
    width: 2.5rem;
    margin-bottom: 0.25rem;
  }

  .dealer-determination-card-label {
    bottom: -0.75rem;
  }

  .dealer-determination-card-label-text {
    font-size: 8px;
    padding: 0.0625rem 0.25rem;
  }
}

/* Player grid styles */
.dealer-determination-player-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
  max-width: 20rem;
}

.dealer-determination-player-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.5rem;
}

.dealer-determination-player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.5rem;
}

.dealer-determination-player-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid;
}

.dealer-determination-player-details {
  margin-top: 0.25rem;
  text-align: center;
}

.dealer-determination-player-name {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
}

.dealer-determination-dealer-label {
  color: #10b981;
  font-size: 0.75rem;
  font-weight: bold;
  display: block;
}

.dealer-determination-player-card {
  height: 6rem;
  width: 4rem;
  position: relative;
}

.dealer-determination-dealer-highlight {
  position: absolute;
  inset: 0;
  border: 4px solid #10b981;
  border-radius: 0.375rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.dealer-determination-team-info {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  text-align: center;
}

.dealer-determination-team-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-weight: 500;
}

.dealer-determination-waiting-text {
  color: #9ca3af;
  font-size: 0.75rem;
}

.dealer-determination-waiting-subtext {
  color: #9ca3af;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Mobile landscape adjustments for player grid */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .dealer-determination-player-grid {
    gap: 0.25rem;
    margin-bottom: 0.25rem;
    max-width: 16rem;
  }

  .dealer-determination-player-item {
    padding: 0.125rem;
  }

  .dealer-determination-player-info {
    margin-bottom: 0.125rem;
  }

  .dealer-determination-player-avatar {
    width: 1.5rem;
    height: 1.5rem;
    border-width: 1px;
  }

  .dealer-determination-player-details {
    margin-top: 0.0625rem;
  }

  .dealer-determination-player-name {
    font-size: 0.625rem;
    line-height: 1.1;
  }

  .dealer-determination-dealer-label {
    font-size: 8px;
  }

  .dealer-determination-player-card {
    height: 2.5rem;
    width: 1.75rem;
  }

  .dealer-determination-dealer-highlight {
    border-width: 1px;
  }

  .dealer-determination-team-info {
    margin-top: 0.125rem;
    font-size: 8px;
  }

  .dealer-determination-team-badge {
    padding: 0.0625rem 0.25rem;
  }

  .dealer-determination-waiting-text {
    font-size: 8px;
  }

  .dealer-determination-waiting-subtext {
    font-size: 8px;
    margin-top: 0.0625rem;
  }
}

/* Black Jack examples styles */
.dealer-determination-blackjack-examples {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 0.75rem;
}

.dealer-determination-blackjack-text {
  color: white;
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
}

.dealer-determination-blackjack-cards {
  display: flex;
  gap: 0.75rem;
}

.dealer-determination-example-card {
  border: 1px solid #E1C760;
  border-radius: 0.375rem;
  overflow: hidden;
  height: 4rem;
  width: 3rem;
}

/* Dealer found result styles */
.dealer-determination-result {
  text-align: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(225, 199, 96, 0.3);
  max-width: 20rem;
}

.dealer-determination-result-content {
  margin-bottom: 1rem;
}

.dealer-determination-result-avatar {
  position: relative;
  width: 4rem;
  height: 4rem;
  margin-bottom: 0.75rem;
}

.dealer-determination-result-avatar-inner {
  position: relative;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #10b981;
}

.dealer-determination-result-text {
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
}

.dealer-determination-result-name {
  color: #E1C760;
  font-weight: bold;
  font-size: 1rem;
}

.dealer-determination-result-description {
  color: white;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.dealer-determination-continue-button {
  background-color: #E1C760;
  color: black;
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.dealer-determination-continue-button:hover {
  background-color: rgba(225, 199, 96, 0.8);
}

/* Mobile landscape adjustments for Black Jack examples and dealer result */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .dealer-determination-blackjack-examples {
    margin-bottom: 0.25rem;
  }

  .dealer-determination-blackjack-text {
    font-size: 8px;
    margin-bottom: 0.0625rem;
  }

  .dealer-determination-blackjack-cards {
    gap: 0.25rem;
  }

  .dealer-determination-example-card {
    height: 2rem;
    width: 1.25rem;
  }

  .dealer-determination-result {
    padding: 0.25rem;
    max-width: 15rem;
  }

  .dealer-determination-result-content {
    margin-bottom: 0.25rem;
  }

  .dealer-determination-result-avatar {
    width: 2rem;
    height: 2rem;
    margin-bottom: 0.25rem;
  }

  .dealer-determination-result-avatar-inner {
    width: 2rem;
    height: 2rem;
    border-width: 1px;
  }

  .dealer-determination-result-text {
    font-size: 0.625rem;
    line-height: 1.2;
  }

  .dealer-determination-result-name {
    font-size: 0.75rem;
  }

  .dealer-determination-result-description {
    font-size: 0.625rem;
    margin-bottom: 0.25rem;
    line-height: 1.2;
  }

  .dealer-determination-continue-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Extra compact styles for very short screens (like iPhone 12 Pro landscape) */
@media (max-width: 915px) and (max-height: 390px) and (orientation: landscape) {
  .dealer-determination-container {
    padding: 0.125rem;
    padding-top: 0.25rem;
  }

  .dealer-determination-title {
    font-size: 0.875rem;
    margin-bottom: 0.0625rem;
  }

  .dealer-determination-description {
    margin-bottom: 0.125rem;
    font-size: 8px;
    max-width: 14rem;
  }

  .dealer-determination-dealing-info {
    margin-bottom: 0.0625rem;
  }

  .dealer-determination-dealing-text {
    font-size: 0.5rem;
  }

  .dealer-determination-dealing-subtext {
    font-size: 8px;
  }

  .dealer-determination-current-card {
    height: 2.5rem;
    width: 1.75rem;
    margin-bottom: 0.125rem;
  }

  .dealer-determination-card-label {
    bottom: -0.5rem;
  }

  .dealer-determination-card-label-text {
    font-size: 7px;
    padding: 0.0625rem 0.125rem;
  }

  .dealer-determination-player-grid {
    gap: 0.125rem;
    margin-bottom: 0.125rem;
    max-width: 14rem;
  }

  .dealer-determination-player-item {
    padding: 0.0625rem;
  }

  .dealer-determination-player-avatar {
    width: 1.25rem;
    height: 1.25rem;
  }

  .dealer-determination-player-name {
    font-size: 0.5rem;
  }

  .dealer-determination-dealer-label {
    font-size: 7px;
  }

  .dealer-determination-player-card {
    height: 2rem;
    width: 1.5rem;
  }

  .dealer-determination-team-info {
    font-size: 7px;
  }

  .dealer-determination-team-badge {
    padding: 0.0625rem 0.125rem;
  }

  .dealer-determination-blackjack-examples {
    margin-bottom: 0.125rem;
  }

  .dealer-determination-blackjack-text {
    font-size: 7px;
  }

  .dealer-determination-example-card {
    height: 1.5rem;
    width: 1rem;
  }

  .dealer-determination-result {
    padding: 0.125rem;
    max-width: 12rem;
  }

  .dealer-determination-result-avatar {
    width: 1.5rem;
    height: 1.5rem;
    margin-bottom: 0.125rem;
  }

  .dealer-determination-result-avatar-inner {
    width: 1.5rem;
    height: 1.5rem;
  }

  .dealer-determination-result-text {
    font-size: 0.5rem;
  }

  .dealer-determination-result-name {
    font-size: 0.625rem;
  }

  .dealer-determination-result-description {
    font-size: 0.5rem;
    margin-bottom: 0.125rem;
  }

  .dealer-determination-continue-button {
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
  }
}
