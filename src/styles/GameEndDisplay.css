/* GameEndDisplay.css */

/* Mobile landscape orientation adjustments */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .game-end-container {
    padding: 0.5rem !important;
    align-items: flex-start !important;
    justify-content: center !important;
    overflow-y: auto !important;
  }

  .game-end-card {
    padding: 0.75rem !important;
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
    max-width: 90vw !important;
    max-height: 85vh !important;
    overflow: hidden !important;
  }

  .game-end-card .text-3xl {
    font-size: 1.125rem !important;
  }

  .game-end-card .text-2xl {
    font-size: 1rem !important;
  }

  .game-end-card .text-xl {
    font-size: 0.875rem !important;
  }

  .game-end-card .text-lg {
    font-size: 0.75rem !important;
  }

  .game-end-card .text-sm {
    font-size: 0.625rem !important;
  }

  .game-end-card .w-12.h-12 {
    width: 2rem !important;
    height: 2rem !important;
  }

  .game-end-card .w-5.h-5 {
    width: 1rem !important;
    height: 1rem !important;
  }

  .game-end-card .w-4.h-4 {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  .game-end-card .p-6 {
    padding: 0.75rem !important;
  }

  .game-end-card .mb-6 {
    margin-bottom: 0.75rem !important;
  }

  .game-end-card .mb-4 {
    margin-bottom: 0.5rem !important;
  }

  .game-end-card .mb-2 {
    margin-bottom: 0.25rem !important;
  }

  .game-end-card .gap-6 {
    gap: 0.75rem !important;
  }

  .game-end-card .gap-4 {
    gap: 0.5rem !important;
  }

  .game-end-card .grid-cols-2 {
    gap: 0.75rem !important;
  }

  .game-end-card button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  /* Game history section */
  .game-end-card .max-h-96 {
    max-height: 12rem !important;
  }

  .game-end-card .space-y-3 {
    gap: 0.5rem !important;
  }

  .game-end-card .space-y-3 > * + * {
    margin-top: 0.5rem !important;
  }

  .game-end-card .p-4 {
    padding: 0.5rem !important;
  }

  .game-end-card .space-x-4 {
    gap: 0.5rem !important;
  }

  .game-end-card .space-x-4 > * + * {
    margin-left: 0.5rem !important;
  }

  .game-end-card .px-2.py-1 {
    padding: 0.25rem 0.5rem !important;
  }

  .game-end-card .mt-2 {
    margin-top: 0.25rem !important;
  }
}

/* Very short screens */
@media (max-height: 400px) and (orientation: landscape) {
  .game-end-container {
    padding: 0.25rem !important;
  }

  .game-end-card {
    padding: 0.5rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    max-height: 90vh !important;
  }

  .game-end-card .text-3xl {
    font-size: 1rem !important;
  }

  .game-end-card .text-2xl {
    font-size: 0.875rem !important;
  }

  .game-end-card .text-xl {
    font-size: 0.75rem !important;
  }

  .game-end-card .text-lg {
    font-size: 0.625rem !important;
  }

  .game-end-card .text-sm {
    font-size: 0.5rem !important;
  }

  .game-end-card .w-12.h-12 {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .game-end-card .w-5.h-5 {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }

  .game-end-card .w-4.h-4 {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }

  .game-end-card .p-6 {
    padding: 0.5rem !important;
  }

  .game-end-card .mb-6 {
    margin-bottom: 0.5rem !important;
  }

  .game-end-card .mb-4 {
    margin-bottom: 0.375rem !important;
  }

  .game-end-card .mb-2 {
    margin-bottom: 0.125rem !important;
  }

  .game-end-card .gap-6 {
    gap: 0.5rem !important;
  }

  .game-end-card .gap-4 {
    gap: 0.375rem !important;
  }

  .game-end-card button {
    padding: 0.375rem 0.5rem !important;
    font-size: 0.625rem !important;
  }

  .game-end-card .max-h-96 {
    max-height: 8rem !important;
  }

  .game-end-card .p-4 {
    padding: 0.375rem !important;
  }
}
