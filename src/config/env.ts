// Environment configuration utility
// This file centralizes all environment variable access

interface AppConfig {
  apiBaseUrl: string;
  socketUrl: string;
  environment: 'development' | 'production' | 'test';
  isDevelopment: boolean;
  isProduction: boolean;
}

// Get environment variables with fallbacks
const getConfig = (): AppConfig => {
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://localhost:57229/api';
  const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001';
  const environment = (import.meta.env.VITE_APP_ENV || 'development') as 'development' | 'production' | 'test';

  return {
    apiBaseUrl,
    socketUrl,
    environment,
    isDevelopment: environment === 'development',
    isProduction: environment === 'production',
  };
};

// Export the configuration
export const config = getConfig();

// Export individual values for convenience
export const {
  apiBaseUrl,
  socketUrl,
  environment,
  isDevelopment,
  isProduction,
} = config;

// Utility function to log configuration in development
if (isDevelopment) {
  console.log('🔧 App Configuration:', {
    apiBaseUrl,
    socketUrl,
    environment,
  });
}
