export type CardSuit = 'H' | 'D' | 'C' | 'S';
export type CardValue = '6';
export type CardFace = `${CardValue}${CardSuit}`;

export interface CardPosition {
  x: number;
  y: number;
  rotation: number;
  zIndex: number;
}

export interface CardPositionMap {
  [ballNumber: number]: {
    [cardFace: string]: CardPosition;
  };
}

export interface Card {
  id: string;
  face: CardFace;
  position: CardPosition;
  isFlipped: boolean;
}
