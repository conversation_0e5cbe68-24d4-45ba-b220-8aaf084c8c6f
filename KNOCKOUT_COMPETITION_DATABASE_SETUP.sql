-- =============================================
-- KNOCKOUT COMPETITION SYSTEM - DATABASE SETUP
-- =============================================

USE [GoldRushThunee]
GO

PRINT 'Starting Knockout Competition System Database Setup...'

-- =============================================
-- 1. ALTER EXISTING TABLES
-- =============================================

PRINT '1. Altering existing tables...'

-- Add columns to CompetitionPhaseLobbies
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'MatchScheduledAt')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD MatchScheduledAt DATETIME2 NULL;
    PRINT '  ✓ Added MatchScheduledAt to CompetitionPhaseLobbies';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'BestOfGames')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD BestOfGames INT NOT NULL DEFAULT 3 CHECK (BestOfGames IN (1, 3, 5, 7, 9));
    PRINT '  ✓ Added BestOfGames to CompetitionPhaseLobbies';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'RequiredWins')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD RequiredWins INT NOT NULL DEFAULT 2 CHECK (RequiredWins >= 1);
    PRINT '  ✓ Added RequiredWins to CompetitionPhaseLobbies';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'MatchStatus')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD MatchStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending' 
        CHECK (MatchStatus IN ('Pending', 'Scheduled', 'InProgress', 'Completed', 'Cancelled'));
    PRINT '  ✓ Added MatchStatus to CompetitionPhaseLobbies';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'NotificationSentAt')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD NotificationSentAt DATETIME2 NULL;
    PRINT '  ✓ Added NotificationSentAt to CompetitionPhaseLobbies';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'CompletedAt')
BEGIN
    ALTER TABLE CompetitionPhaseLobbies ADD CompletedAt DATETIME2 NULL;
    PRINT '  ✓ Added CompletedAt to CompetitionPhaseLobbies';
END

-- Add PhaseLobbyId to Games table to link games to knockout lobbies
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Games') AND name = 'PhaseLobbyId')
BEGIN
    ALTER TABLE Games ADD PhaseLobbyId UNIQUEIDENTIFIER NULL;
    PRINT '  ✓ Added PhaseLobbyId to Games';
END

-- Add foreign key constraint for Games.PhaseLobbyId
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Games_PhaseLobby')
BEGIN
    ALTER TABLE Games ADD CONSTRAINT FK_Games_PhaseLobby 
        FOREIGN KEY (PhaseLobbyId) REFERENCES CompetitionPhaseLobbies(Id);
    PRINT '  ✓ Added FK_Games_PhaseLobby constraint';
END

-- =============================================
-- 2. CREATE NEW TABLES
-- =============================================

PRINT '2. Creating new tables...'

-- CompetitionPhaseSettings table for phase-specific configurations
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseSettings')
BEGIN
    CREATE TABLE CompetitionPhaseSettings (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionId UNIQUEIDENTIFIER NOT NULL,
        Phase NVARCHAR(40) NOT NULL,
        BestOfGames INT NOT NULL DEFAULT 3 CHECK (BestOfGames IN (1, 3, 5, 7, 9)),
        RequiredWins INT NOT NULL DEFAULT 2 CHECK (RequiredWins >= 1),
        AutoAdvancement BIT NOT NULL DEFAULT 0,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
        UNIQUE(CompetitionId, Phase)
    );
    PRINT '  ✓ Created CompetitionPhaseSettings table';
END

-- MatchNotifications table for tracking email notifications
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MatchNotifications')
BEGIN
    CREATE TABLE MatchNotifications (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        LobbyId UNIQUEIDENTIFIER NOT NULL,
        NotificationType NVARCHAR(20) NOT NULL CHECK (NotificationType IN ('MatchScheduled', 'MatchReminder', 'MatchStarted')),
        SentAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        EmailContent NVARCHAR(MAX) NULL,
        Status NVARCHAR(20) NOT NULL DEFAULT 'Sent' CHECK (Status IN ('Pending', 'Sent', 'Failed')),
        FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id)
    );
    PRINT '  ✓ Created MatchNotifications table';
END

-- =============================================
-- 3. CREATE INDEXES FOR PERFORMANCE
-- =============================================

PRINT '3. Creating indexes...'

-- Index for Games.PhaseLobbyId
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Games_PhaseLobbyId')
BEGIN
    CREATE NONCLUSTERED INDEX IX_Games_PhaseLobbyId ON Games(PhaseLobbyId);
    PRINT '  ✓ Created IX_Games_PhaseLobbyId index';
END

-- Index for CompetitionPhaseLobbies.MatchScheduledAt
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_MatchScheduledAt')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CompetitionPhaseLobbies_MatchScheduledAt ON CompetitionPhaseLobbies(MatchScheduledAt);
    PRINT '  ✓ Created IX_CompetitionPhaseLobbies_MatchScheduledAt index';
END

-- Composite index for CompetitionPhaseLobbies
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_Competition_Phase_Status')
BEGIN
    CREATE NONCLUSTERED INDEX IX_CompetitionPhaseLobbies_Competition_Phase_Status 
        ON CompetitionPhaseLobbies(CompetitionId, Phase, MatchStatus);
    PRINT '  ✓ Created IX_CompetitionPhaseLobbies_Competition_Phase_Status index';
END

-- Index for MatchNotifications
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MatchNotifications_LobbyId_Type')
BEGIN
    CREATE NONCLUSTERED INDEX IX_MatchNotifications_LobbyId_Type 
        ON MatchNotifications(LobbyId, NotificationType);
    PRINT '  ✓ Created IX_MatchNotifications_LobbyId_Type index';
END

-- =============================================
-- 4. INSERT DEFAULT PHASE SETTINGS
-- =============================================

PRINT '4. Setting up default phase configurations...'

-- Insert default phase settings for existing competitions
INSERT INTO CompetitionPhaseSettings (CompetitionId, Phase, BestOfGames, RequiredWins, AutoAdvancement)
SELECT DISTINCT 
    c.Id,
    'Top32' as Phase,
    3 as BestOfGames,
    2 as RequiredWins,
    0 as AutoAdvancement
FROM Competitions c
WHERE NOT EXISTS (
    SELECT 1 FROM CompetitionPhaseSettings cps 
    WHERE cps.CompetitionId = c.Id AND cps.Phase = 'Top32'
);

-- Add other default phases
DECLARE @phases TABLE (Phase NVARCHAR(40), BestOf INT, Required INT)
INSERT INTO @phases VALUES 
    ('Top16', 3, 2),
    ('Top8', 5, 3),
    ('Top4', 5, 3),
    ('Final', 7, 4);

INSERT INTO CompetitionPhaseSettings (CompetitionId, Phase, BestOfGames, RequiredWins, AutoAdvancement)
SELECT DISTINCT 
    c.Id,
    p.Phase,
    p.BestOf,
    p.Required,
    0 as AutoAdvancement
FROM Competitions c
CROSS JOIN @phases p
WHERE NOT EXISTS (
    SELECT 1 FROM CompetitionPhaseSettings cps 
    WHERE cps.CompetitionId = c.Id AND cps.Phase = p.Phase
);

PRINT '  ✓ Inserted default phase settings for existing competitions';

PRINT 'Knockout Competition System Database Setup Complete!'
PRINT ''
PRINT 'Summary of changes:'
PRINT '- Enhanced CompetitionPhaseLobbies with match scheduling and Best-of-N support'
PRINT '- Added PhaseLobbyId to Games table for knockout match tracking'
PRINT '- Created CompetitionPhaseSettings table for phase configurations'
PRINT '- Created MatchNotifications table for email tracking'
PRINT '- Added performance indexes'
PRINT '- Inserted default phase settings'
PRINT ''
PRINT 'Next steps:'
PRINT '1. Run the stored procedures script'
PRINT '2. Update API layer with new DTOs and endpoints'
PRINT '3. Implement frontend admin interfaces'
