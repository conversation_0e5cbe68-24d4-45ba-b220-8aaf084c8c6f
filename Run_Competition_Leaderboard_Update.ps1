# PowerShell script to create the competition leaderboard stored procedure

$serverName = "**************"
$databaseName = "GoldRushThunee"
$username = "EG-Dev"
$password = "Password01?"

# Connection string
$connectionString = "Server=$serverName; Database=$databaseName; User Id=$username; Password=$password; TrustServerCertificate=True;"

Write-Host "Creating Competition Leaderboard Stored Procedure..." -ForegroundColor Green
Write-Host "Database: $serverName\$databaseName" -ForegroundColor Yellow

try {
    # Load SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        Write-Host "Using SqlServer PowerShell module" -ForegroundColor Green
        
        # Run the competition leaderboard script
        Write-Host "Running CREATE_COMPETITION_LEADERBOARD_SP.sql..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ConnectionString $connectionString -InputFile "CREATE_COMPETITION_LEADERBOARD_SP.sql" -Verbose
        
        Write-Host "Competition leaderboard stored procedure created successfully!" -ForegroundColor Green
        Write-Host "SP_GetCompetitionLeaderboard is now available." -ForegroundColor Green
    }
    else {
        Write-Host "SqlServer PowerShell module not found." -ForegroundColor Red
        Write-Host "Please install it with: Install-Module -Name SqlServer" -ForegroundColor Yellow
        Write-Host "Or run the script manually in SSMS: CREATE_COMPETITION_LEADERBOARD_SP.sql" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error creating stored procedure: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run the script manually in SSMS: CREATE_COMPETITION_LEADERBOARD_SP.sql" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "After running this script, restart the ThuneeAPI server to use the new stored procedure." -ForegroundColor Cyan
Write-Host "Press any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
