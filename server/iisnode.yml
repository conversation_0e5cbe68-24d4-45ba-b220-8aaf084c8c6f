# IISNode configuration for Thunee Node.js server

# Node.js application entry point
nodeProcessCommandLine: "C:\Program Files\nodejs\node.exe"

# Environment variables
node_env: production

# Logging
loggingEnabled: true
logDirectory: ../iisnode
debuggingEnabled: false
devErrorsEnabled: false

# Performance settings
maxConcurrentRequestsPerProcess: 1024
maxProcessCountPerApplication: 1
nodeProcessCountPerApplication: 1

# Connection pooling for named pipes
maxNamedPipeConnectionRetry: 100
namedPipeConnectionRetryDelay: 250
maxNamedPipeConnectionPoolSize: 512
maxNamedPipePooledConnectionAge: 30000

# Request handling
asyncCompletionThreadCount: 0
flushResponse: false

# File watching for auto-restart
watchedFiles: "*.js;node_modules\*;handlers\*.js;utils\*.js"

# Security
enableXFF: true
promoteServerVars: "LOGON_USER,AUTH_USER,AUTH_TYPE"

# WebSocket support
interceptor: "index.js"
