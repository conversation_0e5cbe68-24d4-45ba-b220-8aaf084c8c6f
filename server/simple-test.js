console.log('Testing Node.js integration...');

try {
  console.log('Loading services...');
  
  // Test loading services
  const apiService = require('./services/apiService');
  console.log('✅ API Service loaded');
  
  const authService = require('./services/authService');
  console.log('✅ Auth Service loaded');
  
  const gameDataService = require('./services/gameDataService');
  console.log('✅ Game Data Service loaded');
  
  console.log('🎉 All services loaded successfully!');
  
} catch (error) {
  console.error('❌ Error loading services:', error.message);
  console.error(error.stack);
}
