const axios = require('axios');
const https = require('https');

// Load environment-specific configuration
const path = require('path');
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.development';
const envPath = path.join(__dirname, '..', envFile);
require('dotenv').config({ path: envPath });

// Fallback to default .env if specific env file doesn't exist
if (!process.env.API_BASE_URL) {
  require('dotenv').config({ path: path.join(__dirname, '..', '.env') });
}

class ApiService {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'https://localhost:57229';
    this.timeout = parseInt(process.env.API_TIMEOUT) || 30000; // Increased to 30 seconds for database operations

    console.log(`[API] Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`[API] Base URL: ${this.baseURL}`);
    console.log(`[API] API_BASE_URL from env: ${process.env.API_BASE_URL}`);
    console.log(`[API] Using HTTPS Agent: ${this.baseURL.startsWith('https://localhost')}`);
    
    // Create axios instance with default configuration
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
      // For development with self-signed certificates
      httpsAgent: this.baseURL.startsWith('https://localhost') ?
        new https.Agent({ rejectUnauthorized: false }) : undefined
    });

    console.log(`[API] SSL Certificate validation: ${this.baseURL.startsWith('https://localhost') ? 'DISABLED (development)' : 'ENABLED (production)'}`);

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error.message);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API] ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`[API] Response error:`, {
          url: error.config?.url,
          status: error.response?.status,
          message: error.response?.data?.message || error.message
        });
        return Promise.reject(error);
      }
    );
  }

  // Set authorization token for authenticated requests
  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.client.defaults.headers.common['Authorization'];
    }
  }

  // Validate user token with the API
  async validateToken(token) {
    try {
      const response = await this.client.get('/api/Auth/validate', {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data;
    } catch (error) {
      console.error('[API] Token validation failed:', error.message);
      return null;
    }
  }

  // Get user information by token
  async getUserInfo(token) {
    try {
      console.log(`[API] GET /api/Auth/me`);
      console.log(`[API] Token: ${token ? token.substring(0, 20) + '...' : 'null'}`);

      const response = await this.client.get('/api/Auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log(`[API] Response status: ${response.status}`);
      console.log(`[API] Response data:`, response.data);

      return response.data.data;
    } catch (error) {
      console.log(`[API] Response error: {`);
      console.log(`  url: '/api/Auth/me',`);
      console.log(`  status: ${error.response?.status || 'unknown'},`);
      console.log(`  message: '${error.message}'`);
      console.log(`}`);

      // Don't log 401 errors as they're expected for invalid/expired tokens
      if (error.response?.status === 401) {
        console.log('[API] Token validation failed - user needs to authenticate');
      } else {
        console.error('[API] Failed to get user info:', error.message);
      }
      return null;
    }
  }

  // Create a new game session
  async createGame(gameData, hostToken) {
    try {
      console.log(`[API] Creating game:`, {
        lobbyCode: gameData.lobbyCode,
        team1Name: gameData.team1Name,
        competitionId: gameData.competitionId
      });

      const response = await this.client.post('/api/Games', gameData, {
        headers: { Authorization: `Bearer ${hostToken}` }
      });

      console.log(`[API] ✅ Game created successfully: ${gameData.lobbyCode} -> ID: ${response.data.data?.id}`);
      return response.data.data;
    } catch (error) {
      console.error(`[API] ❌ Failed to create game ${gameData.lobbyCode}:`, error.message);
      if (error.response) {
        console.error(`[API] Response status: ${error.response.status}`);
        console.error(`[API] Response data:`, error.response.data);
      }
      console.error(`[API] Game data sent:`, gameData);
      throw error;
    }
  }

  // Join an existing game
  async joinGame(lobbyCode, playerData, playerToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/join`, playerData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join game:', error.message);
      throw error;
    }
  }

  // Start a game
  // async startGame(lobbyCode, hostToken) {
  //   try {
  //     const response = await this.client.post(`/api/Games/${lobbyCode}/start`, {}, {
  //       headers: { Authorization: `Bearer ${hostToken}` }
  //     });
  //     return response.data.data;
  //   } catch (error) {
  //     console.error('[API] Failed to start game:', error.message);
  //     throw error;
  //   }
  // }

  // Record a hand result
  async recordHandResult(lobbyCode, handData, playerToken) {
    try {
      console.log(`[API] Recording hand result for ${lobbyCode}:`, {
        handNumber: handData.handNumber,
        ballNumber: handData.ballNumber,
        winnerPlayerId: handData.winnerPlayerId,
        points: handData.points
      });

      const response = await this.client.post(`/api/Games/${lobbyCode}/hand-result`, handData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });

      console.log(`[API] ✅ Hand result recorded successfully for ${lobbyCode}`);
      return response.data.data;
    } catch (error) {
      console.error(`[API] ❌ Failed to record hand result for ${lobbyCode}:`, error.message);
      if (error.response) {
        console.error(`[API] Response status: ${error.response.status}`);
        console.error(`[API] Response data:`, error.response.data);

        // If it's a 401 error, the token might be expired
        if (error.response.status === 401) {
          console.error(`[API] 🔑 Authentication failed for hand result - token may be expired`);
        }
      }
      console.error(`[API] Hand data sent:`, handData);
      // Don't throw error for hand recording to avoid disrupting gameplay
      return null;
    }
  }

  // Record a ball result
  async recordBallResult(lobbyCode, ballData, playerToken) {
    try {
      console.log(`[API] Recording ball result for ${lobbyCode}:`, {
        ballNumber: ballData.ballNumber,
        winnerTeam: ballData.winnerTeam,
        competitionId: ballData.competitionId,
        handsCount: ballData.hands?.length || 0
      });

      // Ensure the data is properly formatted as JSON
      const requestData = {
        lobbyCode: ballData.lobbyCode,
        competitionId: ballData.competitionId,
        ballNumber: ballData.ballNumber,
        winnerTeam: ballData.winnerTeam,
        team1Score: ballData.team1Score,
        team2Score: ballData.team2Score,
        team1BallsWon: ballData.team1BallsWon,
        team2BallsWon: ballData.team2BallsWon,
        team1Player1Id: ballData.team1Player1Id,
        team1Player2Id: ballData.team1Player2Id,
        team2Player1Id: ballData.team2Player1Id,
        team2Player2Id: ballData.team2Player2Id,
        team1Name: ballData.team1Name,
        team2Name: ballData.team2Name,
        trumpSuit: ballData.trumpSuit,
        hasThuneeDouble: ballData.hasThuneeDouble,
        hasKhanka: ballData.hasKhanka,
        specialCallType: ballData.specialCallType,
        specialCallResult: ballData.specialCallResult,
        hands: ballData.hands || []
      };

      console.log(`[API] Sending ball result data:`, JSON.stringify(requestData, null, 2));

      const response = await this.client.post(`/api/Games/${lobbyCode}/ball-result`, requestData, {
        headers: {
          'Authorization': `Bearer ${playerToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`[API] ✅ Ball result recorded successfully for ${lobbyCode}`);
      return response.data.data;
    } catch (error) {
      console.error(`[API] ❌ Failed to record ball result for ${lobbyCode}:`, error.message);
      if (error.response) {
        console.error(`[API] Response status: ${error.response.status}`);
        console.error(`[API] Response data:`, JSON.stringify(error.response.data, null, 2));
      }
      console.error(`[API] Ball data sent:`, JSON.stringify(ballData, null, 2));
      // Don't throw error for ball recording to avoid disrupting gameplay
      return null;
    }
  }

  // Card play recording removed - no longer tracking individual cards

  // Record game result
  async recordGameResult(lobbyCode, gameResultData, playerToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/game-result`, gameResultData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record game result:', error.message);
      throw error;
    }
  }

  // Jordhi call recording removed - no longer tracking individual calls

  // Get ball scores for a game
  async getBallScores(lobbyCode, playerToken) {
    try {
      console.log(`[API] Getting ball scores for lobby code: ${lobbyCode}`);

      const response = await this.client.get(`/api/Games/${lobbyCode}/ball-scores`, {
        headers: {
          'Authorization': `Bearer ${playerToken}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`[API] ✅ Ball scores retrieved successfully for ${lobbyCode}`);
      return response.data.data;
    } catch (error) {
      console.error(`[API] ❌ Failed to get ball scores for ${lobbyCode}:`, error.message);
      if (error.response) {
        console.error(`[API] Response status: ${error.response.status}`);
        console.error(`[API] Response data:`, JSON.stringify(error.response.data, null, 2));
      }
      return { team1: 0, team2: 0 }; // Return default scores on error
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      console.error('[API] Health check failed:', error.message);
      return null;
    }
  }

  // Game Settings methods
  async getGameSettings() {
    try {
      const response = await this.client.get('/api/gamesettings');
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get game settings:', error.message);
      throw error; // Re-throw the error instead of using fallback data
    }
  }

  async updateGameSettings(settings) {
    try {
      const response = await this.client.put('/api/gamesettings', settings);
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to update game settings:', error.message);
      throw error;
    }
  }

  async resetGameSettings() {
    try {
      const response = await this.client.post('/api/gamesettings/reset');
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to reset game settings:', error.message);
      throw error;
    }
  }

  // Competition-related methods

  // Get all competitions
  async getCompetitions(token) {
    try {
      const response = await this.client.get('/api/competitions', {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competitions:', error.message);
      return null;
    }
  }

  // Get competition by ID
  async getCompetition(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition:', error.message);
      return null;
    }
  }

  // Create competition team
  async createCompetitionTeam(competitionId, teamData, token) {
    try {
      const response = await this.client.post(`/api/competitions/${competitionId}/teams/create`, teamData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to create competition team:', error.message);
      throw error;
    }
  }

  // Join competition team using invite code
  async joinCompetitionTeam(inviteCode, token) {
    try {
      const response = await this.client.post('/api/competitions/teams/join', { inviteCode }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join competition team:', error.message);
      throw error;
    }
  }

  // Get competition status for user
  async getCompetitionStatus(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/status`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition status:', error.message);
      return null;
    }
  }

  // Join a competition (legacy)
  async joinCompetition(competitionId, joinData, token) {
    try {
      const response = await this.client.post(`/api/competitions/${competitionId}/join`, joinData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join competition:', error.message);
      throw error;
    }
  }

  // Get competition leaderboard
  async getCompetitionLeaderboard(competitionId, token, page = 1, pageSize = 20) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/leaderboard`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { page, pageSize }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition leaderboard:', error.message);
      return null;
    }
  }

  // Get competition teams
  async getCompetitionTeams(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/teams`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition teams:', error.message);
      return null;
    }
  }

  // Check if user is in competition
  async isUserInCompetition(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/user-status`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to check user competition status:', error.message);
      return null;
    }
  }

  // Record competition game result (separate from regular games)
  async recordCompetitionGameResult(competitionGameData, token) {
    try {
      console.log(`[API] Recording competition game result:`, {
        competitionId: competitionGameData.competitionId,
        team1Id: competitionGameData.team1Id,
        team2Id: competitionGameData.team2Id,
        winnerTeam: competitionGameData.winnerTeam
      });

      // Try sending the data wrapped in a gameResult object
      const wrappedData = { gameResult: competitionGameData };
      console.log(`[API] Sending competition game data wrapped:`, wrappedData);

      try {
        const response = await this.client.post('/api/competitions/games/result', wrappedData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log(`[API] ✅ Competition game result recorded successfully`);
        console.log(`[API] Response status: ${response.status}`);
        console.log(`[API] Response data:`, response.data);

        return response.data.data;
      } catch (wrappedError) {
        console.error('[API] ❌ Failed with wrapped data, trying direct approach:', wrappedError.message);

        // If wrapped approach fails, try direct approach
        const directResponse = await this.client.post('/api/competitions/games/result', competitionGameData, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        console.log(`[API] ✅ Competition game result recorded successfully with direct approach`);
        console.log(`[API] Response status: ${directResponse.status}`);
        console.log(`[API] Response data:`, directResponse.data);

        return directResponse.data.data;
      }
    } catch (error) {
      console.error('[API] ❌ Failed to record competition game result:', error.message);
      if (error.response) {
        console.error(`[API] Response status: ${error.response.status}`);
        console.error(`[API] Response data:`, error.response.data);
      }
      console.error(`[API] Competition game data sent:`, competitionGameData);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new ApiService();
