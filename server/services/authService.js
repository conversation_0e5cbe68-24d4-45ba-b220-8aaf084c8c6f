const apiService = require('./apiService');

class AuthService {
  constructor() {
    // Store authenticated users: socketId -> { user, token, lastActivity }
    this.authenticatedUsers = new Map();
    
    // Store user tokens: userId -> token (for API calls)
    this.userTokens = new Map();
  }

  // Authenticate a user when they connect
  async authenticateUser(socketId, token) {
    try {
      console.log(`[AUTH] Authenticating socket ${socketId}`);
      console.log(`[AUTH] Token provided: ${token ? 'yes' : 'no'}`);

      if (!token || token === '' || token === 'undefined' || token === 'null') {
        console.log(`[AUTH] No valid token provided for socket ${socketId}`);
        return null;
      }

      console.log(`[AUTH] Validating token with API for socket ${socketId}`);

      // Validate token with API
      const userInfo = await apiService.getUserInfo(token);
      if (!userInfo) {
        console.log(`[AUTH] Invalid token for socket ${socketId}`);
        return null;
      }

      console.log(`[AUTH] Token validation successful for socket ${socketId}, user: ${userInfo.username}`);

      // Store authenticated user
      const authData = {
        user: userInfo,
        token: token,
        lastActivity: new Date(),
        socketId: socketId
      };

      this.authenticatedUsers.set(socketId, authData);
      this.userTokens.set(userInfo.id, token);

      console.log(`[AUTH] User authenticated: ${userInfo.username} (${socketId})`);
      return userInfo;
    } catch (error) {
      console.error(`[AUTH] Authentication error for socket ${socketId}:`, error.message);
      return null;
    }
  }

  // Get authenticated user by socket ID
  getAuthenticatedUser(socketId) {
    const authData = this.authenticatedUsers.get(socketId);
    if (authData) {
      // Update last activity
      authData.lastActivity = new Date();
      return authData.user;
    }
    return null;
  }

  // Get user token by socket ID
  getUserToken(socketId) {
    const authData = this.authenticatedUsers.get(socketId);
    return authData ? authData.token : null;
  }

  // Get user token by user ID
  getUserTokenById(userId) {
    return this.userTokens.get(userId);
  }

  // Check if user is authenticated
  isAuthenticated(socketId) {
    return this.authenticatedUsers.has(socketId);
  }

  // Remove user authentication (on disconnect)
  removeAuthentication(socketId) {
    const authData = this.authenticatedUsers.get(socketId);
    if (authData) {
      this.userTokens.delete(authData.user.id);
      this.authenticatedUsers.delete(socketId);
      console.log(`[AUTH] Removed authentication for socket ${socketId}`);
    }
  }

  // Get all authenticated users
  getAllAuthenticatedUsers() {
    return Array.from(this.authenticatedUsers.values()).map(auth => auth.user);
  }

  // Clean up inactive users (call periodically)
  cleanupInactiveUsers(maxInactiveMinutes = 30) {
    const now = new Date();
    const maxInactiveMs = maxInactiveMinutes * 60 * 1000;

    for (const [socketId, authData] of this.authenticatedUsers.entries()) {
      const inactiveMs = now - authData.lastActivity;
      if (inactiveMs > maxInactiveMs) {
        console.log(`[AUTH] Cleaning up inactive user: ${authData.user.username} (${socketId})`);
        this.removeAuthentication(socketId);
      }
    }
  }

  // Validate that a user can perform an action (additional authorization checks)
  canUserPerformAction(socketId, action, resourceId = null) {
    const user = this.getAuthenticatedUser(socketId);
    if (!user) {
      return false;
    }

    // Add specific authorization logic here if needed
    // For now, all authenticated users can perform all actions
    return true;
  }

  // Get user statistics for display
  getAuthStats() {
    return {
      totalAuthenticatedUsers: this.authenticatedUsers.size,
      users: Array.from(this.authenticatedUsers.values()).map(auth => ({
        username: auth.user.username,
        socketId: auth.socketId,
        lastActivity: auth.lastActivity
      }))
    };
  }
}

// Export singleton instance
module.exports = new AuthService();
