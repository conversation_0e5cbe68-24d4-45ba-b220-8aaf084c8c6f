const apiService = require('./apiService');
const authService = require('./authService');

class CompetitionService {
  constructor() {
    // Store competition data: competitionId -> competition details
    this.competitions = new Map();
    
    // Store user competition memberships: userId -> { competitionId, teamId, teamName, gamesPlayed, maxGames }
    this.userCompetitions = new Map();
    
    // Store competition teams: competitionId -> Map(teamId -> team details)
    this.competitionTeams = new Map();
    
    // Store competition match queues: competitionId -> Set(lobbyCode)
    this.competitionMatchQueues = new Map();
    
    // Cache for competition leaderboards
    this.leaderboardCache = new Map();
    this.leaderboardCacheExpiry = new Map();
    
    console.log('[COMPETITION] Competition service initialized');
  }

  // Get all available competitions
  async getCompetitions(socketId) {
    try {
      const token = authService.getUserToken(socketId);
      if (!token) {
        throw new Error('Authentication required');
      }

      const competitions = await apiService.getCompetitions(token);
      if (competitions) {
        // Cache competitions
        competitions.forEach(comp => {
          this.competitions.set(comp.id, comp);
        });
      }
      
      return competitions;
    } catch (error) {
      console.error('[COMPETITION] Failed to get competitions:', error.message);
      throw error;
    }
  }

  // Get competition details by ID
  async getCompetition(competitionId, socketId) {
    try {
      const token = authService.getUserToken(socketId);
      if (!token) {
        throw new Error('Authentication required');
      }

      // Check cache first
      if (this.competitions.has(competitionId)) {
        return this.competitions.get(competitionId);
      }

      const competition = await apiService.getCompetition(competitionId, token);
      if (competition) {
        this.competitions.set(competitionId, competition);
      }
      
      return competition;
    } catch (error) {
      console.error('[COMPETITION] Failed to get competition:', error.message);
      throw error;
    }
  }

  // Join a competition
  async joinCompetition(competitionId, joinData, socketId) {
    try {
      const user = authService.getAuthenticatedUser(socketId);
      const token = authService.getUserToken(socketId);
      
      if (!user || !token) {
        throw new Error('Authentication required');
      }

      // Call API to join competition
      const teamData = await apiService.joinCompetition(competitionId, joinData, token);
      
      if (teamData) {
        // Store user competition membership
        this.userCompetitions.set(user.id, {
          competitionId,
          teamId: teamData.id,
          teamName: teamData.teamName,
          gamesPlayed: teamData.gamesPlayed || 0,
          maxGames: teamData.maxGames || 10,
          partnerId: teamData.player2Id,
          partnerName: teamData.player2Name
        });

        // Store team in competition teams
        if (!this.competitionTeams.has(competitionId)) {
          this.competitionTeams.set(competitionId, new Map());
        }
        this.competitionTeams.get(competitionId).set(teamData.id, teamData);

        console.log(`[COMPETITION] User ${user.username} joined competition ${competitionId} with team ${teamData.teamName}`);
      }
      
      return teamData;
    } catch (error) {
      console.error('[COMPETITION] Failed to join competition:', error.message);
      throw error;
    }
  }

  // Get user's competition status
  getUserCompetitionStatus(socketId) {
    const user = authService.getAuthenticatedUser(socketId);
    if (!user) {
      return null;
    }

    return this.userCompetitions.get(user.id) || null;
  }

  // Check if user is in a specific competition
  isUserInCompetition(competitionId, socketId) {
    const status = this.getUserCompetitionStatus(socketId);
    return status && status.competitionId === competitionId;
  }

  // Get competition leaderboard with caching
  async getCompetitionLeaderboard(competitionId, socketId, page = 1, pageSize = 20) {
    try {
      const token = authService.getUserToken(socketId);
      if (!token) {
        throw new Error('Authentication required');
      }

      const cacheKey = `${competitionId}_${page}_${pageSize}`;
      const now = Date.now();
      
      // Check cache (expire after 30 seconds)
      if (this.leaderboardCache.has(cacheKey) && 
          this.leaderboardCacheExpiry.has(cacheKey) &&
          this.leaderboardCacheExpiry.get(cacheKey) > now) {
        return this.leaderboardCache.get(cacheKey);
      }

      const leaderboard = await apiService.getCompetitionLeaderboard(competitionId, token, page, pageSize);
      
      if (leaderboard) {
        // Cache the result
        this.leaderboardCache.set(cacheKey, leaderboard);
        this.leaderboardCacheExpiry.set(cacheKey, now + 30000); // 30 seconds
      }
      
      return leaderboard;
    } catch (error) {
      console.error('[COMPETITION] Failed to get leaderboard:', error.message);
      throw error;
    }
  }

  // Add lobby to competition match queue
  addToCompetitionMatchQueue(competitionId, lobbyCode) {
    if (!this.competitionMatchQueues.has(competitionId)) {
      this.competitionMatchQueues.set(competitionId, new Set());
    }
    
    this.competitionMatchQueues.get(competitionId).add(lobbyCode);
    console.log(`[COMPETITION] Added lobby ${lobbyCode} to competition ${competitionId} match queue`);
  }

  // Remove lobby from competition match queue
  removeFromCompetitionMatchQueue(competitionId, lobbyCode) {
    if (this.competitionMatchQueues.has(competitionId)) {
      this.competitionMatchQueues.get(competitionId).delete(lobbyCode);
      console.log(`[COMPETITION] Removed lobby ${lobbyCode} from competition ${competitionId} match queue`);
    }
  }

  // Get competition match queue
  getCompetitionMatchQueue(competitionId) {
    return this.competitionMatchQueues.get(competitionId) || new Set();
  }

  // Find match within competition
  findCompetitionMatch(competitionId, lobbyCode, lobbies) {
    const queue = this.getCompetitionMatchQueue(competitionId);
    const queueArray = Array.from(queue);
    
    console.log(`[COMPETITION] Looking for match in competition ${competitionId}, queue size: ${queueArray.length}`);
    
    // Need at least 2 lobbies for a match
    if (queueArray.length < 2) {
      return null;
    }

    // Find the requesting lobby
    const requestingLobby = lobbies.get(lobbyCode);
    if (!requestingLobby) {
      return null;
    }

    // Look for another lobby in the same competition
    for (const otherLobbyCode of queueArray) {
      if (otherLobbyCode === lobbyCode) continue;
      
      const otherLobby = lobbies.get(otherLobbyCode);
      if (!otherLobby) {
        // Clean up invalid lobby
        this.removeFromCompetitionMatchQueue(competitionId, otherLobbyCode);
        continue;
      }

      // Check if both lobbies are ready for competition match
      if (otherLobby.competitionId === competitionId &&
          otherLobby.isFindingMatch &&
          otherLobby.teamReady[1] && 
          otherLobby.teams[1].length === 2 &&
          requestingLobby.teamReady[1] && 
          requestingLobby.teams[1].length === 2) {
        
        console.log(`[COMPETITION] Match found between ${lobbyCode} and ${otherLobbyCode} in competition ${competitionId}`);
        
        // Remove both from queue
        this.removeFromCompetitionMatchQueue(competitionId, lobbyCode);
        this.removeFromCompetitionMatchQueue(competitionId, otherLobbyCode);
        
        return {
          matchedLobby: otherLobbyCode,
          lobby: otherLobby
        };
      }
    }

    return null;
  }

  // Update user's game count after completing a game
  updateUserGameCount(socketId, increment = 1) {
    const user = authService.getAuthenticatedUser(socketId);
    if (!user) {
      return false;
    }

    const status = this.userCompetitions.get(user.id);
    if (status) {
      status.gamesPlayed += increment;
      console.log(`[COMPETITION] Updated game count for user ${user.username}: ${status.gamesPlayed}/${status.maxGames}`);
      return true;
    }

    return false;
  }

  // Check if user can play more games in competition
  canUserPlayMoreGames(socketId) {
    const status = this.getUserCompetitionStatus(socketId);
    if (!status) {
      return false;
    }

    return status.gamesPlayed < status.maxGames;
  }

  // Clear cache
  clearCache() {
    this.leaderboardCache.clear();
    this.leaderboardCacheExpiry.clear();
    console.log('[COMPETITION] Cache cleared');
  }

  // Get service statistics
  getStats() {
    return {
      cachedCompetitions: this.competitions.size,
      userCompetitions: this.userCompetitions.size,
      competitionTeams: this.competitionTeams.size,
      activeMatchQueues: this.competitionMatchQueues.size,
      leaderboardCacheSize: this.leaderboardCache.size
    };
  }
}

// Export singleton instance
module.exports = new CompetitionService();
