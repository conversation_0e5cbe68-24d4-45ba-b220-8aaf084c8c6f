const apiService = require('./apiService');
const authService = require('./authService');

class GameDataService {
  constructor() {
    // Track games that have been saved to API: lobbyCode -> gameId
    this.savedGames = new Map();
    
    // Queue for failed API calls to retry later
    this.retryQueue = [];
    
    // Start retry processor
    this.startRetryProcessor();
  }

  // Save game creation to API
  async saveGameCreation(lobby, hostSocketId) {
    try {
      const hostToken = authService.getUserToken(hostSocketId);
      if (!hostToken) {
        console.error(`[GAME_DATA] ❌ No token found for host ${hostSocketId}`);
        return null;
      }

      console.log(`[GAME_DATA] Creating game for lobby ${lobby.lobbyCode} with host ${hostSocketId}`);

      const gameData = {
        lobbyCode: lobby.lobbyCode, // Pass the lobby code from Node.js server
        team1Name: lobby.teamNames?.[1] || 'Team 1',
        competitionId: lobby.competitionId || null,
        isCompetitionGame: !!lobby.competitionId,
        competitionGameNumber: lobby.competitionGameNumber || null
      };

      console.log(`[GAME_DATA] Game data to send:`, gameData);

      const savedGame = await apiService.createGame(gameData, hostToken);
      if (savedGame) {
        this.savedGames.set(lobby.lobbyCode, savedGame.id);
        console.log(`[GAME_DATA] ✅ Game created in API: ${lobby.lobbyCode} -> ${savedGame.id} ${lobby.competitionId ? `(Competition: ${lobby.competitionId})` : ''}`);
      } else {
        console.error(`[GAME_DATA] ❌ Game creation returned null for ${lobby.lobbyCode}`);
      }

      return savedGame;
    } catch (error) {
      console.error(`[GAME_DATA] ❌ Failed to save game creation for ${lobby.lobbyCode}:`, error.message);
      if (error.response) {
        console.error(`[GAME_DATA] API Response status: ${error.response.status}`);
        console.error(`[GAME_DATA] API Response data:`, error.response.data);
      }
      this.addToRetryQueue('saveGameCreation', { lobby, hostSocketId });
      return null;
    }
  }

  // Save player joining game
  async savePlayerJoin(lobby, playerSocketId, playerName) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      const joinData = {
        playerName: playerName
      };

      const result = await apiService.joinGame(lobby.lobbyCode, joinData, playerToken);
      console.log(`[GAME_DATA] Player joined game in API: ${playerName} -> ${lobby.lobbyCode}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save player join:', error.message);
      this.addToRetryQueue('savePlayerJoin', { lobby, playerSocketId, playerName });
      return null;
    }
  }

  // Save game start
  async saveGameStart(lobby, hostSocketId) {
    try {
      const hostToken = authService.getUserToken(hostSocketId);
      if (!hostToken) {
        console.error('[GAME_DATA] No token found for host');
        return null;
      }

      // Note: startGame API endpoint might not exist, so we'll just log for now
      console.log(`[GAME_DATA] Game start logged for: ${lobby.lobbyCode}`);
      // const result = await apiService.startGame(lobby.lobbyCode, hostToken);
      // console.log(`[GAME_DATA] Game started in API: ${lobby.lobbyCode}`);
      return true; // Return success for now
    } catch (error) {
      console.error('[GAME_DATA] Failed to save game start:', error.message);
      this.addToRetryQueue('saveGameStart', { lobby, hostSocketId });
      return null;
    }
  }

  // Save ball result
  async saveBallResult(lobby, ballData, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      // Check if game exists in our saved games map
      const gameId = this.savedGames.get(lobby.lobbyCode);
      if (!gameId) {
        console.error(`[GAME_DATA] ❌ Game ${lobby.lobbyCode} not found in saved games. Game may not have been created in API.`);
        console.error(`[GAME_DATA] Available saved games:`, Array.from(this.savedGames.keys()));

        // For merged lobbies, try to find any game that has the players
        console.log(`[GAME_DATA] Attempting to find alternative game for merged lobby ${lobby.lobbyCode}`);
        const playerIds = lobby.players?.map(p => p.userId).filter(id => id) || [];
        console.log(`[GAME_DATA] Players in lobby: ${playerIds.join(', ')}`);

        // For now, skip ball result saving if no game found
        // TODO: Implement proper game merging in API
        return null;
      }

      console.log(`[GAME_DATA] Saving ball result for game ${lobby.lobbyCode} (API ID: ${gameId})`);

      // Extract team player information from lobby
      const team1Players = lobby.players?.filter(p => p.team === 1) || [];
      const team2Players = lobby.players?.filter(p => p.team === 2) || [];

      // Construct ball result data in the exact format expected by the API
      const ballResultData = {
        lobbyCode: lobby.lobbyCode,
        competitionId: lobby.competitionId || null,
        ballNumber: ballData.ballNumber || lobby.currentBall || 1,
        winnerTeam: ballData.winnerTeam,
        team1Score: ballData.team1Score || 0,
        team2Score: ballData.team2Score || 0,
        team1BallsWon: ballData.team1BallsWon || 0,
        team2BallsWon: ballData.team2BallsWon || 0,
        // Team 1 Players - use userId (GUID) instead of socket ID
        team1Player1Id: team1Players[0]?.userId || null,
        team1Player2Id: team1Players[1]?.userId || null,
        // Team 2 Players - use userId (GUID) instead of socket ID
        team2Player1Id: team2Players[0]?.userId || null,
        team2Player2Id: team2Players[1]?.userId || null,
        // Team Names
        team1Name: lobby.teamNames?.[1] || ballData.team1Name || 'Team 1',
        team2Name: lobby.teamNames?.[2] || ballData.team2Name || 'Team 2',
        trumpSuit: ballData.trumpSuit || lobby.gameState?.trumpSuit || null,
        hasThuneeDouble: Boolean(ballData.hasThuneeDouble),
        hasKhanka: Boolean(ballData.hasKhanka),
        specialCallType: ballData.specialCallType || null,
        specialCallResult: ballData.specialCallResult || null
        // hands removed - no longer tracking individual card data in ball results
      };

      // Log the data being sent for debugging
      console.log(`[GAME_DATA] Ball result data being sent to API:`, JSON.stringify(ballResultData, null, 2));

      const result = await apiService.recordBallResult(lobby.lobbyCode, ballResultData, playerToken);
      console.log(`[GAME_DATA] Ball result saved for ${lobby.lobbyCode}: Ball ${ballResultData.ballNumber}, Winner: Team ${ballResultData.winnerTeam}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save ball result:', error.message);
      this.addToRetryQueue('saveBallResult', { lobby, ballData, playerSocketId });
      return null;
    }
  }

  // Save hand result
  async saveHandResult(lobby, handData, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      // Check if game exists in our saved games map
      const gameId = this.savedGames.get(lobby.lobbyCode);
      if (!gameId) {
        console.error(`[GAME_DATA] ❌ Game ${lobby.lobbyCode} not found in saved games. Game may not have been created in API.`);
        console.error(`[GAME_DATA] Available saved games:`, Array.from(this.savedGames.keys()));
        return null;
      }

      console.log(`[GAME_DATA] Saving hand result for game ${lobby.lobbyCode} (API ID: ${gameId})`);

      // Find the winner player to get their userId (GUID)
      const winnerPlayer = lobby.players?.find(p => p.id === handData.winner?.id);
      const winnerUserId = winnerPlayer?.userId;

      if (!winnerUserId) {
        console.error('[GAME_DATA] Could not find winner userId for hand result');
        console.error('[GAME_DATA] Winner data:', handData.winner);
        console.error('[GAME_DATA] Available players:', lobby.players?.map(p => ({ id: p.id, userId: p.userId, name: p.name })));
        return null;
      }

      console.log(`[GAME_DATA_SERVICE] 🔍 handData.cards count: ${(handData.cards || []).length}`);
      console.log(`[GAME_DATA_SERVICE] 🔍 handData.cards:`, handData.cards);

      const handResultData = {
        lobbyCode: lobby.lobbyCode,
        handNumber: handData.handNumber || handData.handId || lobby.currentHandId || lobby.currentHand || 1,
        ballNumber: handData.ballNumber || lobby.currentBall || 1,
        winnerPlayerId: winnerUserId,  // Use actual user GUID instead of socket ID
        points: handData.points || 0
        // playedCards removed - no longer tracking individual cards
      };

      console.log(`[GAME_DATA_SERVICE] 🔍 Mapped playedCards:`, handResultData.playedCards);

      const result = await apiService.recordHandResult(lobby.lobbyCode, handResultData, playerToken);
      console.log(`[GAME_DATA] Hand result saved: ${lobby.lobbyCode} Hand ${handResultData.handNumber}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save hand result:', error.message);
      this.addToRetryQueue('saveHandResult', { lobby, handData, playerSocketId });
      return null;
    }
  }

  // Card play saving removed - no longer tracking individual cards

  // Jordhi call saving removed - no longer tracking individual calls

  // Save game result (when game ends) - now includes automatic team statistics updates
  async saveGameResult(lobby, gameResult, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      // Format data according to API specification
      const gameResultData = {
        lobbyCode: lobby.lobbyCode,
        winnerTeam: gameResult.winnerTeam || gameResult.winner,
        team1FinalScore: gameResult.team1FinalScore || gameResult.ballScores?.team1 || gameResult.scores?.team1 || 0,
        team2FinalScore: gameResult.team2FinalScore || gameResult.ballScores?.team2 || gameResult.scores?.team2 || 0
      };

      const result = await apiService.recordGameResult(lobby.lobbyCode, gameResultData, playerToken);
      console.log(`[GAME_DATA] Game result saved: ${lobby.lobbyCode} - Team ${gameResultData.winnerTeam} won with scores ${gameResultData.team1FinalScore}-${gameResultData.team2FinalScore}`);
      
      // Remove from saved games map
      this.savedGames.delete(lobby.lobbyCode);
      
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save game result:', error.message);
      this.addToRetryQueue('saveGameResult', { lobby, gameResult, playerSocketId });
      return null;
    }
  }

  // Add failed operation to retry queue
  addToRetryQueue(operation, data) {
    this.retryQueue.push({
      operation,
      data,
      timestamp: new Date(),
      attempts: 0,
      maxAttempts: 3
    });
  }

  // Process retry queue
  async processRetryQueue() {
    if (this.retryQueue.length === 0) return;

    const now = new Date();
    const retryDelay = 30000; // 30 seconds

    for (let i = this.retryQueue.length - 1; i >= 0; i--) {
      const item = this.retryQueue[i];
      
      // Skip if not enough time has passed
      if (now - item.timestamp < retryDelay) continue;
      
      // Remove if max attempts reached
      if (item.attempts >= item.maxAttempts) {
        console.log(`[GAME_DATA] Giving up on retry: ${item.operation} after ${item.attempts} attempts`);
        this.retryQueue.splice(i, 1);
        continue;
      }

      // Attempt retry
      item.attempts++;
      console.log(`[GAME_DATA] Retrying operation: ${item.operation} (attempt ${item.attempts})`);
      
      try {
        let success = false;
        switch (item.operation) {
          case 'saveGameCreation':
            success = await this.saveGameCreation(item.data.lobby, item.data.hostSocketId);
            break;
          case 'savePlayerJoin':
            success = await this.savePlayerJoin(item.data.lobby, item.data.playerSocketId, item.data.playerName);
            break;
          case 'saveGameStart':
            success = await this.saveGameStart(item.data.lobby, item.data.hostSocketId);
            break;
          case 'saveHandResult':
            success = await this.saveHandResult(item.data.lobby, item.data.handData, item.data.playerSocketId);
            break;
          case 'saveJordhiCall':
            success = await this.saveJordhiCall(item.data.lobby, item.data.jordhiData, item.data.playerSocketId);
            break;
          case 'saveGameResult':
            success = await this.saveGameResult(item.data.lobby, item.data.gameResult, item.data.playerSocketId);
            break;
        }

        if (success) {
          console.log(`[GAME_DATA] Retry successful: ${item.operation}`);
          this.retryQueue.splice(i, 1);
        }
      } catch (error) {
        console.error(`[GAME_DATA] Retry failed: ${item.operation}`, error.message);
      }
    }
  }

  // Start retry processor (runs every 30 seconds)
  startRetryProcessor() {
    setInterval(() => {
      this.processRetryQueue();
    }, 30000);
  }

  // Save competition game result
  async saveCompetitionGameResult(lobby, gameResult, playerSocketId) {
    try {
      console.log(`[GAME_DATA] Starting competition game result save for lobby ${lobby.lobbyCode}`);
      console.log(`[GAME_DATA] Competition ID: ${lobby.competitionId}`);
      console.log(`[GAME_DATA] Game result data:`, gameResult);

      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      // Import competition utils for scoring calculation
      const competitionUtils = require('../utils/competitionUtils');
      const competitionService = require('./competitionService');

      console.log(`[GAME_DATA] Formatting competition game result...`);
      const competitionGameData = competitionUtils.formatCompetitionGameResult(lobby, gameResult);

      console.log(`[GAME_DATA] Formatted competition game data:`, competitionGameData);

      // Validate that we have required team IDs
      if (!competitionGameData.team1Id || !competitionGameData.team2Id) {
        console.error(`[GAME_DATA] ❌ Missing team IDs - cannot save competition game result`);
        console.error(`[GAME_DATA] Team1ID: ${competitionGameData.team1Id}, Team2ID: ${competitionGameData.team2Id}`);
        return null;
      }

      console.log(`[GAME_DATA] Calling competition API endpoint...`);
      // Use the competition-specific endpoint instead of regular game result endpoint
      const result = await apiService.recordCompetitionGameResult(competitionGameData, playerToken);

      if (result) {
        console.log(`[GAME_DATA] ✅ Competition game result saved successfully: ${lobby.lobbyCode} in competition ${lobby.competitionId}`);
        console.log(`[GAME_DATA] API response:`, result);

        // Update user game counts for both teams
        lobby.teams[1].forEach(player => {
          competitionService.updateUserGameCount(player.id, 1);
        });
        lobby.teams[2].forEach(player => {
          competitionService.updateUserGameCount(player.id, 1);
        });

        // Clear leaderboard cache to force refresh
        competitionService.clearCache();
      } else {
        console.error(`[GAME_DATA] ❌ Competition game result save failed - no result returned from API`);
      }

      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save competition game result:', error.message);
      this.addToRetryQueue('saveCompetitionGameResult', { lobby, gameResult, playerSocketId });
      return null;
    }
  }

  // Get statistics
  getStats() {
    return {
      savedGames: this.savedGames.size,
      retryQueueSize: this.retryQueue.length,
      retryQueue: this.retryQueue.map(item => ({
        operation: item.operation,
        attempts: item.attempts,
        timestamp: item.timestamp
      }))
    };
  }
}

// Export singleton instance
module.exports = new GameDataService();
