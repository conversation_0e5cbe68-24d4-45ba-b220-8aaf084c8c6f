// Test authentication flow
require('dotenv').config();

const axios = require('axios');
const https = require('https');

// Create axios instance for API calls
const apiClient = axios.create({
  baseURL: process.env.API_BASE_URL || 'https://localhost:57229',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  // For development with self-signed certificates
  httpsAgent: process.env.NODE_ENV === 'development' ?
    new https.Agent({ rejectUnauthorized: false }) : undefined
});

async function testAuthentication() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Test 1: Try to login with test credentials
    console.log('1. Testing login...');
    const loginResponse = await apiClient.post('/api/Auth/login', {
      username: 'dean',
      password: 'password'
    });

    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      const token = loginResponse.data.data.token;
      console.log(`   Token received: ${token.substring(0, 20)}...`);

      // Test 2: Validate token
      console.log('\n2. Testing token validation...');
      const validateResponse = await apiClient.get('/api/Auth/validate', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (validateResponse.data.success) {
        console.log('✅ Token validation successful');
        console.log(`   User ID: ${validateResponse.data.userId}`);
      } else {
        console.log('❌ Token validation failed');
      }

      // Test 3: Get user info
      console.log('\n3. Testing get user info...');
      const userResponse = await apiClient.get('/api/Auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (userResponse.data.success) {
        console.log('✅ Get user info successful');
        console.log(`   Username: ${userResponse.data.data.username}`);
        console.log(`   Email: ${userResponse.data.data.email}`);
        console.log(`   Verified: ${userResponse.data.data.isVerified}`);

        // Test 4: Test Node.js server authentication
        console.log('\n4. Testing Node.js server authentication...');
        
        // Import the auth service
        const authService = require('./services/authService');
        
        // Test authentication with the token
        const user = await authService.authenticateUser('test-socket-id', token);
        
        if (user) {
          console.log('✅ Node.js authentication successful');
          console.log(`   Authenticated user: ${user.username}`);
          console.log(`   User ID: ${user.id}`);
        } else {
          console.log('❌ Node.js authentication failed');
        }

        return { success: true, token, user: userResponse.data.data };
      } else {
        console.log('❌ Get user info failed');
      }
    } else {
      console.log('❌ Login failed');
    }
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }

  return { success: false };
}

// Run the test
if (require.main === module) {
  testAuthentication().then(result => {
    if (result.success) {
      console.log('\n🎉 Authentication test completed successfully!');
      console.log('\n📋 Next: Try creating a lobby from the frontend');
    } else {
      console.log('\n❌ Authentication test failed');
    }
  });
}

module.exports = { testAuthentication };
