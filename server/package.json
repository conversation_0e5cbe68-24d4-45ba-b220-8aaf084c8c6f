{"name": "thunee-server", "version": "1.0.0", "description": "WebSocket server for Thunee card game", "main": "index.js", "scripts": {"start": "node index.js", "start:prod": "NODE_ENV=production node index.js", "dev": "NODE_ENV=development nodemon index.js", "dev:win": "set NODE_ENV=development && nodemon index.js"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "socket.io": "^4.7.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}