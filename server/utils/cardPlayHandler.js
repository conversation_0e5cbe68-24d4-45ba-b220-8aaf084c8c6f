/**
 * Handler for automatic card play functionality
 */

/**
 * Handle automatic card play for a timed-out player
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object
 * @param {string} playerId - The ID of the player who timed out
 * @param {Object} selectedCard - The card selected by auto-play
 */
function handleAutoCardPlay(io, lobby, matchedLobby, playerId, selectedCard) {
  try {
    console.log(`Auto-playing card for player ${playerId}: ${selectedCard.value} of ${selectedCard.suit}`);
    
    const lobbyCode = lobby.lobbyCode;
    const matchedLobbyCode = matchedLobby.lobbyCode;
    
    // Find the player
    const timedOutPlayer = lobby.players.find(p => p.id === playerId);
    if (!timedOutPlayer) {
      console.error(`Player ${playerId} not found for auto-play`);
      return;
    }
    
    // Remove the card from the player's hand
    if (lobby.playerCards && lobby.playerCards[playerId]) {
      const cardIndex = lobby.playerCards[playerId].findIndex(c => c.id === selectedCard.id);
      if (cardIndex !== -1) {
        lobby.playerCards[playerId].splice(cardIndex, 1);
        console.log(`Removed auto-played card from player ${playerId}'s hand`);
      }
    }
    
    // Do the same for matched lobby
    if (matchedLobby.playerCards && matchedLobby.playerCards[playerId]) {
      const cardIndex = matchedLobby.playerCards[playerId].findIndex(c => c.id === selectedCard.id);
      if (cardIndex !== -1) {
        matchedLobby.playerCards[playerId].splice(cardIndex, 1);
      }
    }
    
    // Create card with player info
    const cardWithPlayer = {
      ...selectedCard,
      playedBy: playerId
    };
    
    // Store the player's hand before playing for 4-ball validation
    if (lobby.playerCards && lobby.playerCards[playerId]) {
      const handBeforePlay = JSON.parse(JSON.stringify(lobby.playerCards[playerId]));
      cardWithPlayer.handBeforePlay = handBeforePlay;
    }
    
    // Initialize current hand cards if needed
    if (!lobby.currentHandCards) {
      lobby.currentHandCards = [];
      if (matchedLobby !== lobby && !matchedLobby.currentHandCards) {
        matchedLobby.currentHandCards = [];
      }
    }
    
    // Add the card to the current hand
    lobby.currentHandCards.push(cardWithPlayer);
    if (matchedLobby !== lobby) {
      matchedLobby.currentHandCards.push(cardWithPlayer);
    }
    
    // Get all players for the event
    const allPlayers = [];
    const team1Count = lobby.teams[1]?.length || 0;
    const team2Count = lobby.teams[2]?.length || 0;
    const totalPlayers = team1Count + team2Count;
    const isSingleLobby = totalPlayers === 4;

    console.log(`Auto-play: Getting all players. Team 1: ${team1Count}, Team 2: ${team2Count}, Single lobby: ${isSingleLobby}`);

    if (isSingleLobby) {
      allPlayers.push(...lobby.teams[1] || [], ...lobby.teams[2] || []);
    } else {
      allPlayers.push(...lobby.teams[1] || [], ...lobby.teams[2] || []);
      if (matchedLobby && matchedLobby.teams) {
        allPlayers.push(...matchedLobby.teams[1] || [], ...matchedLobby.teams[2] || []);
      }
    }

    console.log(`Auto-play: All players (${allPlayers.length}):`, allPlayers.map(p => `${p.name} (pos: ${p.position})`));
    
    // Create play card data
    const playCardData = {
      card: cardWithPlayer,
      playerId: playerId,
      playerInfo: {
        name: timedOutPlayer.name,
        team: timedOutPlayer.team,
        position: timedOutPlayer.position
      },
      allPlayers: allPlayers.map(p => ({
        id: p.id,
        name: p.name,
        team: p.team,
        position: p.position
      })),
      autoPlayed: true // Flag to indicate this was auto-played
    };
    
    // Emit card_played event to both lobbies
    io.to(lobbyCode).emit('card_played', playCardData);
    if (matchedLobby.lobbyCode && matchedLobby.lobbyCode !== lobbyCode) {
      io.to(matchedLobby.lobbyCode).emit('card_played', playCardData);
    }
    
    // Emit auto_play notification
    const autoPlayData = {
      playerId,
      playerName: timedOutPlayer.name,
      card: {
        value: selectedCard.value,
        suit: selectedCard.suit
      },
      message: `${timedOutPlayer.name} timed out - automatically played ${selectedCard.value} of ${selectedCard.suit}`
    };
    
    io.to(lobbyCode).emit('auto_play_notification', autoPlayData);
    if (matchedLobbyCode !== lobbyCode) {
      io.to(matchedLobbyCode).emit('auto_play_notification', autoPlayData);
    }
    
    // Continue with normal game flow - check if hand is complete
    const expectedCards = 4; // Always 4 cards per hand in Thunee

    console.log(`Auto-play: Checking hand completion. Current cards: ${lobby.currentHandCards.length}, Expected: ${expectedCards}`);

    if (lobby.currentHandCards.length >= expectedCards) {
      console.log(`Hand complete after auto-play. Processing hand with ${lobby.currentHandCards.length} cards.`);
      
      // Process the completed hand using existing game logic
      const gameUtils = require('./gameUtils');
      const turnUtils = require('./turnUtils');
      
      // Stop any remaining timers
      turnUtils.stopTurnTimer(lobby);
      
      // Determine hand winner
      const handResult = gameUtils.determineHandWinner(
        lobby.currentHandCards,
        lobby.trumpSuit,
        lobby.thuneePlayer
      );
      
      if (handResult) {
        const { winningCard, winningPlayerId, winReason, points } = handResult;
        const winningPlayer = allPlayers.find(p => p.id === winningPlayerId);
        
        if (winningPlayer) {
          console.log(`Hand won by ${winningPlayer.name} with ${winningCard.value} of ${winningCard.suit}`);
          
          // Update hand tracking
          lobby.currentHandId = (lobby.currentHandId || 0) + 1;
          matchedLobby.currentHandId = lobby.currentHandId;
          
          // Update ball points
          if (!lobby.ballPoints) {
            lobby.ballPoints = { team1: 0, team2: 0 };
            matchedLobby.ballPoints = { team1: 0, team2: 0 };
          }
          
          lobby.ballPoints[`team${winningPlayer.team}`] += points;
          matchedLobby.ballPoints[`team${winningPlayer.team}`] += points;
          
          // Send hand completed event
          const handCompletedData = {
            handId: lobby.currentHandId,
            cards: lobby.currentHandCards,
            winner: winningPlayer,
            nextTurn: winningPlayerId,
            points: points,
            leadSuit: lobby.currentHandCards[0].suit,
            winReason: winReason,
            winningTeam: winningPlayer.team,
            autoPlayInvolved: true
          };
          
          io.to(lobbyCode).emit('hand_completed', handCompletedData);
          if (matchedLobbyCode !== lobbyCode) {
            io.to(matchedLobbyCode).emit('hand_completed', handCompletedData);
          }
          
          // Clear played cards and set next turn after delay
          setTimeout(() => {
            lobby.currentHandCards = [];
            if (matchedLobby !== lobby) {
              matchedLobby.currentHandCards = [];
            }
            
            // Check if ball is complete (6 hands played)
            if (lobby.currentHandId >= 6) {
              console.log('Ball completed after auto-play, calculating winner and next dealer...');

              // Set flag to indicate ball completion is in progress
              lobby.ballCompleted = true;
              matchedLobby.ballCompleted = true;

              // Calculate ball winner using the same logic as regular play
              const ballUtils = require('./ballUtils');
              const ballResult = ballUtils.calculateBallWinner(
                lobby,
                lobby.ballPoints.team1,
                lobby.ballPoints.team2,
                winningPlayer.team
              );

              console.log(`Auto-play ball completion: Team ${ballResult.winner} wins the ball`);

              // Update ball scores
              lobby.ballScores = ballResult.ballScores;
              matchedLobby.ballScores = ballResult.ballScores;

              // Determine the next dealer
              const nextDealerId = ballUtils.determineNextDealer(lobby, allPlayers);
              lobby.dealerId = nextDealerId;
              matchedLobby.dealerId = nextDealerId;

              // Reset for next ball
              const resetUtils = require('./resetUtils');
              resetUtils.resetBallState(lobby, matchedLobby);

              // Reset hand counter and points
              lobby.currentHandId = 0;
              matchedLobby.currentHandId = 0;
              lobby.ballPoints = { team1: 0, team2: 0 };
              matchedLobby.ballPoints = { team1: 0, team2: 0 };

              // Get the current ball ID
              const ballId = (lobby.currentBallId || 0) + 1;
              lobby.currentBallId = ballId;
              matchedLobby.currentBallId = ballId;

              // Send ball_completed event to both lobbies
              const ballCompletedData = {
                ballId,
                winner: ballResult.winner,
                points: {
                  team1: lobby.ballPoints.team1,
                  team2: lobby.ballPoints.team2
                },
                nextDealer: nextDealerId,
                ballScores: ballResult.ballScores,
                autoPlayInvolved: true,
                ...ballResult // Include all ball result data
              };

              io.to(lobbyCode).emit('ball_completed', ballCompletedData);
              if (matchedLobbyCode !== lobbyCode) {
                io.to(matchedLobbyCode).emit('ball_completed', ballCompletedData);
              }

              // Add ball to game history and check for game end
              const gameEndUtils = require('./gameEndUtils');
              gameEndUtils.addBallToHistory(lobby, ballCompletedData);
              if (matchedLobby !== lobby) {
                gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
              }

              // Check if game should end
              const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
              if (gameEndCheck.gameEnded) {
                console.log(`Game ended after auto-play! Team ${gameEndCheck.winner} wins!`);

                const gameHistory = gameEndUtils.getGameHistory(lobby);
                const gameEndData = {
                  ...gameEndCheck,
                  gameHistory
                };

                // Emit game_ended event to both lobbies
                io.to(lobbyCode).emit('game_ended', gameEndData);
                if (matchedLobbyCode !== lobbyCode) {
                  io.to(matchedLobbyCode).emit('game_ended', gameEndData);
                }

                return; // Don't continue to next ball
              }

              // Game continues - start next ball after delay
              setTimeout(() => {
                io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                if (matchedLobbyCode !== lobbyCode) {
                  io.to(matchedLobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
                }
              }, 3000);

            } else {
              // Set next player's turn
              turnUtils.setPlayerTurn(io, lobby, matchedLobby, winningPlayerId, false);
            }
          }, 2000);
        }
      }
    } else {
      // Hand not complete, determine next player using the same logic as regular card play
      const playedPlayerIds = lobby.currentHandCards.map(card => card.playedBy);
      console.log(`Players who have already played: ${playedPlayerIds.join(', ')}`);

      // Use the playerPositionUtils to get the player to the right (counter-clockwise)
      const playerPositionUtils = require('./playerPositionUtils');
      const nextPosition = playerPositionUtils.getPositionToRight(timedOutPlayer.position);
      console.log(`Auto-play: Current player position: ${timedOutPlayer.position}, Next position: ${nextPosition}`);

      // Find the player with the next position
      const nextPlayer = allPlayers.find(p => p.position === nextPosition);
      if (!nextPlayer) {
        console.error(`No player found at position ${nextPosition}`);

        // Try to find any player who hasn't played yet
        const unplayedPlayers = allPlayers.filter(p => !playedPlayerIds.includes(p.id));

        if (unplayedPlayers.length > 0) {
          const fallbackNextPlayer = unplayedPlayers[0];
          console.log(`Auto-play: Falling back to next unplayed player: ${fallbackNextPlayer.name} (${fallbackNextPlayer.id}) at position ${fallbackNextPlayer.position}`);

          const turnUtils = require('./turnUtils');
          turnUtils.setPlayerTurn(io, lobby, matchedLobby, fallbackNextPlayer.id, false);
        }
        return;
      }

      // Check if the next player has already played a card
      if (playedPlayerIds.includes(nextPlayer.id)) {
        console.log(`Auto-play: Next player ${nextPlayer.name} has already played a card. Looking for another player.`);

        // Find any player who hasn't played yet
        const unplayedPlayers = allPlayers.filter(p => !playedPlayerIds.includes(p.id));

        if (unplayedPlayers.length > 0) {
          const fallbackNextPlayer = unplayedPlayers[0];
          console.log(`Auto-play: Falling back to next unplayed player: ${fallbackNextPlayer.name} (${fallbackNextPlayer.id}) at position ${fallbackNextPlayer.position}`);

          const turnUtils = require('./turnUtils');
          turnUtils.setPlayerTurn(io, lobby, matchedLobby, fallbackNextPlayer.id, false);
        } else {
          console.error('Auto-play: No unplayed players found, but not all players have played yet. This should not happen.');
        }
        return;
      }

      // Set the next player's turn
      console.log(`Auto-play complete. Next player: ${nextPlayer.id} (${nextPlayer.name}) at position ${nextPosition}`);
      const turnUtils = require('./turnUtils');
      turnUtils.setPlayerTurn(io, lobby, matchedLobby, nextPlayer.id, false);
    }
    
  } catch (error) {
    console.error('Error in auto card play:', error);
  }
}

module.exports = {
  handleAutoCardPlay
};
