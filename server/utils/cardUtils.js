/**
 * Utility functions for card operations
 */

/**
 * Creates a shuffled deck for <PERSON><PERSON><PERSON> (24 cards)
 * @returns {Array} A shuffled deck of 24 cards
 */
function createGameDeck() {
  // <PERSON><PERSON><PERSON> uses 9, 10, J, Q, K, A from each suit
  const suits = ['hearts', 'diamonds', 'clubs', 'spades'];
  const values = ['9', '10', 'J', 'Q', 'K', 'A'];
  const points = {
    '9': 20,
    '10': 10,
    'J': 30,
    'Q': 2,
    'K': 3,
    'A': 11
  };

  let deck = [];
  let id = 1;

  for (const suit of suits) {
    for (const value of values) {
      deck.push({
        id: `card_${id++}`,
        suit,
        value,
        points: points[value],
        image: `/CardFace/${value === '10' ? 'T' : value}${suit.charAt(0).toUpperCase()}.svg`
      });
    }
  }

  // Shuffle the deck
  for (let i = deck.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [deck[i], deck[j]] = [deck[j], deck[i]];
  }

  console.log(`Created game deck with ${deck.length} cards`);
  return deck;
}

/**
 * Checks if a deck has duplicate cards
 * @param {Array} deck The deck to check
 * @returns {boolean} True if duplicates are found, false otherwise
 */
function hasDuplicateCards(deck) {
  const cardKeys = new Set();
  for (const card of deck) {
    const key = `${card.value}_${card.suit}`;
    if (cardKeys.has(key)) {
      console.error(`Duplicate card found in deck: ${card.value} of ${card.suit}`);
      return true;
    }
    cardKeys.add(key);
  }
  return false;
}

/**
 * Removes duplicate cards from a deck
 * @param {Array} deck The deck with potential duplicates
 * @returns {Array} A deck with no duplicates
 */
function removeDuplicateCards(deck) {
  const uniqueCards = [];
  const cardKeys = new Set();

  for (const card of deck) {
    const key = `${card.value}_${card.suit}`;
    if (!cardKeys.has(key)) {
      cardKeys.add(key);
      uniqueCards.push(card);
    } else {
      console.warn(`Skipping duplicate card: ${card.value} of ${card.suit}`);
    }
  }

  return uniqueCards;
}

/**
 * Ensures a deck has no duplicate cards
 * @param {Array} deck The deck to check and fix
 * @returns {Array} A deck with no duplicates
 */
function ensureNoDuplicates(deck) {
  if (hasDuplicateCards(deck)) {
    console.warn('Duplicates found in deck, removing duplicates');
    return removeDuplicateCards(deck);
  }
  return deck;
}

/**
 * Checks for duplicate cards across all players
 * @param {Object} playerCards Object mapping player IDs to their cards
 * @returns {boolean} True if duplicates are found, false otherwise
 */
function hasDuplicatesAcrossPlayers(playerCards) {
  const allDealtCards = new Set();
  let hasDuplicates = false;

  // Check each player's cards
  Object.entries(playerCards).forEach(([playerId, cards]) => {
    if (Array.isArray(cards)) {
      cards.forEach(card => {
        if (card && card.value && card.suit) {
          const cardKey = `${card.value}_${card.suit}`;
          if (allDealtCards.has(cardKey)) {
            console.error(`Duplicate card found across players: ${card.value} of ${card.suit} in player ${playerId}`);
            hasDuplicates = true;
          }
          allDealtCards.add(cardKey);
        } else {
          console.warn(`Invalid card found for player ${playerId}, skipping duplicate check`);
        }
      });
    } else {
      console.warn(`Invalid cards array for player ${playerId}, skipping duplicate check`);
    }
  });

  return hasDuplicates;
}

/**
 * Gets all cards currently dealt to players
 * @param {Object} playerCards Object mapping player IDs to their cards
 * @returns {Set} Set of card keys that have been dealt
 */
function getAllDealtCardKeys(playerCards) {
  const allDealtCards = new Set();

  // Add each player's cards to the set
  Object.entries(playerCards).forEach(([playerId, cards]) => {
    if (Array.isArray(cards)) {
      cards.forEach(card => {
        if (card && card.value && card.suit) {
          const cardKey = `${card.value}_${card.suit}`;
          allDealtCards.add(cardKey);
        } else {
          console.warn(`Invalid card found for player ${playerId}, skipping`);
        }
      });
    } else {
      console.warn(`Invalid cards array for player ${playerId}, skipping`);
    }
  });

  console.log(`Found ${allDealtCards.size} unique cards across all players`);
  return allDealtCards;
}

/**
 * Deals cards to players ensuring no duplicates
 * @param {Array} players The players to deal cards to
 * @param {Array} deck The deck to deal from
 * @param {number} cardsPerPlayer The number of cards to deal to each player
 * @param {Object} existingPlayerCards Optional existing player cards to consider
 * @returns {Object} An object mapping player IDs to their cards
 */
function dealCardsToPlayers(players, deck, cardsPerPlayer, existingPlayerCards = {}) {
  // Ensure the deck has no duplicates
  const cleanDeck = ensureNoDuplicates([...deck]);

  // Track all dealt cards to ensure no duplicates
  const allDealtCards = existingPlayerCards ? getAllDealtCardKeys(existingPlayerCards) : new Set();

  // Log the existing cards
  console.log(`Initial tracking has ${allDealtCards.size} cards already dealt`);

  // Create a copy of the existing player cards
  const playerCards = {};

  // Properly copy the existing player cards to avoid reference issues
  if (existingPlayerCards) {
    Object.entries(existingPlayerCards).forEach(([playerId, cards]) => {
      if (Array.isArray(cards)) {
        playerCards[playerId] = [...cards];
      } else {
        console.log(`Invalid cards for player ${playerId}, initializing empty array`);
        playerCards[playerId] = [];
      }
    });
  }

  // Initialize player cards if they don't exist
  players.forEach(player => {
    if (!playerCards[player.id]) {
      playerCards[player.id] = [];
    }
  });

  console.log(`Starting with ${allDealtCards.size} cards already dealt across all players`);

  // Deal cards in rounds (one card to each player at a time)
  for (let round = 0; round < cardsPerPlayer; round++) {
    players.forEach(player => {
      if (cleanDeck.length > 0) {
        // Find a unique card that hasn't been dealt yet
        let card;
        let cardKey;

        do {
          // Get the next card from the deck
          card = cleanDeck.shift();
          if (!card) break; // No more cards in the deck

          cardKey = `${card.value}_${card.suit}`;

          // If this card has already been dealt, put it back at the end of the deck
          if (allDealtCards.has(cardKey)) {
            console.log(`Card ${card.value} of ${card.suit} already dealt, trying another`);
            cleanDeck.push(card);
            card = null;
          }
        } while (card === null && cleanDeck.length > 0);

        // If we found a unique card, deal it
        if (card) {
          // Mark this card as dealt
          allDealtCards.add(cardKey);

          // Add the card to the player's hand
          playerCards[player.id].push(card);
          console.log(`Dealt ${card.value} of ${card.suit} to ${player.name} (${player.id})`);
        } else {
          console.error(`Could not find a unique card for player ${player.name}`);
        }
      }
    });
  }

  // Final check for duplicates across all players
  if (hasDuplicatesAcrossPlayers(playerCards)) {
    console.error('CRITICAL ERROR: Duplicates found across players after dealing!');
  } else {
    console.log('No duplicates found across players - dealing successful');
  }

  return playerCards;
}

module.exports = {
  createGameDeck,
  hasDuplicateCards,
  removeDuplicateCards,
  ensureNoDuplicates,
  dealCardsToPlayers,
  hasDuplicatesAcrossPlayers,
  getAllDealtCardKeys
};
