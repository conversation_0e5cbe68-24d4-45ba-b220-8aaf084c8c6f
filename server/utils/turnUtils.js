/**
 * Utility functions for handling player turns and turn timers
 */

const timeframeUtils = require('./timeframeUtils');

/**
 * Set the next player's turn with a timer
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object
 * @param {String} playerId - The ID of the player whose turn it is
 * @param {Boolean} isHandComplete - Whether this is the end of a hand
 */
function setPlayerTurn(io, lobby, matchedLobby, playerId, isHandComplete = false) {
  // Get the lobby code
  const lobbyCode = lobby.lobbyCode;
  const matchedLobbyCode = matchedLobby.lobbyCode;

  // Clear any existing turn timer
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  // Check if this is the first player's turn after Thunee opportunities
  const isFirstPlayerAfterThunee = lobby.thuneeOpportunitiesComplete && lobby.firstPlayerId === playerId;

  // Send player_turn event to both lobbies
  io.to(lobbyCode).emit('player_turn', {
    playerId,
    isFirstPlayerAfterThunee
  });

  if (matchedLobbyCode !== lobbyCode) {
    io.to(matchedLobbyCode).emit('player_turn', {
      playerId,
      isFirstPlayerAfterThunee
    });
  }

  // If this is the end of a hand, don't start a timer
  if (isHandComplete) {
    return;
  }

  // Initialize the turn timer
  const turnTimerState = timeframeUtils.initTurnTimer(lobby, playerId);
  const timeframe = turnTimerState.timeRemaining;

  console.log(`Starting turn timer for player ${playerId} with ${timeframe} seconds`);

  // Set up a timer to update clients about the remaining time
  const updateInterval = setInterval(() => {
    // Update the turn timer
    const updatedTimer = timeframeUtils.updateTurnTimer(lobby);

    if (!updatedTimer || !updatedTimer.timerActive) {
      clearInterval(updateInterval);
      lobby.updateInterval = null;
      return;
    }

    // Send turn_timer_update event to both lobbies
    io.to(lobbyCode).emit('turn_timer_update', {
      playerId,
      timeRemaining: updatedTimer.currentRemaining
    });

    if (matchedLobbyCode !== lobbyCode) {
      io.to(matchedLobbyCode).emit('turn_timer_update', {
        playerId,
        timeRemaining: updatedTimer.currentRemaining
      });
    }
  }, 100); // Update every 100ms for smoother countdown

  // Store the update interval reference so we can clean it up later
  lobby.updateInterval = updateInterval;

  // Set up a timer to handle timeout
  lobby.turnTimer = setTimeout(() => {
    // Stop the update interval
    clearInterval(updateInterval);
    lobby.updateInterval = null;

    // Check if the ball has already been completed (to prevent timeout after ball ends)
    if (lobby.currentHandId >= 6) {
      console.log(`Ball already completed, ignoring timeout for player ${playerId}`);
      return;
    }

    // Check if a 4-ball result is currently being processed
    if (lobby.fourBallInProgress || lobby.ballCompleted) {
      console.log(`4-ball or ball completion in progress, ignoring timeout for player ${playerId}`);
      return;
    }

    // Additional check: if ball scores indicate a completed ball (4 balls to any team)
    if (lobby.ballScores && (lobby.ballScores.team1 >= 4 || lobby.ballScores.team2 >= 4)) {
      console.log(`Ball already completed with scores Team 1: ${lobby.ballScores.team1}, Team 2: ${lobby.ballScores.team2}, ignoring timeout for player ${playerId}`);
      return;
    }

    // Check if the player has already played a card
    if (!lobby.currentHandCards || !lobby.currentHandCards.some(card => card.playedBy === playerId)) {
      console.log(`Player ${playerId} timed out after ${timeframe} seconds - automatically playing a card`);

      // Find the player who timed out
      const timedOutPlayer = lobby.players.find(p => p.id === playerId);
      if (!timedOutPlayer) {
        console.error(`Timed out player ${playerId} not found in lobby ${lobbyCode}`);
        return;
      }

      // Get the player's current hand
      const playerHand = lobby.playerCards[playerId];
      if (!playerHand || playerHand.length === 0) {
        console.error(`No cards found for timed out player ${playerId}`);
        return;
      }

      // Use auto-play logic to select a card
      const autoPlayUtils = require('./autoPlayUtils');
      const selectedCard = autoPlayUtils.selectCardToPlay(
        playerHand,
        lobby.currentHandCards,
        lobby.trumpSuit,
        playerId,
        lobby.players,
        lobby
      );

      if (!selectedCard) {
        console.error(`Auto-play could not select a card for player ${playerId}`);
        return;
      }

      console.log(`Auto-playing ${selectedCard.value} of ${selectedCard.suit} for player ${playerId}`);

      // Simulate the card play by calling the card play handler
      const cardPlayHandler = require('./cardPlayHandler');
      cardPlayHandler.handleAutoCardPlay(io, lobby, matchedLobby, playerId, selectedCard);


    }
  }, timeframe * 1000);
}

/**
 * Stop the turn timer
 * @param {Object} lobby - The lobby object
 */
function stopTurnTimer(lobby) {
  console.log(`Stopping turn timer for lobby ${lobby.lobbyCode}`);

  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimer = null;
  }

  // Also clear any update intervals that might be running
  if (lobby.updateInterval) {
    clearInterval(lobby.updateInterval);
    lobby.updateInterval = null;
  }

  timeframeUtils.stopTurnTimer(lobby);
}

module.exports = {
  setPlayerTurn,
  stopTurnTimer
};
