/**
 * Utility functions for automatic card play when players timeout
 */

/**
 * Card rankings for Thunee game
 * According to Thunee rules: Jack, 9, <PERSON>, 10, <PERSON>, Queen (highest to lowest)
 */
const trumpRanking = ['J', '9', 'A', '10', 'K', 'Q']; // Highest to lowest for trump cards
const nonTrumpRanking = ['J', '9', 'A', '10', 'K', 'Q']; // Highest to lowest for non-trump cards

/**
 * Get the rank value of a card (lower number = higher rank)
 * @param {string} value - Card value
 * @param {boolean} isTrump - Whether the card is trump
 * @returns {number} Rank value (0 = highest, 5 = lowest)
 */
function getCardRank(value, isTrump) {
  const ranking = isTrump ? trumpRanking : nonTrumpRanking;
  return ranking.indexOf(value);
}

/**
 * Check if a card is higher than another card
 * @param {Object} card1 - First card
 * @param {Object} card2 - Second card
 * @param {string} trumpSuit - Trump suit
 * @returns {boolean} True if card1 is higher than card2
 */
function isCardHigher(card1, card2, trumpSuit) {
  const card1IsTrump = card1.suit === trumpSuit;
  const card2IsTrump = card2.suit === trumpSuit;
  
  // Trump always beats non-trump
  if (card1IsTrump && !card2IsTrump) return true;
  if (!card1IsTrump && card2IsTrump) return false;
  
  // Both trump or both non-trump - compare ranks
  const card1Rank = getCardRank(card1.value, card1IsTrump);
  const card2Rank = getCardRank(card2.value, card2IsTrump);
  
  return card1Rank < card2Rank; // Lower rank number = higher card
}

/**
 * Get the highest card from a set of cards
 * @param {Array} cards - Array of cards
 * @param {string} trumpSuit - Trump suit
 * @returns {Object} Highest card
 */
function getHighestCard(cards, trumpSuit) {
  if (!cards || cards.length === 0) return null;
  
  return cards.reduce((highest, current) => {
    if (!highest) return current;
    return isCardHigher(current, highest, trumpSuit) ? current : highest;
  });
}

/**
 * Get the lowest card from a set of cards
 * @param {Array} cards - Array of cards
 * @param {string} trumpSuit - Trump suit
 * @returns {Object} Lowest card
 */
function getLowestCard(cards, trumpSuit) {
  if (!cards || cards.length === 0) return null;
  
  return cards.reduce((lowest, current) => {
    if (!lowest) return current;
    return isCardHigher(lowest, current, trumpSuit) ? current : lowest;
  });
}

/**
 * Filter cards by suit
 * @param {Array} cards - Array of cards
 * @param {string} suit - Suit to filter by
 * @returns {Array} Cards of the specified suit
 */
function getCardsBySuit(cards, suit) {
  return cards.filter(card => card.suit === suit);
}

/**
 * Get trump cards from hand
 * @param {Array} cards - Array of cards
 * @param {string} trumpSuit - Trump suit
 * @returns {Array} Trump cards
 */
function getTrumpCards(cards, trumpSuit) {
  return getCardsBySuit(cards, trumpSuit);
}

/**
 * Get non-trump cards from hand
 * @param {Array} cards - Array of cards
 * @param {string} trumpSuit - Trump suit
 * @returns {Array} Non-trump cards
 */
function getNonTrumpCards(cards, trumpSuit) {
  return cards.filter(card => card.suit !== trumpSuit);
}

/**
 * Find partner of a player
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @returns {Object} Partner player object
 */
function findPartner(playerId, players) {
  const player = players.find(p => p.id === playerId);
  if (!player) return null;
  
  return players.find(p => p.team === player.team && p.id !== playerId);
}

/**
 * Check if partner has played in current hand
 * @param {string} playerId - Player ID
 * @param {Array} currentHandCards - Cards played in current hand
 * @param {Array} players - Array of all players
 * @param {string} trumpSuit - Trump suit
 * @returns {Object} { hasPlayed: boolean, partnerCard: Object|null, isWinning: boolean }
 */
function getPartnerStatus(playerId, currentHandCards, players, trumpSuit) {
  const partner = findPartner(playerId, players);
  if (!partner) {
    console.log(`Auto-play: No partner found for player ${playerId}`);
    return { hasPlayed: false, partnerCard: null, isWinning: false };
  }

  console.log(`Auto-play: Partner found - ${partner.name} (${partner.id})`);

  const partnerCard = currentHandCards.find(card => card.playedBy === partner.id);
  if (!partnerCard) {
    console.log(`Auto-play: Partner ${partner.name} has not played yet`);
    return { hasPlayed: false, partnerCard: null, isWinning: false };
  }

  console.log(`Auto-play: Partner played ${partnerCard.value}${partnerCard.suit}`);

  // Check if partner is currently winning by comparing with other played cards
  const isWinning = isPartnerCurrentlyWinning(partnerCard, currentHandCards, trumpSuit);
  console.log(`Auto-play: Partner is currently winning: ${isWinning}`);

  return { hasPlayed: true, partnerCard, isWinning };
}

/**
 * Check if partner's card is currently winning the hand
 * @param {Object} partnerCard - Partner's played card
 * @param {Array} currentHandCards - All cards played so far
 * @param {string} trumpSuit - Trump suit
 * @returns {boolean} True if partner is winning
 */
function isPartnerCurrentlyWinning(partnerCard, currentHandCards, trumpSuit) {
  if (!partnerCard || currentHandCards.length === 0) return false;

  // Determine the lead suit
  const leadSuit = currentHandCards[0].suit;

  // Find the currently winning card using proper Thunee rules
  let winningCard = currentHandCards[0];

  for (let i = 1; i < currentHandCards.length; i++) {
    const currentCard = currentHandCards[i];

    // Trump always beats non-trump
    if (currentCard.suit === trumpSuit && winningCard.suit !== trumpSuit) {
      winningCard = currentCard;
    }
    // Both trump - higher trump wins
    else if (currentCard.suit === trumpSuit && winningCard.suit === trumpSuit) {
      if (isCardHigher(currentCard, winningCard, trumpSuit)) {
        winningCard = currentCard;
      }
    }
    // Current card follows suit, winning card doesn't follow suit and isn't trump
    else if (currentCard.suit === leadSuit && winningCard.suit !== leadSuit && winningCard.suit !== trumpSuit) {
      winningCard = currentCard;
    }
    // Both follow suit - higher card wins
    else if (currentCard.suit === leadSuit && winningCard.suit === leadSuit) {
      if (isCardHigher(currentCard, winningCard, trumpSuit)) {
        winningCard = currentCard;
      }
    }
  }

  return winningCard.playedBy === partnerCard.playedBy;
}

/**
 * Check if any opponent has played trump
 * @param {string} playerId - Player ID
 * @param {Array} currentHandCards - Cards played in current hand
 * @param {Array} players - Array of all players
 * @param {string} trumpSuit - Trump suit
 * @returns {Object} { hasTrump: boolean, highestTrump: Object|null }
 */
function getOpponentTrumpStatus(playerId, currentHandCards, players, trumpSuit) {
  const player = players.find(p => p.id === playerId);
  if (!player) {
    console.log(`Auto-play: Player ${playerId} not found in players array`);
    return { hasTrump: false, highestTrump: null };
  }

  console.log(`Auto-play: Checking opponent trump status for player ${player.name} (Team ${player.team})`);

  const opponentCards = currentHandCards.filter(card => {
    const cardPlayer = players.find(p => p.id === card.playedBy);
    const isOpponent = cardPlayer && cardPlayer.team !== player.team;
    if (isOpponent) {
      console.log(`Auto-play: Opponent ${cardPlayer.name} (Team ${cardPlayer.team}) played ${card.value}${card.suit}`);
    }
    return isOpponent;
  });

  console.log(`Auto-play: Found ${opponentCards.length} opponent cards`);

  const trumpCards = opponentCards.filter(card => card.suit === trumpSuit);
  console.log(`Auto-play: Found ${trumpCards.length} opponent trump cards`);

  if (trumpCards.length === 0) {
    console.log(`Auto-play: No opponent trump cards found`);
    return { hasTrump: false, highestTrump: null };
  }

  const highestTrump = getHighestCard(trumpCards, trumpSuit);
  console.log(`Auto-play: Highest opponent trump: ${highestTrump.value}${highestTrump.suit}`);
  return { hasTrump: true, highestTrump };
}

/**
 * Select the best card to play automatically based on game rules
 * @param {Array} playerHand - Player's current hand
 * @param {Array} currentHandCards - Cards already played in this hand
 * @param {string} trumpSuit - Trump suit
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @param {Object} lobby - Game lobby object (optional)
 * @returns {Object} Selected card to play
 */
function selectCardToPlay(playerHand, currentHandCards, trumpSuit, playerId, players, lobby = null) {
  // Validate game state first
  const validation = validateGameState(playerHand, currentHandCards, trumpSuit, playerId, players);

  if (!validation.isValid) {
    console.error('Auto-play: Game state validation failed:', validation.errors);
    return null;
  }

  if (validation.warnings.length > 0) {
    console.warn('Auto-play: Game state warnings:', validation.warnings);
  }

  // Enhanced logging for debugging
  const player = players.find(p => p.id === playerId);
  const playerName = player ? player.name : 'Unknown';
  const playerTeam = player ? player.team : 'Unknown';

  console.log(`\n=== AUTO-PLAY DECISION START ===`);
  console.log(`Player: ${playerName} (${playerId}) - Team ${playerTeam}`);
  console.log(`Hand (${playerHand.length} cards): ${playerHand.map(c => `${c.value}${c.suit}`).join(', ')}`);
  console.log(`Trump suit: ${trumpSuit}`);
  console.log(`Current hand cards (${currentHandCards.length}): ${currentHandCards.map(c => `${c.value}${c.suit} (by ${c.playedBy})`).join(', ')}`);

  // Log all players and their teams for context
  console.log(`All players: ${players.map(p => `${p.name} (Team ${p.team}, Pos ${p.position})`).join(', ')}`);

  // FLOWCHART: "Is this the first play of the round?"
  const isFirstPlay = currentHandCards.length === 0;
  console.log(`Is first play of round: ${isFirstPlay}`);

  let selectedCard;
  if (isFirstPlay) {
    selectedCard = selectCardForFirstPlay(playerHand, trumpSuit, playerId, players, lobby);
  } else {
    selectedCard = selectCardForFollowing(playerHand, currentHandCards, trumpSuit, playerId, players);
  }

  console.log(`=== AUTO-PLAY DECISION END: Selected ${selectedCard ? `${selectedCard.value}${selectedCard.suit}` : 'NULL'} ===\n`);
  return selectedCard;
}

/**
 * Select card when player is making the first play of the round
 * @param {Array} playerHand - Player's hand
 * @param {string} trumpSuit - Trump suit
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @param {Object} lobby - Game lobby object
 * @returns {Object} Selected card
 */
function selectCardForFirstPlay(playerHand, trumpSuit, playerId, players, lobby) {
  console.log('Auto-play: Player is making the first play of the round');

  const trumpCards = getTrumpCards(playerHand, trumpSuit);
  const nonTrumpCards = getNonTrumpCards(playerHand, trumpSuit);

  // FLOWCHART: "Is player the Trumper?"
  const isPlayerTrumper = isPlayerTheTrumper(playerId, players, lobby);

  if (isPlayerTrumper) {
    // YES PATH: "Play highest ranking Trump card"
    console.log('Auto-play: Player is the Trumper - playing highest ranking Trump card');
    if (trumpCards.length > 0) {
      return getHighestCard(trumpCards, trumpSuit);
    } else {
      // Fallback if trumper has no trump cards (shouldn't happen normally)
      console.log('Auto-play: Trumper has no trump cards, playing highest non-trump');
      return getHighestCard(nonTrumpCards, trumpSuit);
    }
  } else {
    // NO PATH: "Play highest ranking non-Trump card"
    console.log('Auto-play: Player is not the Trumper - playing highest ranking non-Trump card');
    if (nonTrumpCards.length > 0) {
      return getHighestCard(nonTrumpCards, trumpSuit);
    } else {
      // Fallback if player has only trump cards
      console.log('Auto-play: Player has only trump cards, playing highest trump');
      return getHighestCard(trumpCards, trumpSuit);
    }
  }
}

/**
 * Check if the player is the trumper (trump selector)
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @param {Object} lobby - Game lobby object
 * @returns {boolean} True if player is the trumper
 */
function isPlayerTheTrumper(playerId, players, lobby) {
  // Check if this player is marked as the trump selector
  const player = players.find(p => p.id === playerId);
  if (player && player.isTrumpSelector) {
    return true;
  }

  // Also check lobby trumper information
  if (lobby && lobby.trumperId === playerId) {
    return true;
  }

  return false;
}

/**
 * Select card when player is following in an already initiated round
 * Following the exact flowchart provided
 * @param {Array} playerHand - Player's hand
 * @param {Array} currentHandCards - Cards played in current hand
 * @param {string} trumpSuit - Trump suit
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @returns {Object} Selected card
 */
function selectCardForFollowing(playerHand, currentHandCards, trumpSuit, playerId, players) {
  console.log('Auto-play: Player is following in the hand');
  console.log(`Auto-play: Current hand state - ${currentHandCards.length} cards played`);
  console.log(`Auto-play: Cards in current hand: ${currentHandCards.map(c => `${c.value}${c.suit} (by ${c.playedBy})`).join(', ')}`);

  // Get the lead suit (first card played)
  const leadSuit = currentHandCards[0].suit;
  console.log(`Auto-play: Lead suit: ${leadSuit}`);

  // FLOWCHART STEP: "Can follow suit with led color?"
  const leadSuitCards = getCardsBySuit(playerHand, leadSuit);
  if (leadSuitCards.length > 0) {
    // YES PATH: "Play highest card of the led suit"
    console.log('Auto-play: Can follow suit - playing highest card of led suit');
    const selectedCard = getHighestCard(leadSuitCards, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (highest of led suit)`);
    return selectedCard;
  }

  console.log('Auto-play: Cannot follow suit, checking trump options');

  // NO PATH: "Do you have Trump cards?"
  const trumpCards = getTrumpCards(playerHand, trumpSuit);
  if (trumpCards.length === 0) {
    // NO PATH: "Play lowest valued card"
    console.log('Auto-play: No trump cards available - playing lowest valued card');
    const selectedCard = getLowestCard(playerHand, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (lowest valued card)`);
    return selectedCard;
  }

  console.log(`Auto-play: Has ${trumpCards.length} trump cards available`);

  // YES PATH: "Has partner played yet?"
  const partnerStatus = getPartnerStatus(playerId, currentHandCards, players, trumpSuit);
  console.log(`Auto-play: Partner status - hasPlayed: ${partnerStatus.hasPlayed}`);

  if (!partnerStatus.hasPlayed) {
    // NO PATH: "Play lowest Trump"
    console.log('Auto-play: Partner has not played yet - playing lowest trump card');
    const selectedCard = getLowestCard(trumpCards, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (lowest trump)`);
    return selectedCard;
  }

  console.log('Auto-play: Partner has played, checking opponent trump status');

  // YES PATH: "Has opponent played a Trump card?"
  const opponentTrumpStatus = getOpponentTrumpStatus(playerId, currentHandCards, players, trumpSuit);
  console.log(`Auto-play: Opponent trump status - hasTrump: ${opponentTrumpStatus.hasTrump}`);

  if (!opponentTrumpStatus.hasTrump) {
    // NO PATH: "Play lowest Trump"
    console.log('Auto-play: No opponent trump played - playing lowest trump card');
    const selectedCard = getLowestCard(trumpCards, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (lowest trump, no opponent trump)`);
    return selectedCard;
  }

  console.log(`Auto-play: Opponent has trump: ${opponentTrumpStatus.highestTrump.value}${opponentTrumpStatus.highestTrump.suit}`);

  // YES PATH: "Do you have Trump higher than opponent's?"
  const canBeatOpponentTrump = trumpCards.some(card =>
    isCardHigher(card, opponentTrumpStatus.highestTrump, trumpSuit)
  );
  console.log(`Auto-play: Can beat opponent trump: ${canBeatOpponentTrump}`);

  if (canBeatOpponentTrump) {
    // YES PATH: "Play highest Trump that beats opponent"
    console.log('Auto-play: Can beat opponent trump - playing highest trump that beats opponent');
    const winningTrumps = trumpCards.filter(card =>
      isCardHigher(card, opponentTrumpStatus.highestTrump, trumpSuit)
    );
    const selectedCard = getHighestCard(winningTrumps, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (highest trump that beats opponent)`);
    return selectedCard;
  } else {
    // NO PATH: "Play lowest valued card"
    console.log('Auto-play: Cannot beat opponent trump - playing lowest valued card');
    const selectedCard = getLowestCard(playerHand, trumpSuit);
    console.log(`Auto-play: Selected ${selectedCard.value}${selectedCard.suit} (lowest valued card, cannot beat opponent trump)`);
    return selectedCard;
  }
}

/**
 * Validate game state for auto-play decisions
 * @param {Array} playerHand - Player's hand
 * @param {Array} currentHandCards - Cards played in current hand
 * @param {string} trumpSuit - Trump suit
 * @param {string} playerId - Player ID
 * @param {Array} players - Array of all players
 * @returns {Object} Validation result with warnings/errors
 */
function validateGameState(playerHand, currentHandCards, trumpSuit, playerId, players) {
  const warnings = [];
  const errors = [];

  // Validate player hand
  if (!playerHand || playerHand.length === 0) {
    errors.push('Player hand is empty or null');
  }

  // Validate trump suit
  if (!trumpSuit || !['hearts', 'diamonds', 'clubs', 'spades'].includes(trumpSuit)) {
    errors.push(`Invalid trump suit: ${trumpSuit}`);
  }

  // Validate players array
  if (!players || players.length !== 4) {
    errors.push(`Invalid players array - expected 4 players, got ${players ? players.length : 0}`);
  }

  // Validate current player exists
  const currentPlayer = players.find(p => p.id === playerId);
  if (!currentPlayer) {
    errors.push(`Current player ${playerId} not found in players array`);
  }

  // Validate team structure
  if (players) {
    const team1Players = players.filter(p => p.team === 1);
    const team2Players = players.filter(p => p.team === 2);
    if (team1Players.length !== 2 || team2Players.length !== 2) {
      warnings.push(`Unbalanced teams - Team 1: ${team1Players.length}, Team 2: ${team2Players.length}`);
    }
  }

  // Validate current hand cards
  if (currentHandCards && currentHandCards.length > 4) {
    warnings.push(`Too many cards in current hand: ${currentHandCards.length}`);
  }

  // Check for duplicate cards in current hand
  if (currentHandCards && currentHandCards.length > 0) {
    const cardKeys = new Set();
    const duplicates = [];
    currentHandCards.forEach(card => {
      const key = `${card.value}${card.suit}`;
      if (cardKeys.has(key)) {
        duplicates.push(key);
      }
      cardKeys.add(key);
    });
    if (duplicates.length > 0) {
      errors.push(`Duplicate cards in current hand: ${duplicates.join(', ')}`);
    }
  }

  return { warnings, errors, isValid: errors.length === 0 };
}

module.exports = {
  selectCardToPlay,
  getCardRank,
  isCardHigher,
  getHighestCard,
  getLowestCard,
  getTrumpCards,
  getNonTrumpCards,
  validateGameState,
  // Export helper functions for testing
  getPartnerStatus,
  getOpponentTrumpStatus,
  findPartner
};
