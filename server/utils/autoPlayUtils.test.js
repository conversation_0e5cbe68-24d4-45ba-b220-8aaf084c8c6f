/**
 * Test file for autoPlayUtils.js
 * Tests the Trump card game auto-play decision flowchart implementation
 */

const autoPlayUtils = require('./autoPlayUtils');

// Mock data for testing
const createMockCard = (value, suit) => ({
  id: `${value}_${suit}`,
  value,
  suit,
  points: 0
});

const createMockPlayer = (id, name, team, position, isTrumpSelector = false) => ({
  id,
  name,
  team,
  position,
  isTrumpSelector
});

const createMockPlayers = () => [
  createMockPlayer('player1', 'Player 1', 1, 1),
  createMockPlayer('player2', 'Player 2', 2, 2),
  createMockPlayer('player3', 'Player 3', 1, 3),
  createMockPlayer('player4', 'Player 4', 2, 4)
];

// Test scenarios
function testFirstPlayTrumper() {
  console.log('\n=== TEST: First Play - Player is Trumper ===');
  
  const players = createMockPlayers();
  players[0].isTrumpSelector = true; // Player 1 is trumper
  
  const playerHand = [
    createMockCard('J', 'hearts'), // Highest trump
    createMockCard('9', 'hearts'), // Second highest trump
    createMockCard('A', 'spades'), // High non-trump
    createMockCard('K', 'clubs')   // Non-trump
  ];
  
  const currentHandCards = []; // First play
  const trumpSuit = 'hearts';
  const playerId = 'player1';
  
  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );
  
  console.log(`Expected: J hearts (highest trump)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === 'J' && selectedCard.suit === 'hearts' ? 'PASSED' : 'FAILED'}`);
}

function testFirstPlayNonTrumper() {
  console.log('\n=== TEST: First Play - Player is NOT Trumper ===');

  const players = createMockPlayers();
  players[1].isTrumpSelector = true; // Player 2 is trumper, not player 1

  const playerHand = [
    createMockCard('J', 'hearts'), // Trump
    createMockCard('9', 'hearts'), // Trump
    createMockCard('J', 'spades'), // Highest non-trump (J is highest in Thunee)
    createMockCard('K', 'clubs')   // Lower non-trump
  ];

  const currentHandCards = []; // First play
  const trumpSuit = 'hearts';
  const playerId = 'player1';

  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );

  console.log(`Expected: J spades (highest non-trump in Thunee ranking)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === 'J' && selectedCard.suit === 'spades' ? 'PASSED' : 'FAILED'}`);
}

function testFollowSuit() {
  console.log('\n=== TEST: Following - Can Follow Suit ===');

  const players = createMockPlayers();

  const playerHand = [
    createMockCard('K', 'spades'), // Lower ranking in Thunee
    createMockCard('10', 'spades'), // Higher ranking in Thunee (10 > K)
    createMockCard('J', 'hearts'), // Trump
    createMockCard('A', 'clubs')   // Different suit
  ];

  const currentHandCards = [
    { ...createMockCard('Q', 'spades'), playedBy: 'player2' } // Lead card
  ];
  const trumpSuit = 'hearts';
  const playerId = 'player1';

  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );

  console.log(`Expected: 10 spades (highest of led suit in Thunee ranking)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === '10' && selectedCard.suit === 'spades' ? 'PASSED' : 'FAILED'}`);
}

function testTrumpDecisionPartnerNotPlayed() {
  console.log('\n=== TEST: Trump Decision - Partner Not Played ===');
  
  const players = createMockPlayers();
  
  const playerHand = [
    createMockCard('J', 'hearts'), // High trump
    createMockCard('9', 'hearts'), // Lower trump
    createMockCard('A', 'clubs')   // Non-trump
  ];
  
  const currentHandCards = [
    { ...createMockCard('Q', 'spades'), playedBy: 'player2' } // Opponent played
  ];
  const trumpSuit = 'hearts';
  const playerId = 'player1'; // Player 3 is partner (same team)
  
  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );
  
  console.log(`Expected: 9 hearts (lowest trump - partner not played)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === '9' && selectedCard.suit === 'hearts' ? 'PASSED' : 'FAILED'}`);
}

function testTrumpDecisionBeatOpponent() {
  console.log('\n=== TEST: Trump Decision - Beat Opponent Trump ===');

  const players = createMockPlayers();

  const playerHand = [
    createMockCard('J', 'hearts'), // Highest trump - can beat opponent
    createMockCard('A', 'hearts'), // Lower trump
    createMockCard('K', 'clubs')   // Non-trump
  ];

  const currentHandCards = [
    { ...createMockCard('Q', 'spades'), playedBy: 'player2' }, // Opponent
    { ...createMockCard('K', 'spades'), playedBy: 'player3' }, // Partner played non-trump
    { ...createMockCard('10', 'hearts'), playedBy: 'player4' } // Opponent played trump
  ];
  const trumpSuit = 'hearts';
  const playerId = 'player1';

  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );

  console.log(`Expected: J hearts (highest trump that beats opponent)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === 'J' && selectedCard.suit === 'hearts' ? 'PASSED' : 'FAILED'}`);
}

function testTrumpDecisionCannotBeatOpponent() {
  console.log('\n=== TEST: Trump Decision - Cannot Beat Opponent Trump ===');

  const players = createMockPlayers();

  const playerHand = [
    createMockCard('A', 'hearts'), // Lower trump - cannot beat opponent
    createMockCard('K', 'hearts'), // Even lower trump
    createMockCard('Q', 'clubs')   // Non-trump
  ];

  const currentHandCards = [
    { ...createMockCard('Q', 'spades'), playedBy: 'player2' }, // Opponent
    { ...createMockCard('K', 'spades'), playedBy: 'player3' }, // Partner played non-trump
    { ...createMockCard('J', 'hearts'), playedBy: 'player4' } // Opponent played highest trump
  ];
  const trumpSuit = 'hearts';
  const playerId = 'player1';

  const selectedCard = autoPlayUtils.selectCardToPlay(
    playerHand, currentHandCards, trumpSuit, playerId, players
  );

  console.log(`Expected: Q clubs (lowest valued card - cannot beat opponent trump)`);
  console.log(`Actual: ${selectedCard.value} ${selectedCard.suit}`);
  console.log(`Test ${selectedCard.value === 'Q' && selectedCard.suit === 'clubs' ? 'PASSED' : 'FAILED'}`);
}

function runAllTests() {
  console.log('Starting Auto-Play Utils Tests...\n');

  testFirstPlayTrumper();
  testFirstPlayNonTrumper();
  testFollowSuit();
  testTrumpDecisionPartnerNotPlayed();
  testTrumpDecisionBeatOpponent();
  testTrumpDecisionCannotBeatOpponent();

  console.log('\n=== All Tests Complete ===');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testFirstPlayTrumper,
  testFirstPlayNonTrumper,
  testFollowSuit,
  testTrumpDecisionPartnerNotPlayed,
  testTrumpDecisionBeatOpponent,
  testTrumpDecisionCannotBeatOpponent
};
