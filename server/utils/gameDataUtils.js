const gameDataService = require('../services/gameDataService');

/**
 * Save game result to API when game ends
 * @param {Object} lobby - The game lobby
 * @param {Object} gameEndData - Game end data
 * @param {Array} allPlayers - All players in the game
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameEndResult(lobby, gameEndData, allPlayers, lobbyCode) {
  try {
    // Find the host player to use their token for the API call
    const hostPlayer = allPlayers.find(p => p.isHost);
    if (!hostPlayer) {
      console.error(`[GAME_DATA] No host player found for game end in ${lobbyCode}`);
      return;
    }

    // Check if this is a competition game
    if (lobby.competitionId) {
      // Format game result for competition scoring
      // Note: gameEndData.finalScores contains ball counts, not point scores
      const team1BallsWon = gameEndData.finalScores?.team1 || gameEndData.ballScores?.team1 || lobby.ballScores?.team1 || 0;
      const team2BallsWon = gameEndData.finalScores?.team2 || gameEndData.ballScores?.team2 || lobby.ballScores?.team2 || 0;

      const competitionGameResult = {
        winningTeam: gameEndData.winner,
        team1Score: team1BallsWon, // For competition scoring, use ball counts as scores
        team2Score: team2BallsWon,
        team1BallsWon: team1BallsWon,
        team2BallsWon: team2BallsWon,
        ballDifference: Math.abs(team1BallsWon - team2BallsWon),
        gameEndReason: gameEndData.reason || 'completed',
        duration: gameEndData.gameHistory?.duration || gameEndData.duration || 0
      };

      // Save competition game result using competition-specific endpoint
      gameDataService.saveCompetitionGameResult(lobby, competitionGameResult, hostPlayer.id).then(result => {
        if (result) {
          console.log(`[GAME_DATA] ✅ Competition game result saved successfully for ${lobbyCode} in competition ${lobby.competitionId}`);
        } else {
          console.error(`[GAME_DATA] ❌ Competition game result save failed for ${lobbyCode} - No result returned`);
        }
      }).catch(error => {
        console.error(`[GAME_DATA] ❌ Failed to save competition game result for ${lobbyCode}:`, error.message);
        console.error(`[GAME_DATA] Competition game data:`, competitionGameResult);
        console.error(`[GAME_DATA] Error details:`, error);
      });

      console.log(`[GAME_DATA] Competition game result save initiated for ${lobbyCode} in competition ${lobby.competitionId}`);
      console.log(`[GAME_DATA] Competition game scores: Team 1: ${competitionGameResult.team1Score}, Team 2: ${competitionGameResult.team2Score}, Winner: Team ${competitionGameResult.winningTeam}`);
      console.log(`[GAME_DATA] Competition ball scores: Team 1: ${competitionGameResult.team1BallsWon}, Team 2: ${competitionGameResult.team2BallsWon}, Ball difference: ${competitionGameResult.ballDifference}`);

      // ALSO save the regular game completion for competition games to update Games table
      const regularGameResult = {
        lobbyCode: lobby.lobbyCode,
        winnerTeam: gameEndData.winner,
        team1FinalScore: team1BallsWon,
        team2FinalScore: team2BallsWon
      };

      gameDataService.saveGameResult(lobby, regularGameResult, hostPlayer.id).then(result => {
        if (result) {
          console.log(`[GAME_DATA] ✅ Competition game completion saved successfully for ${lobbyCode}`);
        } else {
          console.error(`[GAME_DATA] ❌ Competition game completion save failed for ${lobbyCode} - No result returned`);
        }
      }).catch(error => {
        console.error(`[GAME_DATA] ❌ Failed to save competition game completion for ${lobbyCode}:`, error.message);
      });

    } else {
      // For non-competition games, save using the regular game result endpoint
      const team1BallsWon = gameEndData.finalScores?.team1 || gameEndData.ballScores?.team1 || lobby.ballScores?.team1 || 0;
      const team2BallsWon = gameEndData.finalScores?.team2 || gameEndData.ballScores?.team2 || lobby.ballScores?.team2 || 0;

      const regularGameResult = {
        lobbyCode: lobby.lobbyCode,
        winnerTeam: gameEndData.winner,
        team1FinalScore: team1BallsWon,
        team2FinalScore: team2BallsWon
      };

      // Save regular game result to API (async, don't wait for it)
      gameDataService.saveGameResult(lobby, regularGameResult, hostPlayer.id).then(result => {
        if (result) {
          console.log(`[GAME_DATA] ✅ Regular game result saved successfully for ${lobbyCode}`);
        } else {
          console.error(`[GAME_DATA] ❌ Regular game result save failed for ${lobbyCode} - No result returned`);
        }
      }).catch(error => {
        console.error(`[GAME_DATA] ❌ Failed to save regular game result for ${lobbyCode}:`, error.message);
        console.error(`[GAME_DATA] Game data:`, regularGameResult);
        console.error(`[GAME_DATA] Error details:`, error);
      });

      console.log(`[GAME_DATA] Regular game result save initiated for ${lobbyCode}`);
    }
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game result save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save ball result to API when ball completes (only for competition games)
 * @param {Object} lobby - The game lobby
 * @param {Object} ballData - Ball completion data
 * @param {string} hostPlayerId - ID of the host player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveBallEndResult(lobby, ballData, hostPlayerId, lobbyCode) {
  try {
    // Only save ball results for competition games
    if (!lobby.competitionId) {
      console.log(`[GAME_DATA] Skipping ball result save for non-competition game ${lobbyCode}`);
      return;
    }

    // Enhance ball data with complete hand information
    const enhancedBallData = {
      ...ballData,
      // Ensure we have all the hands from this ball
      hands: lobby.hands || ballData.hands || [],
      // Ensure we have team information
      team1Name: lobby.teamNames?.[1] || ballData.team1Name || 'Team 1',
      team2Name: lobby.teamNames?.[2] || ballData.team2Name || 'Team 2',
      // Ensure we have trump suit
      trumpSuit: ballData.trumpSuit || lobby.gameState?.trumpSuit,
      // Ensure we have competition ID
      competitionId: lobby.competitionId
    };

    console.log(`[GAME_DATA] Saving ball result for competition game ${lobbyCode}:`);
    console.log(`[GAME_DATA] - Ball Number: ${enhancedBallData.ballNumber || ballData.ballId}`);
    console.log(`[GAME_DATA] - Winner Team: ${enhancedBallData.winnerTeam || enhancedBallData.winner}`);
    console.log(`[GAME_DATA] - Hands in Ball: ${enhancedBallData.hands.length}`);
    console.log(`[GAME_DATA] - Competition ID: ${enhancedBallData.competitionId}`);

    // Save ball result to API (async, don't wait for it)
    gameDataService.saveBallResult(lobby, enhancedBallData, hostPlayerId).then(result => {
      if (result) {
        console.log(`[GAME_DATA] ✅ Ball result saved successfully for ${lobbyCode} ball ${enhancedBallData.ballNumber || ballData.ballId}`);
      } else {
        console.error(`[GAME_DATA] ❌ Ball result save failed for ${lobbyCode} ball ${enhancedBallData.ballNumber || ballData.ballId} - No result returned`);
      }
    }).catch(error => {
      console.error(`[GAME_DATA] ❌ Failed to save ball result for ${lobbyCode}:`, error.message);
      console.error(`[GAME_DATA] Ball data:`, enhancedBallData);
      console.error(`[GAME_DATA] Error details:`, error);
    });

    console.log(`[GAME_DATA] Competition ball result save initiated for ${lobbyCode} ball ${enhancedBallData.ballNumber || ballData.ballId}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating ball result save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save hand result to API when hand completes (only for competition games)
 * @param {Object} lobby - The game lobby
 * @param {Object} handData - Hand completion data
 * @param {string} winningPlayerId - ID of the winning player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveHandEndResult(lobby, handData, winningPlayerId, lobbyCode) {
  try {
    // Only save hand results for competition games
    if (!lobby.competitionId) {
      console.log(`[GAME_DATA] Skipping hand result save for non-competition game ${lobbyCode}`);
      return;
    }

    // Save hand result to API (async, don't wait for it)
    gameDataService.saveHandResult(lobby, handData, winningPlayerId).then(result => {
      if (result) {
        console.log(`[GAME_DATA] ✅ Hand result saved successfully for ${lobbyCode} hand ${handData.handNumber || handData.handId}`);
      } else {
        console.error(`[GAME_DATA] ❌ Hand result save failed for ${lobbyCode} hand ${handData.handNumber || handData.handId} - No result returned`);
      }
    }).catch(error => {
      console.error(`[GAME_DATA] ❌ Failed to save hand result for ${lobbyCode}:`, error.message);
      console.error(`[GAME_DATA] Hand data:`, handData);
      console.error(`[GAME_DATA] Error details:`, error);
    });

    console.log(`[GAME_DATA] Competition hand result save initiated for ${lobbyCode} hand ${handData.handId || handData.handNumber}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating hand result save for ${lobbyCode}:`, error.message);
  }
}

// Card play and Jordhi call saving functions removed - no longer tracking individual cards or calls

/**
 * Save game start to API
 * @param {Object} lobby - The game lobby
 * @param {string} hostPlayerId - ID of the host player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameStartResult(lobby, hostPlayerId, lobbyCode) {
  try {
    // Save game start to API (async, don't wait for it)
    gameDataService.saveGameStart(lobby, hostPlayerId).catch(error => {
      console.error(`[GAME_DATA] Failed to save game start for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Game start save initiated for ${lobbyCode}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game start save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save player join to API
 * @param {Object} lobby - The game lobby
 * @param {string} playerId - ID of the joining player
 * @param {string} playerName - Name of the joining player
 * @param {string} lobbyCode - Lobby code for logging
 */
function savePlayerJoinResult(lobby, playerId, playerName, lobbyCode) {
  try {
    // Save player join to API (async, don't wait for it)
    gameDataService.savePlayerJoin(lobby, playerId, playerName).catch(error => {
      console.error(`[GAME_DATA] Failed to save player join for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Player join save initiated for ${lobbyCode}: ${playerName}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating player join save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save game creation to API
 * @param {Object} lobby - The game lobby
 * @param {string} hostPlayerId - ID of the host player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameCreationResult(lobby, hostPlayerId, lobbyCode) {
  try {
    // Save game creation to API (async, don't wait for it)
    gameDataService.saveGameCreation(lobby, hostPlayerId).then(result => {
      if (result) {
        console.log(`[GAME_DATA] ✅ Game creation saved successfully for ${lobbyCode} -> Game ID: ${result.id}`);
      } else {
        console.error(`[GAME_DATA] ❌ Game creation failed for ${lobbyCode} - No result returned`);
      }
    }).catch(error => {
      console.error(`[GAME_DATA] ❌ Failed to save game creation for ${lobbyCode}:`, error.message);
      console.error(`[GAME_DATA] Error details:`, error);
    });

    console.log(`[GAME_DATA] Game creation save initiated for ${lobbyCode}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game creation save for ${lobbyCode}:`, error.message);
  }
}

module.exports = {
  saveGameEndResult,
  saveBallEndResult,
  saveHandEndResult,
  // saveCardPlayResult and saveJordhiCallResult removed
  saveGameStartResult,
  savePlayerJoinResult,
  saveGameCreationResult
};
