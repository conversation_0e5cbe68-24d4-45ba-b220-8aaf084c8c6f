/**
 * Utility functions for dealing the final 2 cards
 */
const cardUtils = require('./cardUtils');

/**
 * Deal the final 2 cards to all players
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The lobby object
 * @param {Object} matchedLobby - The matched lobby object
 */
function dealFinalCards(io, lobby, matchedLobby) {
  console.log(`Dealing final 2 cards to all players in lobby ${lobby.lobbyCode}`);

  // Get all players from both lobbies
  const allPlayers = [];

  // Check if this is a single lobby with all players (invite code case)
  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const totalPlayers = team1Count + team2Count;
  const isSingleLobby = totalPlayers === 4;

  // Add players from both teams in both lobbies
  for (const teamId of [1, 2]) {
    if (lobby.teams[teamId]) {
      lobby.teams[teamId].forEach(player => allPlayers.push(player));
    }

    // Only add players from matched lobby if it's different from the main lobby
    if (!isSingleLobby && matchedLobby && matchedLobby.teams && matchedLobby.teams[teamId]) {
      matchedLobby.teams[teamId].forEach(player => allPlayers.push(player));
    }
  }

  console.log(`Found ${allPlayers.length} players total for final card dealing`);

  // Create a new deck for the final 2 cards
  console.log('Creating new deck for final cards');
  const finalDeck = cardUtils.createGameDeck();

  // Initialize or reset the allDealtCards set if it doesn't exist
  if (!lobby.allDealtCards) {
    lobby.allDealtCards = new Set();
  }

  if (matchedLobby && !matchedLobby.allDealtCards) {
    matchedLobby.allDealtCards = new Set();
  }

  // Ensure the deck has no duplicates
  if (cardUtils.hasDuplicateCards(finalDeck)) {
    console.warn('Duplicates found in final deck, removing duplicates');
    const uniqueDeck = cardUtils.removeDuplicateCards(finalDeck);
    console.log(`Unique deck has ${uniqueDeck.length} cards`);
  }

  // Get existing cards for each player
  const existingCards = {};
  let allExistingCardKeys = new Set();

  // First check if we have stored player cards in the lobby
  if (lobby.playerCards) {
    console.log('Using stored player cards from lobby');
    Object.entries(lobby.playerCards).forEach(([playerId, cards]) => {
      // Make sure we have a valid array of cards
      if (Array.isArray(cards) && cards.length > 0) {
        existingCards[playerId] = [...cards];
        console.log(`Player ${playerId} already has ${cards.length} cards from lobby storage`);

        // Track all existing cards to avoid duplicates
        cards.forEach(card => {
          if (card && card.value && card.suit) {
            const cardKey = `${card.value}_${card.suit}`;
            allExistingCardKeys.add(cardKey);
          }
        });
      } else {
        console.log(`Player ${playerId} has no valid cards, initializing empty array`);
        existingCards[playerId] = [];
      }
    });
  } else {
    // If no stored cards, initialize empty arrays
    allPlayers.forEach(player => {
      existingCards[player.id] = [];
    });
  }

  // Log the total number of existing cards
  console.log(`Total existing cards tracked: ${allExistingCardKeys.size}`);

  // Verify all players have entries in existingCards
  allPlayers.forEach(player => {
    if (!existingCards[player.id]) {
      console.log(`Player ${player.name} (${player.id}) missing from existingCards, initializing`);
      existingCards[player.id] = [];
    }
  });

  // Deal cards to each player to ensure they have exactly 6 cards
  allPlayers.forEach(player => {
    // Initialize player cards if not already done
    if (!existingCards[player.id]) {
      console.log(`Initializing cards array for player ${player.name} (${player.id})`);
      existingCards[player.id] = [];
    }

    // Validate existing cards array
    if (!Array.isArray(existingCards[player.id])) {
      console.warn(`Invalid cards array for player ${player.id}, resetting to empty array`);
      existingCards[player.id] = [];
    }

    // Skip if player already has 6 cards
    if (existingCards[player.id].length >= 6) {
      console.log(`Player ${player.name} (${player.id}) already has ${existingCards[player.id].length} cards, skipping`);
      return;
    }

    // Calculate how many cards to deal
    const cardsNeeded = 6 - existingCards[player.id].length;
    console.log(`Player ${player.name} (${player.id}) needs ${cardsNeeded} more cards`);

    // Deal the needed cards
    const newCards = [];
    for (let i = 0; i < cardsNeeded; i++) {
      // Find a card that hasn't been dealt yet
      let card = null;
      let attempts = 0;

      while (!card && attempts < 100) {
        attempts++;
        const randomIndex = Math.floor(Math.random() * finalDeck.length);
        const candidateCard = finalDeck[randomIndex];
        const cardKey = `${candidateCard.value}_${candidateCard.suit}`;

        if (!allExistingCardKeys.has(cardKey)) {
          card = candidateCard;
          allExistingCardKeys.add(cardKey);
          finalDeck.splice(randomIndex, 1); // Remove the card from the deck
        }
      }

      if (card) {
        newCards.push(card);
        console.log(`Dealt ${card.value} of ${card.suit} to ${player.name} (${player.id})`);
      } else {
        console.error(`Could not find a unique card for player ${player.name} after ${attempts} attempts`);
      }
    }

    // Add the new cards to the player's existing cards
    existingCards[player.id] = [...existingCards[player.id], ...newCards];

    // Emit card dealt events for each new card
    newCards.forEach(card => {
      console.log(`Emitting card_dealt event for ${card.value} of ${card.suit} to ${player.name} (${player.id})`);

      // Emit to the main lobby
      io.to(lobby.lobbyCode).emit('card_dealt', {
        playerId: player.id,
        card: card,
        isDealer: false
      });

      // Only emit to matched lobby if it's different from the main lobby
      if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('card_dealt', {
          playerId: player.id,
          card: card,
          isDealer: false
        });
      }
    });

    // Send all cards to the player
    if (existingCards[player.id].length > 0) {
      console.log(`Sending all ${existingCards[player.id].length} cards to player ${player.name} (${player.id})`);
      io.to(player.id).emit('receive_cards', { cards: existingCards[player.id] });
    }
  });

  // Store the updated player cards in the lobby
  lobby.playerCards = existingCards;
  matchedLobby.playerCards = existingCards;

  // Log the number of cards each player has and validate
  let allPlayersHaveSixCards = true;
  Object.entries(existingCards).forEach(([playerId, cards]) => {
    console.log(`Player ${playerId} now has ${cards.length} cards total`);
    if (cards.length !== 6) {
      console.error(`ERROR: Player ${playerId} has ${cards.length} cards instead of 6!`);
      allPlayersHaveSixCards = false;
    }
  });

  if (!allPlayersHaveSixCards) {
    console.error("CRITICAL ERROR: Not all players have 6 cards after dealing!");

    // Force fix: ensure all players have exactly 6 cards
    allPlayers.forEach(player => {
      if (!existingCards[player.id] || !Array.isArray(existingCards[player.id]) || existingCards[player.id].length !== 6) {
        console.log(`Fixing card count for player ${player.name} (${player.id})`);

        // Create a new set of 6 cards for this player
        const playerCards = [];
        const playerCardKeys = new Set();

        // Try to keep any valid existing cards
        if (existingCards[player.id] && Array.isArray(existingCards[player.id])) {
          existingCards[player.id].forEach(card => {
            if (card && card.value && card.suit) {
              playerCards.push(card);
              playerCardKeys.add(`${card.value}_${card.suit}`);
            }
          });
        }

        // Add new cards until we have 6
        while (playerCards.length < 6 && finalDeck.length > 0) {
          const randomIndex = Math.floor(Math.random() * finalDeck.length);
          const card = finalDeck[randomIndex];
          const cardKey = `${card.value}_${card.suit}`;

          if (!playerCardKeys.has(cardKey) && !allExistingCardKeys.has(cardKey)) {
            playerCards.push(card);
            playerCardKeys.add(cardKey);
            allExistingCardKeys.add(cardKey);
            finalDeck.splice(randomIndex, 1);

            // Emit this card to all players
            console.log(`Emergency dealing: ${card.value} of ${card.suit} to ${player.name} (${player.id})`);
            io.to(lobby.lobbyCode).emit('card_dealt', {
              playerId: player.id,
              card: card,
              isDealer: false
            });

            if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
              io.to(matchedLobby.lobbyCode).emit('card_dealt', {
                playerId: player.id,
                card: card,
                isDealer: false
              });
            }
          }
        }

        // Update the player's cards
        existingCards[player.id] = playerCards;

        // Send all cards to the player
        io.to(player.id).emit('receive_cards', { cards: playerCards });
      }
    });
  }

  // Notify all clients that all cards have been dealt
  io.to(lobby.lobbyCode).emit('cards_dealt', { dealerId: lobby.dealerId });

  // Only emit to matched lobby if it's different from the main lobby
  if (!isSingleLobby && matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('cards_dealt', { dealerId: lobby.dealerId });
  }
}

module.exports = {
  dealFinalCards
};
