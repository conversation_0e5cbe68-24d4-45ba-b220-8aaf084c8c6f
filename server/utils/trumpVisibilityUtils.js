/**
 * Trump Visibility Voting Utilities
 * Handles voting for trump suit display duration
 */

/**
 * Initialize trump visibility voting for a lobby
 * @param {Object} lobby - The lobby object
 */
function initTrumpVisibilityVoting(lobby) {
  if (!lobby.trumpVisibilityVoting) {
    lobby.trumpVisibilityVoting = {
      votes: {}, // playerId -> 'short' | 'full'
      results: {
        short: 0,
        full: 0
      },
      selectedMode: 'full', // Default to full hand if tied
      votingComplete: false
    };
  }
}

/**
 * Record a trump visibility vote
 * @param {Object} lobby - The lobby object
 * @param {string} playerId - The player ID
 * @param {string} mode - The visibility mode ('short' or 'full')
 * @returns {Object} The updated voting state
 */
function recordTrumpVisibilityVote(lobby, playerId, mode) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);

  // Validate mode
  if (mode !== 'short' && mode !== 'full') {
    throw new Error(`Invalid trump visibility mode: ${mode}`);
  }

  console.log(`Recording trump visibility vote from player ${playerId}: ${mode}`);

  // Record the vote
  lobby.trumpVisibilityVoting.votes[playerId] = mode;

  // Update the results
  updateVoteResults(lobby);

  // Log the current voting state
  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const playerCount = team1Count + team2Count;
  const voteCount = Object.keys(lobby.trumpVisibilityVoting.votes).length;

  console.log(`Current trump visibility voting state: ${voteCount}/${playerCount} votes recorded`);
  console.log(`Vote results:`, lobby.trumpVisibilityVoting.results);

  return lobby.trumpVisibilityVoting;
}

/**
 * Update the vote results based on the current votes
 * @param {Object} lobby - The lobby object
 */
function updateVoteResults(lobby) {
  // Reset the results
  lobby.trumpVisibilityVoting.results = {
    short: 0,
    full: 0
  };

  // Count the votes
  for (const playerId in lobby.trumpVisibilityVoting.votes) {
    const vote = lobby.trumpVisibilityVoting.votes[playerId];
    lobby.trumpVisibilityVoting.results[vote]++;
  }
}

/**
 * Check if all players have voted
 * @param {Object} lobby - The lobby object
 * @returns {boolean} True if all players have voted
 */
function allPlayersVoted(lobby) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);

  const team1Count = lobby.teams[1]?.length || 0;
  const team2Count = lobby.teams[2]?.length || 0;
  const playerCount = team1Count + team2Count;
  const voteCount = Object.keys(lobby.trumpVisibilityVoting.votes).length;

  console.log(`Trump visibility voting check: ${voteCount}/${playerCount} players voted`);
  return voteCount >= playerCount;
}

/**
 * Determine the selected trump visibility mode based on votes
 * @param {Object} lobby - The lobby object
 * @returns {string} The selected mode ('short' or 'full')
 */
function determineSelectedMode(lobby) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);

  const results = lobby.trumpVisibilityVoting.results;
  
  console.log(`Determining trump visibility mode from results:`, results);

  // If 'full' has more votes, select 'full'
  if (results.full > results.short) {
    console.log(`'full' mode selected with ${results.full} votes vs ${results.short}`);
    lobby.trumpVisibilityVoting.selectedMode = 'full';
    lobby.trumpVisibilityVoting.votingComplete = true;
    return 'full';
  }
  // If 'short' has more votes, select 'short'
  else if (results.short > results.full) {
    console.log(`'short' mode selected with ${results.short} votes vs ${results.full}`);
    lobby.trumpVisibilityVoting.selectedMode = 'short';
    lobby.trumpVisibilityVoting.votingComplete = true;
    return 'short';
  }
  // If tied or no votes, default to 'full' as specified
  else {
    console.log(`Tie or no votes - defaulting to 'full' mode`);
    lobby.trumpVisibilityVoting.selectedMode = 'full';
    lobby.trumpVisibilityVoting.votingComplete = true;
    return 'full';
  }
}

/**
 * Get the current trump visibility voting state
 * @param {Object} lobby - The lobby object
 * @returns {Object} The current voting state
 */
function getVotingState(lobby) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);

  return {
    votes: lobby.trumpVisibilityVoting.votes,
    results: lobby.trumpVisibilityVoting.results,
    selectedMode: lobby.trumpVisibilityVoting.selectedMode,
    votingComplete: lobby.trumpVisibilityVoting.votingComplete
  };
}

/**
 * Reset the trump visibility voting state
 * @param {Object} lobby - The lobby object
 */
function resetTrumpVisibilityVoting(lobby) {
  lobby.trumpVisibilityVoting = {
    votes: {},
    results: {
      short: 0,
      full: 0
    },
    selectedMode: 'full',
    votingComplete: false
  };
}

/**
 * Check if trump visibility voting is complete
 * @param {Object} lobby - The lobby object
 * @returns {boolean} True if voting is complete
 */
function isVotingComplete(lobby) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);
  
  return lobby.trumpVisibilityVoting.votingComplete;
}

/**
 * Get the selected trump visibility mode
 * @param {Object} lobby - The lobby object
 * @returns {string} The selected mode ('short' or 'full')
 */
function getSelectedMode(lobby) {
  // Initialize voting if needed
  initTrumpVisibilityVoting(lobby);
  
  return lobby.trumpVisibilityVoting.selectedMode;
}

module.exports = {
  initTrumpVisibilityVoting,
  recordTrumpVisibilityVote,
  updateVoteResults,
  allPlayersVoted,
  determineSelectedMode,
  getVotingState,
  resetTrumpVisibilityVoting,
  isVotingComplete,
  getSelectedMode
};
