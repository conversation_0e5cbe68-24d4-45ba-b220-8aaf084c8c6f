# Trump Card Game Auto-Play Implementation

## Overview
This document describes the implementation of the Trump card game auto-play system that follows the exact decision flowchart provided. The system automatically selects cards for players who timeout during their turn.

## Implementation Files
- **`autoPlayUtils.js`** - Main implementation with decision logic
- **`autoPlayUtils.test.js`** - Comprehensive test suite
- **`cardPlayHandler.js`** - Integration with game server
- **`turnUtils.js`** - Timer and timeout handling

## Decision Flowchart Implementation

The auto-play system follows this exact decision tree:

### First Play Logic
1. **Is this the first play of the round?** → YES
   - **Is player the Trumper?** → YES: Play highest ranking Trump card
   - **Is player the Trumper?** → NO: Play highest ranking non-Trump card

### Following Play Logic
2. **Is this the first play of the round?** → NO
   - **Can follow suit with led color?** → YES: Play highest card of the led suit
   - **Can follow suit with led color?** → NO: Continue to trump logic

### Trump Decision Logic
3. **Do you have Trump cards?** → NO: Play lowest valued card
4. **Do you have Trump cards?** → YES: Continue to partner logic
5. **Has partner played yet?** → NO: Play lowest Trump
6. **Has partner played yet?** → YES: Continue to opponent logic
7. **Has opponent played a Trump card?** → NO: Play lowest Trump
8. **Has opponent played a Trump card?** → YES: Continue to beating logic
9. **Do you have Trump higher than opponent's?** → YES: Play highest Trump that beats opponent
10. **Do you have Trump higher than opponent's?** → NO: Play lowest valued card

## Key Features

### Card Rankings
- **Trump cards**: J, 9, A, 10, K, Q (highest to lowest)
- **Non-trump cards**: J, 9, A, 10, K, Q (highest to lowest)

### Hand Tracking
- Tracks all players and their teams
- Monitors cards played in current hand
- Identifies partner and opponent relationships
- Validates game state before making decisions

### Enhanced Logging
- Comprehensive debug logging for all decisions
- Game state validation with warnings and errors
- Step-by-step decision process tracking
- Clear indication of selected card and reasoning

### Partnership System
- Automatically identifies partner based on team membership
- Tracks whether partner has played in current hand
- Determines if partner is currently winning the trick

### Opponent Analysis
- Identifies opponent team members
- Tracks trump cards played by opponents
- Finds highest opponent trump for beating decisions

## Integration Points

### Game Server Integration
The auto-play system integrates with the game server through:

1. **Timer System** (`turnUtils.js`)
   - Triggers auto-play when player times out
   - Configurable timeout duration (default: 60 seconds)

2. **Card Play Handler** (`cardPlayHandler.js`)
   - Processes auto-played cards
   - Updates game state
   - Emits events to all players
   - Continues game flow

3. **Game State Management**
   - Uses lobby.playerCards for player hands
   - Uses lobby.currentHandCards for played cards
   - Uses lobby.trumpSuit for trump information
   - Uses lobby.players for team/position data

## Testing

The implementation includes comprehensive tests covering:

1. **First Play Scenarios**
   - Trumper playing highest trump
   - Non-trumper playing highest non-trump

2. **Following Scenarios**
   - Following suit with highest card
   - Cannot follow suit, using trump logic

3. **Trump Decision Scenarios**
   - Partner not played yet
   - Beating opponent trump
   - Cannot beat opponent trump

All tests pass and validate the correct implementation of the flowchart logic.

## Error Handling

### Validation
- Validates player hand exists and is not empty
- Validates trump suit is valid
- Validates players array has correct structure
- Validates game state consistency

### Fallbacks
- Returns null if no valid card can be selected
- Logs errors for debugging
- Provides warnings for unusual game states

## Performance Considerations

- Efficient card filtering and sorting
- Minimal memory allocation during decisions
- Fast partner/opponent identification
- Optimized card comparison algorithms

## Future Enhancements

Potential improvements could include:
- Machine learning for more sophisticated strategies
- Historical game analysis for pattern recognition
- Dynamic difficulty adjustment based on player skill
- Advanced partnership coordination strategies

## Usage Example

```javascript
const autoPlayUtils = require('./autoPlayUtils');

const selectedCard = autoPlayUtils.selectCardToPlay(
  playerHand,        // Array of cards in player's hand
  currentHandCards,  // Array of cards played in current hand
  trumpSuit,         // String: 'hearts', 'diamonds', 'clubs', 'spades'
  playerId,          // String: Player ID
  players,           // Array: All players with team/position info
  lobby              // Object: Game lobby (optional)
);
```

The function returns the optimal card to play based on the implemented decision flowchart.
