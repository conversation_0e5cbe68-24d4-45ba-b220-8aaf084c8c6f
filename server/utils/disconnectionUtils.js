/**
 * Disconnection and Reconnection Utilities
 * Handles player disconnections, timeouts, and rejoin logic for Thunee game
 */

const gameEndUtils = require('./gameEndUtils');

// Store active disconnection timers
const disconnectionTimers = new Map();

// Store disconnected players for rejoin attempts
const disconnectedPlayers = new Map();

/**
 * Start disconnection timer for a player
 * @param {Object} io - Socket.io instance
 * @param {String} socketId - Player's socket ID
 * @param {String} lobbyCode - Lobby code
 * @param {Object} player - Player object
 * @param {Object} lobby - Lobby object
 * @param {Object} matchedLobby - Matched lobby object (if exists)
 * @param {Number} timeoutSeconds - Timeout in seconds (default 300)
 */
function startDisconnectionTimer(io, socketId, lobbyCode, player, lobby, matchedLobby, timeoutSeconds = 300) {
  // Clear any existing timer for this player
  clearDisconnectionTimer(socketId);

  console.log(`Starting disconnection timer for player ${player.name} (${socketId}) - ${timeoutSeconds} seconds`);

  const startTime = Date.now();
  const timeoutMs = timeoutSeconds * 1000;

  // Store disconnected player info for rejoin attempts
  const persistentKey = player.persistentId || socketId;
  console.log(`Storing disconnected player with key: ${persistentKey}`);
  disconnectedPlayers.set(persistentKey, {
    socketId,
    lobbyCode,
    player: { ...player },
    lobby,
    matchedLobby,
    disconnectedAt: startTime
  });

  // Also store by socket ID for backward compatibility
  if (player.persistentId && player.persistentId !== socketId) {
    disconnectedPlayers.set(socketId, {
      socketId,
      lobbyCode,
      player: { ...player },
      lobby,
      matchedLobby,
      disconnectedAt: startTime
    });
  }

  // Pause the game if it's in progress
  pauseGame(io, lobby, matchedLobby, player);

  // Notify other players about the disconnection
  const disconnectionData = {
    playerId: socketId,
    playerName: player.name,
    playerTeam: player.team,
    timeoutSeconds,
    startTime
  };

  console.log(`Emitting player_disconnected to lobby ${lobbyCode}:`, disconnectionData);
  io.to(lobbyCode).emit('player_disconnected', disconnectionData);
  if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
    console.log(`Emitting player_disconnected to matched lobby ${matchedLobby.lobbyCode}:`, disconnectionData);
    io.to(matchedLobby.lobbyCode).emit('player_disconnected', disconnectionData);
  }

  // Send toast notification to remaining players
  const toastMessage = `${player.name} disconnected. Waiting ${timeoutSeconds} seconds to reconnect.`;
  console.log(`Emitting toast_notification to lobby ${lobbyCode}: ${toastMessage}`);
  io.to(lobbyCode).emit('toast_notification', {
    type: 'warning',
    message: toastMessage,
    duration: 5000
  });
  if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
    console.log(`Emitting toast_notification to matched lobby ${matchedLobby.lobbyCode}: ${toastMessage}`);
    io.to(matchedLobby.lobbyCode).emit('toast_notification', {
      type: 'warning',
      message: toastMessage,
      duration: 5000
    });
  }

  // Set up countdown updates every 5 seconds
  const countdownInterval = setInterval(() => {
    const elapsed = Date.now() - startTime;
    const remaining = Math.max(0, Math.ceil((timeoutMs - elapsed) / 1000));
    
    if (remaining > 0) {
      io.to(lobbyCode).emit('disconnection_countdown', {
        playerId: socketId,
        playerName: player.name,
        timeRemaining: remaining
      });
      if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
        io.to(matchedLobby.lobbyCode).emit('disconnection_countdown', {
          playerId: socketId,
          playerName: player.name,
          timeRemaining: remaining
        });
      }
    }
  }, 5000);

  // Set up the main timeout
  const timer = setTimeout(() => {
    clearInterval(countdownInterval);
    handleDisconnectionTimeout(io, socketId, lobbyCode, player, lobby, matchedLobby);
  }, timeoutMs);

  // Store timer info
  disconnectionTimers.set(socketId, {
    timer,
    countdownInterval,
    startTime,
    lobbyCode,
    playerId: socketId,
    playerName: player.name,
    playerTeam: player.team
  });
}

/**
 * Clear disconnection timer for a player
 * @param {String} socketId - Player's socket ID
 */
function clearDisconnectionTimer(socketId) {
  const timerInfo = disconnectionTimers.get(socketId);
  if (timerInfo) {
    clearTimeout(timerInfo.timer);
    clearInterval(timerInfo.countdownInterval);
    disconnectionTimers.delete(socketId);
    console.log(`Cleared disconnection timer for player ${socketId}`);
  }
}

/**
 * Check if a disconnection timer exists for a player
 * @param {String} socketId - Player's socket ID
 * @returns {Boolean} True if timer exists
 */
function hasDisconnectionTimer(socketId) {
  return disconnectionTimers.has(socketId);
}

/**
 * Handle disconnection timeout - end game with appropriate ball allocation
 * @param {Object} io - Socket.io instance
 * @param {String} socketId - Player's socket ID
 * @param {String} lobbyCode - Lobby code
 * @param {Object} player - Player object
 * @param {Object} lobby - Lobby object
 * @param {Object} matchedLobby - Matched lobby object (if exists)
 */
function handleDisconnectionTimeout(io, socketId, lobbyCode, player, lobby, matchedLobby) {
  console.log(`Disconnection timeout for player ${player.name} (${socketId})`);

  // Remove from disconnected players map (clean up all related entries)
  const persistentKey = player.persistentId || socketId;
  disconnectedPlayers.delete(persistentKey);
  disconnectedPlayers.delete(socketId);

  // Also clean up any entries with the same user ID pattern
  if (player.persistentId) {
    const userIdMatch = player.persistentId.match(/^player_([^_]+)_/);
    if (userIdMatch) {
      const userId = userIdMatch[1];
      const keysToDelete = [];
      for (const [key, info] of disconnectedPlayers.entries()) {
        if (key.includes(userId) && info.lobbyCode === lobbyCode) {
          keysToDelete.push(key);
        }
      }
      keysToDelete.forEach(key => disconnectedPlayers.delete(key));
      console.log(`Timeout cleanup: removed ${keysToDelete.length} disconnected player entries for user ${userId}`);
    }
  }

  // Determine if there was a winning Khanak call
  const hasWinningKhanak = lobby.winningKhanakCall || (matchedLobby && matchedLobby.winningKhanakCall);
  const ballsToWin = hasWinningKhanak ? 13 : 12;

  // Get current ball scores
  const currentScores = lobby.ballScores || { team1: 0, team2: 0 };
  
  // Determine which team the disconnected player belongs to
  const disconnectedTeam = player.team;
  const opposingTeam = disconnectedTeam === 1 ? 2 : 1;

  // Set the opposing team's score to the winning amount
  const finalScores = { ...currentScores };
  finalScores[`team${opposingTeam}`] = ballsToWin;

  // Create game end data
  const gameEndData = {
    gameEnded: true,
    winner: opposingTeam,
    reason: 'disconnection_timeout',
    finalScores,
    disconnectedPlayer: {
      name: player.name,
      team: disconnectedTeam
    },
    ballsToWin,
    hasWinningKhanak
  };

  // Send final toast notification
  const finalMessage = `${player.name} failed to reconnect. Game ended. Team ${opposingTeam} wins!`;
  io.to(lobbyCode).emit('toast_notification', {
    type: 'error',
    message: finalMessage,
    duration: 8000
  });
  if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('toast_notification', {
      type: 'error',
      message: finalMessage,
      duration: 8000
    });
  }

  // Emit game ended event
  io.to(lobbyCode).emit('game_ended', gameEndData);
  if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);
  }

  // Update lobby state
  lobby.gameEnded = true;
  lobby.gameEndReason = 'disconnection_timeout';
  lobby.ballScores = finalScores;
  
  if (matchedLobby) {
    matchedLobby.gameEnded = true;
    matchedLobby.gameEndReason = 'disconnection_timeout';
    matchedLobby.ballScores = finalScores;
  }

  console.log(`Game ended due to disconnection timeout. Team ${opposingTeam} wins with ${ballsToWin} balls.`);
}

/**
 * Handle player rejoin attempt
 * @param {Object} io - Socket.io instance
 * @param {Object} socket - New socket connection
 * @param {String} persistentId - Player's persistent ID
 * @param {String} gameId - Game/lobby ID
 * @returns {Object} Rejoin result
 */
function handlePlayerRejoin(io, socket, persistentId, gameId) {
  console.log(`Rejoin attempt: persistentId=${persistentId}, gameId=${gameId}`);
  console.log(`Available disconnected players:`, Array.from(disconnectedPlayers.keys()));

  // First check if the player is already in the lobby and connected
  const lobbies = require('../index').lobbies;
  const lobby = lobbies.get(gameId);

  if (lobby) {
    // Extract user ID from persistent ID to find the player
    const userIdMatch = persistentId.match(/^player_([^_]+)_/);
    if (userIdMatch) {
      const userId = userIdMatch[1];

      // Check if player is already in the lobby
      const existingPlayer = lobby.players.find(p => p.userId === userId);
      if (existingPlayer) {
        console.log(`Player ${userId} is already in lobby ${gameId}, updating socket ID`);

        // Update the existing player's socket ID
        existingPlayer.id = socket.id;

        // Update team arrays as well
        const teamPlayer = lobby.teams[existingPlayer.team].find(p => p.userId === userId);
        if (teamPlayer) {
          teamPlayer.id = socket.id;
        }

        // Update matched lobby if it exists
        const matchedLobby = lobby.matchedLobby ? lobbies.get(lobby.matchedLobby) : null;
        if (matchedLobby) {
          const matchedPlayer = matchedLobby.players.find(p => p.userId === userId);
          if (matchedPlayer) {
            matchedPlayer.id = socket.id;

            const matchedTeamPlayer = matchedLobby.teams[matchedPlayer.team].find(p => p.userId === userId);
            if (matchedTeamPlayer) {
              matchedTeamPlayer.id = socket.id;
            }
          }
        }

        console.log(`Player ${existingPlayer.name} successfully reconnected to lobby ${gameId}`);

        return {
          success: true,
          lobbyCode: gameId,
          gameState: getGameStateForPlayer(lobby, matchedLobby, socket.id),
          alreadyConnected: true
        };
      }
    }
  }

  // Find the disconnected player - try multiple lookup strategies
  let disconnectedInfo = disconnectedPlayers.get(persistentId);

  // If direct lookup failed, try to find by user ID pattern
  if (!disconnectedInfo) {
    console.log('Direct lookup failed, trying pattern matching for persistentId:', persistentId);
    const userIdMatch = persistentId.match(/^player_([^_]+)_/);
    if (userIdMatch) {
      const userId = userIdMatch[1];
      console.log('Extracted user ID:', userId, 'searching for matching disconnected players');

      // Look for any disconnected player with the same user ID in the same game
      for (const [key, info] of disconnectedPlayers.entries()) {
        if (key.includes(userId) && info.lobbyCode === gameId) {
          console.log('Found matching disconnected player with key:', key);
          disconnectedInfo = info;
          // Update the persistent ID to match what we found
          persistentId = key;
          break;
        }
      }
    }
  }



  if (!disconnectedInfo) {
    console.log(`No disconnected player found for persistentId: ${persistentId}`);
    return { success: false, error: 'No disconnection record found or timeout expired' };
  }

  // Verify the game ID matches
  if (disconnectedInfo.lobbyCode !== gameId) {
    console.log(`Game ID mismatch: expected ${disconnectedInfo.lobbyCode}, got ${gameId}`);
    return { success: false, error: 'Game ID does not match disconnection record' };
  }

  const { lobby: disconnectedLobby, matchedLobby: disconnectedMatchedLobby, player } = disconnectedInfo;

  // Clear the disconnection timer
  clearDisconnectionTimer(disconnectedInfo.socketId);

  // Update player's socket ID in the lobby
  const playerIndex = disconnectedLobby.players.findIndex(p => p.id === disconnectedInfo.socketId);
  if (playerIndex !== -1) {
    disconnectedLobby.players[playerIndex].id = socket.id;
    disconnectedLobby.players[playerIndex].reconnected = true;

    // Update team arrays as well
    const teamPlayer = disconnectedLobby.teams[player.team].find(p => p.id === disconnectedInfo.socketId);
    if (teamPlayer) {
      teamPlayer.id = socket.id;
      teamPlayer.reconnected = true;
    }
  }

  // Update matched lobby if it exists
  if (disconnectedMatchedLobby) {
    const matchedPlayerIndex = disconnectedMatchedLobby.players.findIndex(p => p.id === disconnectedInfo.socketId);
    if (matchedPlayerIndex !== -1) {
      disconnectedMatchedLobby.players[matchedPlayerIndex].id = socket.id;
      disconnectedMatchedLobby.players[matchedPlayerIndex].reconnected = true;

      // Update team arrays as well
      const matchedTeamPlayer = disconnectedMatchedLobby.teams[player.team].find(p => p.id === disconnectedInfo.socketId);
      if (matchedTeamPlayer) {
        matchedTeamPlayer.id = socket.id;
        matchedTeamPlayer.reconnected = true;
      }
    }
  }

  // Remove from disconnected players map (clean up all related entries)
  disconnectedPlayers.delete(persistentId);
  disconnectedPlayers.delete(disconnectedInfo.socketId);

  // Also clean up any entries with the same user ID pattern
  const userIdMatch = persistentId.match(/^player_([^_]+)_/);
  if (userIdMatch) {
    const userId = userIdMatch[1];
    const keysToDelete = [];
    for (const [key, info] of disconnectedPlayers.entries()) {
      if (key.includes(userId) && info.lobbyCode === gameId) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => disconnectedPlayers.delete(key));
    console.log(`Cleaned up ${keysToDelete.length} disconnected player entries for user ${userId}`);
  }

  // Resume the game
  resumeGame(io, disconnectedLobby, disconnectedMatchedLobby, player);

  // Notify other players about the rejoin
  const rejoinData = {
    playerId: socket.id,
    playerName: player.name,
    playerTeam: player.team
  };

  io.to(gameId).emit('player_reconnected', rejoinData);
  if (disconnectedMatchedLobby && disconnectedMatchedLobby.lobbyCode !== gameId) {
    io.to(disconnectedMatchedLobby.lobbyCode).emit('player_reconnected', rejoinData);
  }

  // Send success toast notification
  const successMessage = `${player.name} reconnected successfully!`;
  io.to(gameId).emit('toast_notification', {
    type: 'success',
    message: successMessage,
    duration: 3000
  });
  if (disconnectedMatchedLobby && disconnectedMatchedLobby.lobbyCode !== gameId) {
    io.to(disconnectedMatchedLobby.lobbyCode).emit('toast_notification', {
      type: 'success',
      message: successMessage,
      duration: 3000
    });
  }

  console.log(`Player ${player.name} successfully rejoined lobby ${gameId}`);

  return {
    success: true,
    lobbyCode: gameId,
    gameState: getGameStateForPlayer(disconnectedLobby, disconnectedMatchedLobby, socket.id)
  };
}

/**
 * Pause the game due to disconnection
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - Lobby object
 * @param {Object} matchedLobby - Matched lobby object
 * @param {Object} disconnectedPlayer - Disconnected player object
 */
function pauseGame(io, lobby, matchedLobby, disconnectedPlayer) {
  // Set game pause state
  lobby.gamePaused = true;
  lobby.pauseReason = 'player_disconnection';
  lobby.pausedAt = Date.now();
  
  if (matchedLobby) {
    matchedLobby.gamePaused = true;
    matchedLobby.pauseReason = 'player_disconnection';
    matchedLobby.pausedAt = Date.now();
  }

  // Pause any active turn timers
  if (lobby.turnTimer) {
    clearTimeout(lobby.turnTimer);
    lobby.turnTimerPaused = true;
    console.log(`Paused turn timer for lobby ${lobby.lobbyCode}`);
  }
  if (lobby.updateInterval) {
    clearInterval(lobby.updateInterval);
    lobby.updateIntervalPaused = true;
    console.log(`Paused update interval for lobby ${lobby.lobbyCode}`);
  }

  // Also pause matched lobby timers
  if (matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
    if (matchedLobby.turnTimer) {
      clearTimeout(matchedLobby.turnTimer);
      matchedLobby.turnTimerPaused = true;
      console.log(`Paused turn timer for matched lobby ${matchedLobby.lobbyCode}`);
    }
    if (matchedLobby.updateInterval) {
      clearInterval(matchedLobby.updateInterval);
      matchedLobby.updateIntervalPaused = true;
      console.log(`Paused update interval for matched lobby ${matchedLobby.lobbyCode}`);
    }
  }

  // Notify players that game is paused
  io.to(lobby.lobbyCode).emit('game_paused', {
    reason: 'player_disconnection',
    disconnectedPlayer: {
      name: disconnectedPlayer.name,
      team: disconnectedPlayer.team
    }
  });
  
  if (matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('game_paused', {
      reason: 'player_disconnection',
      disconnectedPlayer: {
        name: disconnectedPlayer.name,
        team: disconnectedPlayer.team
      }
    });
  }

  console.log(`Game paused in lobby ${lobby.lobbyCode} due to ${disconnectedPlayer.name} disconnection`);
}

/**
 * Resume the game after reconnection
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - Lobby object
 * @param {Object} matchedLobby - Matched lobby object
 * @param {Object} reconnectedPlayer - Reconnected player object
 */
function resumeGame(io, lobby, matchedLobby, reconnectedPlayer) {
  // Clear game pause state
  lobby.gamePaused = false;
  lobby.pauseReason = null;
  lobby.pausedAt = null;
  
  if (matchedLobby) {
    matchedLobby.gamePaused = false;
    matchedLobby.pauseReason = null;
    matchedLobby.pausedAt = null;
  }

  // Resume turn timers if they were paused
  if (lobby.turnTimerPaused && lobby.turnTimerState) {
    // Calculate remaining time and restart timer
    const turnUtils = require('./turnUtils');
    turnUtils.setPlayerTurn(io, lobby, matchedLobby, lobby.turnTimerState.playerId);
    lobby.turnTimerPaused = false;
  }

  // Notify players that game is resumed
  io.to(lobby.lobbyCode).emit('game_resumed', {
    reconnectedPlayer: {
      name: reconnectedPlayer.name,
      team: reconnectedPlayer.team
    }
  });
  
  if (matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
    io.to(matchedLobby.lobbyCode).emit('game_resumed', {
      reconnectedPlayer: {
        name: reconnectedPlayer.name,
        team: reconnectedPlayer.team
      }
    });
  }

  console.log(`Game resumed in lobby ${lobby.lobbyCode} after ${reconnectedPlayer.name} reconnection`);
}

/**
 * Get current game state for a rejoining player
 * @param {Object} lobby - Lobby object
 * @param {Object} matchedLobby - Matched lobby object
 * @param {String} playerId - Player's socket ID
 * @returns {Object} Game state
 */
function getGameStateForPlayer(lobby, matchedLobby, playerId) {
  return {
    lobby: {
      ...lobby,
      // Remove sensitive server-side data
      turnTimer: undefined,
      updateInterval: undefined
    },
    matchedLobby: matchedLobby ? {
      ...matchedLobby,
      turnTimer: undefined,
      updateInterval: undefined
    } : null,
    playerId,
    gamePhase: lobby.gamePhase || 'waiting',
    currentTurn: lobby.turnTimerState?.playerId || null,
    ballScores: lobby.ballScores || { team1: 0, team2: 0 }
  };
}

module.exports = {
  startDisconnectionTimer,
  clearDisconnectionTimer,
  hasDisconnectionTimer,
  handleDisconnectionTimeout,
  handlePlayerRejoin,
  pauseGame,
  resumeGame,
  getGameStateForPlayer
};
