const competitionService = require('../services/competitionService');

class CompetitionUtils {
  
  // Calculate competition score based on game result
  static calculateCompetitionScore(gameResult) {
    const { winningTeam, team1Score, team2Score } = gameResult;
    
    if (!winningTeam || winningTeam < 1 || winningTeam > 2) {
      return { team1Points: 0, team2Points: 0 };
    }

    const ballDifference = Math.abs(team1Score - team2Score);
    const bonusThreshold = 6; // Bonus point for winning by 6+ balls
    
    let team1Points = 0;
    let team2Points = 0;
    
    if (winningTeam === 1) {
      team1Points = 1; // Base point for winning
      if (ballDifference >= bonusThreshold) {
        team1Points += 1; // Bonus point
      }
    } else {
      team2Points = 1; // Base point for winning
      if (ballDifference >= bonusThreshold) {
        team2Points += 1; // Bonus point
      }
    }
    
    return {
      team1Points,
      team2Points,
      ballDifference,
      bonusAwarded: ballDifference >= bonusThreshold
    };
  }

  // Validate competition lobby creation
  static validateCompetitionLobby(competitionId, socketId) {
    if (!competitionId) {
      throw new Error('Competition ID is required');
    }

    // Check if user is in the competition
    if (!competitionService.isUserInCompetition(competitionId, socketId)) {
      throw new Error('User is not registered for this competition');
    }

    // Check if user can play more games
    if (!competitionService.canUserPlayMoreGames(socketId)) {
      const status = competitionService.getUserCompetitionStatus(socketId);
      throw new Error(`Maximum games reached (${status.gamesPlayed}/${status.maxGames})`);
    }

    return true;
  }

  // Create competition lobby data
  static createCompetitionLobbyData(lobby, competitionId) {
    const competitionLobby = {
      ...lobby,
      competitionId,
      isCompetitionGame: true,
      competitionGameNumber: null, // Will be set when game starts
      competitionScoring: {
        enabled: true,
        bonusThreshold: 6
      }
    };

    return competitionLobby;
  }

  // Validate competition match
  static validateCompetitionMatch(lobby1, lobby2) {
    // Both lobbies must be competition games
    if (!lobby1.competitionId || !lobby2.competitionId) {
      throw new Error('Both lobbies must be competition games');
    }

    // Must be in the same competition
    if (lobby1.competitionId !== lobby2.competitionId) {
      throw new Error('Lobbies must be in the same competition');
    }

    // Both teams must be ready
    if (!lobby1.teamReady[1] || !lobby2.teamReady[1]) {
      throw new Error('Both teams must be ready');
    }

    // Both teams must have 2 players
    if (lobby1.teams[1].length !== 2 || lobby2.teams[1].length !== 2) {
      throw new Error('Both teams must have exactly 2 players');
    }

    return true;
  }

  // Format competition game result for API
  static formatCompetitionGameResult(lobby, gameResult) {
    // Find team IDs from the lobby - try multiple possible locations
    let team1Id = lobby.team1Id || lobby.competitionTeam1Id || null;
    let team2Id = lobby.team2Id || lobby.competitionTeam2Id || null;

    // If team IDs are not available, try to extract from players
    if (!team1Id || !team2Id) {
      console.log(`[COMPETITION] Team IDs not found in lobby, attempting to extract from players`);
      console.log(`[COMPETITION] Lobby data:`, {
        competitionId: lobby.competitionId,
        team1Id: lobby.team1Id,
        team2Id: lobby.team2Id,
        competitionTeam1Id: lobby.competitionTeam1Id,
        competitionTeam2Id: lobby.competitionTeam2Id,
        playerCount: lobby.players?.length || 0
      });

      // Try to find team IDs from player data
      if (lobby.players && lobby.players.length > 0) {
        const team1Players = lobby.players.filter(p => p.team === 1);
        const team2Players = lobby.players.filter(p => p.team === 2);

        if (team1Players.length > 0 && team1Players[0].competitionTeamId) {
          team1Id = team1Players[0].competitionTeamId;
        }
        if (team2Players.length > 0 && team2Players[0].competitionTeamId) {
          team2Id = team2Players[0].competitionTeamId;
        }
      }
    }

    // Generate a new GUID if no game ID is available
    const crypto = require('crypto');
    const gameId = lobby.gameId || crypto.randomUUID();

    const formattedResult = {
      gameId: gameId,
      competitionId: lobby.competitionId,
      team1Id: team1Id,
      team2Id: team2Id,
      team1Score: gameResult.team1Score || 0,
      team2Score: gameResult.team2Score || 0,
      team1BallsWon: gameResult.team1BallsWon || 0,
      team2BallsWon: gameResult.team2BallsWon || 0,
      winnerTeam: gameResult.winningTeam
    };

    console.log(`[COMPETITION] Formatted competition game result:`, formattedResult);

    if (!team1Id || !team2Id) {
      console.error(`[COMPETITION] ❌ Missing team IDs - Team1: ${team1Id}, Team2: ${team2Id}`);
      console.error(`[COMPETITION] This will cause the API call to fail!`);
    }

    return formattedResult;
  }

  // Get competition lobby display info
  static getCompetitionLobbyInfo(lobby) {
    if (!lobby.competitionId) {
      return null;
    }

    return {
      competitionId: lobby.competitionId,
      isCompetitionGame: true,
      gameNumber: lobby.competitionGameNumber,
      maxGames: lobby.maxCompetitionGames || 10,
      scoringInfo: {
        winPoints: 1,
        bonusPoints: 1,
        bonusThreshold: 6
      }
    };
  }

  // Check if competition game limit reached
  static isGameLimitReached(gamesPlayed, maxGames) {
    return gamesPlayed >= maxGames;
  }

  // Calculate remaining games
  static getRemainingGames(gamesPlayed, maxGames) {
    return Math.max(0, maxGames - gamesPlayed);
  }

  // In-memory storage for competition ball scores
  static competitionBallScores = new Map(); // Map: lobbyCode -> { team1: number, team2: number }

  // Initialize ball scores for competition games using server-side tracking
  static initializeBallScores(lobby, matchedLobby = null) {
    if (!lobby.competitionId) {
      // Non-competition game, use default
      lobby.ballScores = { team1: 0, team2: 0 };
      if (matchedLobby && matchedLobby !== lobby) {
        matchedLobby.ballScores = { team1: 0, team2: 0 };
      }
      console.log('[COMPETITION] Initialized default ball scores for non-competition game');
      return;
    }

    // Competition game, check if we have stored scores
    const storedScores = this.competitionBallScores.get(lobby.lobbyCode);

    if (storedScores) {
      // Use stored scores
      lobby.ballScores = { ...storedScores };
      if (matchedLobby && matchedLobby !== lobby) {
        matchedLobby.ballScores = { ...storedScores };
      }
      console.log('[COMPETITION] Loaded stored ball scores for competition game:', lobby.ballScores);
    } else {
      // First time, initialize to zero
      lobby.ballScores = { team1: 0, team2: 0 };
      if (matchedLobby && matchedLobby !== lobby) {
        matchedLobby.ballScores = { team1: 0, team2: 0 };
      }
      // Store the initial scores
      this.competitionBallScores.set(lobby.lobbyCode, { ...lobby.ballScores });
      console.log('[COMPETITION] Initialized new ball scores for competition game:', lobby.ballScores);
    }
  }

  // Update ball scores in server-side storage
  static updateBallScores(lobby, matchedLobby = null) {
    if (!lobby.competitionId) {
      return; // Don't store non-competition games
    }

    // Update the stored scores
    this.competitionBallScores.set(lobby.lobbyCode, { ...lobby.ballScores });

    // Also update matched lobby if different
    if (matchedLobby && matchedLobby !== lobby && matchedLobby.lobbyCode) {
      this.competitionBallScores.set(matchedLobby.lobbyCode, { ...lobby.ballScores });
    }

    console.log('[COMPETITION] Updated stored ball scores for', lobby.lobbyCode, ':', lobby.ballScores);
  }

  // Get stored ball scores for a lobby
  static getStoredBallScores(lobbyCode) {
    return this.competitionBallScores.get(lobbyCode) || { team1: 0, team2: 0 };
  }

  // Clear stored ball scores (useful for game completion)
  static clearStoredBallScores(lobbyCode) {
    this.competitionBallScores.delete(lobbyCode);
    console.log('[COMPETITION] Cleared stored ball scores for', lobbyCode);
  }

  // Format leaderboard entry
  static formatLeaderboardEntry(entry, rank) {
    return {
      rank,
      teamName: entry.teamName,
      player1Name: entry.player1Name,
      player2Name: entry.player2Name,
      gamesPlayed: entry.gamesPlayed,
      totalPoints: entry.totalPoints,
      wins: entry.wins,
      bonusPoints: entry.bonusPoints,
      averagePointsPerGame: entry.gamesPlayed > 0 ? 
        (entry.totalPoints / entry.gamesPlayed).toFixed(2) : '0.00'
    };
  }

  // Validate team name for competition
  static validateTeamName(teamName) {
    if (!teamName || typeof teamName !== 'string') {
      throw new Error('Team name is required');
    }

    const trimmed = teamName.trim();
    if (trimmed.length < 2) {
      throw new Error('Team name must be at least 2 characters long');
    }

    if (trimmed.length > 50) {
      throw new Error('Team name must be less than 50 characters');
    }

    // Check for inappropriate content (basic check)
    const inappropriate = /\b(fuck|shit|damn|hell|ass|bitch)\b/i;
    if (inappropriate.test(trimmed)) {
      throw new Error('Team name contains inappropriate content');
    }

    return trimmed;
  }

  // Generate competition invite message
  static generateCompetitionInviteMessage(competitionName, teamName, inviterName) {
    return `${inviterName} has invited you to join team "${teamName}" in the "${competitionName}" competition. Accept the invitation to become their partner!`;
  }

  // Check competition status
  static getCompetitionStatus(competition) {
    const now = new Date();
    const startDate = new Date(competition.startDate);
    const endDate = new Date(competition.endDate);

    if (now < startDate) {
      return 'upcoming';
    } else if (now > endDate) {
      return 'ended';
    } else {
      return 'active';
    }
  }

  // Calculate time remaining in competition
  static getTimeRemaining(competition) {
    const now = new Date();
    const endDate = new Date(competition.endDate);
    
    if (now >= endDate) {
      return { expired: true, timeRemaining: 0 };
    }

    const timeRemaining = endDate.getTime() - now.getTime();
    const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

    return {
      expired: false,
      timeRemaining,
      days,
      hours,
      minutes,
      formatted: `${days}d ${hours}h ${minutes}m`
    };
  }
}

module.exports = CompetitionUtils;
