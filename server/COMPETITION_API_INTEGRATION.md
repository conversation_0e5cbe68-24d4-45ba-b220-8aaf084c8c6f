# Competition API Integration - Data Flow to Database

## Overview

The competition system is now properly configured to send competition game results to the ASP.NET Core API for storage in the competition leaderboard database tables. Here's the complete data flow:

## ✅ **Data Flow Architecture**

### 1. **Game Completion Detection**
When a competition game ends, the system detects it's a competition game via:
```javascript
if (lobby.competitionId) {
  // This is a competition game - use competition-specific processing
}
```

### 2. **Competition Game Result Processing**
The `gameDataUtils.js` formats the game result specifically for competition scoring:
```javascript
const competitionGameResult = {
  winningTeam: gameEndData.winner,
  team1Score: gameEndData.finalScores?.team1 || gameEndData.scores?.team1 || 0,
  team2Score: gameEndData.finalScores?.team2 || gameEndData.scores?.team2 || 0,
  ballDifference: Math.abs(team1Score - team2Score),
  gameEndReason: gameEndData.reason || 'completed',
  duration: gameEndData.duration || 0
};
```

### 3. **Competition Scoring Calculation**
The `competitionUtils.js` calculates competition points:
```javascript
// Base scoring: 1 point for winning
// Bonus scoring: +1 point for winning by 6+ balls
const scoring = {
  team1Points: winningTeam === 1 ? (1 + (ballDifference >= 6 ? 1 : 0)) : 0,
  team2Points: winningTeam === 2 ? (1 + (ballDifference >= 6 ? 1 : 0)) : 0,
  ballDifference,
  bonusAwarded: ballDifference >= 6
};
```

### 4. **API Data Formatting**
The system formats the data for the competition API endpoint:
```javascript
const apiData = {
  lobbyCode: "COMP123",
  competitionId: "comp-uuid-123",
  gameNumber: 5,
  
  // Team information with player IDs
  team1Players: [
    { playerId: "user1", playerName: "Player 1" },
    { playerId: "user2", playerName: "Player 2" }
  ],
  team2Players: [
    { playerId: "user3", playerName: "Player 3" },
    { playerId: "user4", playerName: "Player 4" }
  ],
  
  // Game scores
  team1FinalScore: 15,
  team2FinalScore: 8,
  winnerTeam: 1,
  
  // Competition-specific scoring
  team1Points: 2, // 1 win + 1 bonus
  team2Points: 0,
  ballDifference: 7,
  bonusAwarded: true,
  
  // Timing information
  gameStartedAt: "2023-12-01T10:00:00Z",
  gameCompletedAt: "2023-12-01T10:25:00Z",
  gameDurationMinutes: 25
};
```

### 5. **API Endpoint Call**
The system calls the competition-specific API endpoint:
```javascript
// Uses POST /api/competitions/game-result instead of regular game endpoint
await apiService.recordCompetitionGameResult(competitionGameData, playerToken);
```

## ✅ **Key Differences from Regular Games**

### Regular Games:
- Use `POST /api/games/{lobbyCode}/game-result`
- Only update `Games` table
- Basic win/loss tracking
- No competition scoring

### Competition Games:
- Use `POST /api/competitions/game-result`
- Update competition leaderboard tables
- Competition-specific scoring (1 point + bonus)
- Team tracking and statistics
- Leaderboard integration

## ✅ **Database Tables Updated**

When a competition game result is sent to the API, it should update:

1. **Games Table** - Basic game record
2. **CompetitionTeams Table** - Team statistics
3. **CompetitionGameResults Table** - Individual game results
4. **CompetitionLeaderboard Table** - Aggregated team scores

## ✅ **API Endpoint Requirements**

The ASP.NET Core API needs to implement:
```csharp
[HttpPost("game-result")]
public async Task<ActionResult> RecordCompetitionGameResult([FromBody] CompetitionGameResultDto gameResult)
{
    // 1. Validate competition and teams
    // 2. Save game result to CompetitionGameResults table
    // 3. Update team statistics in CompetitionTeams table
    // 4. Recalculate leaderboard in CompetitionLeaderboard table
    // 5. Return success response
}
```

## ✅ **Data Validation**

The Node.js server validates:
- ✅ User is authenticated
- ✅ User is in the competition
- ✅ Game limit not exceeded
- ✅ Team composition is valid
- ✅ Game scores are valid
- ✅ Competition scoring calculation

## ✅ **Error Handling**

The system includes comprehensive error handling:
- API call failures are logged and retried
- Invalid data is rejected with clear error messages
- Network issues are handled gracefully
- Failed operations are queued for retry

## ✅ **Leaderboard Cache Management**

After saving competition results:
- Leaderboard cache is cleared to force refresh
- Next leaderboard request will fetch updated data
- Real-time leaderboard updates are supported

## ✅ **Testing Verification**

The test suite verifies:
- ✅ Competition scoring calculation (1 + bonus points)
- ✅ API data formatting matches expected structure
- ✅ Player information is correctly mapped
- ✅ Game timing and metadata is included
- ✅ Ball difference and bonus calculation

## ✅ **Example API Call**

When a competition game ends, the system makes this API call:
```http
POST /api/competitions/game-result
Authorization: Bearer {user-token}
Content-Type: application/json

{
  "lobbyCode": "COMP123",
  "competitionId": "comp-uuid-123",
  "gameNumber": 5,
  "team1Players": [
    { "playerId": "user1", "playerName": "Player 1" },
    { "playerId": "user2", "playerName": "Player 2" }
  ],
  "team2Players": [
    { "playerId": "user3", "playerName": "Player 3" },
    { "playerId": "user4", "playerName": "Player 4" }
  ],
  "team1FinalScore": 15,
  "team2FinalScore": 8,
  "winnerTeam": 1,
  "team1Points": 2,
  "team2Points": 0,
  "ballDifference": 7,
  "bonusAwarded": true,
  "gameStartedAt": "2023-12-01T10:00:00Z",
  "gameCompletedAt": "2023-12-01T10:25:00Z",
  "gameDurationMinutes": 25
}
```

## ✅ **Next Steps for API Implementation**

To complete the integration, the ASP.NET Core API needs:

1. **Create CompetitionGameResultDto** - Data transfer object for the endpoint
2. **Implement POST /api/competitions/game-result** - Endpoint to receive game results
3. **Update CompetitionService** - Business logic to process results
4. **Database Updates** - Store results and update leaderboards
5. **Leaderboard Recalculation** - Update team rankings and statistics

## ✅ **Monitoring and Debugging**

Debug endpoints available:
- `GET /debug/competitions` - Competition service statistics
- `GET /api/health` - Includes competition stats
- Console logging for all competition game result saves

The competition system is now **fully configured** to send properly formatted data to the API for database storage and leaderboard updates.
