// Simple authentication test
const axios = require('axios');
const https = require('https');

async function testAuth() {
  console.log('Testing authentication...');
  
  try {
    const response = await axios.post('https://localhost:57229/api/Auth/login', {
      username: 'dean',
      password: 'password'
    }, {
      httpsAgent: new https.Agent({ rejectUnauthorized: false })
    });
    
    console.log('Login successful!');
    console.log('Token:', response.data.data.token.substring(0, 20) + '...');
    
    // Test the Node.js auth service
    const authService = require('./services/authService');
    const user = await authService.authenticateUser('test-socket', response.data.data.token);
    
    if (user) {
      console.log('Node.js auth successful!');
      console.log('User:', user.username);
    } else {
      console.log('Node.js auth failed');
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testAuth();
