/**
 * <PERSON><PERSON> for revealing Jordhi cards to all players
 */

/**
 * Handle a request to reveal or not reveal Jordhi cards to all players
 * @param {Object} socket - The socket of the player revealing the cards
 * @param {Object} data - The data containing the Jord<PERSON> cards and reveal choice
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched game lobby (if any)
 * @param {Function} io - The Socket.IO instance
 * @returns {Object} - Result of the operation
 */
function handleJordhiReveal(socket, data, lobby, matchedLobby, io) {
  try {
    // Find the player who is making the choice about revealing cards
    const player = lobby.players.find(p => p.id === socket.id);
    if (!player) {
      console.error(`Player ${socket.id} not found in lobby ${lobby.lobbyCode}`);
      return {
        success: false,
        error: 'Player not found'
      };
    }

    const revealChoice = data.revealCards === true;
    console.log(`Player ${player.name} (${player.id}) has chosen to ${revealChoice ? 'reveal' : 'not reveal'} Jord<PERSON> cards for a ${data.jordhiValue} Jordhi in ${data.jordhiSuit}`);

    // Validate that this player has made a valid Jordhi call
    const jordhiCalls = lobby.jordhiCalls || [];
    const playerJordhiCall = jordhiCalls.find(call =>
      call.playerId === socket.id &&
      call.value === data.jordhiValue &&
      call.jordhiSuit === data.jordhiSuit &&
      call.isValidCards === true
    );

    if (!playerJordhiCall) {
      console.error(`No valid Jordhi call found for player ${player.name} (${player.id}) with value ${data.jordhiValue} in ${data.jordhiSuit}`);
      return {
        success: false,
        error: 'No valid Jordhi call found'
      };
    }

    // Prepare the data to send to all players
    const jordhiData = {
      playerName: player.name,
      playerTeam: player.team,
      value: data.jordhiValue,
      jordhiSuit: data.jordhiSuit,
      cardsRevealed: revealChoice,
      // Only include cards if the player chose to reveal them
      jordhiCards: revealChoice ? data.jordhiCards : undefined
    };

    // Send the Jordhi notification to all players in the lobby
    io.to(lobby.lobbyCode).emit('jordhi_cards_revealed', jordhiData);

    // If there's a matched lobby, send to that one too
    if (matchedLobby && matchedLobby.lobbyCode !== lobby.lobbyCode) {
      io.to(matchedLobby.lobbyCode).emit('jordhi_cards_revealed', jordhiData);
    }

    // Store the reveal choice in the lobby for future reference
    if (!lobby.jordhiReveals) {
      lobby.jordhiReveals = [];
      if (matchedLobby) matchedLobby.jordhiReveals = [];
    }

    const jordhiReveal = {
      playerId: socket.id,
      playerName: player.name,
      playerTeam: player.team,
      value: data.jordhiValue,
      jordhiSuit: data.jordhiSuit,
      cardsRevealed: revealChoice,
      jordhiCards: revealChoice ? data.jordhiCards : undefined,
      timestamp: Date.now()
    };

    lobby.jordhiReveals.push(jordhiReveal);
    if (matchedLobby) matchedLobby.jordhiReveals.push(jordhiReveal);

    // Update the original Jordhi call to mark whether cards were revealed
    playerJordhiCall.cardsRevealed = revealChoice;

    // If this is a matched lobby, update the Jordhi call in the matched lobby too
    if (matchedLobby && matchedLobby !== lobby) {
      const matchedJordhiCall = matchedLobby.jordhiCalls?.find(call =>
        call.playerId === socket.id &&
        call.value === data.jordhiValue &&
        call.jordhiSuit === data.jordhiSuit
      );

      if (matchedJordhiCall) {
        matchedJordhiCall.cardsRevealed = revealChoice;
      }
    }

    return {
      success: true,
      jordhiData
    };
  } catch (error) {
    console.error('Error in handleJordhiReveal:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  handleJordhiReveal
};
