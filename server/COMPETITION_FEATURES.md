# Competition Features Implementation

## Overview

The Competition feature has been successfully implemented in the Node.js server, allowing logged-in players to participate in structured tournaments with team-based gameplay, scoring, and leaderboards.

## Key Features Implemented

### 1. Competition Management
- **Join Competition**: Players can join competitions with partner invitation
- **Team Formation**: Players must invite a partner and set a team name
- **Game Limits**: Each team can play a maximum of 10 games (configurable via database)
- **Resume Functionality**: Teams can resume competitions from where they left off

### 2. Competition Match Finding
- **Separate Match Queue**: Competition teams have their own match-finding system
- **Competition-Only Matching**: Teams are only matched with other teams in the same competition
- **Regular Match Isolation**: Regular lobbies cannot match with competition lobbies

### 3. Competition Scoring System
- **Base Points**: 1 point for winning a game
- **Bonus Points**: +1 additional point for winning by 6 or more balls
- **Leaderboard Integration**: Scores are tracked and sent to the API for leaderboard calculation

### 4. Socket Events

#### Competition Information
- `get_competitions` - Get all available competitions
- `get_competition` - Get specific competition details
- `get_competition_status` - Get user's current competition status
- `get_competition_leaderboard` - Get competition leaderboard with pagination

#### Competition Participation
- `join_competition` - Join a competition with team name and partner username
- `create_competition_lobby` - Create a lobby specifically for competition games
- `find_competition_match` - Find match within the same competition
- `cancel_competition_match` - Cancel competition match finding

### 5. API Integration

#### New API Methods Added
- `getCompetitions(token)` - Fetch all competitions
- `getCompetition(competitionId, token)` - Fetch specific competition
- `joinCompetition(competitionId, joinData, token)` - Join competition
- `getCompetitionLeaderboard(competitionId, token, page, pageSize)` - Get leaderboard
- `getCompetitionTeams(competitionId, token)` - Get competition teams
- `isUserInCompetition(competitionId, token)` - Check user participation

### 6. Competition Services

#### CompetitionService (`services/competitionService.js`)
- Manages competition data and user memberships
- Handles competition match queues
- Provides leaderboard caching
- Tracks user game counts and limits

#### CompetitionUtils (`utils/competitionUtils.js`)
- Competition scoring calculations
- Team name validation
- Competition lobby validation
- Game result formatting for API
- Leaderboard entry formatting

### 7. Game Data Integration

#### Enhanced Game Data Service
- Competition game creation tracking
- Competition-specific game result saving
- Automatic user game count updates
- Competition scoring integration

#### Modified Game End Logic
- Detects competition games automatically
- Applies competition scoring rules
- Updates user game counts
- Saves competition-specific results to API

### 8. Debug and Monitoring

#### New Debug Endpoints
- `/debug/competitions` - Competition service statistics
- `/api/health` - Now includes competition stats

#### Enhanced Health Check
- Competition service status
- Match queue statistics
- User participation tracking

## Technical Implementation Details

### Competition Lobby Structure
```javascript
{
  lobbyCode: "ABC123",
  competitionId: "comp-uuid",
  isCompetitionGame: true,
  competitionGameNumber: 3,
  maxCompetitionGames: 10,
  competitionScoring: {
    enabled: true,
    bonusThreshold: 6
  },
  // ... standard lobby properties
}
```

### Competition Scoring Logic
```javascript
// Base scoring
winningTeam gets 1 point

// Bonus scoring
if (ballDifference >= 6) {
  winningTeam gets +1 bonus point
}

// Example: Team A wins 12-5 (7 ball difference)
// Team A gets 2 points (1 win + 1 bonus)
// Team B gets 0 points
```

### Match Finding Separation
- Regular lobbies use `matchQueue` (Set)
- Competition lobbies use `competitionMatchQueues` (Map by competitionId)
- Separate periodic checking functions
- No cross-contamination between regular and competition matches

## Files Created/Modified

### New Files
- `services/competitionService.js` - Main competition service
- `utils/competitionUtils.js` - Competition utility functions
- `test-competition.js` - Comprehensive test suite
- `COMPETITION_FEATURES.md` - This documentation

### Modified Files
- `index.js` - Added competition socket events and match finding logic
- `services/apiService.js` - Added competition API methods
- `services/gameDataService.js` - Added competition game result handling
- `utils/gameDataUtils.js` - Enhanced game end result processing

## Testing

The implementation includes a comprehensive test suite (`test-competition.js`) that verifies:
- Competition scoring calculations
- Team name validation
- Competition lobby validation
- Service initialization
- Game limit calculations
- Competition status detection
- Time remaining calculations
- Leaderboard formatting

All tests pass successfully, confirming the implementation is working correctly.

## Integration with ASP.NET Core API

The competition features integrate seamlessly with the existing ASP.NET Core API:
- Uses existing authentication system
- Leverages existing competition endpoints
- Maintains consistent data flow
- Supports existing user management

## Future Enhancements

The implementation is designed to be extensible for future features:
- Tournament brackets
- Multiple competition formats
- Custom scoring rules
- Advanced statistics
- Real-time notifications
- Competition chat systems

## Usage Example

1. User joins a competition via API
2. User creates competition lobby: `create_competition_lobby`
3. Partner joins using invite code
4. Team finds match: `find_competition_match`
5. Game plays normally with competition tracking
6. Results automatically scored and saved
7. Leaderboard updated in real-time

The competition system is now fully operational and ready for production use.
