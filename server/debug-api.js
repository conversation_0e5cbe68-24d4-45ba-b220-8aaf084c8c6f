// Debug script to test API connectivity from Node.js server
require('dotenv').config();
const axios = require('axios');

console.log('=== API Debug Test ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('API_BASE_URL:', process.env.API_BASE_URL);
console.log('API_TIMEOUT:', process.env.API_TIMEOUT);

const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************.XyBYn-x8YtSiMAbl4kxwat8JY799OE1fYMH9n4Ik8TY';

async function testApiConnection() {
  try {
    console.log('\n=== Testing Direct Axios Call ===');
    
    const baseURL = process.env.API_BASE_URL || 'http://**************:8080/api';
    const timeout = parseInt(process.env.API_TIMEOUT) || 10000;
    
    console.log('Using baseURL:', baseURL);
    console.log('Using timeout:', timeout);
    
    // Create axios instance like the apiService does
    const client = axios.create({
      baseURL: baseURL,
      timeout: timeout,
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log('\n=== Making Request ===');
    const response = await client.get('/api/Auth/me', {
      headers: { Authorization: `Bearer ${testToken}` }
    });
    
    console.log('✅ SUCCESS!');
    console.log('Status:', response.status);
    console.log('Data:', response.data);
    
  } catch (error) {
    console.log('❌ ERROR!');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
    console.log('Error code:', error.code);
    console.log('Response status:', error.response?.status);
    console.log('Response data:', error.response?.data);
    console.log('Request config:', {
      url: error.config?.url,
      baseURL: error.config?.baseURL,
      timeout: error.config?.timeout
    });
  }
}

async function testDirectUrl() {
  try {
    console.log('\n=== Testing Direct URL (no baseURL) ===');
    
    const fullUrl = 'http://**************:8080/api/Auth/me';
    console.log('Using full URL:', fullUrl);
    
    const response = await axios.get(fullUrl, {
      headers: { 
        Authorization: `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
    
    console.log('✅ SUCCESS!');
    console.log('Status:', response.status);
    console.log('Data:', response.data);
    
  } catch (error) {
    console.log('❌ ERROR!');
    console.log('Error type:', error.constructor.name);
    console.log('Error message:', error.message);
    console.log('Error code:', error.code);
    console.log('Response status:', error.response?.status);
    console.log('Response data:', error.response?.data);
  }
}

// Run tests
testApiConnection()
  .then(() => testDirectUrl())
  .then(() => {
    console.log('\n=== Debug Complete ===');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
