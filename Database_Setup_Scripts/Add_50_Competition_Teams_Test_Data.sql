-- =============================================
-- ADD 50 COMPETITION TEAMS WITH REALISTIC GAME DATA
-- FOR TESTING LEADERBOARD AND NEXT PHASE ADVANCEMENT
-- =============================================

USE [GoldRushThunee]
GO

-- Start transaction with rollback capability
BEGIN TRANSACTION AddCompetitionTestData;

BEGIN TRY
    PRINT 'Starting to add 50 competition teams with game data...';
    
    -- Competition ID (Summer Thunee Championship)
    DECLARE @CompetitionId UNIQUEIDENTIFIER = '4860C19D-E3F3-4E2D-B359-275527461BD6';
    
    -- Variables for data generation
    DECLARE @Counter INT = 1;
    DECLARE @UserId1 UNIQUEIDENTIFIER, @UserId2 UNIQUEIDENTIFIER;
    DECLARE @TeamId UNIQUEIDENTIFIER;
    DECLARE @TeamName NVARCHAR(50);
    DECLARE @InviteCode NVARCHAR(10);
    DECLARE @GamesPlayed INT, @Points INT, @BonusPoints INT;
    DECLARE @GameCounter INT, @GameId UNIQUEIDENTIFIER;
    DECLARE @LobbyCode NVARCHAR(6);
    DECLARE @Team1BallsWon INT, @Team2BallsWon INT, @WinnerTeam INT;
    DECLARE @BallDifference INT, @IsBonus BIT;
    DECLARE @StartDate DATETIME2, @EndDate DATETIME2;
    
    -- Create 100 users for the teams (2 per team)
    PRINT 'Creating 100 users for teams...';
    DECLARE @UserCounter INT = 1;
    WHILE @UserCounter <= 100
    BEGIN
        INSERT INTO Users (Id, Username, Email, PasswordHash, IsVerified, IsActive, CreatedAt, UpdatedAt)
        VALUES (
            NEWID(),
            'TestUser' + RIGHT('000' + CAST(@UserCounter AS VARCHAR(3)), 3),
            'testuser' + CAST(@UserCounter AS VARCHAR(3)) + '@thunee.com',
            '$2a$10$dummy.hash.for.testing.purposes.only.remove.in.production',
            1,
            1,
            DATEADD(DAY, -RAND() * 30, GETUTCDATE()),
            GETUTCDATE()
        );
        SET @UserCounter = @UserCounter + 1;
    END
    
    PRINT 'Created 100 test users successfully.';
    
    -- Get user IDs for team creation
    DECLARE @UserIds TABLE (Id UNIQUEIDENTIFIER, RowNum INT);
    INSERT INTO @UserIds (Id, RowNum)
    SELECT Id, ROW_NUMBER() OVER (ORDER BY Username) 
    FROM Users 
    WHERE Username LIKE 'TestUser%'
    ORDER BY Username;
    
    -- Create 50 teams with varying performance levels
    PRINT 'Creating 50 competition teams...';
    WHILE @Counter <= 50
    BEGIN
        -- Get two users for this team
        SELECT @UserId1 = Id FROM @UserIds WHERE RowNum = (@Counter * 2) - 1;
        SELECT @UserId2 = Id FROM @UserIds WHERE RowNum = (@Counter * 2);
        
        SET @TeamId = NEWID();
        SET @TeamName = 'Team ' + CAST(@Counter AS VARCHAR(2));
        SET @InviteCode = 'T' + RIGHT('00' + CAST(@Counter AS VARCHAR(2)), 2) + 'CODE';
        
        -- Determine team performance tier for realistic distribution
        -- Top tier (teams 1-10): 15-20 games, high win rate
        -- Mid tier (teams 11-35): 12-18 games, medium win rate  
        -- Lower tier (teams 36-50): 10-15 games, lower win rate
        IF @Counter <= 10
        BEGIN
            SET @GamesPlayed = 15 + (ABS(CHECKSUM(NEWID())) % 6); -- 15-20 games
            SET @Points = CAST(@GamesPlayed * (0.7 + (ABS(CHECKSUM(NEWID())) % 30) / 100.0) AS INT); -- 70-100% win rate
            SET @BonusPoints = CAST(@Points * (0.3 + (ABS(CHECKSUM(NEWID())) % 40) / 100.0) AS INT); -- 30-70% bonus rate
        END
        ELSE IF @Counter <= 35
        BEGIN
            SET @GamesPlayed = 12 + (ABS(CHECKSUM(NEWID())) % 7); -- 12-18 games
            SET @Points = CAST(@GamesPlayed * (0.4 + (ABS(CHECKSUM(NEWID())) % 40) / 100.0) AS INT); -- 40-80% win rate
            SET @BonusPoints = CAST(@Points * (0.2 + (ABS(CHECKSUM(NEWID())) % 40) / 100.0) AS INT); -- 20-60% bonus rate
        END
        ELSE
        BEGIN
            SET @GamesPlayed = 10 + (ABS(CHECKSUM(NEWID())) % 6); -- 10-15 games
            SET @Points = CAST(@GamesPlayed * (0.2 + (ABS(CHECKSUM(NEWID())) % 50) / 100.0) AS INT); -- 20-70% win rate
            SET @BonusPoints = CAST(@Points * (0.1 + (ABS(CHECKSUM(NEWID())) % 30) / 100.0) AS INT); -- 10-40% bonus rate
        END
        
        -- Ensure points don't exceed games played
        IF @Points > @GamesPlayed SET @Points = @GamesPlayed;
        IF @BonusPoints > @Points SET @BonusPoints = @Points;
        
        -- Create the team
        INSERT INTO CompetitionTeams (
            Id, CompetitionId, TeamName, Player1Id, Player2Id, InviteCode,
            GamesPlayed, Points, BonusPoints, MaxGames, IsActive, IsComplete,
            RegisteredAt, CompletedAt, Phase, IsEliminated, AdvancedToNextPhase
        )
        VALUES (
            @TeamId, @CompetitionId, @TeamName, @UserId1, @UserId2, @InviteCode,
            @GamesPlayed, @Points, @BonusPoints, 20, 1, 1,
            DATEADD(DAY, -RAND() * 20, GETUTCDATE()),
            CASE WHEN @GamesPlayed >= 20 THEN GETUTCDATE() ELSE NULL END,
            'Leaderboard', 0, 0
        );
        
        PRINT 'Created team ' + @TeamName + ' with ' + CAST(@GamesPlayed AS VARCHAR(2)) + ' games, ' + 
              CAST(@Points AS VARCHAR(2)) + ' points, ' + CAST(@BonusPoints AS VARCHAR(2)) + ' bonus points';
        
        SET @Counter = @Counter + 1;
    END
    
    PRINT 'Successfully created 50 competition teams.';

    -- Now create realistic game data for each team
    PRINT 'Creating realistic game data for teams...';

    -- Get all created teams
    DECLARE @Teams TABLE (
        TeamId UNIQUEIDENTIFIER,
        TeamName NVARCHAR(50),
        Player1Id UNIQUEIDENTIFIER,
        Player2Id UNIQUEIDENTIFIER,
        GamesPlayed INT,
        Points INT,
        BonusPoints INT,
        RowNum INT
    );

    INSERT INTO @Teams (TeamId, TeamName, Player1Id, Player2Id, GamesPlayed, Points, BonusPoints, RowNum)
    SELECT Id, TeamName, Player1Id, Player2Id, GamesPlayed, Points, BonusPoints,
           ROW_NUMBER() OVER (ORDER BY NEWID())
    FROM CompetitionTeams
    WHERE CompetitionId = @CompetitionId
    AND TeamName LIKE 'Team %';

    -- Create games between teams
    DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
    DECLARE @Team1Name NVARCHAR(50), @Team2Name NVARCHAR(50);
    DECLARE @Team1Player1 UNIQUEIDENTIFIER, @Team1Player2 UNIQUEIDENTIFIER;
    DECLARE @Team2Player1 UNIQUEIDENTIFIER, @Team2Player2 UNIQUEIDENTIFIER;
    DECLARE @Team1GamesNeeded INT, @Team2GamesNeeded INT;
    DECLARE @Team1Points INT, @Team2Points INT, @Team1Bonus INT, @Team2Bonus INT;
    DECLARE @GamesCreated INT = 0;

    -- Create games by pairing teams randomly
    DECLARE @TeamPair INT = 1;
    WHILE @TeamPair <= 25 -- 25 pairs = 50 teams
    BEGIN
        -- Get two teams for this pairing
        SELECT @Team1Id = TeamId, @Team1Name = TeamName, @Team1Player1 = Player1Id, @Team1Player2 = Player2Id,
               @Team1GamesNeeded = GamesPlayed, @Team1Points = Points, @Team1Bonus = BonusPoints
        FROM @Teams WHERE RowNum = (@TeamPair * 2) - 1;

        SELECT @Team2Id = TeamId, @Team2Name = TeamName, @Team2Player1 = Player1Id, @Team2Player2 = Player2Id,
               @Team2GamesNeeded = GamesPlayed, @Team2Points = Points, @Team2Bonus = BonusPoints
        FROM @Teams WHERE RowNum = (@TeamPair * 2);

        -- Create games between these two teams (they will play multiple times)
        DECLARE @PairGames INT = CASE
            WHEN @Team1GamesNeeded + @Team2GamesNeeded > 30 THEN 3
            WHEN @Team1GamesNeeded + @Team2GamesNeeded > 20 THEN 2
            ELSE 1
        END;

        DECLARE @PairGameNum INT = 1;
        WHILE @PairGameNum <= @PairGames AND @GamesCreated < 200 -- Limit total games
        BEGIN
            SET @GameId = NEWID();
            SET @LobbyCode = 'G' + RIGHT('00000' + CAST(@GamesCreated + 1 AS VARCHAR(5)), 5);

            -- Determine winner based on team performance (higher points = higher win chance)
            DECLARE @Team1WinChance FLOAT = CASE
                WHEN (@Team1Points + @Team1Bonus) + (@Team2Points + @Team2Bonus) = 0 THEN 0.5
                ELSE CAST(@Team1Points + @Team1Bonus AS FLOAT) / ((@Team1Points + @Team1Bonus) + (@Team2Points + @Team2Bonus))
            END;

            -- Random winner selection based on performance
            IF RAND() < @Team1WinChance
            BEGIN
                SET @WinnerTeam = 1;
                SET @Team1BallsWon = 6 + (ABS(CHECKSUM(NEWID())) % 7); -- 6-12 balls
                SET @Team2BallsWon = ABS(CHECKSUM(NEWID())) % (@Team1BallsWon - 5); -- 0 to (winner-6) balls
                IF @Team2BallsWon < 0 SET @Team2BallsWon = 0;
            END
            ELSE
            BEGIN
                SET @WinnerTeam = 2;
                SET @Team2BallsWon = 6 + (ABS(CHECKSUM(NEWID())) % 7); -- 6-12 balls
                SET @Team1BallsWon = ABS(CHECKSUM(NEWID())) % (@Team2BallsWon - 5); -- 0 to (winner-6) balls
                IF @Team1BallsWon < 0 SET @Team1BallsWon = 0;
            END

            SET @BallDifference = ABS(@Team1BallsWon - @Team2BallsWon);
            SET @IsBonus = CASE WHEN @BallDifference >= 6 THEN 1 ELSE 0 END;

            -- Random game timing (within last 20 days)
            SET @StartDate = DATEADD(MINUTE, -RAND() * 28800, GETUTCDATE()); -- Random time in last 20 days
            SET @EndDate = DATEADD(MINUTE, 30 + RAND() * 90, @StartDate); -- Game duration 30-120 minutes

            -- Create the game record
            INSERT INTO Games (
                Id, LobbyCode, CompetitionId, Team1Player1Id, Team1Player2Id, Team2Player1Id, Team2Player2Id,
                Team1Name, Team2Name, Status, CurrentBall, CurrentHand, Team1Score, Team2Score,
                Team1BallsWon, Team2BallsWon, WinnerTeam, StartedAt, CompletedAt, CreatedAt, UpdatedAt
            )
            VALUES (
                @GameId, @LobbyCode, @CompetitionId, @Team1Player1, @Team1Player2, @Team2Player1, @Team2Player2,
                @Team1Name, @Team2Name, 'completed', 6, 6, @Team1BallsWon * 20, @Team2BallsWon * 20,
                @Team1BallsWon, @Team2BallsWon, @WinnerTeam, @StartDate, @EndDate, @StartDate, @EndDate
            );

            SET @GamesCreated = @GamesCreated + 1;
            SET @PairGameNum = @PairGameNum + 1;
        END

        SET @TeamPair = @TeamPair + 1;
    END

    PRINT 'Created ' + CAST(@GamesCreated AS VARCHAR(10)) + ' realistic game records.';

    -- Create GameBalls data for completed games
    PRINT 'Creating GameBalls data for completed games...';

    DECLARE @GameBallsCreated INT = 0;
    DECLARE game_cursor CURSOR FOR
    SELECT Id, Team1BallsWon, Team2BallsWon, WinnerTeam, CompletedAt
    FROM Games
    WHERE CompetitionId = @CompetitionId
    AND Status = 'completed'
    AND Id NOT IN (SELECT DISTINCT GameId FROM GameBalls WHERE GameId IS NOT NULL);

    OPEN game_cursor;
    FETCH NEXT FROM game_cursor INTO @GameId, @Team1BallsWon, @Team2BallsWon, @WinnerTeam, @EndDate;

    WHILE @@FETCH_STATUS = 0 AND @GameBallsCreated < 1000 -- Limit to prevent excessive data
    BEGIN
        -- Create ball records for this game
        DECLARE @BallNum INT = 1;
        DECLARE @Team1BallScore INT = 0, @Team2BallScore INT = 0;
        DECLARE @BallWinner INT;

        WHILE @BallNum <= 6 AND (@Team1BallScore < @Team1BallsWon OR @Team2BallScore < @Team2BallsWon)
        BEGIN
            -- Determine ball winner based on remaining balls needed
            IF @Team1BallScore < @Team1BallsWon AND @Team2BallScore < @Team2BallsWon
            BEGIN
                -- Both teams still need balls - random winner with slight bias to overall winner
                IF (@WinnerTeam = 1 AND RAND() < 0.6) OR (@WinnerTeam = 2 AND RAND() < 0.4)
                    SET @BallWinner = 1
                ELSE
                    SET @BallWinner = 2;
            END
            ELSE IF @Team1BallScore < @Team1BallsWon
                SET @BallWinner = 1
            ELSE
                SET @BallWinner = 2;

            -- Update ball scores
            IF @BallWinner = 1
                SET @Team1BallScore = @Team1BallScore + 1
            ELSE
                SET @Team2BallScore = @Team2BallScore + 1;

            -- Insert GameBall record
            INSERT INTO GameBalls (
                Id, GameId, BallNumber, Team1Score, Team2Score, WinnerTeam, CompletedAt,
                Team1BallsWon, Team2BallsWon, TrumpSuit, CompetitionId,
                Team1Player1Id, Team1Player2Id, Team2Player1Id, Team2Player2Id,
                Team1Name, Team2Name, CreatedAt, UpdatedAt
            )
            SELECT
                NEWID(), g.Id, @BallNum, @Team1BallScore * 20, @Team2BallScore * 20, @BallWinner,
                DATEADD(MINUTE, @BallNum * 15, g.StartedAt),
                @Team1BallScore, @Team2BallScore,
                CASE (ABS(CHECKSUM(NEWID())) % 4)
                    WHEN 0 THEN 'hearts'
                    WHEN 1 THEN 'diamonds'
                    WHEN 2 THEN 'clubs'
                    ELSE 'spades'
                END,
                g.CompetitionId, g.Team1Player1Id, g.Team1Player2Id, g.Team2Player1Id, g.Team2Player2Id,
                g.Team1Name, g.Team2Name, g.CreatedAt, g.UpdatedAt
            FROM Games g
            WHERE g.Id = @GameId;

            SET @GameBallsCreated = @GameBallsCreated + 1;
            SET @BallNum = @BallNum + 1;
        END

        FETCH NEXT FROM game_cursor INTO @GameId, @Team1BallsWon, @Team2BallsWon, @WinnerTeam, @EndDate;
    END

    CLOSE game_cursor;
    DEALLOCATE game_cursor;

    PRINT 'Created ' + CAST(@GameBallsCreated AS VARCHAR(10)) + ' GameBalls records.';
    PRINT 'All teams have been created with realistic game statistics.';
    PRINT 'Top 32 teams by total points will advance to the next phase.';

    -- Show summary statistics
    SELECT
        'SUMMARY' as Info,
        COUNT(*) as TotalTeams,
        AVG(GamesPlayed) as AvgGamesPlayed,
        AVG(Points) as AvgPoints,
        AVG(BonusPoints) as AvgBonusPoints,
        AVG(Points + BonusPoints) as AvgTotalPoints
    FROM CompetitionTeams
    WHERE CompetitionId = @CompetitionId
    AND TeamName LIKE 'Team %';

    -- Show top 10 teams
    SELECT TOP 10
        'TOP 10 TEAMS' as Info,
        TeamName,
        GamesPlayed,
        Points,
        BonusPoints,
        (Points + BonusPoints) as TotalPoints,
        CASE
            WHEN GamesPlayed = 0 THEN 0.0
            ELSE ROUND((CAST(Points AS FLOAT) / CAST(GamesPlayed AS FLOAT)) * 100, 1)
        END as WinRate
    FROM CompetitionTeams
    WHERE CompetitionId = @CompetitionId
    AND TeamName LIKE 'Team %'
    ORDER BY (Points + BonusPoints) DESC, Points DESC;

    -- Show teams that would advance to next phase (Top 32)
    PRINT 'Teams that would advance to next phase (Top 32):';
    WITH RankedTeams AS (
        SELECT
            ROW_NUMBER() OVER (ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC) as Rank,
            TeamName,
            GamesPlayed,
            Points,
            BonusPoints,
            (Points + BonusPoints) as TotalPoints,
            CASE
                WHEN GamesPlayed = 0 THEN 0.0
                ELSE ROUND((CAST(Points AS FLOAT) / CAST(GamesPlayed AS FLOAT)) * 100, 1)
            END as WinRate
        FROM CompetitionTeams
        WHERE CompetitionId = @CompetitionId
        AND TeamName LIKE 'Team %'
    )
    SELECT
        'ADVANCING TEAMS (TOP 32)' as Info,
        Rank,
        TeamName,
        GamesPlayed,
        Points,
        BonusPoints,
        TotalPoints,
        WinRate
    FROM RankedTeams
    WHERE Rank <= 32
    ORDER BY Rank;

    -- Show eliminated teams
    WITH EliminatedTeams AS (
        SELECT
            ROW_NUMBER() OVER (ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC) as Rank
        FROM CompetitionTeams
        WHERE CompetitionId = @CompetitionId
        AND TeamName LIKE 'Team %'
    )
    SELECT
        'ELIMINATED TEAMS' as Info,
        COUNT(*) as EliminatedCount
    FROM EliminatedTeams
    WHERE Rank > 32;

    -- Final statistics
    SELECT
        'FINAL STATISTICS' as Info,
        COUNT(*) as TotalTeams,
        COUNT(CASE WHEN GamesPlayed >= 15 THEN 1 END) as TeamsWithHighActivity,
        AVG(CAST(GamesPlayed AS FLOAT)) as AvgGamesPlayed,
        AVG(CAST(Points + BonusPoints AS FLOAT)) as AvgTotalPoints,
        MAX(Points + BonusPoints) as HighestTotalPoints,
        MIN(Points + BonusPoints) as LowestTotalPoints
    FROM CompetitionTeams
    WHERE CompetitionId = @CompetitionId
    AND TeamName LIKE 'Team %';

    COMMIT TRANSACTION AddCompetitionTestData;
    PRINT 'Transaction committed successfully. All test data has been added.';
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION AddCompetitionTestData;
    
    PRINT 'ERROR: Transaction rolled back due to an error.';
    PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR(10));
    PRINT 'Error Message: ' + ERROR_MESSAGE();
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10));
    
    -- Re-throw the error
    THROW;
END CATCH

GO

PRINT 'Script execution completed.';
PRINT 'You can now test the leaderboard with: EXEC SP_GetCompetitionLeaderboard @CompetitionId=''4860C19D-E3F3-4E2D-B359-275527461BD6''';
