# GoldRushThunee Database Setup Guide

## Overview
This folder contains all the SQL scripts needed to set up the complete GoldRushThunee database for the Thunee multiplayer card game with competition system.

## Database Information
- **Server**: **************
- **Database**: GoldRushThunee
- **User**: EG-Dev
- **Password**: Password01?
- **Connection String**: `Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;`

## Quick Setup (Recommended)

### Option 1: Run Master Script
1. Open SQL Server Management Studio (SSMS)
2. Connect to server `**************` with user `EG-Dev`
3. Open and execute `00_Master_Setup_Script.sql`
4. Then run `03_GoldRushThunee_StoredProcedures.sql`
5. Finally run `04_GoldRushThunee_Leaderboard_Procedures.sql`

### Option 2: Run Individual Scripts (In Order)
1. `01_GoldRushThunee_Database_Creation.sql` - Creates database and core tables
2. `02_GoldRushThunee_Tables_Part2.sql` - Creates game tracking tables and views
3. `03_GoldRushThunee_StoredProcedures.sql` - Creates essential stored procedures
4. `04_GoldRushThunee_Leaderboard_Procedures.sql` - Creates leaderboard and reporting procedures

## File Descriptions

### Core Setup Scripts
- **00_Master_Setup_Script.sql**: Complete database setup in one script (tables + views)
- **01_GoldRushThunee_Database_Creation.sql**: Database creation and core tables
- **02_GoldRushThunee_Tables_Part2.sql**: Game tracking tables and database views
- **03_GoldRushThunee_StoredProcedures.sql**: Essential stored procedures
- **04_GoldRushThunee_Leaderboard_Procedures.sql**: Leaderboard and reporting procedures

### Documentation
- **Competition_System_Documentation.md**: Complete system documentation
- **README.md**: This setup guide

## Database Schema Overview

### Core Tables
1. **Users** - Player accounts and authentication
2. **Competitions** - Tournament definitions and settings
3. **CompetitionTeams** - Team registrations with invite codes
4. **CompetitionTeamInvites** - Partner invitation system
5. **Games** - Game sessions and results
6. **GameBalls** - Individual ball results within games
7. **GameHands** - Individual hand results within balls
8. **PlayedCards** - Card tracking for each hand

### Views
1. **UserStatistics** - Aggregated player performance metrics
2. **CompetitionLeaderboard** - Real-time competition rankings
3. **GameHistory** - Complete game records with player details

### Key Features
- **Competition System**: Team-based tournaments with configurable game limits
- **Scoring System**: Base points (1 per win) + bonus points (6+ ball difference)
- **Invite System**: Unique codes for team partner invitations
- **Leaderboards**: Global and competition-specific rankings
- **Game Tracking**: Complete hand and card history
- **Statistics**: Comprehensive player and team performance metrics

## Verification Steps

After running the setup scripts, verify the installation:

```sql
-- Check database exists
SELECT name FROM sys.databases WHERE name = 'GoldRushThunee';

-- Check all tables exist
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';

-- Check all views exist
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.VIEWS;

-- Check stored procedures exist
SELECT name FROM sys.procedures WHERE type = 'P';

-- Verify table counts (should be 0 for new database)
SELECT 
    'Users' as TableName, COUNT(*) as RecordCount FROM Users
UNION ALL SELECT 'Competitions', COUNT(*) FROM Competitions
UNION ALL SELECT 'CompetitionTeams', COUNT(*) FROM CompetitionTeams
UNION ALL SELECT 'Games', COUNT(*) FROM Games;
```

Expected Results:
- 8 Tables: Users, Competitions, CompetitionTeams, CompetitionTeamInvites, Games, GameBalls, GameHands, PlayedCards
- 3 Views: UserStatistics, CompetitionLeaderboard, GameHistory
- 15+ Stored Procedures for user management, competitions, games, and leaderboards

## Application Configuration

### ASP.NET Core (ThuneeAPI)
Update your `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"
  }
}
```

### Node.js Server
Update your environment configuration to point API calls to the ASP.NET Core server.

## Sample Data (Optional)

To create test data for development, you can run:

```sql
-- Create test users
INSERT INTO Users (Id, Username, Email, PasswordHash, IsVerified, IsActive)
VALUES 
    (NEWID(), 'TestPlayer1', '<EMAIL>', 'hashedpassword1', 1, 1),
    (NEWID(), 'TestPlayer2', '<EMAIL>', 'hashedpassword2', 1, 1),
    (NEWID(), 'TestPlayer3', '<EMAIL>', 'hashedpassword3', 1, 1),
    (NEWID(), 'TestPlayer4', '<EMAIL>', 'hashedpassword4', 1, 1);

-- Create test competition
DECLARE @CompetitionId UNIQUEIDENTIFIER = NEWID();
INSERT INTO Competitions (Id, Name, Description, StartDate, EndDate, Status, MaxTeams, MaxGamesPerTeam)
VALUES (@CompetitionId, 'Test Championship 2025', 'A test competition for development', 
        GETUTCDATE(), DATEADD(DAY, 30, GETUTCDATE()), 'active', 16, 10);
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify server IP: **************
   - Check SQL Server is running
   - Confirm user `EG-Dev` has proper permissions

2. **Permission Denied**
   - Ensure `EG-Dev` user has `db_owner` or sufficient permissions
   - Check if user can create databases and tables

3. **Foreign Key Errors**
   - Run scripts in the correct order
   - Ensure all parent tables exist before creating child tables

4. **Stored Procedure Errors**
   - Verify all tables and views exist first
   - Check for syntax errors in dynamic SQL

### Performance Considerations

- All tables include appropriate indexes
- Views use efficient joins and aggregations
- Stored procedures include error handling
- Pagination support for large result sets

## Support

For issues or questions:
1. Check the `Competition_System_Documentation.md` for detailed system information
2. Verify all scripts ran successfully without errors
3. Test basic CRUD operations on core tables
4. Ensure application connection strings are correct

## Next Steps

After database setup:
1. Configure your ASP.NET Core API connection string
2. Test API endpoints for user authentication
3. Test competition creation and team management
4. Verify game result submission and scoring
5. Check leaderboard functionality

The database is now ready to support the complete Thunee competition system!
