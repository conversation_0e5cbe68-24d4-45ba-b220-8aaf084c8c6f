-- =============================================
-- HOTFIX: Add Missing SP_GetCompetitions Stored Procedure
-- =============================================
-- Server: **************
-- Database: BME (or GoldRushThunee)
-- Description: Add the missing SP_GetCompetitions stored procedure
-- =============================================

-- Use the correct database name (BME based on user's connection string from memories)
-- Note: User mentioned using BME database in their connection string
USE BME;
GO

-- Get All Competitions
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitions')
    DROP PROCEDURE SP_GetCompetitions;
GO

CREATE PROCEDURE SP_GetCompetitions
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Competitions 
    ORDER BY CreatedAt DESC;
END
GO

PRINT 'SP_GetCompetitions stored procedure created successfully!';
