# Thunee Competition System Documentation

## Overview
The Thunee Competition System is a comprehensive tournament management system that allows players to create teams, join competitions, and compete in structured tournaments with scoring and leaderboards.

## Database Connection
- **Server**: **************
- **Database**: GoldRushThunee
- **User**: EG-Dev
- **Password**: Password01?
- **Connection String**: `Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;`

## Competition System Architecture

### 1. Core Entities

#### Users
- **Purpose**: Store player information and authentication data
- **Key Fields**: Id, Username, Email, PasswordHash, IsActive
- **Relationships**: Referenced by all game and competition entities

#### Competitions
- **Purpose**: Define tournament parameters and rules
- **Key Fields**: 
  - `MaxGamesPerTeam`: Configurable limit (default: 10 games)
  - `Status`: upcoming, active, completed, cancelled
  - `MaxTeams`: Maximum number of teams allowed
  - `EntryFee`, `PrizePool`: Financial aspects
- **Business Rules**: 
  - Teams can only join 'upcoming' or 'active' competitions
  - Game limits are configurable per competition

#### CompetitionTeams
- **Purpose**: Represent team registrations in competitions
- **Key Fields**:
  - `InviteCode`: Unique 8-character code for partner invitation
  - `GamesPlayed`: Current number of games completed
  - `Points`: Base points (1 per win)
  - `BonusPoints`: Additional points for 6+ ball difference wins
  - `MaxGames`: Team-specific game limit (inherited from competition)
  - `IsComplete`: Whether partner has joined (Player2Id is set)

### 2. Team Creation & Management Flow

#### Step 1: Create Team
```sql
EXEC SP_CreateCompetitionTeam 
    @Id = NEWID(),
    @CompetitionId = 'competition-guid',
    @TeamName = 'Team Name',
    @Player1Id = 'player-guid',
    @InviteCode = 'ABC123XY',
    @MaxGames = 10
```

**Process**:
1. Player creates team with custom name
2. System generates unique 8-character invite code
3. Team status: `IsComplete = 0` (waiting for partner)
4. Competition team count incremented

#### Step 2: Partner Joins Team
```sql
EXEC SP_JoinCompetitionTeam 
    @InviteCode = 'ABC123XY',
    @Player2Id = 'partner-player-guid'
```

**Process**:
1. Partner uses invite code to join
2. System validates code and availability
3. Team status: `IsComplete = 1`, `CompletedAt = GETUTCDATE()`
4. Team can now participate in matchmaking

### 3. Game Lifecycle & Scoring

#### Game Creation
- Teams with `IsComplete = 1` can create lobbies
- System tracks `team1Id` and `team2Id` for result processing
- Competition games have special lobby properties

#### Game Completion & Scoring
```sql
EXEC SP_CompleteGame 
    @GameId = 'game-guid',
    @WinnerTeam = 1,
    @Team1Score = 4,
    @Team2Score = 2,
    @Team1BallsWon = 6,
    @Team2BallsWon = 0
```

**Scoring Rules**:
1. **Base Points**: 1 point for winning a game
2. **Bonus Points**: +1 point for winning by 6+ ball difference
3. **Game Count**: Both teams increment `GamesPlayed`
4. **Game Limits**: Teams cannot exceed `MaxGames` per competition

**Example Scoring Scenarios**:
- Win by 6-0 balls: 2 points (1 base + 1 bonus)
- Win by 6-1 balls: 2 points (1 base + 1 bonus)  
- Win by 6-5 balls: 1 point (1 base only)
- Loss: 0 points (but `GamesPlayed` still increments)

### 4. Competition Status Management

#### Player Status States
1. **Not Registered**: Player hasn't joined competition
2. **Waiting for Partner**: Team created, `IsComplete = 0`
3. **Ready to Play**: Team complete, games < maxGames
4. **Competition Complete**: `GamesPlayed >= MaxGames`

#### Status Check
```sql
EXEC SP_GetPlayerCompetitionStatus 
    @PlayerId = 'player-guid',
    @CompetitionId = 'competition-guid'
```

### 5. Leaderboard System

#### Competition Leaderboard
- **Primary Sort**: Total Points (Points + BonusPoints) DESC
- **Secondary Sort**: Games Played ASC (fewer games = higher rank if tied)
- **Real-time Updates**: Updated after each game completion

#### Global Leaderboard
- **Metrics**: Total Score, Win Rate, Games Played, Competitions Joined
- **Calculation**: Aggregated from all completed games across all competitions

### 6. Matchmaking Integration

#### Competition Matchmaking
- Only teams with `IsComplete = 1` can join matchmaking
- System enforces game limits during lobby creation
- Teams with `GamesPlayed >= MaxGames` cannot create new lobbies

#### Lobby Properties
```javascript
{
  competitionGameNumber: team.gamesPlayed + 1,
  maxCompetitionGames: team.maxGames,
  team1Id: team.id,
  team2Id: null // Set when matched
}
```

### 7. Database Views & Reporting

#### UserStatistics View
- Aggregates player performance across all games
- Calculates win rates, average scores, total games
- Used for global leaderboards

#### CompetitionLeaderboard View
- Real-time competition rankings
- Includes team information, scores, and status
- Supports pagination and filtering

#### GameHistory View
- Complete game records with player names
- Ball difference calculations
- Game duration tracking

### 8. API Integration Points

#### Key Endpoints
- `POST /api/competitions/{id}/teams/create` - Create team
- `POST /api/competitions/teams/join` - Join team via invite code
- `GET /api/competitions/{id}/status` - Get player status
- `POST /api/competitions/games/result` - Submit game results
- `GET /api/competitions/{id}/leaderboard` - Get rankings

#### Node.js Server Integration
- Competition service tracks user memberships
- Validates game limits before lobby creation
- Submits results to ASP.NET Core API after game completion

### 9. Security & Validation

#### Invite Code System
- 8-character alphanumeric codes
- Unique across all teams and invites
- No expiration (unlike CompetitionTeamInvites which have 7-day expiry)

#### Data Integrity
- Foreign key constraints prevent orphaned records
- Check constraints validate game states and scores
- Unique constraints prevent duplicate team memberships

### 10. Configuration & Customization

#### Configurable Parameters
- `MaxGamesPerTeam`: Set per competition (default: 10)
- `MaxTeams`: Competition capacity
- `BonusPointThreshold`: Ball difference for bonus (currently 6)
- `InviteCodeLength`: Length of invite codes (default: 8)

#### Competition Types
- **Public**: Open registration, visible to all players
- **Private**: Invitation-only, hidden from public lists
- **Entry Fees**: Optional monetary entry requirements
- **Prize Pools**: Configurable prize structures

## Setup Instructions

1. **Run Database Scripts** (in order):
   - `01_GoldRushThunee_Database_Creation.sql`
   - `02_GoldRushThunee_Tables_Part2.sql`
   - `03_GoldRushThunee_StoredProcedures.sql`
   - `04_GoldRushThunee_Leaderboard_Procedures.sql`

2. **Configure Connection String** in ASP.NET Core:
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"
     }
   }
   ```

3. **Test Database Connection**:
   ```sql
   SELECT COUNT(*) FROM Users;
   SELECT COUNT(*) FROM Competitions;
   ```

## Troubleshooting

### Common Issues
1. **Invite Code Conflicts**: Use `SP_GenerateUniqueInviteCode` for guaranteed uniqueness
2. **Game Limit Exceeded**: Check `GamesPlayed` vs `MaxGames` before lobby creation
3. **Team Already Complete**: Validate `IsComplete = 0` before allowing joins
4. **Competition Status**: Ensure competition is 'active' or 'upcoming' for team registration

### Performance Considerations
- Indexes on frequently queried fields (UserId, CompetitionId, InviteCode)
- Pagination for leaderboards and game history
- View-based aggregations for statistics

This documentation provides a complete overview of the competition system architecture, data flow, and implementation details for the GoldRushThunee database.
