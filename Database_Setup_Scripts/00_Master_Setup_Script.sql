-- =============================================
-- GoldRushThunee Master Database Setup Script
-- =============================================
-- Server: **************
-- Database: GoldRushThunee
-- User: EG-Dev
-- Description: Master script to set up the complete Thunee database
-- 
-- INSTRUCTIONS:
-- 1. Connect to SQL Server using SSMS
-- 2. Run this script as a single execution
-- 3. Verify all tables and procedures are created
-- =============================================

PRINT '==============================================';
PRINT 'Starting GoldRushThunee Database Setup';
PRINT 'Server: **************';
PRINT 'Database: GoldRushThunee';
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '==============================================';

-- =============================================
-- STEP 1: CREATE DATABASE
-- =============================================
PRINT 'STEP 1: Creating Database...';

IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'GoldRushThunee')
BEGIN
    CREATE DATABASE GoldRushThunee
    COLLATE SQL_Latin1_General_CP1_CI_AS;
    PRINT '✓ Database GoldRushThunee created successfully.';
END
ELSE
BEGIN
    PRINT '✓ Database GoldRushThunee already exists.';
END
GO

USE GoldRushThunee;
GO

-- =============================================
-- STEP 2: CREATE CORE TABLES
-- =============================================
PRINT 'STEP 2: Creating Core Tables...';

-- Users Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        Username NVARCHAR(50) NOT NULL UNIQUE,
        Email NVARCHAR(255) NOT NULL UNIQUE,
        PasswordHash NVARCHAR(255) NOT NULL,
        IsVerified BIT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        IsAdmin BIT NOT NULL DEFAULT 0,
        LastLoginAt DATETIME2 NULL,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );

    CREATE INDEX IX_Users_Username ON Users(Username);
    CREATE INDEX IX_Users_Email ON Users(Email);
    CREATE INDEX IX_Users_IsActive ON Users(IsActive);
    CREATE INDEX IX_Users_CreatedAt ON Users(CreatedAt);
    
    PRINT '✓ Users table created.';
END
ELSE
BEGIN
    PRINT '✓ Users table already exists.';
END

-- Competitions Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Competitions' AND xtype='U')
BEGIN
    CREATE TABLE Competitions (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(500) NULL,
        StartDate DATETIME2 NOT NULL,
        EndDate DATETIME2 NOT NULL,
        Status NVARCHAR(20) NOT NULL DEFAULT 'upcoming' CHECK (Status IN ('upcoming', 'active', 'completed', 'cancelled')),
        MaxTeams INT NOT NULL DEFAULT 32 CHECK (MaxTeams >= 2 AND MaxTeams <= 64),
        CurrentTeams INT NOT NULL DEFAULT 0,
        EntryFee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        PrizeFirst NVARCHAR(100) NULL,
        PrizeSecond NVARCHAR(100) NULL,
        PrizeThird NVARCHAR(100) NULL,
        TotalPrizePool DECIMAL(10,2) NULL,
        Rules NVARCHAR(1000) NULL,
        IsPublic BIT NOT NULL DEFAULT 1,
        AllowSpectators BIT NOT NULL DEFAULT 1,
        MaxGamesPerTeam INT NOT NULL DEFAULT 10,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );

    CREATE INDEX IX_Competitions_Status ON Competitions(Status);
    CREATE INDEX IX_Competitions_StartDate ON Competitions(StartDate);
    CREATE INDEX IX_Competitions_EndDate ON Competitions(EndDate);
    CREATE INDEX IX_Competitions_IsPublic ON Competitions(IsPublic);
    
    PRINT '✓ Competitions table created.';
END
ELSE
BEGIN
    PRINT '✓ Competitions table already exists.';
END

-- CompetitionTeams Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CompetitionTeams' AND xtype='U')
BEGIN
    CREATE TABLE CompetitionTeams (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionId UNIQUEIDENTIFIER NOT NULL,
        TeamName NVARCHAR(50) NOT NULL,
        Player1Id UNIQUEIDENTIFIER NOT NULL,
        Player2Id UNIQUEIDENTIFIER NULL,
        InviteCode NVARCHAR(10) NOT NULL UNIQUE,
        GamesPlayed INT NOT NULL DEFAULT 0,
        Points INT NOT NULL DEFAULT 0,
        BonusPoints INT NOT NULL DEFAULT 0,
        MaxGames INT NOT NULL DEFAULT 10,
        IsActive BIT NOT NULL DEFAULT 1,
        IsComplete BIT NOT NULL DEFAULT 0,
        RegisteredAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        CompletedAt DATETIME2 NULL,
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE CASCADE,
        FOREIGN KEY (Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION
    );

    CREATE INDEX IX_CompetitionTeams_CompetitionId ON CompetitionTeams(CompetitionId);
    CREATE INDEX IX_CompetitionTeams_Player1Id ON CompetitionTeams(Player1Id);
    CREATE INDEX IX_CompetitionTeams_Player2Id ON CompetitionTeams(Player2Id);
    CREATE INDEX IX_CompetitionTeams_InviteCode ON CompetitionTeams(InviteCode);
    CREATE INDEX IX_CompetitionTeams_IsActive ON CompetitionTeams(IsActive);
    CREATE INDEX IX_CompetitionTeams_IsComplete ON CompetitionTeams(IsComplete);
    
    PRINT '✓ CompetitionTeams table created.';
END
ELSE
BEGIN
    PRINT '✓ CompetitionTeams table already exists.';
END

-- CompetitionTeamInvites Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CompetitionTeamInvites' AND xtype='U')
BEGIN
    CREATE TABLE CompetitionTeamInvites (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionId UNIQUEIDENTIFIER NOT NULL,
        TeamId UNIQUEIDENTIFIER NOT NULL,
        InviterId UNIQUEIDENTIFIER NOT NULL,
        InviteeId UNIQUEIDENTIFIER NULL,
        InviteCode NVARCHAR(10) NOT NULL UNIQUE,
        Status NVARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (Status IN ('pending', 'accepted', 'expired', 'cancelled')),
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ExpiresAt DATETIME2 NOT NULL DEFAULT DATEADD(DAY, 7, GETUTCDATE()),
        AcceptedAt DATETIME2 NULL,
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE NO ACTION,
        FOREIGN KEY (TeamId) REFERENCES CompetitionTeams(Id) ON DELETE CASCADE,
        FOREIGN KEY (InviterId) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (InviteeId) REFERENCES Users(Id) ON DELETE NO ACTION
    );

    CREATE INDEX IX_CompetitionTeamInvites_CompetitionId ON CompetitionTeamInvites(CompetitionId);
    CREATE INDEX IX_CompetitionTeamInvites_TeamId ON CompetitionTeamInvites(TeamId);
    CREATE INDEX IX_CompetitionTeamInvites_InviteCode ON CompetitionTeamInvites(InviteCode);
    CREATE INDEX IX_CompetitionTeamInvites_Status ON CompetitionTeamInvites(Status);
    CREATE INDEX IX_CompetitionTeamInvites_ExpiresAt ON CompetitionTeamInvites(ExpiresAt);
    
    PRINT '✓ CompetitionTeamInvites table created.';
END
ELSE
BEGIN
    PRINT '✓ CompetitionTeamInvites table already exists.';
END

-- Games Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Games' AND xtype='U')
BEGIN
    CREATE TABLE Games (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        LobbyCode NVARCHAR(6) NOT NULL UNIQUE,
        CompetitionId UNIQUEIDENTIFIER NULL,
        Team1Player1Id UNIQUEIDENTIFIER NOT NULL,
        Team1Player2Id UNIQUEIDENTIFIER NOT NULL,
        Team2Player1Id UNIQUEIDENTIFIER NOT NULL,
        Team2Player2Id UNIQUEIDENTIFIER NOT NULL,
        Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1',
        Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2',
        Status NVARCHAR(20) NOT NULL DEFAULT 'waiting' CHECK (Status IN ('waiting', 'in_progress', 'completed', 'abandoned')),
        DealerId UNIQUEIDENTIFIER NULL,
        TrumpSuit NVARCHAR(10) NULL CHECK (TrumpSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
        CurrentBall INT NOT NULL DEFAULT 1 CHECK (CurrentBall >= 1 AND CurrentBall <= 6),
        CurrentHand INT NOT NULL DEFAULT 1 CHECK (CurrentHand >= 1 AND CurrentHand <= 6),
        Team1Score INT NOT NULL DEFAULT 0,
        Team2Score INT NOT NULL DEFAULT 0,
        Team1BallsWon INT NOT NULL DEFAULT 0,
        Team2BallsWon INT NOT NULL DEFAULT 0,
        WinnerTeam INT NULL CHECK (WinnerTeam IN (1, 2)),
        StartedAt DATETIME2 NULL,
        CompletedAt DATETIME2 NULL,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE SET NULL,
        FOREIGN KEY (Team1Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (Team1Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (Team2Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (Team2Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION,
        FOREIGN KEY (DealerId) REFERENCES Users(Id) ON DELETE NO ACTION
    );

    CREATE INDEX IX_Games_LobbyCode ON Games(LobbyCode);
    CREATE INDEX IX_Games_CompetitionId ON Games(CompetitionId);
    CREATE INDEX IX_Games_Status ON Games(Status);
    CREATE INDEX IX_Games_StartedAt ON Games(StartedAt);
    CREATE INDEX IX_Games_CompletedAt ON Games(CompletedAt);
    CREATE INDEX IX_Games_Team1Player1Id ON Games(Team1Player1Id);
    CREATE INDEX IX_Games_Team1Player2Id ON Games(Team1Player2Id);
    CREATE INDEX IX_Games_Team2Player1Id ON Games(Team2Player1Id);
    CREATE INDEX IX_Games_Team2Player2Id ON Games(Team2Player2Id);
    
    PRINT '✓ Games table created.';
END
ELSE
BEGIN
    PRINT '✓ Games table already exists.';
END

PRINT 'STEP 2 Complete: Core tables created successfully.';
PRINT '';

-- =============================================
-- STEP 3: CREATE GAME TRACKING TABLES
-- =============================================
PRINT 'STEP 3: Creating Game Tracking Tables...';

-- GameBalls Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='GameBalls' AND xtype='U')
BEGIN
    CREATE TABLE GameBalls (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameId UNIQUEIDENTIFIER NOT NULL,
        BallNumber INT NOT NULL CHECK (BallNumber >= 1 AND BallNumber <= 6),
        Team1Score INT NOT NULL DEFAULT 0,
        Team2Score INT NOT NULL DEFAULT 0,
        WinnerTeam INT NOT NULL CHECK (WinnerTeam IN (1, 2)),
        CompletedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (GameId) REFERENCES Games(Id) ON DELETE CASCADE,
        UNIQUE(GameId, BallNumber)
    );

    CREATE INDEX IX_GameBalls_GameId ON GameBalls(GameId);
    CREATE INDEX IX_GameBalls_BallNumber ON GameBalls(BallNumber);
    CREATE INDEX IX_GameBalls_WinnerTeam ON GameBalls(WinnerTeam);
    
    PRINT '✓ GameBalls table created.';
END
ELSE
BEGIN
    PRINT '✓ GameBalls table already exists.';
END

-- GameHands Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='GameHands' AND xtype='U')
BEGIN
    CREATE TABLE GameHands (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameId UNIQUEIDENTIFIER NOT NULL,
        BallNumber INT NOT NULL CHECK (BallNumber >= 1 AND BallNumber <= 6),
        HandNumber INT NOT NULL CHECK (HandNumber >= 1 AND HandNumber <= 6),
        WinnerPlayerId UNIQUEIDENTIFIER NOT NULL,
        Points INT NOT NULL DEFAULT 0,
        TrumpSuit NVARCHAR(10) NULL CHECK (TrumpSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
        CompletedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (GameId) REFERENCES Games(Id) ON DELETE CASCADE,
        FOREIGN KEY (WinnerPlayerId) REFERENCES Users(Id) ON DELETE NO ACTION,
        UNIQUE(GameId, BallNumber, HandNumber)
    );

    CREATE INDEX IX_GameHands_GameId ON GameHands(GameId);
    CREATE INDEX IX_GameHands_BallNumber ON GameHands(BallNumber);
    CREATE INDEX IX_GameHands_HandNumber ON GameHands(HandNumber);
    CREATE INDEX IX_GameHands_WinnerPlayerId ON GameHands(WinnerPlayerId);
    CREATE INDEX IX_GameHands_CompletedAt ON GameHands(CompletedAt);
    
    PRINT '✓ GameHands table created.';
END
ELSE
BEGIN
    PRINT '✓ GameHands table already exists.';
END

-- PlayedCards Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlayedCards' AND xtype='U')
BEGIN
    CREATE TABLE PlayedCards (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameHandId UNIQUEIDENTIFIER NOT NULL,
        PlayerId UNIQUEIDENTIFIER NOT NULL,
        CardSuit NVARCHAR(10) NOT NULL CHECK (CardSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
        CardValue NVARCHAR(2) NOT NULL CHECK (CardValue IN ('9', '10', 'J', 'Q', 'K', 'A')),
        PlayOrder INT NOT NULL CHECK (PlayOrder >= 1 AND PlayOrder <= 4),
        PlayedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (GameHandId) REFERENCES GameHands(Id) ON DELETE CASCADE,
        FOREIGN KEY (PlayerId) REFERENCES Users(Id) ON DELETE NO ACTION,
        UNIQUE(GameHandId, PlayOrder),
        UNIQUE(GameHandId, PlayerId)
    );

    CREATE INDEX IX_PlayedCards_GameHandId ON PlayedCards(GameHandId);
    CREATE INDEX IX_PlayedCards_PlayerId ON PlayedCards(PlayerId);
    CREATE INDEX IX_PlayedCards_PlayOrder ON PlayedCards(PlayOrder);
    CREATE INDEX IX_PlayedCards_CardSuit ON PlayedCards(CardSuit);
    CREATE INDEX IX_PlayedCards_PlayedAt ON PlayedCards(PlayedAt);
    
    PRINT '✓ PlayedCards table created.';
END
ELSE
BEGIN
    PRINT '✓ PlayedCards table already exists.';
END

PRINT 'STEP 3 Complete: Game tracking tables created successfully.';
PRINT '';

-- =============================================
-- STEP 4: CREATE VIEWS
-- =============================================
PRINT 'STEP 4: Creating Database Views...';

-- UserStatistics View
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='UserStatistics')
BEGIN
    EXEC('
    CREATE VIEW UserStatistics AS
    SELECT
        u.Id AS UserId,
        u.Username,
        COALESCE(stats.GamesPlayed, 0) AS GamesPlayed,
        COALESCE(stats.GamesWon, 0) AS GamesWon,
        COALESCE(stats.GamesLost, 0) AS GamesLost,
        CASE
            WHEN COALESCE(stats.GamesPlayed, 0) = 0 THEN 0.0
            ELSE CAST(COALESCE(stats.GamesWon, 0) AS FLOAT) / CAST(stats.GamesPlayed AS FLOAT) * 100
        END AS WinRate,
        COALESCE(stats.TotalScore, 0) AS TotalScore,
        CASE
            WHEN COALESCE(stats.GamesPlayed, 0) = 0 THEN 0.0
            ELSE CAST(COALESCE(stats.TotalScore, 0) AS FLOAT) / CAST(stats.GamesPlayed AS FLOAT)
        END AS AverageScore,
        COALESCE(stats.HandsPlayed, 0) AS HandsPlayed,
        COALESCE(comp_stats.CompetitionsJoined, 0) AS CompetitionsJoined,
        COALESCE(comp_stats.CompetitionsWon, 0) AS CompetitionsWon
    FROM Users u
    LEFT JOIN (
        SELECT
            player_id,
            COUNT(*) AS GamesPlayed,
            SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) AS GamesWon,
            SUM(CASE WHEN is_winner = 0 THEN 1 ELSE 0 END) AS GamesLost,
            SUM(score) AS TotalScore,
            SUM(hands_count) AS HandsPlayed
        FROM (
            SELECT
                Team1Player1Id AS player_id,
                CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END AS is_winner,
                Team1Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT
                Team1Player2Id AS player_id,
                CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END AS is_winner,
                Team1Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT
                Team2Player1Id AS player_id,
                CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END AS is_winner,
                Team2Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT
                Team2Player2Id AS player_id,
                CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END AS is_winner,
                Team2Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
        ) all_players
        GROUP BY player_id
    ) stats ON u.Id = stats.player_id
    LEFT JOIN (
        SELECT
            player_id,
            COUNT(DISTINCT CompetitionId) AS CompetitionsJoined,
            0 AS CompetitionsWon
        FROM (
            SELECT Player1Id AS player_id, CompetitionId FROM CompetitionTeams WHERE Player1Id IS NOT NULL
            UNION
            SELECT Player2Id AS player_id, CompetitionId FROM CompetitionTeams WHERE Player2Id IS NOT NULL
        ) comp_players
        GROUP BY player_id
    ) comp_stats ON u.Id = comp_stats.player_id
    WHERE u.IsActive = 1
    ')

    PRINT '✓ UserStatistics view created.';
END
ELSE
BEGIN
    PRINT '✓ UserStatistics view already exists.';
END

-- CompetitionLeaderboard View
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='CompetitionLeaderboard')
BEGIN
    EXEC('
    CREATE VIEW CompetitionLeaderboard AS
    SELECT
        ct.CompetitionId,
        c.Name AS CompetitionName,
        ct.Id AS TeamId,
        ct.TeamName,
        u1.Username AS Player1Name,
        u2.Username AS Player2Name,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        CASE
            WHEN ct.GamesPlayed >= ct.MaxGames THEN ''Completed''
            WHEN ct.IsComplete = 1 THEN ''Active''
            ELSE ''Waiting for Partner''
        END AS Status,
        ROW_NUMBER() OVER (PARTITION BY ct.CompetitionId ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.GamesPlayed ASC) AS Rank
    FROM CompetitionTeams ct
    INNER JOIN Competitions c ON ct.CompetitionId = c.Id
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.IsActive = 1
    ')

    PRINT '✓ CompetitionLeaderboard view created.';
END
ELSE
BEGIN
    PRINT '✓ CompetitionLeaderboard view already exists.';
END

-- GameHistory View
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='GameHistory')
BEGIN
    EXEC('
    CREATE VIEW GameHistory AS
    SELECT
        g.Id AS GameId,
        g.LobbyCode,
        g.CompetitionId,
        c.Name AS CompetitionName,
        g.Team1Name,
        g.Team2Name,
        u1.Username AS Team1Player1Name,
        u2.Username AS Team1Player2Name,
        u3.Username AS Team2Player1Name,
        u4.Username AS Team2Player2Name,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        CASE
            WHEN g.WinnerTeam = 1 THEN g.Team1Name
            WHEN g.WinnerTeam = 2 THEN g.Team2Name
            ELSE ''No Winner''
        END AS WinnerTeamName,
        ABS(g.Team1BallsWon - g.Team2BallsWon) AS BallDifference,
        CASE
            WHEN ABS(g.Team1BallsWon - g.Team2BallsWon) >= 6 THEN 1
            ELSE 0
        END AS IsBonusWin,
        g.Status,
        g.StartedAt,
        g.CompletedAt,
        DATEDIFF(MINUTE, g.StartedAt, g.CompletedAt) AS GameDurationMinutes
    FROM Games g
    LEFT JOIN Competitions c ON g.CompetitionId = c.Id
    LEFT JOIN Users u1 ON g.Team1Player1Id = u1.Id
    LEFT JOIN Users u2 ON g.Team1Player2Id = u2.Id
    LEFT JOIN Users u3 ON g.Team2Player1Id = u3.Id
    LEFT JOIN Users u4 ON g.Team2Player2Id = u4.Id
    ')

    PRINT '✓ GameHistory view created.';
END
ELSE
BEGIN
    PRINT '✓ GameHistory view already exists.';
END

PRINT 'STEP 4 Complete: Database views created successfully.';
PRINT '';

PRINT '==============================================';
PRINT 'GoldRushThunee Database Setup Complete!';
PRINT '';
PRINT 'Database Objects Created:';
PRINT '- 8 Tables (with indexes and constraints)';
PRINT '- 3 Views (UserStatistics, CompetitionLeaderboard, GameHistory)';
PRINT '- Foreign key relationships';
PRINT '- Check constraints for data integrity';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Run stored procedures scripts (03_ and 04_)';
PRINT '2. Test database connectivity';
PRINT '3. Configure your application connection string';
PRINT '4. Create sample data if needed';
PRINT '';
PRINT 'Connection String:';
PRINT 'Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;';
PRINT '';
PRINT 'Database is ready for use!';
PRINT '==============================================';
