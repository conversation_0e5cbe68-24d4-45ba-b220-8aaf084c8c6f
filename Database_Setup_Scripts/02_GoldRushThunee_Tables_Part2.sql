-- =============================================
-- GoldRushThunee Database Tables Part 2
-- =============================================
-- Server: **************
-- Database: GoldRushThunee
-- Description: Game tracking tables (GameHands, PlayedCards, Views)
-- =============================================

USE GoldRushThunee;
GO

-- =============================================
-- 7. GAME HANDS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='GameHands' AND xtype='U')
BEGIN
    CREATE TABLE GameHands (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameId UNIQUEIDENTIFIER NOT NULL,
        BallNumber INT NOT NULL CHECK (BallNumber >= 1 AND BallNumber <= 6),
        HandNumber INT NOT NULL CHECK (HandNumber >= 1 AND HandNumber <= 6),
        WinnerPlayerId UNIQUEIDENTIFIER NOT NULL,
        Points INT NOT NULL DEFAULT 0,
        TrumpSuit NVARCHAR(10) NULL CHECK (TrumpSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
        CompletedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (GameId) REFERENCES Games(Id) ON DELETE CASCADE,
        FOREIGN KEY (WinnerPlayerId) REFERENCES Users(Id) ON DELETE NO ACTION,
        UNIQUE(GameId, BallNumber, HandNumber)
    );

    -- Create indexes for GameHands table
    CREATE INDEX IX_GameHands_GameId ON GameHands(GameId);
    CREATE INDEX IX_GameHands_BallNumber ON GameHands(BallNumber);
    CREATE INDEX IX_GameHands_HandNumber ON GameHands(HandNumber);
    CREATE INDEX IX_GameHands_WinnerPlayerId ON GameHands(WinnerPlayerId);
    CREATE INDEX IX_GameHands_CompletedAt ON GameHands(CompletedAt);
    
    PRINT 'GameHands table created successfully.';
END
ELSE
BEGIN
    PRINT 'GameHands table already exists.';
END
GO

-- =============================================
-- 8. PLAYED CARDS TABLE
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlayedCards' AND xtype='U')
BEGIN
    CREATE TABLE PlayedCards (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameHandId UNIQUEIDENTIFIER NOT NULL,
        PlayerId UNIQUEIDENTIFIER NOT NULL,
        CardSuit NVARCHAR(10) NOT NULL CHECK (CardSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
        CardValue NVARCHAR(2) NOT NULL CHECK (CardValue IN ('9', '10', 'J', 'Q', 'K', 'A')),
        PlayOrder INT NOT NULL CHECK (PlayOrder >= 1 AND PlayOrder <= 4),
        PlayedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY (GameHandId) REFERENCES GameHands(Id) ON DELETE CASCADE,
        FOREIGN KEY (PlayerId) REFERENCES Users(Id) ON DELETE NO ACTION,
        UNIQUE(GameHandId, PlayOrder),
        UNIQUE(GameHandId, PlayerId)
    );

    -- Create indexes for PlayedCards table
    CREATE INDEX IX_PlayedCards_GameHandId ON PlayedCards(GameHandId);
    CREATE INDEX IX_PlayedCards_PlayerId ON PlayedCards(PlayerId);
    CREATE INDEX IX_PlayedCards_PlayOrder ON PlayedCards(PlayOrder);
    CREATE INDEX IX_PlayedCards_CardSuit ON PlayedCards(CardSuit);
    CREATE INDEX IX_PlayedCards_PlayedAt ON PlayedCards(PlayedAt);
    
    PRINT 'PlayedCards table created successfully.';
END
ELSE
BEGIN
    PRINT 'PlayedCards table already exists.';
END
GO

-- =============================================
-- 9. USER STATISTICS VIEW
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='UserStatistics')
BEGIN
    EXEC('
    CREATE VIEW UserStatistics AS
    SELECT 
        u.Id AS UserId,
        u.Username,
        COALESCE(stats.GamesPlayed, 0) AS GamesPlayed,
        COALESCE(stats.GamesWon, 0) AS GamesWon,
        COALESCE(stats.GamesLost, 0) AS GamesLost,
        CASE 
            WHEN COALESCE(stats.GamesPlayed, 0) = 0 THEN 0.0
            ELSE CAST(COALESCE(stats.GamesWon, 0) AS FLOAT) / CAST(stats.GamesPlayed AS FLOAT) * 100
        END AS WinRate,
        COALESCE(stats.TotalScore, 0) AS TotalScore,
        CASE 
            WHEN COALESCE(stats.GamesPlayed, 0) = 0 THEN 0.0
            ELSE CAST(COALESCE(stats.TotalScore, 0) AS FLOAT) / CAST(stats.GamesPlayed AS FLOAT)
        END AS AverageScore,
        COALESCE(stats.HandsPlayed, 0) AS HandsPlayed,
        COALESCE(comp_stats.CompetitionsJoined, 0) AS CompetitionsJoined,
        COALESCE(comp_stats.CompetitionsWon, 0) AS CompetitionsWon
    FROM Users u
    LEFT JOIN (
        SELECT 
            player_id,
            COUNT(*) AS GamesPlayed,
            SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) AS GamesWon,
            SUM(CASE WHEN is_winner = 0 THEN 1 ELSE 0 END) AS GamesLost,
            SUM(score) AS TotalScore,
            SUM(hands_count) AS HandsPlayed
        FROM (
            SELECT 
                Team1Player1Id AS player_id,
                CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END AS is_winner,
                Team1Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT 
                Team1Player2Id AS player_id,
                CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END AS is_winner,
                Team1Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT 
                Team2Player1Id AS player_id,
                CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END AS is_winner,
                Team2Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
            UNION ALL
            SELECT 
                Team2Player2Id AS player_id,
                CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END AS is_winner,
                Team2Score AS score,
                (SELECT COUNT(*) FROM GameHands gh WHERE gh.GameId = g.Id) AS hands_count
            FROM Games g WHERE Status = ''completed''
        ) all_players
        GROUP BY player_id
    ) stats ON u.Id = stats.player_id
    LEFT JOIN (
        SELECT 
            player_id,
            COUNT(DISTINCT CompetitionId) AS CompetitionsJoined,
            0 AS CompetitionsWon -- TODO: Calculate based on competition results
        FROM (
            SELECT Player1Id AS player_id, CompetitionId FROM CompetitionTeams WHERE Player1Id IS NOT NULL
            UNION
            SELECT Player2Id AS player_id, CompetitionId FROM CompetitionTeams WHERE Player2Id IS NOT NULL
        ) comp_players
        GROUP BY player_id
    ) comp_stats ON u.Id = comp_stats.player_id
    WHERE u.IsActive = 1
    ')
    
    PRINT 'UserStatistics view created successfully.';
END
ELSE
BEGIN
    PRINT 'UserStatistics view already exists.';
END
GO

-- =============================================
-- 10. COMPETITION LEADERBOARD VIEW
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='CompetitionLeaderboard')
BEGIN
    EXEC('
    CREATE VIEW CompetitionLeaderboard AS
    SELECT 
        ct.CompetitionId,
        c.Name AS CompetitionName,
        ct.Id AS TeamId,
        ct.TeamName,
        u1.Username AS Player1Name,
        u2.Username AS Player2Name,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        CASE 
            WHEN ct.GamesPlayed >= ct.MaxGames THEN ''Completed''
            WHEN ct.IsComplete = 1 THEN ''Active''
            ELSE ''Waiting for Partner''
        END AS Status,
        ROW_NUMBER() OVER (PARTITION BY ct.CompetitionId ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.GamesPlayed ASC) AS Rank
    FROM CompetitionTeams ct
    INNER JOIN Competitions c ON ct.CompetitionId = c.Id
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.IsActive = 1
    ')
    
    PRINT 'CompetitionLeaderboard view created successfully.';
END
ELSE
BEGIN
    PRINT 'CompetitionLeaderboard view already exists.';
END
GO

-- =============================================
-- 11. GAME HISTORY VIEW
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.views WHERE name='GameHistory')
BEGIN
    EXEC('
    CREATE VIEW GameHistory AS
    SELECT 
        g.Id AS GameId,
        g.LobbyCode,
        g.CompetitionId,
        c.Name AS CompetitionName,
        g.Team1Name,
        g.Team2Name,
        u1.Username AS Team1Player1Name,
        u2.Username AS Team1Player2Name,
        u3.Username AS Team2Player1Name,
        u4.Username AS Team2Player2Name,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        CASE 
            WHEN g.WinnerTeam = 1 THEN g.Team1Name
            WHEN g.WinnerTeam = 2 THEN g.Team2Name
            ELSE ''No Winner''
        END AS WinnerTeamName,
        ABS(g.Team1BallsWon - g.Team2BallsWon) AS BallDifference,
        CASE 
            WHEN ABS(g.Team1BallsWon - g.Team2BallsWon) >= 6 THEN 1
            ELSE 0
        END AS IsBonusWin,
        g.Status,
        g.StartedAt,
        g.CompletedAt,
        DATEDIFF(MINUTE, g.StartedAt, g.CompletedAt) AS GameDurationMinutes
    FROM Games g
    LEFT JOIN Competitions c ON g.CompetitionId = c.Id
    LEFT JOIN Users u1 ON g.Team1Player1Id = u1.Id
    LEFT JOIN Users u2 ON g.Team1Player2Id = u2.Id
    LEFT JOIN Users u3 ON g.Team2Player1Id = u3.Id
    LEFT JOIN Users u4 ON g.Team2Player2Id = u4.Id
    ')
    
    PRINT 'GameHistory view created successfully.';
END
ELSE
BEGIN
    PRINT 'GameHistory view already exists.';
END
GO

-- =============================================
-- 12. CREATE SAMPLE DATA (OPTIONAL)
-- =============================================
-- Uncomment the following section if you want to create sample data for testing

/*
-- Sample Users
INSERT INTO Users (Id, Username, Email, PasswordHash, IsVerified, IsActive)
VALUES 
    (NEWID(), 'TestPlayer1', '<EMAIL>', 'hashedpassword1', 1, 1),
    (NEWID(), 'TestPlayer2', '<EMAIL>', 'hashedpassword2', 1, 1),
    (NEWID(), 'TestPlayer3', '<EMAIL>', 'hashedpassword3', 1, 1),
    (NEWID(), 'TestPlayer4', '<EMAIL>', 'hashedpassword4', 1, 1);

-- Sample Competition
DECLARE @CompetitionId UNIQUEIDENTIFIER = NEWID();
INSERT INTO Competitions (Id, Name, Description, StartDate, EndDate, Status, MaxTeams, EntryFee, PrizeFirst, PrizeSecond, PrizeThird, TotalPrizePool, IsPublic, AllowSpectators, MaxGamesPerTeam)
VALUES (@CompetitionId, 'Test Championship 2025', 'A test competition for development', GETUTCDATE(), DATEADD(DAY, 30, GETUTCDATE()), 'active', 16, 0.00, 'Gold Trophy', 'Silver Trophy', 'Bronze Trophy', 0.00, 1, 1, 10);

PRINT 'Sample data created successfully.';
*/

PRINT '==============================================';
PRINT 'GoldRushThunee Database Tables Part 2 Complete!';
PRINT 'Tables created: GameHands, PlayedCards';
PRINT 'Views created: UserStatistics, CompetitionLeaderboard, GameHistory';
PRINT 'Next: Run 03_GoldRushThunee_StoredProcedures.sql for stored procedures';
PRINT '==============================================';
