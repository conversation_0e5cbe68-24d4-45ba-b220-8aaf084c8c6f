-- =============================================
-- GoldRushThunee Leaderboard & Reporting Procedures
-- =============================================
-- Server: **************
-- Database: GoldRushThunee
-- Description: Leaderboard and reporting stored procedures
-- =============================================

USE GoldRushThunee;
GO

-- =============================================
-- LEADERBOARD PROCEDURES
-- =============================================

-- Get Global Leaderboard
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetGlobalLeaderboard')
    DROP PROCEDURE SP_GetGlobalLeaderboard;
GO

CREATE PROCEDURE SP_GetGlobalLeaderboard
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        ROW_NUMBER() OVER (ORDER BY us.TotalScore DESC, us.WinRate DESC, us.GamesPlayed DESC) AS Rank,
        us.UserId AS PlayerId,
        us.Username AS PlayerName,
        us.TotalScore AS Score,
        us.GamesPlayed,
        us.GamesWon,
        us.GamesLost,
        ROUND(us.WinRate, 2) AS WinRate,
        us.HandsPlayed,
        us.CompetitionsJoined,
        us.CompetitionsWon
    FROM UserStatistics us
    WHERE us.GamesPlayed > 0
    ORDER BY us.TotalScore DESC, us.WinRate DESC, us.GamesPlayed DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- Get Competition Leaderboard
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionLeaderboard')
    DROP PROCEDURE SP_GetCompetitionLeaderboard;
GO

CREATE PROCEDURE SP_GetCompetitionLeaderboard
    @CompetitionId UNIQUEIDENTIFIER,
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        cl.Rank,
        cl.TeamId,
        cl.TeamName,
        cl.Player1Name,
        cl.Player2Name,
        cl.GamesPlayed,
        cl.Points,
        cl.BonusPoints,
        cl.TotalPoints,
        cl.MaxGames,
        cl.Status,
        CASE 
            WHEN cl.GamesPlayed = 0 THEN 0.0
            ELSE ROUND((CAST(cl.Points AS FLOAT) / CAST(cl.GamesPlayed AS FLOAT)) * 100, 2)
        END AS WinRate
    FROM CompetitionLeaderboard cl
    WHERE cl.CompetitionId = @CompetitionId
    ORDER BY cl.Rank
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- Get Player Competition Status
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetPlayerCompetitionStatus')
    DROP PROCEDURE SP_GetPlayerCompetitionStatus;
GO

CREATE PROCEDURE SP_GetPlayerCompetitionStatus
    @PlayerId UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ct.Id AS TeamId,
        ct.TeamName,
        ct.Player1Id,
        u1.Username AS Player1Name,
        ct.Player2Id,
        u2.Username AS Player2Name,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt,
        CASE 
            WHEN ct.GamesPlayed >= ct.MaxGames THEN 'Completed'
            WHEN ct.IsComplete = 1 THEN 'Active'
            ELSE 'Waiting for Partner'
        END AS Status,
        (ct.MaxGames - ct.GamesPlayed) AS RemainingGames
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId 
    AND (ct.Player1Id = @PlayerId OR ct.Player2Id = @PlayerId)
    AND ct.IsActive = 1;
END
GO

-- =============================================
-- GAME HISTORY PROCEDURES
-- =============================================

-- Get Player Game History
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetPlayerGameHistory')
    DROP PROCEDURE SP_GetPlayerGameHistory;
GO

CREATE PROCEDURE SP_GetPlayerGameHistory
    @PlayerId UNIQUEIDENTIFIER,
    @PageSize INT = 20,
    @PageNumber INT = 1,
    @CompetitionId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        gh.GameId,
        gh.LobbyCode,
        gh.CompetitionId,
        gh.CompetitionName,
        gh.Team1Name,
        gh.Team2Name,
        gh.Team1Player1Name,
        gh.Team1Player2Name,
        gh.Team2Player1Name,
        gh.Team2Player2Name,
        gh.Team1Score,
        gh.Team2Score,
        gh.Team1BallsWon,
        gh.Team2BallsWon,
        gh.WinnerTeam,
        gh.WinnerTeamName,
        gh.BallDifference,
        gh.IsBonusWin,
        CASE 
            WHEN (@PlayerId IN (SELECT Team1Player1Id FROM Games WHERE Id = gh.GameId) 
                  OR @PlayerId IN (SELECT Team1Player2Id FROM Games WHERE Id = gh.GameId))
                 AND gh.WinnerTeam = 1 THEN 'Won'
            WHEN (@PlayerId IN (SELECT Team2Player1Id FROM Games WHERE Id = gh.GameId) 
                  OR @PlayerId IN (SELECT Team2Player2Id FROM Games WHERE Id = gh.GameId))
                 AND gh.WinnerTeam = 2 THEN 'Won'
            ELSE 'Lost'
        END AS PlayerResult,
        gh.Status,
        gh.StartedAt,
        gh.CompletedAt,
        gh.GameDurationMinutes
    FROM GameHistory gh
    WHERE (gh.Team1Player1Name = (SELECT Username FROM Users WHERE Id = @PlayerId)
           OR gh.Team1Player2Name = (SELECT Username FROM Users WHERE Id = @PlayerId)
           OR gh.Team2Player1Name = (SELECT Username FROM Users WHERE Id = @PlayerId)
           OR gh.Team2Player2Name = (SELECT Username FROM Users WHERE Id = @PlayerId))
    AND gh.Status = 'completed'
    AND (@CompetitionId IS NULL OR gh.CompetitionId = @CompetitionId)
    ORDER BY gh.CompletedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- =============================================
-- COMPETITION STATISTICS PROCEDURES
-- =============================================

-- Get Competition Statistics
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionStatistics')
    DROP PROCEDURE SP_GetCompetitionStatistics;
GO

CREATE PROCEDURE SP_GetCompetitionStatistics
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        c.Id,
        c.Name,
        c.Description,
        c.StartDate,
        c.EndDate,
        c.Status,
        c.MaxTeams,
        c.CurrentTeams,
        c.EntryFee,
        c.TotalPrizePool,
        c.MaxGamesPerTeam,
        
        -- Team Statistics
        (SELECT COUNT(*) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND IsActive = 1) AS TotalTeams,
        (SELECT COUNT(*) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND IsComplete = 1) AS CompleteTeams,
        (SELECT COUNT(*) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND IsComplete = 0) AS IncompleteTeams,
        
        -- Game Statistics
        (SELECT COUNT(*) FROM Games WHERE CompetitionId = @CompetitionId) AS TotalGames,
        (SELECT COUNT(*) FROM Games WHERE CompetitionId = @CompetitionId AND Status = 'completed') AS CompletedGames,
        (SELECT COUNT(*) FROM Games WHERE CompetitionId = @CompetitionId AND Status = 'in_progress') AS ActiveGames,
        
        -- Score Statistics
        (SELECT AVG(CAST(Points + BonusPoints AS FLOAT)) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND IsComplete = 1) AS AverageTeamScore,
        (SELECT MAX(Points + BonusPoints) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId) AS HighestTeamScore,
        (SELECT AVG(CAST(GamesPlayed AS FLOAT)) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND IsComplete = 1) AS AverageGamesPlayed
        
    FROM Competitions c
    WHERE c.Id = @CompetitionId;
END
GO

-- =============================================
-- HAND AND CARD TRACKING PROCEDURES
-- =============================================

-- Record Hand Result
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordHandResult')
    DROP PROCEDURE SP_RecordHandResult;
GO

CREATE PROCEDURE SP_RecordHandResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT,
    @TrumpSuit NVARCHAR(10) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @HandId UNIQUEIDENTIFIER = NEWID();
        
        INSERT INTO GameHands (Id, GameId, BallNumber, HandNumber, WinnerPlayerId, Points, TrumpSuit, CompletedAt)
        VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, @TrumpSuit, GETUTCDATE());
        
        SELECT @HandId AS HandId, 'Hand recorded successfully' AS Message;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Record Ball Result
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordBallResult')
    DROP PROCEDURE SP_RecordBallResult;
GO

CREATE PROCEDURE SP_RecordBallResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @Team1Score INT,
    @Team2Score INT,
    @WinnerTeam INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO GameBalls (Id, GameId, BallNumber, Team1Score, Team2Score, WinnerTeam, CompletedAt)
        VALUES (NEWID(), @GameId, @BallNumber, @Team1Score, @Team2Score, @WinnerTeam, GETUTCDATE());
        
        SELECT 'Ball result recorded successfully' AS Message;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- =============================================
-- UTILITY PROCEDURES
-- =============================================

-- Generate Unique Invite Code
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GenerateUniqueInviteCode')
    DROP PROCEDURE SP_GenerateUniqueInviteCode;
GO

CREATE PROCEDURE SP_GenerateUniqueInviteCode
    @CodeLength INT = 8
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Code NVARCHAR(10);
    DECLARE @Attempts INT = 0;
    DECLARE @MaxAttempts INT = 100;
    
    WHILE @Attempts < @MaxAttempts
    BEGIN
        -- Generate random alphanumeric code
        SET @Code = '';
        DECLARE @i INT = 0;
        WHILE @i < @CodeLength
        BEGIN
            DECLARE @CharType INT = ABS(CHECKSUM(NEWID())) % 3; -- 0=number, 1=uppercase, 2=lowercase
            DECLARE @Char CHAR(1);
            
            IF @CharType = 0
                SET @Char = CHAR(48 + ABS(CHECKSUM(NEWID())) % 10); -- 0-9
            ELSE IF @CharType = 1
                SET @Char = CHAR(65 + ABS(CHECKSUM(NEWID())) % 26); -- A-Z
            ELSE
                SET @Char = CHAR(97 + ABS(CHECKSUM(NEWID())) % 26); -- a-z
            
            SET @Code = @Code + @Char;
            SET @i = @i + 1;
        END
        
        -- Check if code is unique
        IF NOT EXISTS (SELECT 1 FROM CompetitionTeams WHERE InviteCode = @Code)
           AND NOT EXISTS (SELECT 1 FROM CompetitionTeamInvites WHERE InviteCode = @Code)
        BEGIN
            SELECT @Code AS InviteCode;
            RETURN;
        END
        
        SET @Attempts = @Attempts + 1;
    END
    
    -- If we couldn't generate a unique code, throw an error
    RAISERROR('Unable to generate unique invite code after maximum attempts', 16, 1);
END
GO

PRINT '==============================================';
PRINT 'GoldRushThunee Leaderboard & Reporting Procedures Complete!';
PRINT 'Procedures created: Leaderboards, Game History, Competition Statistics, Utility Functions';
PRINT 'Database setup is now complete!';
PRINT '==============================================';
