-- =============================================
-- GoldRushThunee Database Creation Script - FIXED VERSION
-- =============================================
-- Server: **************
-- Database: GoldRushThunee
-- User: EG-Dev
-- Description: Complete database setup for Thunee multiplayer card game
-- FIXED: Resolved foreign key cascade path conflicts
-- =============================================

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'GoldRushThunee')
BEGIN
    CREATE DATABASE GoldRushThunee
    COLLATE SQL_Latin1_General_CP1_CI_AS;
    PRINT 'Database GoldRushThunee created successfully.';
END
ELSE
BEGIN
    PRINT 'Database GoldRushThunee already exists.';
END
GO

USE GoldRushThunee;
GO

-- Drop tables if they exist (in reverse dependency order)
IF EXISTS (SELECT * FROM sysobjects WHERE name='CompetitionTeamInvites' AND xtype='U')
    DROP TABLE CompetitionTeamInvites;

IF EXISTS (SELECT * FROM sysobjects WHERE name='GameBalls' AND xtype='U')
    DROP TABLE GameBalls;

IF EXISTS (SELECT * FROM sysobjects WHERE name='PlayedCards' AND xtype='U')
    DROP TABLE PlayedCards;

IF EXISTS (SELECT * FROM sysobjects WHERE name='GameHands' AND xtype='U')
    DROP TABLE GameHands;

IF EXISTS (SELECT * FROM sysobjects WHERE name='Games' AND xtype='U')
    DROP TABLE Games;

IF EXISTS (SELECT * FROM sysobjects WHERE name='CompetitionTeams' AND xtype='U')
    DROP TABLE CompetitionTeams;

IF EXISTS (SELECT * FROM sysobjects WHERE name='Competitions' AND xtype='U')
    DROP TABLE Competitions;

IF EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
    DROP TABLE Users;

PRINT 'Existing tables dropped (if any existed).';
GO

-- =============================================
-- 1. USERS TABLE
-- =============================================
CREATE TABLE Users (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Email NVARCHAR(255) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    IsVerified BIT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    IsAdmin BIT NOT NULL DEFAULT 0,
    LastLoginAt DATETIME2 NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Create indexes for Users table
CREATE INDEX IX_Users_Username ON Users(Username);
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_IsActive ON Users(IsActive);
CREATE INDEX IX_Users_CreatedAt ON Users(CreatedAt);

PRINT 'Users table created successfully.';
GO

-- =============================================
-- 2. COMPETITIONS TABLE
-- =============================================
CREATE TABLE Competitions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500) NULL,
    StartDate DATETIME2 NOT NULL,
    EndDate DATETIME2 NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'upcoming' CHECK (Status IN ('upcoming', 'active', 'completed', 'cancelled')),
    MaxTeams INT NOT NULL DEFAULT 32 CHECK (MaxTeams >= 2 AND MaxTeams <= 64),
    CurrentTeams INT NOT NULL DEFAULT 0,
    EntryFee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    PrizeFirst NVARCHAR(100) NULL,
    PrizeSecond NVARCHAR(100) NULL,
    PrizeThird NVARCHAR(100) NULL,
    TotalPrizePool DECIMAL(10,2) NULL,
    Rules NVARCHAR(1000) NULL,
    IsPublic BIT NOT NULL DEFAULT 1,
    AllowSpectators BIT NOT NULL DEFAULT 1,
    MaxGamesPerTeam INT NOT NULL DEFAULT 10, -- Configurable game limit per team
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);

-- Create indexes for Competitions table
CREATE INDEX IX_Competitions_Status ON Competitions(Status);
CREATE INDEX IX_Competitions_StartDate ON Competitions(StartDate);
CREATE INDEX IX_Competitions_EndDate ON Competitions(EndDate);
CREATE INDEX IX_Competitions_IsPublic ON Competitions(IsPublic);

PRINT 'Competitions table created successfully.';
GO

-- =============================================
-- 3. COMPETITION TEAMS TABLE
-- =============================================
CREATE TABLE CompetitionTeams (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompetitionId UNIQUEIDENTIFIER NOT NULL,
    TeamName NVARCHAR(50) NOT NULL,
    Player1Id UNIQUEIDENTIFIER NOT NULL,
    Player2Id UNIQUEIDENTIFIER NULL, -- Nullable until partner joins
    InviteCode NVARCHAR(10) NOT NULL UNIQUE, -- Code for partner invitation
    GamesPlayed INT NOT NULL DEFAULT 0,
    Points INT NOT NULL DEFAULT 0, -- Total points earned
    BonusPoints INT NOT NULL DEFAULT 0, -- Bonus points for 6+ ball difference wins
    MaxGames INT NOT NULL DEFAULT 10, -- Maximum games allowed (configurable)
    IsActive BIT NOT NULL DEFAULT 1,
    IsComplete BIT NOT NULL DEFAULT 0, -- Whether partner has joined
    RegisteredAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CompletedAt DATETIME2 NULL, -- When team was completed
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE CASCADE,
    FOREIGN KEY (Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION
);

-- Create indexes for CompetitionTeams table
CREATE INDEX IX_CompetitionTeams_CompetitionId ON CompetitionTeams(CompetitionId);
CREATE INDEX IX_CompetitionTeams_Player1Id ON CompetitionTeams(Player1Id);
CREATE INDEX IX_CompetitionTeams_Player2Id ON CompetitionTeams(Player2Id);
CREATE INDEX IX_CompetitionTeams_InviteCode ON CompetitionTeams(InviteCode);
CREATE INDEX IX_CompetitionTeams_IsActive ON CompetitionTeams(IsActive);
CREATE INDEX IX_CompetitionTeams_IsComplete ON CompetitionTeams(IsComplete);

PRINT 'CompetitionTeams table created successfully.';
GO

-- =============================================
-- 4. COMPETITION TEAM INVITES TABLE (FIXED)
-- =============================================
CREATE TABLE CompetitionTeamInvites (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompetitionId UNIQUEIDENTIFIER NOT NULL,
    TeamId UNIQUEIDENTIFIER NOT NULL,
    InviterId UNIQUEIDENTIFIER NOT NULL,
    InviteeId UNIQUEIDENTIFIER NULL, -- Nullable until someone accepts
    InviteCode NVARCHAR(10) NOT NULL UNIQUE,
    Status NVARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (Status IN ('pending', 'accepted', 'expired', 'cancelled')),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ExpiresAt DATETIME2 NOT NULL DEFAULT DATEADD(DAY, 7, GETUTCDATE()), -- 7 days expiration
    AcceptedAt DATETIME2 NULL,
    -- FIXED: Changed CompetitionId to NO ACTION to avoid cascade conflicts
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE NO ACTION,
    FOREIGN KEY (TeamId) REFERENCES CompetitionTeams(Id) ON DELETE CASCADE,
    FOREIGN KEY (InviterId) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (InviteeId) REFERENCES Users(Id) ON DELETE NO ACTION
);

-- Create indexes for CompetitionTeamInvites table
CREATE INDEX IX_CompetitionTeamInvites_CompetitionId ON CompetitionTeamInvites(CompetitionId);
CREATE INDEX IX_CompetitionTeamInvites_TeamId ON CompetitionTeamInvites(TeamId);
CREATE INDEX IX_CompetitionTeamInvites_InviteCode ON CompetitionTeamInvites(InviteCode);
CREATE INDEX IX_CompetitionTeamInvites_Status ON CompetitionTeamInvites(Status);
CREATE INDEX IX_CompetitionTeamInvites_ExpiresAt ON CompetitionTeamInvites(ExpiresAt);

PRINT 'CompetitionTeamInvites table created successfully.';
GO

-- =============================================
-- 5. GAMES TABLE
-- =============================================
CREATE TABLE Games (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LobbyCode NVARCHAR(6) NOT NULL UNIQUE,
    CompetitionId UNIQUEIDENTIFIER NULL,
    Team1Player1Id UNIQUEIDENTIFIER NOT NULL,
    Team1Player2Id UNIQUEIDENTIFIER NOT NULL,
    Team2Player1Id UNIQUEIDENTIFIER NOT NULL,
    Team2Player2Id UNIQUEIDENTIFIER NOT NULL,
    Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1',
    Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2',
    Status NVARCHAR(20) NOT NULL DEFAULT 'waiting' CHECK (Status IN ('waiting', 'in_progress', 'completed', 'abandoned')),
    DealerId UNIQUEIDENTIFIER NULL,
    TrumpSuit NVARCHAR(10) NULL CHECK (TrumpSuit IN ('hearts', 'diamonds', 'clubs', 'spades')),
    CurrentBall INT NOT NULL DEFAULT 1 CHECK (CurrentBall >= 1 AND CurrentBall <= 6),
    CurrentHand INT NOT NULL DEFAULT 1 CHECK (CurrentHand >= 1 AND CurrentHand <= 6),
    Team1Score INT NOT NULL DEFAULT 0,
    Team2Score INT NOT NULL DEFAULT 0,
    Team1BallsWon INT NOT NULL DEFAULT 0,
    Team2BallsWon INT NOT NULL DEFAULT 0,
    WinnerTeam INT NULL CHECK (WinnerTeam IN (1, 2)),
    StartedAt DATETIME2 NULL,
    CompletedAt DATETIME2 NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE SET NULL,
    FOREIGN KEY (Team1Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (Team1Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (Team2Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (Team2Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION,
    FOREIGN KEY (DealerId) REFERENCES Users(Id) ON DELETE NO ACTION
);

-- Create indexes for Games table
CREATE INDEX IX_Games_LobbyCode ON Games(LobbyCode);
CREATE INDEX IX_Games_CompetitionId ON Games(CompetitionId);
CREATE INDEX IX_Games_Status ON Games(Status);
CREATE INDEX IX_Games_StartedAt ON Games(StartedAt);
CREATE INDEX IX_Games_CompletedAt ON Games(CompletedAt);
CREATE INDEX IX_Games_Team1Player1Id ON Games(Team1Player1Id);
CREATE INDEX IX_Games_Team1Player2Id ON Games(Team1Player2Id);
CREATE INDEX IX_Games_Team2Player1Id ON Games(Team2Player1Id);
CREATE INDEX IX_Games_Team2Player2Id ON Games(Team2Player2Id);

PRINT 'Games table created successfully.';
GO

-- =============================================
-- 6. GAME BALLS TABLE
-- =============================================
CREATE TABLE GameBalls (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    GameId UNIQUEIDENTIFIER NOT NULL,
    BallNumber INT NOT NULL CHECK (BallNumber >= 1 AND BallNumber <= 6),
    Team1Score INT NOT NULL DEFAULT 0,
    Team2Score INT NOT NULL DEFAULT 0,
    WinnerTeam INT NOT NULL CHECK (WinnerTeam IN (1, 2)),
    CompletedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (GameId) REFERENCES Games(Id) ON DELETE CASCADE,
    UNIQUE(GameId, BallNumber)
);

-- Create indexes for GameBalls table
CREATE INDEX IX_GameBalls_GameId ON GameBalls(GameId);
CREATE INDEX IX_GameBalls_BallNumber ON GameBalls(BallNumber);
CREATE INDEX IX_GameBalls_WinnerTeam ON GameBalls(WinnerTeam);

PRINT 'GameBalls table created successfully.';
GO

PRINT '==============================================';
PRINT 'GoldRushThunee Database Setup Complete!';
PRINT 'Tables created: Users, Competitions, CompetitionTeams, CompetitionTeamInvites, Games, GameBalls';
PRINT 'FIXED: Foreign key cascade conflicts resolved';
PRINT 'Next: Run 02_GoldRushThunee_Tables_Part2.sql for remaining tables';
PRINT '==============================================';
