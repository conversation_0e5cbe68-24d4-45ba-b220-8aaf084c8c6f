-- =============================================
-- GoldRushThunee Stored Procedures
-- =============================================
-- Server: **************
-- Database: GoldRushThunee
-- Description: Essential stored procedures for Thunee game operations
-- =============================================

USE GoldRushThunee;
GO

-- =============================================
-- USER MANAGEMENT PROCEDURES
-- =============================================

-- Create User
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CreateUser')
    DROP PROCEDURE SP_CreateUser;
GO

CREATE PROCEDURE SP_CreateUser
    @Id UNIQUEIDENTIFIER,
    @Username NVARCHAR(50),
    @Email NVARCHAR(255),
    @PasswordHash NVARCHAR(255),
    @IsVerified BIT = 0,
    @IsActive BIT = 1,
    @IsAdmin BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO Users (Id, Username, Email, PasswordHash, IsVerified, IsActive, IsAdmin, CreatedAt, UpdatedAt)
        VALUES (@Id, @Username, @Email, @PasswordHash, @IsVerified, @IsActive, @IsAdmin, GETUTCDATE(), GETUTCDATE());
        
        SELECT * FROM Users WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Get User by Username
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetUserByUsername')
    DROP PROCEDURE SP_GetUserByUsername;
GO

CREATE PROCEDURE SP_GetUserByUsername
    @Username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Users 
    WHERE Username = @Username AND IsActive = 1;
END
GO

-- Get User by ID
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetUserById')
    DROP PROCEDURE SP_GetUserById;
GO

CREATE PROCEDURE SP_GetUserById
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Users 
    WHERE Id = @Id AND IsActive = 1;
END
GO

-- Update User Last Login
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_UpdateUserLastLogin')
    DROP PROCEDURE SP_UpdateUserLastLogin;
GO

CREATE PROCEDURE SP_UpdateUserLastLogin
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE Users 
    SET LastLoginAt = GETUTCDATE(), UpdatedAt = GETUTCDATE()
    WHERE Id = @Id;
END
GO

-- =============================================
-- COMPETITION MANAGEMENT PROCEDURES
-- =============================================

-- Get All Competitions
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitions')
    DROP PROCEDURE SP_GetCompetitions;
GO

CREATE PROCEDURE SP_GetCompetitions
AS
BEGIN
    SET NOCOUNT ON;

    SELECT * FROM Competitions
    ORDER BY CreatedAt DESC;
END
GO

-- Create Competition
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CreateCompetition')
    DROP PROCEDURE SP_CreateCompetition;
GO

CREATE PROCEDURE SP_CreateCompetition
    @Id UNIQUEIDENTIFIER,
    @Name NVARCHAR(100),
    @Description NVARCHAR(500) = NULL,
    @StartDate DATETIME2,
    @EndDate DATETIME2,
    @MaxTeams INT = 32,
    @EntryFee DECIMAL(10,2) = 0,
    @PrizeFirst NVARCHAR(100) = NULL,
    @PrizeSecond NVARCHAR(100) = NULL,
    @PrizeThird NVARCHAR(100) = NULL,
    @TotalPrizePool DECIMAL(10,2) = NULL,
    @IsPublic BIT = 1,
    @AllowSpectators BIT = 1,
    @MaxGamesPerTeam INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO Competitions (Id, Name, Description, StartDate, EndDate, MaxTeams, EntryFee, 
                                 PrizeFirst, PrizeSecond, PrizeThird, TotalPrizePool, IsPublic, 
                                 AllowSpectators, MaxGamesPerTeam, Status, CurrentTeams, CreatedAt, UpdatedAt)
        VALUES (@Id, @Name, @Description, @StartDate, @EndDate, @MaxTeams, @EntryFee,
                @PrizeFirst, @PrizeSecond, @PrizeThird, @TotalPrizePool, @IsPublic,
                @AllowSpectators, @MaxGamesPerTeam, 'upcoming', 0, GETUTCDATE(), GETUTCDATE());
        
        SELECT * FROM Competitions WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Create Competition Team
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CreateCompetitionTeam')
    DROP PROCEDURE SP_CreateCompetitionTeam;
GO

CREATE PROCEDURE SP_CreateCompetitionTeam
    @Id UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER,
    @TeamName NVARCHAR(50),
    @Player1Id UNIQUEIDENTIFIER,
    @InviteCode NVARCHAR(10),
    @MaxGames INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if competition exists and is active
        IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @CompetitionId AND Status IN ('upcoming', 'active'))
        BEGIN
            RAISERROR('Competition not found or not accepting teams', 16, 1);
            RETURN;
        END
        
        -- Check if player is already in this competition
        IF EXISTS (SELECT 1 FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND (Player1Id = @Player1Id OR Player2Id = @Player1Id))
        BEGIN
            RAISERROR('Player is already registered in this competition', 16, 1);
            RETURN;
        END
        
        INSERT INTO CompetitionTeams (Id, CompetitionId, TeamName, Player1Id, InviteCode, MaxGames, IsActive, IsComplete, RegisteredAt)
        VALUES (@Id, @CompetitionId, @TeamName, @Player1Id, @InviteCode, @MaxGames, 1, 0, GETUTCDATE());
        
        -- Update competition team count
        UPDATE Competitions 
        SET CurrentTeams = CurrentTeams + 1, UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;
        
        SELECT * FROM CompetitionTeams WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Join Competition Team
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_JoinCompetitionTeam')
    DROP PROCEDURE SP_JoinCompetitionTeam;
GO

CREATE PROCEDURE SP_JoinCompetitionTeam
    @InviteCode NVARCHAR(10),
    @Player2Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @TeamId UNIQUEIDENTIFIER;
        DECLARE @CompetitionId UNIQUEIDENTIFIER;
        
        -- Find the team by invite code
        SELECT @TeamId = Id, @CompetitionId = CompetitionId
        FROM CompetitionTeams 
        WHERE InviteCode = @InviteCode AND IsActive = 1 AND IsComplete = 0 AND Player2Id IS NULL;
        
        IF @TeamId IS NULL
        BEGIN
            RAISERROR('Invalid invite code or team is already complete', 16, 1);
            RETURN;
        END
        
        -- Check if player is already in this competition
        IF EXISTS (SELECT 1 FROM CompetitionTeams WHERE CompetitionId = @CompetitionId AND (Player1Id = @Player2Id OR Player2Id = @Player2Id))
        BEGIN
            RAISERROR('Player is already registered in this competition', 16, 1);
            RETURN;
        END
        
        -- Update team with second player
        UPDATE CompetitionTeams 
        SET Player2Id = @Player2Id, IsComplete = 1, CompletedAt = GETUTCDATE()
        WHERE Id = @TeamId;
        
        SELECT * FROM CompetitionTeams WHERE Id = @TeamId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- =============================================
-- GAME MANAGEMENT PROCEDURES
-- =============================================

-- Create Game
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CreateGame')
    DROP PROCEDURE SP_CreateGame;
GO

CREATE PROCEDURE SP_CreateGame
    @Id UNIQUEIDENTIFIER,
    @LobbyCode NVARCHAR(6),
    @Team1Name NVARCHAR(50),
    @Team2Name NVARCHAR(50),
    @Team1Player1Id UNIQUEIDENTIFIER,
    @Team1Player2Id UNIQUEIDENTIFIER,
    @Team2Player1Id UNIQUEIDENTIFIER,
    @Team2Player2Id UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO Games (Id, LobbyCode, Team1Name, Team2Name, Team1Player1Id, Team1Player2Id, 
                          Team2Player1Id, Team2Player2Id, CompetitionId, Status, CreatedAt, UpdatedAt)
        VALUES (@Id, @LobbyCode, @Team1Name, @Team2Name, @Team1Player1Id, @Team1Player2Id,
                @Team2Player1Id, @Team2Player2Id, @CompetitionId, 'waiting', GETUTCDATE(), GETUTCDATE());
        
        SELECT * FROM Games WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Complete Game and Update Competition Scores
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CompleteGame')
    DROP PROCEDURE SP_CompleteGame;
GO

CREATE PROCEDURE SP_CompleteGame
    @GameId UNIQUEIDENTIFIER,
    @WinnerTeam INT,
    @Team1Score INT,
    @Team2Score INT,
    @Team1BallsWon INT,
    @Team2BallsWon INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @CompetitionId UNIQUEIDENTIFIER;
        DECLARE @BallDifference INT;
        DECLARE @IsBonusWin BIT = 0;
        
        -- Update game status
        UPDATE Games 
        SET Status = 'completed',
            WinnerTeam = @WinnerTeam,
            Team1Score = @Team1Score,
            Team2Score = @Team2Score,
            Team1BallsWon = @Team1BallsWon,
            Team2BallsWon = @Team2BallsWon,
            CompletedAt = GETUTCDATE(),
            UpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
        
        -- Get competition ID if this is a competition game
        SELECT @CompetitionId = CompetitionId FROM Games WHERE Id = @GameId;
        
        IF @CompetitionId IS NOT NULL
        BEGIN
            -- Calculate ball difference for bonus points
            SET @BallDifference = ABS(@Team1BallsWon - @Team2BallsWon);
            IF @BallDifference >= 6
                SET @IsBonusWin = 1;
            
            -- Update winning team's score
            IF @WinnerTeam = 1
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + 1,
                    BonusPoints = BonusPoints + CASE WHEN @IsBonusWin = 1 THEN 1 ELSE 0 END
                WHERE CompetitionId = @CompetitionId 
                AND (Player1Id IN (SELECT Team1Player1Id FROM Games WHERE Id = @GameId)
                     OR Player1Id IN (SELECT Team1Player2Id FROM Games WHERE Id = @GameId)
                     OR Player2Id IN (SELECT Team1Player1Id FROM Games WHERE Id = @GameId)
                     OR Player2Id IN (SELECT Team1Player2Id FROM Games WHERE Id = @GameId));
            END
            ELSE IF @WinnerTeam = 2
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + 1,
                    BonusPoints = BonusPoints + CASE WHEN @IsBonusWin = 1 THEN 1 ELSE 0 END
                WHERE CompetitionId = @CompetitionId 
                AND (Player1Id IN (SELECT Team2Player1Id FROM Games WHERE Id = @GameId)
                     OR Player1Id IN (SELECT Team2Player2Id FROM Games WHERE Id = @GameId)
                     OR Player2Id IN (SELECT Team2Player1Id FROM Games WHERE Id = @GameId)
                     OR Player2Id IN (SELECT Team2Player2Id FROM Games WHERE Id = @GameId));
            END
            
            -- Update losing team's games played count
            UPDATE CompetitionTeams 
            SET GamesPlayed = GamesPlayed + 1
            WHERE CompetitionId = @CompetitionId 
            AND Id NOT IN (
                SELECT ct.Id FROM CompetitionTeams ct
                WHERE ct.CompetitionId = @CompetitionId 
                AND (ct.Player1Id IN (
                    CASE WHEN @WinnerTeam = 1 
                    THEN (SELECT Team1Player1Id FROM Games WHERE Id = @GameId)
                    ELSE (SELECT Team2Player1Id FROM Games WHERE Id = @GameId) END,
                    CASE WHEN @WinnerTeam = 1 
                    THEN (SELECT Team1Player2Id FROM Games WHERE Id = @GameId)
                    ELSE (SELECT Team2Player2Id FROM Games WHERE Id = @GameId) END
                ))
            );
        END
        
        SELECT 'Game completed successfully' AS Message;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

PRINT '==============================================';
PRINT 'GoldRushThunee Stored Procedures Complete!';
PRINT 'Procedures created: User Management, Competition Management, Game Management';
PRINT 'Next: Run 04_GoldRushThunee_Leaderboard_Procedures.sql for leaderboard procedures';
PRINT '==============================================';
