# Database Changes Proposal for Competition Phases

## Overview

This document outlines recommended changes and enhancements to the current database schema to fully support the competition flow as described, including leaderboard phases, elimination rounds, and admin-created lobbies for knockout stages.

---

## 1. Competition Phases & Progression

### **Current State**
- The `Competitions` table does not explicitly track phases (e.g., Leaderboard, Top 32, Top 16, etc.).
- There is no direct way to associate teams with their phase or elimination status.

### **Proposed Changes**
- **Add a `Phase` column to `Competitions`**  
  - Type: `nvarchar(40)`  
  - Description: Tracks the current phase (e.g., "Leaderboard", "Top32", "Top16", "Top8", "Top4", "Final").
- **Add a `Phase` column to `CompetitionTeams`**  
  - Type: `nvarchar(40)`  
  - Description: Indicates which phase the team is currently in or was last in.
- **Add an `IsEliminated` column to `CompetitionTeams`**  
  - Type: `bit`  
  - Description: Indicates if the team has been eliminated from the competition.

---

## 2. Phase Deadlines & Game Limits

### **Current State**
- `Competitions` has `StartDate` and `EndDate`, but not per-phase deadlines or game limits.

### **Proposed Changes**
- **Add a `PhaseEndDate` column to `Competitions`**  
  - Type: `datetime2`  
  - Description: End date for the current phase.
- **Add a `MaxGamesPerPhase` column to `Competitions`**  
  - Type: `int`  
  - Description: Maximum games allowed per team in the current phase.

---

## 3. Knockout Rounds & Admin Lobbies

### **Current State**
- No explicit structure for admin-created lobbies or knockout matchups.

### **Proposed Changes**
- **Create a new table: `CompetitionPhaseLobbies`**
  - Columns:
    - `Id` (uniqueidentifier, PK)
    - `CompetitionId` (uniqueidentifier, FK)
    - `Phase` (nvarchar(40))
    - `LobbyCode` (nvarchar(12))
    - `CreatedByAdminId` (uniqueidentifier, FK to Users)
    - `CreatedAt` (datetime2)
- **Create a new table: `CompetitionPhaseLobbyTeams`**
  - Columns:
    - `Id` (uniqueidentifier, PK)
    - `LobbyId` (uniqueidentifier, FK to CompetitionPhaseLobbies)
    - `CompetitionTeamId` (uniqueidentifier, FK)
    - `IsWinner` (bit)
    - `EliminatedAt` (datetime2, nullable)

---

## 4. Tracking Advancement & Elimination

### **Current State**
- No explicit tracking of which teams advance or are eliminated per phase.

### **Proposed Changes**
- **Add an `AdvancedToNextPhase` column to `CompetitionTeams`**
  - Type: `bit`
  - Description: Indicates if the team advanced to the next phase.
- **Add a `PhaseEliminatedAt` column to `CompetitionTeams`**
  - Type: `datetime2`, nullable
  - Description: When the team was eliminated (if applicable).

---

## 5. Points & Bonus Points Calculation

### **Current State**
- `CompetitionTeams` has `Points` and `BonusPoints`, but no breakdown per phase.

### **Proposed Changes**
- **Create a new table: `CompetitionTeamPhaseStats`**
  - Columns:
    - `Id` (uniqueidentifier, PK)
    - `CompetitionTeamId` (uniqueidentifier, FK)
    - `Phase` (nvarchar(40))
    - `Points` (int)
    - `BonusPoints` (int)
    - `GamesPlayed` (int)
    - `BallsWon` (int)
    - `CreatedAt` (datetime2)
    - `UpdatedAt` (datetime2)

---

## 6. General Recommendations

- **Indexes:** Add indexes on new columns used for filtering (e.g., `Phase`, `IsEliminated`, `AdvancedToNextPhase`).
- **Foreign Keys:** Ensure all new relationships are enforced with foreign keys.
- **Data Migration:** Plan for migration scripts to populate new columns for existing competitions/teams.

---

## 7. Example: New/Altered Table Definitions

```sql
-- Example: Add Phase to Competitions
ALTER TABLE Competitions ADD Phase nvarchar(40) NOT NULL DEFAULT 'Leaderboard';
ALTER TABLE Competitions ADD PhaseEndDate datetime2 NULL;
ALTER TABLE Competitions ADD MaxGamesPerPhase int NULL;

-- Example: Add Phase and Elimination Tracking to CompetitionTeams
ALTER TABLE CompetitionTeams ADD Phase nvarchar(40) NOT NULL DEFAULT 'Leaderboard';
ALTER TABLE CompetitionTeams ADD IsEliminated bit NOT NULL DEFAULT 0;
ALTER TABLE CompetitionTeams ADD AdvancedToNextPhase bit NOT NULL DEFAULT 0;
ALTER TABLE CompetitionTeams ADD PhaseEliminatedAt datetime2 NULL;

-- Example: New Table for Admin Lobbies
CREATE TABLE CompetitionPhaseLobbies (
    Id uniqueidentifier PRIMARY KEY,
    CompetitionId uniqueidentifier NOT NULL,
    Phase nvarchar(40) NOT NULL,
    LobbyCode nvarchar(12) NOT NULL,
    CreatedByAdminId uniqueidentifier NOT NULL,
    CreatedAt datetime2 NOT NULL,
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
    FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id)
);

CREATE TABLE CompetitionPhaseLobbyTeams (
    Id uniqueidentifier PRIMARY KEY,
    LobbyId uniqueidentifier NOT NULL,
    CompetitionTeamId uniqueidentifier NOT NULL,
    IsWinner bit NOT NULL DEFAULT 0,
    EliminatedAt datetime2 NULL,
    FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id),
    FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);

-- Example: New Table for Phase Stats
CREATE TABLE CompetitionTeamPhaseStats (
    Id uniqueidentifier PRIMARY KEY,
    CompetitionTeamId uniqueidentifier NOT NULL,
    Phase nvarchar(40) NOT NULL,
    Points int NOT NULL DEFAULT 0,
    BonusPoints int NOT NULL DEFAULT 0,
    GamesPlayed int NOT NULL DEFAULT 0,
    BallsWon int NOT NULL DEFAULT 0,
    CreatedAt datetime2 NOT NULL,
    UpdatedAt datetime2 NOT NULL,
    FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);
```

---

## 8. Summary Table

| Change Area                | Table(s) Affected                | New/Altered Columns/Tables                |
|----------------------------|----------------------------------|-------------------------------------------|
| Phase Tracking             | Competitions, CompetitionTeams   | Phase, PhaseEndDate, MaxGamesPerPhase     |
| Elimination/Advancement    | CompetitionTeams                 | IsEliminated, AdvancedToNextPhase, PhaseEliminatedAt |
| Knockout Lobbies           | (new) CompetitionPhaseLobbies, CompetitionPhaseLobbyTeams | All columns as above |
| Per-Phase Stats            | (new) CompetitionTeamPhaseStats  | All columns as above                      |

---

## 9. Next Steps

- Review and refine the proposed schema changes with your development team.
- Update application logic to utilize new columns/tables for phase progression, elimination, and admin lobbies.
- Plan and test migration scripts for existing data.

--- 