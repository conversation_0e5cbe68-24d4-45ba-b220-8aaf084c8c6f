# PowerShell script to run database fixes
# Run this script to fix the database schema and add missing stored procedures

$serverName = "**************"
$databaseName = "BME"  # Note: Using BME as per user's connection string
$username = "EG-Dev"
$password = "Password01?"

# Connection string
$connectionString = "Server=$serverName; Database=$databaseName; User Id=$username; Password=$password; TrustServerCertificate=True;"

Write-Host "Connecting to database: $serverName\$databaseName" -ForegroundColor Green

try {
    # Load SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        Write-Host "Using SqlServer PowerShell module" -ForegroundColor Green
        
        # Run the schema fix script
        Write-Host "Running schema fix script..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ConnectionString $connectionString -InputFile "04_Fix_Games_Table_Schema.sql" -Verbose
        
        # Run the stored procedures script
        Write-Host "Running stored procedures script..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ConnectionString $connectionString -InputFile "05_Add_Missing_StoredProcedures.sql" -Verbose
        
        Write-Host "Database fixes completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "SqlServer PowerShell module not found. Please run the scripts manually in SSMS:" -ForegroundColor Red
        Write-Host "1. 04_Fix_Games_Table_Schema.sql" -ForegroundColor Yellow
        Write-Host "2. 05_Add_Missing_StoredProcedures.sql" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error running database fixes: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run the scripts manually in SSMS:" -ForegroundColor Yellow
    Write-Host "1. 04_Fix_Games_Table_Schema.sql" -ForegroundColor Yellow
    Write-Host "2. 05_Add_Missing_StoredProcedures.sql" -ForegroundColor Yellow
}

Write-Host "Press any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
