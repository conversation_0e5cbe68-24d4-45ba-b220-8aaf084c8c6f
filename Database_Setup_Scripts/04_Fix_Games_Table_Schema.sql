-- =============================================
-- Fix Games Table Schema to Allow NULL Player IDs
-- =============================================

USE GoldRushThunee;
GO

-- Check if the Games table exists and modify the constraints
IF EXISTS (SELECT * FROM sysobjects WHERE name='Games' AND xtype='U')
BEGIN
    -- Drop foreign key constraints first
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK__Games__Team1Play__Team1Player2Id')
        ALTER TABLE Games DROP CONSTRAINT FK__Games__Team1Play__Team1Player2Id;
    
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK__Games__Team2Play__Team2Player1Id')
        ALTER TABLE Games DROP CONSTRAINT FK__Games__Team2Play__Team2Player1Id;
    
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK__Games__Team2Play__Team2Player2Id')
        ALTER TABLE Games DROP CONSTRAINT FK__Games__Team2Play__Team2Player2Id;

    -- Alter columns to allow NULL
    ALTER TABLE Games ALTER COLUMN Team1Player2Id UNIQUEIDENTIFIER NULL;
    ALTER TABLE Games ALTER COLUMN Team2Player1Id UNIQUEIDENTIFIER NULL;
    ALTER TABLE Games ALTER COLUMN Team2Player2Id UNIQUEIDENTIFIER NULL;

    -- Re-add foreign key constraints
    ALTER TABLE Games ADD CONSTRAINT FK_Games_Team1Player2Id 
        FOREIGN KEY (Team1Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION;
    
    ALTER TABLE Games ADD CONSTRAINT FK_Games_Team2Player1Id 
        FOREIGN KEY (Team2Player1Id) REFERENCES Users(Id) ON DELETE NO ACTION;
    
    ALTER TABLE Games ADD CONSTRAINT FK_Games_Team2Player2Id 
        FOREIGN KEY (Team2Player2Id) REFERENCES Users(Id) ON DELETE NO ACTION;

    PRINT 'Games table schema updated successfully - Player ID columns now allow NULL';
END
ELSE
BEGIN
    PRINT 'Games table not found';
END
GO
