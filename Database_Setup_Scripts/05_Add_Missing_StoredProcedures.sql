-- =============================================
-- Add Missing Stored Procedures
-- =============================================

USE GoldRushThunee;
GO

-- =============================================
-- SP_JoinGame - Join a player to an existing game
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_JoinGame')
    DROP PROCEDURE SP_JoinGame;
GO

CREATE PROCEDURE SP_JoinGame
    @LobbyCode NVARCHAR(6),
    @PlayerId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @GameId UNIQUEIDENTIFIER;
        DECLARE @Team1Player1Id UNIQUEIDENTIFIER;
        DECLARE @Team1Player2Id UNIQUEIDENTIFIER;
        DECLARE @Team2Player1Id UNIQUEIDENTIFIER;
        DECLARE @Team2Player2Id UNIQUEIDENTIFIER;
        
        -- Get the game and current player assignments
        SELECT @GameId = Id, 
               @Team1Player1Id = Team1Player1Id,
               @Team1Player2Id = Team1Player2Id,
               @Team2Player1Id = Team2Player1Id,
               @Team2Player2Id = Team2Player2Id
        FROM Games 
        WHERE LobbyCode = @LobbyCode AND Status = 'waiting';
        
        IF @GameId IS NULL
        BEGIN
            RAISERROR('Game not found or not accepting players', 16, 1);
            RETURN;
        END
        
        -- Check if player is already in the game
        IF @PlayerId IN (@Team1Player1Id, @Team1Player2Id, @Team2Player1Id, @Team2Player2Id)
        BEGIN
            RAISERROR('Player is already in this game', 16, 1);
            RETURN;
        END
        
        -- Assign player to the first available slot
        IF @Team1Player2Id IS NULL
        BEGIN
            UPDATE Games SET Team1Player2Id = @PlayerId, UpdatedAt = GETUTCDATE()
            WHERE Id = @GameId;
        END
        ELSE IF @Team2Player1Id IS NULL
        BEGIN
            UPDATE Games SET Team2Player1Id = @PlayerId, UpdatedAt = GETUTCDATE()
            WHERE Id = @GameId;
        END
        ELSE IF @Team2Player2Id IS NULL
        BEGIN
            UPDATE Games SET Team2Player2Id = @PlayerId, UpdatedAt = GETUTCDATE()
            WHERE Id = @GameId;
        END
        ELSE
        BEGIN
            RAISERROR('Game is full', 16, 1);
            RETURN;
        END
        
        -- Return the updated game
        SELECT * FROM Games WHERE Id = @GameId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_GetGameByLobbyCode - Get game by lobby code
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetGameByLobbyCode')
    DROP PROCEDURE SP_GetGameByLobbyCode;
GO

CREATE PROCEDURE SP_GetGameByLobbyCode
    @LobbyCode NVARCHAR(6)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Games WHERE LobbyCode = @LobbyCode;
END
GO

PRINT 'Missing stored procedures added successfully';
