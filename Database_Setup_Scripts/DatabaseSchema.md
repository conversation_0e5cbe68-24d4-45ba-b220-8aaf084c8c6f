# Database Schema Documentation

## Tables and Columns

### Competitions
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| Name             | nvarchar         | 200        | No       |             |
| Description      | nvarchar         | 1000       | Yes      |             |
| StartDate        | datetime2        | 8          | No       |             |
| EndDate          | datetime2        | 8          | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| MaxTeams         | int              | 4          | No       |             |
| CurrentTeams     | int              | 4          | No       |             |
| EntryFee         | decimal          | 9          | No       |             |
| PrizeFirst       | nvarchar         | 200        | Yes      |             |
| PrizeSecond      | nvarchar         | 200        | Yes      |             |
| PrizeThird       | nvarchar         | 200        | Yes      |             |
| TotalPrizePool   | decimal          | 9          | Yes      |             |
| Rules            | nvarchar         | 2000       | Yes      |             |
| IsPublic         | bit              | 1          | No       |             |
| AllowSpectators  | bit              | 1          | No       |             |
| MaxGamesPerTeam  | int              | 4          | No       |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| UpdatedAt        | datetime2        | 8          | No       |             |

### CompetitionTeamInvites
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| CompetitionId    | uniqueidentifier | 16         | No       | FK to Competitions(Id) |
| TeamId           | uniqueidentifier | 16         | No       | FK to CompetitionTeams(Id) |
| InviterId        | uniqueidentifier | 16         | No       | FK to Users(Id) |
| InviteeId        | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| InviteCode       | nvarchar         | 20         | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| ExpiresAt        | datetime2        | 8          | No       |             |
| AcceptedAt       | datetime2        | 8          | Yes      |             |

### CompetitionTeams
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| CompetitionId    | uniqueidentifier | 16         | No       | FK to Competitions(Id) |
| TeamName         | nvarchar         | 100        | No       |             |
| Player1Id        | uniqueidentifier | 16         | No       | FK to Users(Id) |
| Player2Id        | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| InviteCode       | nvarchar         | 20         | No       |             |
| GamesPlayed      | int              | 4          | No       |             |
| Points           | int              | 4          | No       |             |
| BonusPoints      | int              | 4          | No       |             |
| MaxGames         | int              | 4          | No       |             |
| IsActive         | bit              | 1          | No       |             |
| IsComplete       | bit              | 1          | No       |             |
| RegisteredAt     | datetime2        | 8          | No       |             |
| CompletedAt      | datetime2        | 8          | Yes      |             |

### GameBalls
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| GameId           | uniqueidentifier | 16         | No       | FK to Games(Id) |
| BallNumber       | int              | 4          | No       |             |
| Team1Score       | int              | 4          | No       |             |
| Team2Score       | int              | 4          | No       |             |
| WinnerTeam       | int              | 4          | No       |             |
| CompletedAt      | datetime2        | 8          | No       |             |

### GameHands
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| GameId           | uniqueidentifier | 16         | No       | FK to Games(Id) |
| BallNumber       | int              | 4          | No       |             |
| HandNumber       | int              | 4          | No       |             |
| WinnerPlayerId   | uniqueidentifier | 16         | No       | FK to Users(Id) |
| Points           | int              | 4          | No       |             |
| TrumpSuit        | nvarchar         | 20         | Yes      |             |
| CompletedAt      | datetime2        | 8          | No       |             |

### Games
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| LobbyCode        | nvarchar         | 12         | No       |             |
| CompetitionId    | uniqueidentifier | 16         | Yes      | FK to Competitions(Id) |
| Team1Player1Id   | uniqueidentifier | 16         | No       | FK to Users(Id) |
| Team1Player2Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team2Player1Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team2Player2Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team1Name        | nvarchar         | 100        | No       |             |
| Team2Name        | nvarchar         | 100        | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| DealerId         | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| TrumpSuit        | nvarchar         | 20         | Yes      |             |
| CurrentBall      | int              | 4          | No       |             |
| CurrentHand      | int              | 4          | No       |             |
| Team1Score       | int              | 4          | No       |             |
| Team2Score       | int              | 4          | No       |             |
| Team1BallsWon    | int              | 4          | No       |             |
| Team2BallsWon    | int              | 4          | No       |             |
| WinnerTeam       | int              | 4          | Yes      |             |
| StartedAt        | datetime2        | 8          | Yes      |             |
| CompletedAt      | datetime2        | 8          | Yes      |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| UpdatedAt        | datetime2        | 8          | No       |             |

### GameSettings
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id                           | int              | 4          | No       | Primary Key |
| PlayTimeframeOptions         | nvarchar         | -1         | No       |             |
| DefaultPlayTimeframe         | int              | 4          | No       |             |
| TrumperThuneeCallingDuration | int              | 4          | No       |             |
| FirstRemainingThuneeCallingDuration | int        | 4          | No       |             |
| LastRemainingThuneeCallingDuration  | int        | 4          | No       |             |
| VotingTimeLimit              | int              | 4          | No       |             |
| TrumpDisplayDuration         | int              | 4          | No       |             |
| CardDealingSpeed             | int              | 4          | No       |             |
| TimerUpdateInterval          | int              | 4          | No       |             |
| CreatedAt                    | datetime2        | 8          | No       |             |
| UpdatedAt                    | datetime2        | 8          | No       |             |
| UpdatedBy                    | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| CardFaceBaseUrl              | nvarchar         | 1000       | No       |             |
| CardBackImageUrl             | nvarchar         | 1000       | No       |             |
| CustomCardFaceMappings       | nvarchar         | -1         | Yes      |             |
| TrumpVisibilityOptions       | nvarchar         | -1         | No       |             |
| DefaultTrumpVisibility       | nvarchar         | 100        | No       |             |
| TemporaryTrumpDisplayDuration| int              | 4          | No       |             |

### PlayedCards
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| GameHandId       | uniqueidentifier | 16         | No       | FK to GameHands(Id) |
| PlayerId         | uniqueidentifier | 16         | No       | FK to Users(Id) |
| CardSuit         | nvarchar         | 20         | No       |             |
| CardValue        | nvarchar         | 4          | No       |             |
| PlayOrder        | int              | 4          | No       |             |
| PlayedAt         | datetime2        | 8          | No       |             |

### Users
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| Username         | nvarchar         | 100        | No       |             |
| Email            | nvarchar         | 510        | No       |             |
| PasswordHash     | nvarchar         | 510        | No       |             |
| IsVerified       | bit              | 1          | No       |             |
| IsActive         | bit              | 1          | No       |             |
| IsAdmin          | bit              | 1          | No       |             |
| LastLoginAt      | datetime2        | 8          | Yes      |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| UpdatedAt        | datetime2        | 8          | No       |             |

## Foreign Key Relationships

| Foreign Key Name                  | Parent Table      | Parent Column | Referencing Table         | Referencing Column |
|-----------------------------------|-------------------|---------------|--------------------------|--------------------|
| FK__Competiti__Compe__619B8048    | Competitions      | Id            | CompetitionTeamInvites    | CompetitionId      |
| FK__Competiti__Compe__571DF1D5    | Competitions      | Id            | CompetitionTeams          | CompetitionId      |
| FK__Games__Competiti__787EE5A0    | Competitions      | Id            | Games                    | CompetitionId      |
| FK__Competiti__TeamI__628FA481    | CompetitionTeams  | Id            | CompetitionTeamInvites    | TeamId             |
| FK__PlayedCar__GameH__236943A5    | GameHands         | Id            | PlayedCards              | GameHandId         |
| FK__GameBalls__GameI__06CD04F7    | Games             | Id            | GameBalls                | GameId             |
| FK__GameHands__GameI__18EBB532    | Games             | Id            | GameHands                | GameId             |
| FK__Competiti__Invit__6383C8BA    | Users             | Id            | CompetitionTeamInvites    | InviterId          |
| FK__Competiti__Invit__6477ECF3    | Users             | Id            | CompetitionTeamInvites    | InviteeId          |
| FK__Competiti__Playe__5812160E    | Users             | Id            | CompetitionTeams          | Player1Id          |
| FK__Competiti__Playe__59063A47    | Users             | Id            | CompetitionTeams          | Player2Id          |
| FK__GameHands__Winne__19DFD96B    | Users             | Id            | GameHands                | WinnerPlayerId     |
| FK__Games__DealerId__7D439ABD     | Users             | Id            | Games                    | DealerId           |
| FK__Games__Team1Play__797309D9    | Users             | Id            | Games                    | Team1Player1Id     |
| FK__Games__Team1Play__7A672E12    | Users             | Id            | Games                    | Team1Player2Id     |
| FK__Games__Team2Play__7B5B524B    | Users             | Id            | Games                    | Team2Player1Id     |
| FK__Games__Team2Play__7C4F7684    | Users             | Id            | Games                    | Team2Player2Id     |
| FK_Games_Team1Player2Id           | Users             | Id            | Games                    | Team1Player2Id     |
| FK_Games_Team2Player1Id           | Users             | Id            | Games                    | Team2Player1Id     |
| FK_Games_Team2Player2Id           | Users             | Id            | Games                    | Team2Player2Id     |
| FK__PlayedCar__Playe__245D67DE    | Users             | Id            | PlayedCards              | PlayerId           |

## Competition Flow and Rules

### Leaderboard and Match Phase
- Teams compete within a competition, and matches are found based on teams registered in that competition.
- At the end of each game, game data is saved to the API and database to calculate and award points and bonus points.

#### Points System
- **3 points** are awarded for a win.
- **Bonus 1 point** is awarded if a team wins by 6 or more balls.

### Competition Time Frame
- Each competition has a deadline by which teams must complete their games.
- After the deadline, teams can no longer play additional games in that phase.
- All points and bonus points are used to generate the leaderboard.
- The **top 32 teams** on the leaderboard advance to the next phase; all other teams are eliminated from the competition.

### Top 32 Phase
- The leaderboard now consists only of the top 32 teams.
- Matchmaking is restricted to these teams.
- There is a maximum number of games (e.g., 10) that can be played in this phase.
- This phase also has an end date.
- Points and bonus points are again used to update the leaderboard.
- The **top 16 teams** advance to the next phase; all others are eliminated.

### Top 16 Phase (GoldRush Event)
- The top 16 teams are invited to the GoldRush event.
- This phase is a knockout round: teams are placed into lobbies created by the admin.
- The winning team in each match advances to the next round (**Top 8**).

### Top 8 Phase (GoldRush Event)
- The top 8 teams continue in knockout format.
- Teams are placed into admin-created lobbies.
- Winners advance to the **Top 4** round.

### Top 4 Phase (GoldRush Event)
- The top 4 teams continue in knockout format.
- Teams are placed into admin-created lobbies.
- Winners advance to the **Final Phase** (Top 2).

### Final Phase (GoldRush Event)
- The final 2 teams compete in a knockout match.
- The winning team is declared the competition winner. 