# WebRTC Video Calling Implementation

This document describes the new WebRTC-based video calling system that replaces the previous Node.js video server.

## 🚨 Current Status: Authentication Issue Fixed

**Issue Resolved**: The 401 Unauthorized error has been addressed by:
1. Configuring JWT authentication for SignalR in `Program.cs`
2. Temporarily removing `[Authorize]` attribute from the Hub for testing
3. Adding proper token handling for SignalR connections
4. Adding a test component for debugging connections

## 🧪 Testing the Implementation

A test component (`VideoCallTest.tsx`) has been added to help debug the SignalR connection. This component:
- Tests SignalR connection with JWT authentication
- Shows real-time connection logs
- Allows testing room join functionality
- Displays connection status and errors

**To test:**
1. Start the API: `dotnet run` in `ThuneeAPI/ThuneeAPI`
2. Start the frontend: `npm run dev`
3. Login to the application
4. Look for the "Video Call Connection Test" panel in the top-right corner
5. Click "Connect" to test the SignalR connection

## Architecture Overview

The new implementation uses:
- **ASP.NET Core SignalR** for signaling (replacing the Node.js video server)
- **WebRTC** for peer-to-peer audio/video communication
- **React frontend** with TypeScript for the user interface

## Backend Components

### 1. SignalR Hub (`VideoSignalingHub.cs`)
- Handles WebSocket connections for signaling
- Manages user rooms and connections
- Forwards WebRTC signaling messages between peers
- Requires JWT authentication

### 2. Models (`Models/VideoSignaling/`)
- `SignalMessage`: WebRTC signaling message structure
- `UserConnection`: User connection tracking
- `RoomUser`: Room participant information

### 3. Configuration
- Added SignalR service in `Program.cs`
- Updated CORS policy for SignalR connections
- Hub endpoint: `/videohub`

## Frontend Components

### 1. Video Signaling Service (`videoSignalingService.ts`)
- SignalR client for WebSocket communication
- Handles connection management and room operations
- Forwards signaling events to WebRTC service

### 2. WebRTC Service (`webRTCService.ts`)
- Manages WebRTC peer connections
- Handles media stream acquisition
- Processes offer/answer/ICE candidate exchange
- Provides media control (mute/unmute, camera on/off)

### 3. React Component (`WebRTCVideoCall.tsx`)
- User interface for video calling
- Video grid layout for multiple participants
- Control buttons for audio/video/call management
- Error handling and connection status

## Key Features

### Authentication
- Uses JWT tokens for SignalR authentication
- Integrates with existing user authentication system

### Room Management
- Users join rooms using lobby codes
- Automatic cleanup of empty rooms
- Real-time user join/leave notifications

### WebRTC Features
- Peer-to-peer audio/video communication
- STUN servers for NAT traversal
- Automatic offer/answer negotiation
- ICE candidate exchange
- Media stream management

### User Interface
- Responsive video grid layout
- Local and remote video streams
- Audio/video toggle controls
- Connection status indicators
- Error message display

## Usage

### Starting a Video Call
1. User clicks the video call button
2. System initializes WebRTC service
3. Connects to SignalR hub with JWT authentication
4. Joins room using lobby code
5. Establishes peer connections with other users

### Signaling Flow
1. User A joins room → SignalR notifies existing users
2. Existing users create WebRTC offers → Send via SignalR
3. User A receives offers → Creates answers → Sends via SignalR
4. ICE candidates exchanged → Direct peer connection established
5. Media streams flow directly between peers

## Configuration

### Backend (ASP.NET Core)
```csharp
// Program.cs
builder.Services.AddSignalR();
app.MapHub<VideoSignalingHub>("/videohub");
```

### Frontend Environment Variables
```env
VITE_API_BASE_URL=https://localhost:57229/api
```

### STUN Servers
```typescript
iceServers: [
  { urls: 'stun:stun.l.google.com:19302' },
  { urls: 'stun:stun1.l.google.com:19302' }
]
```

## Migration from Node.js Video Server

### What's Removed
- `server/videoServer.js` - Node.js video signaling server
- `src/services/videoService.ts` - Socket.io video service
- Dependency on separate video server port (3002)

### What's Added
- SignalR Hub in ASP.NET Core API
- WebRTC service with direct peer connections
- New React component with improved UI

### Benefits
1. **Simplified Architecture**: One less server to manage
2. **Better Authentication**: Integrated JWT authentication
3. **Improved Performance**: Direct peer-to-peer connections
4. **Enhanced Security**: Secure WebSocket connections (WSS)
5. **Better Error Handling**: Comprehensive error management

## Testing

### Development Testing
1. Start ASP.NET Core API: `dotnet run`
2. Start React frontend: `npm run dev`
3. Login with multiple users in different browsers
4. Join the same game lobby
5. Click video call button to test

### Production Deployment
1. Build and deploy ASP.NET Core API to IIS
2. Build and deploy React frontend
3. Ensure HTTPS/WSS for secure connections
4. Configure TURN servers for production NAT traversal

## Troubleshooting

### Common Issues
1. **Authentication Errors**: Check JWT token validity
2. **Connection Failures**: Verify SignalR hub endpoint
3. **Media Access Denied**: Check browser permissions
4. **Peer Connection Failed**: Configure TURN servers for production

### Debug Logging
- Browser console shows WebRTC and SignalR logs
- ASP.NET Core logs show hub connection events
- Check network tab for WebSocket connections

## Future Enhancements

1. **TURN Server Integration**: For better NAT traversal
2. **Screen Sharing**: Add screen share capability
3. **Recording**: Implement call recording
4. **Quality Controls**: Bandwidth and quality management
5. **Mobile Optimization**: Enhanced mobile experience

## Security Considerations

1. **Authentication**: JWT tokens required for SignalR
2. **HTTPS/WSS**: Secure connections in production
3. **CORS**: Properly configured allowed origins
4. **Media Permissions**: User consent for camera/microphone
5. **Room Isolation**: Users can only signal within their rooms
