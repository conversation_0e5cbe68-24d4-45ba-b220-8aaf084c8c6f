-- =============================================
-- DATABASE UPDATE SCRIPT - Remove Card Storage and Implement Ball-Level Tracking
-- Execute this script to update the database for the new ball-level tracking system
-- =============================================

USE GoldRushThunee;
GO

PRINT 'Starting database update for ball-level tracking system...';
GO

-- =============================================
-- 1. ADD NEW COLUMNS TO GAMES TABLE
-- =============================================

PRINT 'Adding team tracking columns to Games table...';
GO

-- Check if columns exist before adding them
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Games') AND name = 'IsTeamStatsUpdated')
BEGIN
    ALTER TABLE Games ADD IsTeamStatsUpdated BIT DEFAULT 0;
    PRINT 'Added IsTeamStatsUpdated column to Games table';
END
ELSE
BEGIN
    PRINT 'IsTeamStatsUpdated column already exists in Games table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Games') AND name = 'TeamStatsUpdatedAt')
BEGIN
    ALTER TABLE Games ADD TeamStatsUpdatedAt DATETIME2 NULL;
    PRINT 'Added TeamStatsUpdatedAt column to Games table';
END
ELSE
BEGIN
    PRINT 'TeamStatsUpdatedAt column already exists in Games table';
END

-- =============================================
-- 2. CREATE PERFORMANCE INDEXES
-- =============================================

PRINT 'Creating performance indexes...';
GO

-- Index for GameBalls competition queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('GameBalls') AND name = 'IX_GameBalls_CompetitionId_BallNumber')
BEGIN
    CREATE INDEX IX_GameBalls_CompetitionId_BallNumber ON GameBalls(CompetitionId, BallNumber);
    PRINT 'Created index IX_GameBalls_CompetitionId_BallNumber';
END
ELSE
BEGIN
    PRINT 'Index IX_GameBalls_CompetitionId_BallNumber already exists';
END

-- Index for GameBalls game queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('GameBalls') AND name = 'IX_GameBalls_GameId_BallNumber')
BEGIN
    CREATE INDEX IX_GameBalls_GameId_BallNumber ON GameBalls(GameId, BallNumber);
    PRINT 'Created index IX_GameBalls_GameId_BallNumber';
END
ELSE
BEGIN
    PRINT 'Index IX_GameBalls_GameId_BallNumber already exists';
END

-- =============================================
-- 3. UPDATE SP_RecordHandResult (SIMPLIFIED)
-- =============================================

PRINT 'Updating SP_RecordHandResult stored procedure...';
GO

CREATE OR ALTER PROCEDURE SP_RecordHandResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @HandId UNIQUEIDENTIFIER;

    BEGIN TRY
        -- Check if hand already exists
        SELECT @HandId = Id
        FROM GameHands
        WHERE GameId = @GameId
          AND BallNumber = @BallNumber
          AND HandNumber = @HandNumber;

        IF @HandId IS NULL
        BEGIN
            -- Insert new hand
            SET @HandId = NEWID();
            INSERT INTO GameHands (Id, GameId, BallNumber, HandNumber, WinnerPlayerId, Points, CompletedAt)
            VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, GETUTCDATE());
            
            PRINT 'New hand result recorded: Hand ' + CAST(@HandNumber AS VARCHAR) + ', Ball ' + CAST(@BallNumber AS VARCHAR);
        END
        ELSE
        BEGIN
            -- Update existing hand
            UPDATE GameHands
            SET WinnerPlayerId = @WinnerPlayerId,
                Points = @Points,
                CompletedAt = GETUTCDATE()
            WHERE Id = @HandId;
            
            PRINT 'Updated existing hand result: Hand ' + CAST(@HandNumber AS VARCHAR) + ', Ball ' + CAST(@BallNumber AS VARCHAR);
        END

        SELECT @HandId AS HandId;
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_RecordHandResult: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END
GO

PRINT 'SP_RecordHandResult updated successfully';
GO

-- =============================================
-- 4. CREATE SP_RecordGameCompletion (NEW)
-- =============================================

PRINT 'Creating SP_RecordGameCompletion stored procedure...';
GO

CREATE OR ALTER PROCEDURE SP_RecordGameCompletion
    @GameId UNIQUEIDENTIFIER,
    @WinnerTeam INT,
    @Team1FinalScore INT,
    @Team2FinalScore INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CompetitionId UNIQUEIDENTIFIER;
    DECLARE @Team1Player1Id UNIQUEIDENTIFIER, @Team1Player2Id UNIQUEIDENTIFIER;
    DECLARE @Team2Player1Id UNIQUEIDENTIFIER, @Team2Player2Id UNIQUEIDENTIFIER;
    DECLARE @Team1BallsWon INT, @Team2BallsWon INT;
    DECLARE @LobbyCode NVARCHAR(6);
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Get game details
        SELECT @CompetitionId = CompetitionId, 
               @Team1Player1Id = Team1Player1Id, @Team1Player2Id = Team1Player2Id,
               @Team2Player1Id = Team2Player1Id, @Team2Player2Id = Team2Player2Id,
               @Team1BallsWon = Team1BallsWon, @Team2BallsWon = Team2BallsWon,
               @LobbyCode = LobbyCode
        FROM Games WHERE Id = @GameId;
        
        IF @CompetitionId IS NULL
        BEGIN
            PRINT 'Game ' + @LobbyCode + ' is not a competition game - updating game status only';
        END
        ELSE
        BEGIN
            PRINT 'Processing competition game completion: ' + @LobbyCode + ' in competition ' + CAST(@CompetitionId AS VARCHAR(36));
        END
        
        -- Update game status
        UPDATE Games 
        SET Status = 'completed', 
            WinnerTeam = @WinnerTeam,
            Team1Score = @Team1FinalScore, 
            Team2Score = @Team2FinalScore,
            CompletedAt = GETUTCDATE(), 
            UpdatedAt = GETUTCDATE(),
            IsTeamStatsUpdated = 1, 
            TeamStatsUpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
        
        PRINT 'Game status updated: Winner Team ' + CAST(@WinnerTeam AS VARCHAR) + ', Scores: ' + CAST(@Team1FinalScore AS VARCHAR) + '-' + CAST(@Team2FinalScore AS VARCHAR);
        
        -- Update competition team statistics if this is a competition game
        IF @CompetitionId IS NOT NULL
        BEGIN
            DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
            DECLARE @BallDifference INT = ABS(@Team1BallsWon - @Team2BallsWon);
            DECLARE @BonusAwarded BIT = CASE WHEN @BallDifference >= 6 THEN 1 ELSE 0 END;
            DECLARE @Team1Points INT = 0, @Team2Points INT = 0;
            
            -- Calculate points
            IF @WinnerTeam = 1
                SET @Team1Points = 1 + CASE WHEN @BonusAwarded = 1 THEN 1 ELSE 0 END;
            ELSE
                SET @Team2Points = 1 + CASE WHEN @BonusAwarded = 1 THEN 1 ELSE 0 END;
            
            PRINT 'Ball difference: ' + CAST(@BallDifference AS VARCHAR) + ', Bonus awarded: ' + CASE WHEN @BonusAwarded = 1 THEN 'Yes' ELSE 'No' END;
            PRINT 'Points awarded - Team 1: ' + CAST(@Team1Points AS VARCHAR) + ', Team 2: ' + CAST(@Team2Points AS VARCHAR);
            
            -- Find team IDs
            SELECT @Team1Id = Id FROM CompetitionTeams 
            WHERE CompetitionId = @CompetitionId 
              AND (Player1Id = @Team1Player1Id OR Player2Id = @Team1Player1Id);
              
            SELECT @Team2Id = Id FROM CompetitionTeams 
            WHERE CompetitionId = @CompetitionId 
              AND (Player1Id = @Team2Player1Id OR Player2Id = @Team2Player1Id);
            
            -- Update Team 1 statistics
            IF @Team1Id IS NOT NULL
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + @Team1Points,
                    BonusPoints = BonusPoints + CASE WHEN @WinnerTeam = 1 AND @BonusAwarded = 1 THEN 1 ELSE 0 END
                WHERE Id = @Team1Id;
                
                PRINT 'Updated Team 1 statistics: +1 game, +' + CAST(@Team1Points AS VARCHAR) + ' points';
            END
            ELSE
            BEGIN
                PRINT 'Warning: Could not find Team 1 in CompetitionTeams table';
            END
            
            -- Update Team 2 statistics
            IF @Team2Id IS NOT NULL
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + @Team2Points,
                    BonusPoints = BonusPoints + CASE WHEN @WinnerTeam = 2 AND @BonusAwarded = 1 THEN 1 ELSE 0 END
                WHERE Id = @Team2Id;
                
                PRINT 'Updated Team 2 statistics: +1 game, +' + CAST(@Team2Points AS VARCHAR) + ' points';
            END
            ELSE
            BEGIN
                PRINT 'Warning: Could not find Team 2 in CompetitionTeams table';
            END
        END
        
        COMMIT TRANSACTION;
        
        SELECT 'Game completion recorded successfully for ' + @LobbyCode AS Result;
        PRINT 'Game completion processed successfully for ' + @LobbyCode;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        PRINT 'Error in SP_RecordGameCompletion: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END
GO

PRINT 'SP_RecordGameCompletion created successfully';
GO

-- =============================================
-- 5. UPDATE SP_RecordBallResult (ENHANCED)
-- =============================================

PRINT 'Updating SP_RecordBallResult stored procedure...';
GO

CREATE OR ALTER PROCEDURE SP_RecordBallResult
    @GameId UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER = NULL,
    @BallNumber INT,
    @WinnerTeam INT,
    @Team1Score INT,
    @Team2Score INT,
    @Team1BallsWon INT,
    @Team2BallsWon INT,
    @Team1Player1Id UNIQUEIDENTIFIER = NULL,
    @Team1Player2Id UNIQUEIDENTIFIER = NULL,
    @Team2Player1Id UNIQUEIDENTIFIER = NULL,
    @Team2Player2Id UNIQUEIDENTIFIER = NULL,
    @Team1Name NVARCHAR(50) = 'Team 1',
    @Team2Name NVARCHAR(50) = 'Team 2',
    @TrumpSuit NVARCHAR(10) = NULL,
    @HasThuneeDouble BIT = 0,
    @HasKhanka BIT = 0,
    @SpecialCallType NVARCHAR(20) = NULL,
    @SpecialCallResult NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BallId UNIQUEIDENTIFIER;
    DECLARE @CompletedAt DATETIME2 = GETUTCDATE();

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Check if ball result already exists
        SELECT @BallId = Id FROM GameBalls
        WHERE GameId = @GameId AND BallNumber = @BallNumber;

        IF @BallId IS NULL
        BEGIN
            -- Insert new ball result
            SET @BallId = NEWID();
            INSERT INTO GameBalls (
                Id, GameId, BallNumber, Team1Score, Team2Score, WinnerTeam,
                Team1BallsWon, Team2BallsWon, CompetitionId, Team1Player1Id, Team1Player2Id,
                Team2Player1Id, Team2Player2Id, Team1Name, Team2Name, TrumpSuit,
                HasThuneeDouble, HasKhanka, SpecialCallType, SpecialCallResult, CompletedAt
            )
            VALUES (
                @BallId, @GameId, @BallNumber, @Team1Score, @Team2Score, @WinnerTeam,
                @Team1BallsWon, @Team2BallsWon, @CompetitionId, @Team1Player1Id, @Team1Player2Id,
                @Team2Player1Id, @Team2Player2Id, @Team1Name, @Team2Name, @TrumpSuit,
                @HasThuneeDouble, @HasKhanka, @SpecialCallType, @SpecialCallResult, @CompletedAt
            );

            PRINT 'New ball result recorded: Ball ' + CAST(@BallNumber AS VARCHAR) + ', Winner: Team ' + CAST(@WinnerTeam AS VARCHAR);
        END
        ELSE
        BEGIN
            -- Update existing ball result
            UPDATE GameBalls
            SET Team1Score = @Team1Score,
                Team2Score = @Team2Score,
                WinnerTeam = @WinnerTeam,
                Team1BallsWon = @Team1BallsWon,
                Team2BallsWon = @Team2BallsWon,
                TrumpSuit = @TrumpSuit,
                HasThuneeDouble = @HasThuneeDouble,
                HasKhanka = @HasKhanka,
                SpecialCallType = @SpecialCallType,
                SpecialCallResult = @SpecialCallResult,
                CompletedAt = @CompletedAt
            WHERE Id = @BallId;

            PRINT 'Updated existing ball result: Ball ' + CAST(@BallNumber AS VARCHAR) + ', Winner: Team ' + CAST(@WinnerTeam AS VARCHAR);
        END

        -- Update the game with current ball and ball scores
        -- Don't increment CurrentBall beyond the maximum allowed (usually 12)
        DECLARE @NextBall INT = @BallNumber + 1;
        IF @NextBall > 12 SET @NextBall = 12; -- Cap at maximum ball number

        UPDATE Games
        SET CurrentBall = @NextBall,
            Team1BallsWon = @Team1BallsWon,
            Team2BallsWon = @Team2BallsWon,
            UpdatedAt = @CompletedAt
        WHERE Id = @GameId;

        PRINT 'Game updated: CurrentBall = ' + CAST(@BallNumber + 1 AS VARCHAR) + ', Ball scores: ' + CAST(@Team1BallsWon AS VARCHAR) + '-' + CAST(@Team2BallsWon AS VARCHAR);

        COMMIT TRANSACTION;

        SELECT @BallId AS BallId;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        PRINT 'Error in SP_RecordBallResult: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END
GO

PRINT 'SP_RecordBallResult updated successfully';
GO

-- =============================================
-- 6. REMOVE OLD STORED PROCEDURES
-- =============================================

PRINT 'Removing obsolete stored procedures...';
GO

-- Remove SP_RecordPlayedCards if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordPlayedCards')
BEGIN
    DROP PROCEDURE SP_RecordPlayedCards;
    PRINT 'Removed SP_RecordPlayedCards stored procedure';
END
ELSE
BEGIN
    PRINT 'SP_RecordPlayedCards does not exist - skipping removal';
END

-- =============================================
-- 7. OPTIONAL: BACKUP AND REMOVE PLAYEDCARDS TABLE
-- =============================================

PRINT 'Checking PlayedCards table...';
GO

-- Check if PlayedCards table exists and has data
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'PlayedCards')
BEGIN
    DECLARE @PlayedCardsCount INT;
    SELECT @PlayedCardsCount = COUNT(*) FROM PlayedCards;

    IF @PlayedCardsCount > 0
    BEGIN
        PRINT 'PlayedCards table exists with ' + CAST(@PlayedCardsCount AS VARCHAR) + ' records';
        PRINT 'RECOMMENDATION: Create a backup of PlayedCards table before dropping it';
        PRINT 'Example: SELECT * INTO PlayedCards_Backup_' + FORMAT(GETDATE(), 'yyyyMMdd') + ' FROM PlayedCards;';
        PRINT 'Then run: DROP TABLE PlayedCards; (when ready)';
    END
    ELSE
    BEGIN
        PRINT 'PlayedCards table exists but is empty - safe to drop';
        PRINT 'Run: DROP TABLE PlayedCards; (when ready)';
    END
END
ELSE
BEGIN
    PRINT 'PlayedCards table does not exist - no action needed';
END

-- =============================================
-- 8. VERIFICATION QUERIES
-- =============================================

PRINT 'Running verification queries...';
GO

-- Check new columns exist
SELECT
    CASE WHEN COL_LENGTH('Games', 'IsTeamStatsUpdated') IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END AS IsTeamStatsUpdated_Column,
    CASE WHEN COL_LENGTH('Games', 'TeamStatsUpdatedAt') IS NOT NULL THEN 'EXISTS' ELSE 'MISSING' END AS TeamStatsUpdatedAt_Column;

-- Check new indexes exist
SELECT
    CASE WHEN EXISTS(SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('GameBalls') AND name = 'IX_GameBalls_CompetitionId_BallNumber')
         THEN 'EXISTS' ELSE 'MISSING' END AS CompetitionId_BallNumber_Index,
    CASE WHEN EXISTS(SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('GameBalls') AND name = 'IX_GameBalls_GameId_BallNumber')
         THEN 'EXISTS' ELSE 'MISSING' END AS GameId_BallNumber_Index;

-- Check stored procedures exist
SELECT
    CASE WHEN EXISTS(SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordHandResult')
         THEN 'EXISTS' ELSE 'MISSING' END AS SP_RecordHandResult,
    CASE WHEN EXISTS(SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordBallResult')
         THEN 'EXISTS' ELSE 'MISSING' END AS SP_RecordBallResult,
    CASE WHEN EXISTS(SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordGameCompletion')
         THEN 'EXISTS' ELSE 'MISSING' END AS SP_RecordGameCompletion,
    CASE WHEN EXISTS(SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_RecordPlayedCards')
         THEN 'STILL EXISTS (SHOULD BE REMOVED)' ELSE 'REMOVED' END AS SP_RecordPlayedCards;

PRINT 'Database update completed successfully!';
PRINT '';
PRINT 'SUMMARY OF CHANGES:';
PRINT '- Added team tracking columns to Games table';
PRINT '- Created performance indexes on GameBalls table';
PRINT '- Updated SP_RecordHandResult (simplified, no card storage)';
PRINT '- Created SP_RecordGameCompletion (automatic team updates)';
PRINT '- Updated SP_RecordBallResult (enhanced logging)';
PRINT '- Removed SP_RecordPlayedCards stored procedure';
PRINT '';
PRINT 'NEXT STEPS:';
PRINT '1. Deploy the updated API and Node.js server code';
PRINT '2. Test the new game completion flow';
PRINT '3. Verify team statistics are updating correctly';
PRINT '4. Consider backing up and removing PlayedCards table when ready';
PRINT '';
PRINT 'The database is now ready for ball-level game tracking!';
GO
