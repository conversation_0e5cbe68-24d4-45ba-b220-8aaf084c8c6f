# PowerShell script to run the database update for card storage removal
# This script creates the missing SP_RecordGameCompletion stored procedure

$serverName = "**************"
$databaseName = "GoldRushThunee"  # Using GoldRushThunee as per user's connection string
$username = "EG-Dev"
$password = "Password01?"

# Connection string
$connectionString = "Server=$serverName; Database=$databaseName; User Id=$username; Password=$password; TrustServerCertificate=True;"

Write-Host "Connecting to database: $serverName\$databaseName" -ForegroundColor Green
Write-Host "This script will create the SP_RecordGameCompletion stored procedure" -ForegroundColor Yellow

try {
    # Load SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        Write-Host "Using SqlServer PowerShell module" -ForegroundColor Green
        
        # Run the database update script
        Write-Host "Running database update script..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ConnectionString $connectionString -InputFile "DATABASE_UPDATE_REMOVE_CARD_STORAGE.sql" -Verbose
        
        Write-Host "Database update completed successfully!" -ForegroundColor Green
        Write-Host "The SP_RecordGameCompletion stored procedure has been created." -ForegroundColor Green
    }
    else {
        Write-Host "SqlServer PowerShell module not found." -ForegroundColor Red
        Write-Host "Please install it with: Install-Module -Name SqlServer" -ForegroundColor Yellow
        Write-Host "Or run the script manually in SSMS: DATABASE_UPDATE_REMOVE_CARD_STORAGE.sql" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error running database update: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please run the script manually in SSMS: DATABASE_UPDATE_REMOVE_CARD_STORAGE.sql" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "After running this script, restart the ThuneeAPI server to use the new stored procedure." -ForegroundColor Cyan
Write-Host "Press any key to continue..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
