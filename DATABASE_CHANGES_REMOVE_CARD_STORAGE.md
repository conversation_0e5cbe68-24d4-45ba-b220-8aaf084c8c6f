# Database Changes Required - Remove Card Storage and Implement Ball-Level Tracking

## Overview
This document outlines the database changes needed to remove individual card storage and implement ball-level game tracking with automatic team statistics updates.

## 1. Tables to Modify/Remove

### A. PlayedCards Table - REMOVE COMPLETELY
```sql
-- This table will no longer be needed
-- DROP TABLE PlayedCards;
-- Note: Consider backing up data first if needed for historical analysis
```

### B. GameHands Table - SIMPLIFY
```sql
-- Remove card-related foreign key constraints
-- The table will only track hand winners and points, not individual cards
-- Current structure is acceptable, just remove card storage logic
```

### C. GameBalls Table - ENHANCE
```sql
-- Add indexes for better performance on competition queries
CREATE INDEX IX_GameBalls_CompetitionId_BallNumber ON GameBalls(CompetitionId, BallNumber);
CREATE INDEX IX_GameBalls_GameId_BallNumber ON GameBalls(GameId, BallNumber);
```

### D. Games Table - ADD TEAM TRACKING
```sql
-- Add fields to track if team statistics have been updated
ALTER TABLE Games ADD IsTeamStatsUpdated BIT DEFAULT 0;
ALTER TABLE Games ADD TeamStatsUpdatedAt DATETIME2 NULL;
```

## 2. Stored Procedures to Remove

### A. SP_RecordPlayedCards - REMOVE
```sql
-- This stored procedure is no longer needed
DROP PROCEDURE IF EXISTS SP_RecordPlayedCards;
```

## 3. Stored Procedures to Modify

### A. SP_RecordHandResult - SIMPLIFY
```sql
-- Remove all card recording logic
-- Keep only hand winner and points tracking
CREATE OR ALTER PROCEDURE SP_RecordHandResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @HandId UNIQUEIDENTIFIER;

    -- Check if hand already exists
    SELECT @HandId = Id
    FROM GameHands
    WHERE GameId = @GameId
      AND BallNumber = @BallNumber
      AND HandNumber = @HandNumber;

    IF @HandId IS NULL
    BEGIN
        -- Insert new hand
        SET @HandId = NEWID();
        INSERT INTO GameHands (Id, GameId, BallNumber, HandNumber, WinnerPlayerId, Points, CompletedAt)
        VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, GETUTCDATE());
    END
    ELSE
    BEGIN
        -- Update existing hand
        UPDATE GameHands
        SET WinnerPlayerId = @WinnerPlayerId,
            Points = @Points,
            CompletedAt = GETUTCDATE()
        WHERE Id = @HandId;
    END

    SELECT @HandId AS HandId;
END
```

### B. SP_RecordBallResult - ENHANCE WITH TEAM UPDATES
```sql
-- Add automatic team statistics updates for competition games
CREATE OR ALTER PROCEDURE SP_RecordBallResult
    @GameId UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER = NULL,
    @BallNumber INT,
    @WinnerTeam INT,
    @Team1Score INT,
    @Team2Score INT,
    @Team1BallsWon INT,
    @Team2BallsWon INT,
    @Team1Player1Id UNIQUEIDENTIFIER = NULL,
    @Team1Player2Id UNIQUEIDENTIFIER = NULL,
    @Team2Player1Id UNIQUEIDENTIFIER = NULL,
    @Team2Player2Id UNIQUEIDENTIFIER = NULL,
    @Team1Name NVARCHAR(50) = 'Team 1',
    @Team2Name NVARCHAR(50) = 'Team 2',
    @TrumpSuit NVARCHAR(10) = NULL,
    @HasThuneeDouble BIT = 0,
    @HasKhanka BIT = 0,
    @SpecialCallType NVARCHAR(20) = NULL,
    @SpecialCallResult NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @BallId UNIQUEIDENTIFIER;
    DECLARE @CompletedAt DATETIME2 = GETUTCDATE();
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Record ball result (existing logic)
        SELECT @BallId = Id FROM GameBalls 
        WHERE GameId = @GameId AND BallNumber = @BallNumber;
        
        IF @BallId IS NULL
        BEGIN
            SET @BallId = NEWID();
            INSERT INTO GameBalls (
                Id, GameId, BallNumber, Team1Score, Team2Score, WinnerTeam,
                Team1BallsWon, Team2BallsWon, CompetitionId, Team1Player1Id, Team1Player2Id,
                Team2Player1Id, Team2Player2Id, Team1Name, Team2Name, TrumpSuit,
                HasThuneeDouble, HasKhanka, SpecialCallType, SpecialCallResult, CompletedAt
            )
            VALUES (
                @BallId, @GameId, @BallNumber, @Team1Score, @Team2Score, @WinnerTeam,
                @Team1BallsWon, @Team2BallsWon, @CompetitionId, @Team1Player1Id, @Team1Player2Id,
                @Team2Player1Id, @Team2Player2Id, @Team1Name, @Team2Name, @TrumpSuit,
                @HasThuneeDouble, @HasKhanka, @SpecialCallType, @SpecialCallResult, @CompletedAt
            );
        END
        ELSE
        BEGIN
            UPDATE GameBalls
            SET Team1Score = @Team1Score, Team2Score = @Team2Score, WinnerTeam = @WinnerTeam,
                Team1BallsWon = @Team1BallsWon, Team2BallsWon = @Team2BallsWon,
                TrumpSuit = @TrumpSuit, HasThuneeDouble = @HasThuneeDouble,
                HasKhanka = @HasKhanka, SpecialCallType = @SpecialCallType,
                SpecialCallResult = @SpecialCallResult, CompletedAt = @CompletedAt
            WHERE Id = @BallId;
        END
        
        -- Update game with current ball and ball scores
        UPDATE Games 
        SET CurrentBall = @BallNumber + 1, Team1BallsWon = @Team1BallsWon,
            Team2BallsWon = @Team2BallsWon, UpdatedAt = @CompletedAt
        WHERE Id = @GameId;
        
        COMMIT TRANSACTION;
        SELECT @BallId AS BallId;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
```

## 4. New Stored Procedures to Create

### A. SP_RecordGameCompletion - HANDLE GAME END WITH TEAM UPDATES
```sql
CREATE OR ALTER PROCEDURE SP_RecordGameCompletion
    @GameId UNIQUEIDENTIFIER,
    @WinnerTeam INT,
    @Team1FinalScore INT,
    @Team2FinalScore INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @CompetitionId UNIQUEIDENTIFIER;
    DECLARE @Team1Player1Id UNIQUEIDENTIFIER, @Team1Player2Id UNIQUEIDENTIFIER;
    DECLARE @Team2Player1Id UNIQUEIDENTIFIER, @Team2Player2Id UNIQUEIDENTIFIER;
    DECLARE @Team1BallsWon INT, @Team2BallsWon INT;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Get game details
        SELECT @CompetitionId = CompetitionId, 
               @Team1Player1Id = Team1Player1Id, @Team1Player2Id = Team1Player2Id,
               @Team2Player1Id = Team2Player1Id, @Team2Player2Id = Team2Player2Id,
               @Team1BallsWon = Team1BallsWon, @Team2BallsWon = Team2BallsWon
        FROM Games WHERE Id = @GameId;
        
        -- Update game status
        UPDATE Games 
        SET Status = 'completed', WinnerTeam = @WinnerTeam,
            Team1Score = @Team1FinalScore, Team2Score = @Team2FinalScore,
            CompletedAt = GETUTCDATE(), UpdatedAt = GETUTCDATE(),
            IsTeamStatsUpdated = 1, TeamStatsUpdatedAt = GETUTCDATE()
        WHERE Id = @GameId;
        
        -- Update competition team statistics if this is a competition game
        IF @CompetitionId IS NOT NULL
        BEGIN
            DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
            DECLARE @BallDifference INT = ABS(@Team1BallsWon - @Team2BallsWon);
            DECLARE @BonusAwarded BIT = CASE WHEN @BallDifference >= 6 THEN 1 ELSE 0 END;
            
            -- Find team IDs
            SELECT @Team1Id = Id FROM CompetitionTeams 
            WHERE CompetitionId = @CompetitionId 
              AND (Player1Id = @Team1Player1Id OR Player2Id = @Team1Player1Id);
              
            SELECT @Team2Id = Id FROM CompetitionTeams 
            WHERE CompetitionId = @CompetitionId 
              AND (Player1Id = @Team2Player1Id OR Player2Id = @Team2Player1Id);
            
            -- Update Team 1 statistics
            IF @Team1Id IS NOT NULL
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + CASE WHEN @WinnerTeam = 1 THEN (1 + CASE WHEN @BonusAwarded = 1 THEN 1 ELSE 0 END) ELSE 0 END,
                    BonusPoints = BonusPoints + CASE WHEN @WinnerTeam = 1 AND @BonusAwarded = 1 THEN 1 ELSE 0 END
                WHERE Id = @Team1Id;
            END
            
            -- Update Team 2 statistics
            IF @Team2Id IS NOT NULL
            BEGIN
                UPDATE CompetitionTeams 
                SET GamesPlayed = GamesPlayed + 1,
                    Points = Points + CASE WHEN @WinnerTeam = 2 THEN (1 + CASE WHEN @BonusAwarded = 1 THEN 1 ELSE 0 END) ELSE 0 END,
                    BonusPoints = BonusPoints + CASE WHEN @WinnerTeam = 2 AND @BonusAwarded = 1 THEN 1 ELSE 0 END
                WHERE Id = @Team2Id;
            END
        END
        
        COMMIT TRANSACTION;
        
        SELECT 'Game completion recorded successfully' AS Result;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
```

## 5. Execution Order

1. **Backup existing data** (if card history is needed)
2. **Create new stored procedures** (SP_RecordGameCompletion)
3. **Modify existing stored procedures** (SP_RecordHandResult, SP_RecordBallResult)
4. **Add new table columns** (Games table modifications)
5. **Create indexes** (GameBalls performance indexes)
6. **Remove old stored procedures** (SP_RecordPlayedCards)
7. **Consider dropping PlayedCards table** (after confirming no dependencies)

## 6. Testing Checklist

- [ ] Ball result recording works without card data
- [ ] Hand result recording works without card data  
- [ ] Game completion automatically updates team statistics
- [ ] Competition team GamesPlayed increments correctly
- [ ] Points and bonus points are calculated correctly
- [ ] No references to PlayedCards table remain in code
