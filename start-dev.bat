@echo off
echo ========================================
echo    Starting Thunee Development Environment
echo ========================================
echo.

REM Set console colors for better visibility
color 0A

REM Check if we're in the correct directory
if not exist "package.json" (
    echo Error: package.json not found. Please run this script from the Thunee-FE directory.
    pause
    exit /b 1
)

echo [1/4] Starting ASP.NET Core API Server...
echo ----------------------------------------
start "Thunee ASP.NET API" cmd /k "cd ThuneeAPI\ThuneeAPI && echo Starting ASP.NET Core API Server on https://localhost:57229... && dotnet run"

REM Wait a moment for API to start
timeout /t 5 /nobreak >nul

echo [2/4] Starting Video Server...
echo ----------------------------------------
start "Thunee Video Server" cmd /k "cd server && echo Starting Video Server... && node videoServer.js"

REM Wait a moment for video server to start
timeout /t 2 /nobreak >nul

echo [3/4] Starting Game Server...
echo ----------------------------------------
start "Thunee Game Server" cmd /k "cd server && echo Starting Game Server in Development Mode... && npm run dev:win"

REM Wait a moment for game server to start
timeout /t 2 /nobreak >nul

echo [4/4] Starting Frontend Development Server...
echo ----------------------------------------
echo Starting Frontend on http://localhost:5173/
start "Thunee Frontend" cmd /k "echo Starting Frontend Development Server... && npm run dev"

echo.
echo ========================================
echo    All Development Servers Started!
echo ========================================
echo.
echo Services running:
echo   - Frontend:     http://localhost:5173/
echo   - ASP.NET API:  https://localhost:57229/
echo   - API Docs:     https://localhost:57229/api-docs
echo   - Game Server:  WebSocket connection
echo   - Video Server: WebRTC connection
echo.
echo Press any key to open the application in your browser...
pause >nul

REM Open the application in default browser
start http://localhost:5173/

echo.
echo Development environment is ready!
echo Close this window or press Ctrl+C to stop all servers.
echo.
pause
