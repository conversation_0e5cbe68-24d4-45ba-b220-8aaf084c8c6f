# Environment Configuration

This document explains how to configure the Thunee Frontend application for different environments.

## Environment Files

The application uses environment files to configure API endpoints and other settings:

- `.env.development` - Development environment (used by default when running `npm run dev`)
- `.env.production` - Production environment (used when building for production)
- `.env.local` - Local overrides (not committed to git, highest priority)

## Environment Variables

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_API_BASE_URL` | Base URL for the ASP.NET Core API server | `https://localhost:57229/api` |
| `VITE_SOCKET_URL` | URL for the WebSocket server | `https://localhost:57229` |
| `VITE_APP_ENV` | Application environment | `development` or `production` |

### Development Setup

1. Copy the example file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` with your local settings:
   ```env
   VITE_API_BASE_URL=https://localhost:57229/api
   VITE_SOCKET_URL=https://localhost:57229
   VITE_APP_ENV=development
   ```

3. If your ASP.NET Core API runs on a different port, update the URLs:
   ```env
   VITE_API_BASE_URL=https://localhost:57230/api
   VITE_SOCKET_URL=https://localhost:57230
   ```

4. **Important**: The API now uses HTTPS by default. If you encounter SSL certificate issues in development, you may need to:
   - Trust the development certificate: `dotnet dev-certs https --trust`
   - Or use HTTP instead: `http://localhost:57230/api`

### Production Setup

For production deployment, ensure your `.env.production` file contains the correct URLs:

```env
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_SOCKET_URL=https://your-api-domain.com
VITE_APP_ENV=production
```

## API Integration

The frontend now connects directly to the ASP.NET Core API server using the configured URLs:

### ASP.NET Core API Server

The new API server is located at `C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI\` and provides:

- **Layered Architecture**: Clean separation with Core, Application, Infrastructure, and Presentation layers
- **Entity Framework Core**: SQL Server integration with comprehensive database schema
- **JWT Authentication**: Secure token-based authentication
- **Swagger Documentation**: Interactive API documentation at `/api-docs`
- **Real-time Game Tracking**: Hand-by-hand and game result storage
- **Competition Management**: Tournament creation and leaderboard tracking

### Starting the API Server

Use the updated development scripts:

```bash
# Enhanced script with API building and validation
start-dev-enhanced.bat

# Standard script (updated for ASP.NET Core)
start-dev.bat

# Test API connection
test-api-connection.bat
```

### Authentication Endpoints
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user

### Competition Endpoints
- `GET /competitions` - Get all competitions
- `GET /competitions/:id` - Get specific competition
- `POST /competitions/:id/join` - Join a competition
- `GET /competitions/:id/leaderboard` - Get competition leaderboard

### Leaderboard Endpoints
- `GET /leaderboard/global` - Get global leaderboard
- `GET /leaderboard/weekly` - Get weekly leaderboard
- `GET /leaderboard/monthly` - Get monthly leaderboard

### Lobby Endpoints
- `POST /lobbies` - Create a new lobby
- `GET /lobbies/:code` - Get lobby information
- `POST /lobbies/:code/join` - Join a lobby
- `POST /lobbies/:code/leave` - Leave a lobby
- `POST /lobbies/:code/ready` - Set ready status
- `POST /lobbies/:code/start` - Start the game

## 4-Player Game Setup

The system supports team-based 4-player games:

1. **Create Lobby**: Player 1 creates a lobby with a team name
2. **Partner Join**: Player 2 uses the partner invite code to join Team 1
3. **Opponent Join**: Player 3 uses the opponent invite code to create Team 2
4. **Complete Team**: Player 4 uses the opponent invite code to join Team 2
5. **Start Game**: All players set ready status and the host starts the game

## Troubleshooting

### API Connection Issues

1. Check that your API server is running on the configured port
2. Verify the `VITE_API_BASE_URL` in your environment file
3. Check browser console for CORS errors
4. Ensure the API server allows requests from your frontend domain

### Socket Connection Issues

1. Verify the `VITE_SOCKET_URL` in your environment file
2. Check that the WebSocket server is running
3. Look for connection errors in the browser console
4. Ensure firewall/proxy settings allow WebSocket connections

### Environment Variables Not Loading

1. Ensure environment files are in the root directory (`Thunee-FE/`)
2. Variable names must start with `VITE_` to be accessible in the frontend
3. Restart the development server after changing environment files
4. Check that `.env.local` is not committed to git (it should be ignored)

## Development vs Production

- **Development**: Uses `.env.development` by default, can be overridden by `.env.local`
- **Production**: Uses `.env.production` when building with `npm run build`
- **Local**: `.env.local` always takes precedence and should contain your personal settings

The application will automatically log the configuration in development mode to help with debugging.
