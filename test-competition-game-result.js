const axios = require('axios');

// Test competition game result saving
async function testCompetitionGameResult() {
  console.log('Testing competition game result saving...');

  // Mock data similar to what the Node.js server would send
  const testData = {
    gameId: "12345678-1234-1234-1234-123456789012",
    competitionId: "4860c19d-e3f3-4e2d-b359-275527461bd6",
    team1Id: "7374dc05-5e5c-42be-bc56-c9b71ca33a21",
    team2Id: "1ab65b13-df3b-4d97-b931-40925d55c847",
    team1Score: 4,
    team2Score: 16,
    team1BallsWon: 0,
    team2BallsWon: 2,
    winnerTeam: 2
  };

  console.log('Test data:', JSON.stringify(testData, null, 2));

  try {
    // Test direct approach
    console.log('\n--- Testing direct approach ---');
    const directResponse = await axios.post('https://localhost:57229/api/competitions/games/result', testData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // You'll need a real token
      },
      timeout: 10000
    });

    console.log('✅ Direct approach succeeded');
    console.log('Response status:', directResponse.status);
    console.log('Response data:', directResponse.data);

  } catch (directError) {
    console.log('❌ Direct approach failed');
    console.log('Error status:', directError.response?.status);
    console.log('Error data:', JSON.stringify(directError.response?.data, null, 2));

    // Test wrapped approach
    try {
      console.log('\n--- Testing wrapped approach ---');
      const wrappedData = { gameResult: testData };
      console.log('Wrapped data:', JSON.stringify(wrappedData, null, 2));

      const wrappedResponse = await axios.post('https://localhost:57229/api/competitions/games/result', wrappedData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // You'll need a real token
        },
        timeout: 10000
      });

      console.log('✅ Wrapped approach succeeded');
      console.log('Response status:', wrappedResponse.status);
      console.log('Response data:', wrappedResponse.data);

    } catch (wrappedError) {
      console.log('❌ Wrapped approach also failed');
      console.log('Error status:', wrappedError.response?.status);
      console.log('Error data:', JSON.stringify(wrappedError.response?.data, null, 2));
    }
  }
}

// Run the test
testCompetitionGameResult().catch(console.error);
