# Admin System Stored Procedures

This document outlines the stored procedures needed to support the admin functionality in the Thunee application.

## User Management Stored Procedures

### SP_GetAllUsers
**Purpose:** Retrieve all users for admin management
```sql
CREATE PROCEDURE SP_GetAllUsers
AS
BEGIN
    SELECT 
        Id,
        Username,
        Email,
        IsVerified,
        IsActive,
        IsAdmin,
        LastLoginAt,
        CreatedAt,
        UpdatedAt
    FROM Users
    ORDER BY CreatedAt DESC
END
```

### SP_UpdateUser
**Purpose:** Update user details (admin only)
```sql
CREATE PROCEDURE SP_UpdateUser
    @Id UNIQUEIDENTIFIER,
    @Username NVARCHAR(50) = NULL,
    @Email NVARCHAR(255) = NULL,
    @IsVerified BIT = NULL,
    @IsActive BIT = NULL,
    @IsAdmin BIT = NULL
AS
BEGIN
    UPDATE Users 
    SET 
        Username = ISNULL(@Username, Username),
        Email = ISNULL(@Email, Email),
        IsVerified = ISNULL(@IsVerified, IsVerified),
        IsActive = ISNULL(@IsActive, IsActive),
        IsAdmin = ISNULL(@IsAdmin, IsAdmin),
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    SELECT 
        Id,
        Username,
        Email,
        IsVerified,
        IsActive,
        IsAdmin,
        LastLoginAt,
        CreatedAt,
        UpdatedAt
    FROM Users 
    WHERE Id = @Id
END
```

### SP_DeleteUser
**Purpose:** Delete a user (admin only)
```sql
CREATE PROCEDURE SP_DeleteUser
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if user exists
    IF NOT EXISTS (SELECT 1 FROM Users WHERE Id = @Id)
    BEGIN
        RAISERROR('User not found', 16, 1)
        RETURN
    END

    -- Delete related records first (if any foreign key constraints)
    -- Delete from CompetitionTeams where Player1Id or Player2Id = @Id
    DELETE FROM CompetitionTeams 
    WHERE Player1Id = @Id OR Player2Id = @Id

    -- Delete from Games where user is involved
    DELETE FROM Games 
    WHERE Team1Player1Id = @Id OR Team1Player2Id = @Id 
       OR Team2Player1Id = @Id OR Team2Player2Id = @Id

    -- Finally delete the user
    DELETE FROM Users WHERE Id = @Id

    SELECT 1 as Success
END
```

### SP_ChangeUserPassword
**Purpose:** Change user password (admin only)
```sql
CREATE PROCEDURE SP_ChangeUserPassword
    @Id UNIQUEIDENTIFIER,
    @NewPasswordHash NVARCHAR(255)
AS
BEGIN
    UPDATE Users 
    SET 
        PasswordHash = @NewPasswordHash,
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    SELECT @@ROWCOUNT as RowsAffected
END
```

## Competition Management Stored Procedures

### SP_GetAllCompetitionsAdmin
**Purpose:** Get all competitions with admin details
```sql
CREATE PROCEDURE SP_GetAllCompetitionsAdmin
AS
BEGIN
    SELECT 
        c.Id,
        c.Name,
        c.Description,
        c.StartDate,
        c.EndDate,
        c.Status,
        c.MaxTeams,
        c.CurrentTeams,
        c.EntryFee,
        c.PrizeFirst,
        c.PrizeSecond,
        c.PrizeThird,
        c.TotalPrizePool,
        c.Rules,
        c.IsPublic,
        c.AllowSpectators,
        c.MaxGamesPerTeam,
        c.CreatedAt,
        c.UpdatedAt
    FROM Competitions c
    ORDER BY c.CreatedAt DESC
END
```

### SP_UpdateCompetition
**Purpose:** Update competition details (admin only)
```sql
CREATE PROCEDURE SP_UpdateCompetition
    @Id UNIQUEIDENTIFIER,
    @Name NVARCHAR(255) = NULL,
    @Description NVARCHAR(MAX) = NULL,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @Status NVARCHAR(50) = NULL,
    @MaxTeams INT = NULL,
    @EntryFee DECIMAL(10,2) = NULL,
    @PrizeFirst DECIMAL(10,2) = NULL,
    @PrizeSecond DECIMAL(10,2) = NULL,
    @PrizeThird DECIMAL(10,2) = NULL,
    @Rules NVARCHAR(MAX) = NULL,
    @IsPublic BIT = NULL,
    @AllowSpectators BIT = NULL,
    @MaxGamesPerTeam INT = NULL
AS
BEGIN
    -- Calculate TotalPrizePool if any prize values are updated
    DECLARE @NewTotalPrizePool DECIMAL(10,2)
    
    SELECT @NewTotalPrizePool = 
        ISNULL(@PrizeFirst, PrizeFirst) + 
        ISNULL(@PrizeSecond, PrizeSecond) + 
        ISNULL(@PrizeThird, PrizeThird)
    FROM Competitions WHERE Id = @Id

    UPDATE Competitions 
    SET 
        Name = ISNULL(@Name, Name),
        Description = ISNULL(@Description, Description),
        StartDate = ISNULL(@StartDate, StartDate),
        EndDate = ISNULL(@EndDate, EndDate),
        Status = ISNULL(@Status, Status),
        MaxTeams = ISNULL(@MaxTeams, MaxTeams),
        EntryFee = ISNULL(@EntryFee, EntryFee),
        PrizeFirst = ISNULL(@PrizeFirst, PrizeFirst),
        PrizeSecond = ISNULL(@PrizeSecond, PrizeSecond),
        PrizeThird = ISNULL(@PrizeThird, PrizeThird),
        TotalPrizePool = @NewTotalPrizePool,
        Rules = ISNULL(@Rules, Rules),
        IsPublic = ISNULL(@IsPublic, IsPublic),
        AllowSpectators = ISNULL(@AllowSpectators, AllowSpectators),
        MaxGamesPerTeam = ISNULL(@MaxGamesPerTeam, MaxGamesPerTeam),
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    -- Return updated competition
    SELECT 
        Id,
        Name,
        Description,
        StartDate,
        EndDate,
        Status,
        MaxTeams,
        CurrentTeams,
        EntryFee,
        PrizeFirst,
        PrizeSecond,
        PrizeThird,
        TotalPrizePool,
        Rules,
        IsPublic,
        AllowSpectators,
        MaxGamesPerTeam,
        CreatedAt,
        UpdatedAt
    FROM Competitions 
    WHERE Id = @Id
END
```

### SP_DeleteCompetition
**Purpose:** Delete a competition (admin only)
```sql
CREATE PROCEDURE SP_DeleteCompetition
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if competition exists
    IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @Id)
    BEGIN
        RAISERROR('Competition not found', 16, 1)
        RETURN
    END

    -- Delete related records first
    DELETE FROM Games WHERE CompetitionId = @Id
    DELETE FROM CompetitionTeams WHERE CompetitionId = @Id

    -- Delete the competition
    DELETE FROM Competitions WHERE Id = @Id

    SELECT 1 as Success
END
```

## Competition Teams Management Stored Procedures

### SP_GetCompetitionTeamsAdmin
**Purpose:** Get all teams in a competition with player details
```sql
CREATE PROCEDURE SP_GetCompetitionTeamsAdmin
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        ct.Id,
        ct.CompetitionId,
        ct.TeamName,
        ct.Player1Id,
        u1.Username as Player1Username,
        u1.Email as Player1Email,
        ct.Player2Id,
        u2.Username as Player2Username,
        u2.Email as Player2Email,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        ct.MaxGames,
        ct.IsActive,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    INNER JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId
    ORDER BY ct.Points DESC, ct.RegisteredAt ASC
END
```

### SP_DeleteCompetitionTeam
**Purpose:** Delete a competition team (admin only)
```sql
CREATE PROCEDURE SP_DeleteCompetitionTeam
    @TeamId UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if team exists
    IF NOT EXISTS (SELECT 1 FROM CompetitionTeams WHERE Id = @TeamId)
    BEGIN
        RAISERROR('Team not found', 16, 1)
        RETURN
    END

    -- Get competition ID for updating current teams count
    DECLARE @CompetitionId UNIQUEIDENTIFIER
    SELECT @CompetitionId = CompetitionId FROM CompetitionTeams WHERE Id = @TeamId

    -- Delete related games first
    DELETE FROM Games 
    WHERE CompetitionId = @CompetitionId 
    AND (
        (Team1Player1Id IN (SELECT Player1Id FROM CompetitionTeams WHERE Id = @TeamId) 
         AND Team1Player2Id IN (SELECT Player2Id FROM CompetitionTeams WHERE Id = @TeamId))
        OR
        (Team2Player1Id IN (SELECT Player1Id FROM CompetitionTeams WHERE Id = @TeamId) 
         AND Team2Player2Id IN (SELECT Player2Id FROM CompetitionTeams WHERE Id = @TeamId))
    )

    -- Delete the team
    DELETE FROM CompetitionTeams WHERE Id = @TeamId

    -- Update competition current teams count
    UPDATE Competitions 
    SET CurrentTeams = (SELECT COUNT(*) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId)
    WHERE Id = @CompetitionId

    SELECT 1 as Success
END
```

## Games Management Stored Procedures

### SP_GetCompetitionGamesAdmin
**Purpose:** Get all games in a competition (admin view)
```sql
CREATE PROCEDURE SP_GetCompetitionGamesAdmin
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        g.Id,
        g.LobbyCode,
        g.CompetitionId,
        c.Name as CompetitionName,
        g.Team1Name,
        g.Team2Name,
        g.Status,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        g.StartedAt,
        g.CompletedAt,
        g.CreatedAt
    FROM Games g
    INNER JOIN Competitions c ON g.CompetitionId = c.Id
    WHERE g.CompetitionId = @CompetitionId
    ORDER BY g.CreatedAt DESC
END
```

### SP_GetAllGamesAdmin
**Purpose:** Get all games across all competitions (admin view)
```sql
CREATE PROCEDURE SP_GetAllGamesAdmin
AS
BEGIN
    SELECT 
        g.Id,
        g.LobbyCode,
        g.CompetitionId,
        c.Name as CompetitionName,
        g.Team1Name,
        g.Team2Name,
        g.Status,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        g.StartedAt,
        g.CompletedAt,
        g.CreatedAt
    FROM Games g
    LEFT JOIN Competitions c ON g.CompetitionId = c.Id
    ORDER BY g.CreatedAt DESC
END
```

## Email Management Stored Procedures

### SP_GetCompetitionPlayerEmails
**Purpose:** Get all player emails in a competition for mass emailing
```sql
CREATE PROCEDURE SP_GetCompetitionPlayerEmails
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT DISTINCT u.Email, u.Username
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    INNER JOIN Users u2 ON ct.Player2Id = u2.Id
    CROSS APPLY (
        SELECT u1.Email, u1.Username
        UNION
        SELECT u2.Email, u2.Username
    ) u
    WHERE ct.CompetitionId = @CompetitionId
    AND u.Email IS NOT NULL
    ORDER BY u.Username
END
```

## Complete Installation Script

Save this as `CreateAdminStoredProcedures.sql` and run it in your database:

```sql
USE [GoldRushThunee]
GO

-- Drop existing procedures if they exist
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllUsers')
    DROP PROCEDURE SP_GetAllUsers
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_UpdateUser')
    DROP PROCEDURE SP_UpdateUser
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteUser')
    DROP PROCEDURE SP_DeleteUser
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_ChangeUserPassword')
    DROP PROCEDURE SP_ChangeUserPassword
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllCompetitionsAdmin')
    DROP PROCEDURE SP_GetAllCompetitionsAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_UpdateCompetition')
    DROP PROCEDURE SP_UpdateCompetition
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteCompetition')
    DROP PROCEDURE SP_DeleteCompetition
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionTeamsAdmin')
    DROP PROCEDURE SP_GetCompetitionTeamsAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteCompetitionTeam')
    DROP PROCEDURE SP_DeleteCompetitionTeam
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionGamesAdmin')
    DROP PROCEDURE SP_GetCompetitionGamesAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllGamesAdmin')
    DROP PROCEDURE SP_GetAllGamesAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionPlayerEmails')
    DROP PROCEDURE SP_GetCompetitionPlayerEmails
GO

-- Create all procedures (copy each CREATE PROCEDURE statement from above)
-- ... (All the CREATE PROCEDURE statements would go here)
```

## Notes

1. **Error Handling:** All procedures include basic error handling with RAISERROR for critical failures
2. **Transactions:** Consider wrapping delete operations in transactions for data integrity
3. **Permissions:** Ensure only admin users can execute these procedures
4. **Indexes:** Consider adding indexes on frequently queried columns for better performance
5. **Audit Trail:** Consider adding audit logging for admin operations
