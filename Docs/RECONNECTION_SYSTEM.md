# Thunee Game Reconnection System

This document explains the implementation of the reconnection and timeout logic for the Thunee card game.

## Overview

The reconnection system allows players to rejoin their game within a 30-second timeout window if they disconnect (e.g., page refresh, internet loss). If they fail to reconnect within this window, the game ends with the opposing team winning.

## Features

### ✅ Implemented Features

1. **Persistent Player IDs**: Each player gets a unique persistent ID stored in localStorage
2. **30-Second Reconnection Window**: Configurable timeout (default 30 seconds)
3. **Game Pause/Resume**: Game pauses during disconnection, resumes on reconnection
4. **Automatic Timeout Handling**: Game ends with appropriate ball allocation if timeout expires
5. **Real-time Notifications**: Toast messages and UI updates for all players
6. **Khanka Support**: Respects winning Khanka calls (13 balls vs 12 balls)

### 🎯 Key Requirements Met

- ✅ Rejoin functionality with persistent player IDs
- ✅ 300-second (5-minute) configurable timeout
- ✅ Game pause during disconnection
- ✅ Toast notifications for all players
- ✅ Automatic game ending on timeout
- ✅ Proper ball allocation (12 or 13 based on Khanka)
- ✅ Client-side reconnection after page refresh

## Backend Implementation

### New Files Created

#### `server/utils/disconnectionUtils.js`
Core utility handling disconnection logic:

```javascript
// Key functions:
- startDisconnectionTimer(io, socketId, lobbyCode, player, lobby, matchedLobby, timeoutSeconds)
- handlePlayerRejoin(io, socket, persistentId, gameId)
- pauseGame(io, lobby, matchedLobby, disconnectedPlayer)
- resumeGame(io, lobby, matchedLobby, reconnectedPlayer)
```

### Modified Files

#### `server/index.js`
- Added import for `disconnectionUtils`
- Added `rejoinRequest` event handler
- Modified disconnect handler to start disconnection timer for active games
- Generates persistent player IDs if not present

### Socket Events Added

#### Server → Client Events
- `player_disconnected`: Notifies when a player disconnects
- `player_reconnected`: Notifies when a player rejoins
- `disconnection_countdown`: Updates remaining time every 5 seconds
- `game_paused`: Notifies when game is paused due to disconnection
- `game_resumed`: Notifies when game resumes after reconnection
- `toast_notification`: Generic toast messages

#### Client → Server Events
- `rejoinRequest`: Attempts to rejoin a game with persistent ID

## Frontend Implementation

### New Files Created

#### `src/utils/disconnectionHandler.ts`
Client-side utility for managing disconnection events:

```typescript
// Key features:
- Event listener setup for all disconnection-related events
- Toast notification management
- Game pause state tracking
- Manual rejoin functionality
```

#### `src/components/DisconnectionNotifications.tsx`
React component providing UI for disconnection notifications:

```typescript
// Features:
- Toast notifications with progress bars
- Game pause overlay
- Disconnected players status panel
- Real-time countdown display
```

### Modified Files

#### `src/services/socketService.ts`
- Added persistent player ID management
- Added automatic rejoin attempt on connection
- Added event listeners for disconnection events
- Store current game ID for rejoin purposes

## Usage Instructions

### 1. Backend Setup

The backend changes are automatically active. No additional configuration needed.

### 2. Frontend Integration

Add the disconnection notifications component to your main game component:

```tsx
import DisconnectionNotifications from '@/components/DisconnectionNotifications';

function GameComponent() {
  return (
    <div>
      {/* Your existing game UI */}
      <DisconnectionNotifications />
    </div>
  );
}
```

### 3. Customizing Timeout Duration

To change the default 300-second timeout, modify the server code:

```javascript
// In server/index.js, line ~7825
disconnectionUtils.startDisconnectionTimer(io, socket.id, lobbyCode, player, lobby, matchedLobby, 600); // 600 seconds (10 minutes)
```

## Data Structures

### Persistent Player ID
```javascript
// Format: "player_timestamp_randomstring"
// Example: "player_1703123456789_abc123def"
// Stored in: localStorage['thunee_persistent_player_id']
```

### Game State Storage
```javascript
// Current game ID stored in: localStorage['thunee_current_game_id']
// Used for automatic rejoin attempts
```

### Disconnection Timer Data
```javascript
{
  timer: NodeJS.Timeout,
  countdownInterval: NodeJS.Timeout,
  startTime: number,
  lobbyCode: string,
  playerId: string,
  playerName: string,
  playerTeam: number
}
```

## Example Toast Messages

### Disconnection
```
"Player X disconnected. Waiting 30 seconds to reconnect."
```

### Countdown Updates
```
"Player X has 25 seconds to reconnect"
```

### Successful Reconnection
```
"Player X reconnected successfully!"
```

### Timeout Failure
```
"Player X failed to reconnect. Game ended. Team 2 wins!"
```

## Game End Logic

When a player fails to reconnect within the timeout:

1. **Determine winning balls**: 12 (standard) or 13 (if Khanka was called)
2. **Set opposing team score**: Set to winning ball count
3. **Keep disconnected team score**: Unchanged from current value
4. **Broadcast game end**: Send `game_ended` event with reason `'disconnection_timeout'`

### Example Game End Data
```javascript
{
  gameEnded: true,
  winner: 2, // Opposing team
  reason: 'disconnection_timeout',
  finalScores: { team1: 8, team2: 12 }, // team2 wins
  disconnectedPlayer: { name: "John", team: 1 },
  ballsToWin: 12,
  hasWinningKhanak: false
}
```

## Testing

### Manual Testing Steps

1. **Start a 4-player game**
2. **Refresh one player's browser** (simulates disconnection)
3. **Verify notifications appear** for remaining players
4. **Rejoin within 30 seconds** - should succeed
5. **Let timeout expire** - game should end with opposing team winning

### Development Tools

- Manual rejoin button appears in development mode
- Console logging for all disconnection events
- localStorage inspection for persistent IDs

## Error Handling

### Common Scenarios Handled

1. **Invalid persistent ID**: Rejoin fails gracefully
2. **Game not found**: Rejoin fails with appropriate error
3. **Timeout expired**: Disconnection record cleaned up
4. **Multiple disconnections**: Each player tracked separately
5. **Server restart**: Persistent IDs regenerated on next connection

## Performance Considerations

- **Timer cleanup**: All timers properly cleared on reconnection/timeout
- **Memory management**: Disconnected player records removed after timeout
- **Event listener cleanup**: Proper cleanup in React components
- **Minimal network traffic**: Countdown updates only every 5 seconds

## Future Enhancements

Potential improvements for future versions:

1. **Spectator reconnection**: Allow spectators to rejoin
2. **Partial game state sync**: Send only changed data on rejoin
3. **Reconnection history**: Track reconnection attempts
4. **Custom timeout per game**: Allow hosts to set timeout duration
5. **Mobile app support**: Handle app backgrounding/foregrounding

## Troubleshooting

### Common Issues

1. **Rejoin fails**: Check browser localStorage for persistent ID
2. **No notifications**: Ensure DisconnectionNotifications component is rendered
3. **Game doesn't pause**: Verify gameStarted flag is set correctly
4. **Timer doesn't clear**: Check for proper event listener cleanup

### Debug Information

Enable debug logging by setting:
```javascript
localStorage.setItem('thunee_debug_reconnection', 'true');
```

This implementation provides a robust reconnection system that enhances the user experience while maintaining game integrity and fairness.
