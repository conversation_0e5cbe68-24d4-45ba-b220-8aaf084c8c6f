# Thunee Competition Database: Initial Design, Competition Flow, and Enhancement Proposals

---

## 1. Initial Database Schema

### 1.1 Tables and Columns

#### **Competitions**
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| Name             | nvarchar         | 200        | No       |             |
| Description      | nvarchar         | 1000       | Yes      |             |
| StartDate        | datetime2        | 8          | No       |             |
| EndDate          | datetime2        | 8          | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| MaxTeams         | int              | 4          | No       |             |
| CurrentTeams     | int              | 4          | No       |             |
| EntryFee         | decimal          | 9          | No       |             |
| PrizeFirst       | nvarchar         | 200        | Yes      |             |
| PrizeSecond      | nvarchar         | 200        | Yes      |             |
| PrizeThird       | nvarchar         | 200        | Yes      |             |
| TotalPrizePool   | decimal          | 9          | Yes      |             |
| Rules            | nvarchar         | 2000       | Yes      |             |
| IsPublic         | bit              | 1          | No       |             |
| AllowSpectators  | bit              | 1          | No       |             |
| MaxGamesPerTeam  | int              | 4          | No       |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| UpdatedAt        | datetime2        | 8          | No       |             |

#### **CompetitionTeamInvites**
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| CompetitionId    | uniqueidentifier | 16         | No       | FK to Competitions(Id) |
| TeamId           | uniqueidentifier | 16         | No       | FK to CompetitionTeams(Id) |
| InviterId        | uniqueidentifier | 16         | No       | FK to Users(Id) |
| InviteeId        | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| InviteCode       | nvarchar         | 20         | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| ExpiresAt        | datetime2        | 8          | No       |             |
| AcceptedAt       | datetime2        | 8          | Yes      |             |

#### **CompetitionTeams**
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| CompetitionId    | uniqueidentifier | 16         | No       | FK to Competitions(Id) |
| TeamName         | nvarchar         | 100        | No       |             |
| Player1Id        | uniqueidentifier | 16         | No       | FK to Users(Id) |
| Player2Id        | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| InviteCode       | nvarchar         | 20         | No       |             |
| GamesPlayed      | int              | 4          | No       |             |
| Points           | int              | 4          | No       |             |
| BonusPoints      | int              | 4          | No       |             |
| MaxGames         | int              | 4          | No       |             |
| IsActive         | bit              | 1          | No       |             |
| IsComplete       | bit              | 1          | No       |             |
| RegisteredAt     | datetime2        | 8          | No       |             |
| CompletedAt      | datetime2        | 8          | Yes      |             |

#### **Games**
| Column Name      | Data Type        | Max Length | Nullable | Description |
|------------------|------------------|------------|----------|-------------|
| Id               | uniqueidentifier | 16         | No       | Primary Key |
| LobbyCode        | nvarchar         | 12         | No       |             |
| CompetitionId    | uniqueidentifier | 16         | Yes      | FK to Competitions(Id) |
| Team1Player1Id   | uniqueidentifier | 16         | No       | FK to Users(Id) |
| Team1Player2Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team2Player1Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team2Player2Id   | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| Team1Name        | nvarchar         | 100        | No       |             |
| Team2Name        | nvarchar         | 100        | No       |             |
| Status           | nvarchar         | 40         | No       |             |
| DealerId         | uniqueidentifier | 16         | Yes      | FK to Users(Id) |
| TrumpSuit        | nvarchar         | 20         | Yes      |             |
| CurrentBall      | int              | 4          | No       |             |
| CurrentHand      | int              | 4          | No       |             |
| Team1Score       | int              | 4          | No       |             |
| Team2Score       | int              | 4          | No       |             |
| Team1BallsWon    | int              | 4          | No       |             |
| Team2BallsWon    | int              | 4          | No       |             |
| WinnerTeam       | int              | 4          | Yes      |             |
| StartedAt        | datetime2        | 8          | Yes      |             |
| CompletedAt      | datetime2        | 8          | Yes      |             |
| CreatedAt        | datetime2        | 8          | No       |             |
| UpdatedAt        | datetime2        | 8          | No       |             |

#### **GameBalls, GameHands, PlayedCards, Users, GameSettings**
- See original schema for full details (not shown here for brevity, but available in DatabaseSchema.md).

### 1.2 Foreign Key Relationships

| Foreign Key Name                  | Parent Table      | Parent Column | Referencing Table         | Referencing Column |
|-----------------------------------|-------------------|---------------|--------------------------|--------------------|
| FK__Competiti__Compe__619B8048    | Competitions      | Id            | CompetitionTeamInvites    | CompetitionId      |
| FK__Competiti__Compe__571DF1D5    | Competitions      | Id            | CompetitionTeams          | CompetitionId      |
| FK__Games__Competiti__787EE5A0    | Competitions      | Id            | Games                    | CompetitionId      |
| FK__Competiti__TeamI__628FA481    | CompetitionTeams  | Id            | CompetitionTeamInvites    | TeamId             |
| FK__PlayedCar__GameH__236943A5    | GameHands         | Id            | PlayedCards              | GameHandId         |
| FK__GameBalls__GameI__06CD04F7    | Games             | Id            | GameBalls                | GameId             |
| FK__GameHands__GameI__18EBB532    | Games             | Id            | GameHands                | GameId             |
| FK__Competiti__Invit__6383C8BA    | Users             | Id            | CompetitionTeamInvites    | InviterId          |
| FK__Competiti__Invit__6477ECF3    | Users             | Id            | CompetitionTeamInvites    | InviteeId          |
| FK__Competiti__Playe__5812160E    | Users             | Id            | CompetitionTeams          | Player1Id          |
| FK__Competiti__Playe__59063A47    | Users             | Id            | CompetitionTeams          | Player2Id          |
| FK__GameHands__Winne__19DFD96B    | Users             | Id            | GameHands                | WinnerPlayerId     |
| FK__Games__DealerId__7D439ABD     | Users             | Id            | Games                    | DealerId           |
| FK__Games__Team1Play__797309D9    | Users             | Id            | Games                    | Team1Player1Id     |
| FK__Games__Team1Play__7A672E12    | Users             | Id            | Games                    | Team1Player2Id     |
| FK__Games__Team2Play__7B5B524B    | Users             | Id            | Games                    | Team2Player1Id     |
| FK__Games__Team2Play__7C4F7684    | Users             | Id            | Games                    | Team2Player2Id     |
| FK_Games_Team1Player2Id           | Users             | Id            | Games                    | Team1Player2Id     |
| FK_Games_Team2Player1Id           | Users             | Id            | Games                    | Team2Player1Id     |
| FK_Games_Team2Player2Id           | Users             | Id            | Games                    | Team2Player2Id     |
| FK__PlayedCar__Playe__245D67DE    | Users             | Id            | PlayedCards              | PlayerId           |

---

## 2. Competition Flow and Phases

### 2.1 Leaderboard and Match Phase
- Teams compete within a competition, and matches are found based on teams registered in that competition.
- At the end of each game, game data is saved to the API and database to calculate and award points and bonus points.

#### Points System
- **3 points** are awarded for a win.
- **Bonus 1 point** is awarded if a team wins by 6 or more balls.

### 2.2 Competition Time Frame
- Each competition has a deadline by which teams must complete their games.
- After the deadline, teams can no longer play additional games in that phase.
- All points and bonus points are used to generate the leaderboard.
- The **top 32 teams** on the leaderboard advance to the next phase; all other teams are eliminated from the competition.

### 2.3 Top 32 Phase
- The leaderboard now consists only of the top 32 teams.
- Matchmaking is restricted to these teams.
- There is a maximum number of games (e.g., 10) that can be played in this phase.
- This phase also has an end date.
- Points and bonus points are again used to update the leaderboard.
- The **top 16 teams** advance to the next phase; all others are eliminated.

### 2.4 Top 16 Phase (GoldRush Event)
- The top 16 teams are invited to the GoldRush event.
- This phase is a knockout round: teams are placed into lobbies created by the admin.
- The winning team in each match advances to the next round (**Top 8**).

### 2.5 Top 8 Phase (GoldRush Event)
- The top 8 teams continue in knockout format.
- Teams are placed into admin-created lobbies.
- Winners advance to the **Top 4** round.

### 2.6 Top 4 Phase (GoldRush Event)
- The top 4 teams continue in knockout format.
- Teams are placed into admin-created lobbies.
- Winners advance to the **Final Phase** (Top 2).

### 2.7 Final Phase (GoldRush Event)
- The final 2 teams compete in a knockout match.
- The winning team is declared the competition winner.

---

## 3. Proposed Database Changes for Competition Phases

### 3.1 Phase Tracking and Progression
- **Add a `Phase` column to `Competitions`**: Tracks the current phase (e.g., "Leaderboard", "Top32", "Top16", etc.).
- **Add a `Phase` column to `CompetitionTeams`**: Indicates which phase the team is currently in or was last in.
- **Add an `IsEliminated` column to `CompetitionTeams`**: Indicates if the team has been eliminated from the competition.

### 3.2 Phase Deadlines & Game Limits
- **Add a `PhaseEndDate` column to `Competitions`**: End date for the current phase.
- **Add a `MaxGamesPerPhase` column to `Competitions`**: Maximum games allowed per team in the current phase.

### 3.3 Knockout Rounds & Admin Lobbies
- **Create a new table: `CompetitionPhaseLobbies`**
  - `Id` (uniqueidentifier, PK)
  - `CompetitionId` (uniqueidentifier, FK)
  - `Phase` (nvarchar(40))
  - `LobbyCode` (nvarchar(12))
  - `CreatedByAdminId` (uniqueidentifier, FK to Users)
  - `CreatedAt` (datetime2)
- **Create a new table: `CompetitionPhaseLobbyTeams`**
  - `Id` (uniqueidentifier, PK)
  - `LobbyId` (uniqueidentifier, FK to CompetitionPhaseLobbies)
  - `CompetitionTeamId` (uniqueidentifier, FK)
  - `IsWinner` (bit)
  - `EliminatedAt` (datetime2, nullable)

### 3.4 Tracking Advancement & Elimination
- **Add an `AdvancedToNextPhase` column to `CompetitionTeams`**: Indicates if the team advanced to the next phase.
- **Add a `PhaseEliminatedAt` column to `CompetitionTeams`**: When the team was eliminated (if applicable).

### 3.5 Points & Bonus Points Calculation Per Phase
- **Create a new table: `CompetitionTeamPhaseStats`**
  - `Id` (uniqueidentifier, PK)
  - `CompetitionTeamId` (uniqueidentifier, FK)
  - `Phase` (nvarchar(40))
  - `Points` (int)
  - `BonusPoints` (int)
  - `GamesPlayed` (int)
  - `BallsWon` (int)
  - `CreatedAt` (datetime2)
  - `UpdatedAt` (datetime2)

### 3.6 General Recommendations
- **Indexes:** Add indexes on new columns used for filtering (e.g., `Phase`, `IsEliminated`, `AdvancedToNextPhase`).
- **Foreign Keys:** Ensure all new relationships are enforced with foreign keys.
- **Data Migration:** Plan for migration scripts to populate new columns for existing competitions/teams.

### 3.7 Example: New/Altered Table Definitions

```sql
-- Example: Add Phase to Competitions
ALTER TABLE Competitions ADD Phase nvarchar(40) NOT NULL DEFAULT 'Leaderboard';
ALTER TABLE Competitions ADD PhaseEndDate datetime2 NULL;
ALTER TABLE Competitions ADD MaxGamesPerPhase int NULL;

-- Example: Add Phase and Elimination Tracking to CompetitionTeams
ALTER TABLE CompetitionTeams ADD Phase nvarchar(40) NOT NULL DEFAULT 'Leaderboard';
ALTER TABLE CompetitionTeams ADD IsEliminated bit NOT NULL DEFAULT 0;
ALTER TABLE CompetitionTeams ADD AdvancedToNextPhase bit NOT NULL DEFAULT 0;
ALTER TABLE CompetitionTeams ADD PhaseEliminatedAt datetime2 NULL;

-- Example: New Table for Admin Lobbies
CREATE TABLE CompetitionPhaseLobbies (
    Id uniqueidentifier PRIMARY KEY,
    CompetitionId uniqueidentifier NOT NULL,
    Phase nvarchar(40) NOT NULL,
    LobbyCode nvarchar(12) NOT NULL,
    CreatedByAdminId uniqueidentifier NOT NULL,
    CreatedAt datetime2 NOT NULL,
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
    FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id)
);

CREATE TABLE CompetitionPhaseLobbyTeams (
    Id uniqueidentifier PRIMARY KEY,
    LobbyId uniqueidentifier NOT NULL,
    CompetitionTeamId uniqueidentifier NOT NULL,
    IsWinner bit NOT NULL DEFAULT 0,
    EliminatedAt datetime2 NULL,
    FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id),
    FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);

-- Example: New Table for Phase Stats
CREATE TABLE CompetitionTeamPhaseStats (
    Id uniqueidentifier PRIMARY KEY,
    CompetitionTeamId uniqueidentifier NOT NULL,
    Phase nvarchar(40) NOT NULL,
    Points int NOT NULL DEFAULT 0,
    BonusPoints int NOT NULL DEFAULT 0,
    GamesPlayed int NOT NULL DEFAULT 0,
    BallsWon int NOT NULL DEFAULT 0,
    CreatedAt datetime2 NOT NULL,
    UpdatedAt datetime2 NOT NULL,
    FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);
```

### 3.8 Summary Table

| Change Area                | Table(s) Affected                | New/Altered Columns/Tables                |
|----------------------------|----------------------------------|-------------------------------------------|
| Phase Tracking             | Competitions, CompetitionTeams   | Phase, PhaseEndDate, MaxGamesPerPhase     |
| Elimination/Advancement    | CompetitionTeams                 | IsEliminated, AdvancedToNextPhase, PhaseEliminatedAt |
| Knockout Lobbies           | (new) CompetitionPhaseLobbies, CompetitionPhaseLobbyTeams | All columns as above |
| Per-Phase Stats            | (new) CompetitionTeamPhaseStats  | All columns as above                      |

---

## 4. Next Steps

- Review and refine the proposed schema changes with your development team.
- Update application logic to utilize new columns/tables for phase progression, elimination, and admin lobbies.
- Plan and test migration scripts for existing data.

--- 