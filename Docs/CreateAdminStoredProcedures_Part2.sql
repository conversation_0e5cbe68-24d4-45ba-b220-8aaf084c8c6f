-- =============================================
-- Competition Teams Management Procedures
-- =============================================

CREATE PROCEDURE SP_GetCompetitionTeamsAdmin
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        ct.Id,
        ct.CompetitionId,
        ct.TeamName,
        ct.Player1Id,
        u1.Username as Player1Username,
        u1.Email as Player1Email,
        ct.Player2Id,
        u2.Username as Player2Username,
        u2.Email as Player2Email,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        ct.MaxGames,
        ct.IsActive,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    INNER JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId
    ORDER BY ct.Points DESC, ct.RegisteredAt ASC
END
GO

CREATE PROCEDURE SP_DeleteCompetitionTeam
    @TeamId UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if team exists
    IF NOT EXISTS (SELECT 1 FROM CompetitionTeams WHERE Id = @TeamId)
    BEGIN
        RAISERROR('Team not found', 16, 1)
        RETURN
    END

    -- Get competition ID for updating current teams count
    DECLARE @CompetitionId UNIQUEIDENTIFIER
    SELECT @CompetitionId = CompetitionId FROM CompetitionTeams WHERE Id = @TeamId

    -- Delete related games first
    DELETE FROM Games 
    WHERE CompetitionId = @CompetitionId 
    AND (
        (Team1Player1Id IN (SELECT Player1Id FROM CompetitionTeams WHERE Id = @TeamId) 
         AND Team1Player2Id IN (SELECT Player2Id FROM CompetitionTeams WHERE Id = @TeamId))
        OR
        (Team2Player1Id IN (SELECT Player1Id FROM CompetitionTeams WHERE Id = @TeamId) 
         AND Team2Player2Id IN (SELECT Player2Id FROM CompetitionTeams WHERE Id = @TeamId))
    )

    -- Delete the team
    DELETE FROM CompetitionTeams WHERE Id = @TeamId

    -- Update competition current teams count
    UPDATE Competitions 
    SET CurrentTeams = (SELECT COUNT(*) FROM CompetitionTeams WHERE CompetitionId = @CompetitionId)
    WHERE Id = @CompetitionId

    SELECT 1 as Success
END
GO

-- =============================================
-- Games Management Procedures
-- =============================================

CREATE PROCEDURE SP_GetCompetitionGamesAdmin
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT 
        g.Id,
        g.LobbyCode,
        g.CompetitionId,
        c.Name as CompetitionName,
        g.Team1Name,
        g.Team2Name,
        g.Status,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        g.StartedAt,
        g.CompletedAt,
        g.CreatedAt
    FROM Games g
    INNER JOIN Competitions c ON g.CompetitionId = c.Id
    WHERE g.CompetitionId = @CompetitionId
    ORDER BY g.CreatedAt DESC
END
GO

CREATE PROCEDURE SP_GetAllGamesAdmin
AS
BEGIN
    SELECT 
        g.Id,
        g.LobbyCode,
        g.CompetitionId,
        c.Name as CompetitionName,
        g.Team1Name,
        g.Team2Name,
        g.Status,
        g.Team1Score,
        g.Team2Score,
        g.Team1BallsWon,
        g.Team2BallsWon,
        g.WinnerTeam,
        g.StartedAt,
        g.CompletedAt,
        g.CreatedAt
    FROM Games g
    LEFT JOIN Competitions c ON g.CompetitionId = c.Id
    ORDER BY g.CreatedAt DESC
END
GO

-- =============================================
-- Email Management Procedures
-- =============================================

CREATE PROCEDURE SP_GetCompetitionPlayerEmails
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SELECT DISTINCT u.Email, u.Username
    FROM CompetitionTeams ct
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    INNER JOIN Users u2 ON ct.Player2Id = u2.Id
    CROSS APPLY (
        SELECT u1.Email, u1.Username
        UNION
        SELECT u2.Email, u2.Username
    ) u
    WHERE ct.CompetitionId = @CompetitionId
    AND u.Email IS NOT NULL
    ORDER BY u.Username
END
GO

PRINT 'All admin stored procedures created successfully!'
