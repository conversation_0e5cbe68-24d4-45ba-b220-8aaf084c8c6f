# Ball Data Analysis Queries

## Overview

This document provides SQL queries to analyze the saved ball data from competition games. The enhanced GameBalls table now contains comprehensive information about each ball played, including team composition, special calls, and competition context.

## Database Schema Reference

### GameBalls Table Structure
```sql
-- Core ball information
Id UNIQUEIDENTIFIER PRIMARY KEY
GameId UNIQUEIDENTIFIER (FK to Games)
CompetitionId UNIQUEIDENTIFIER (FK to Competitions)
BallNumber INT
WinnerTeam INT (1 or 2)

-- Scores and ball counts
Team1Score INT
Team2Score INT
Team1BallsWon INT
Team2BallsWon INT

-- Team composition
Team1Player1Id UNIQUEIDENTIFIER (FK to Users)
Team1Player2Id UNIQUEIDENTIFIER (FK to Users)
Team2Player1Id UNIQUEIDENTIFIER (FK to Users)
Team2Player2Id UNIQUEIDENTIFIER (FK to Users)
Team1Name NVARCHAR(50)
Team2Name NVARCHAR(50)

-- Game state
TrumpSuit NVARCHAR(10)
HasThuneeDouble BIT
HasKhanka BIT
SpecialCallType NVARCHAR(20)
SpecialCallResult NVARCHAR(20)

-- Timestamps
CompletedAt DATETIME2
CreatedAt DATETIME2
```

## Analysis Queries

### 1. Competition Ball Summary
```sql
-- Get ball statistics for a specific competition
SELECT 
    c.Name AS CompetitionName,
    COUNT(*) AS TotalBalls,
    COUNT(CASE WHEN HasThuneeDouble = 1 THEN 1 END) AS ThuneeDoubleBalls,
    COUNT(CASE WHEN HasKhanka = 1 THEN 1 END) AS KhankaBalls,
    COUNT(CASE WHEN SpecialCallType = 'four_ball' THEN 1 END) AS FourBallPenalties,
    AVG(CAST(Team1Score + Team2Score AS FLOAT)) AS AvgPointsPerBall
FROM GameBalls gb
JOIN Competitions c ON gb.CompetitionId = c.Id
WHERE gb.CompetitionId = @CompetitionId
GROUP BY c.Name;
```

### 2. Team Performance Analysis
```sql
-- Analyze team performance across all their competition games
SELECT 
    gb.Team1Name,
    COUNT(*) AS BallsPlayed,
    COUNT(CASE WHEN WinnerTeam = 1 THEN 1 END) AS BallsWon,
    CAST(COUNT(CASE WHEN WinnerTeam = 1 THEN 1 END) AS FLOAT) / COUNT(*) * 100 AS WinPercentage,
    AVG(CASE WHEN WinnerTeam = 1 THEN Team1Score ELSE Team2Score END) AS AvgPointsScored,
    AVG(CASE WHEN WinnerTeam = 1 THEN Team2Score ELSE Team1Score END) AS AvgPointsConceded
FROM GameBalls gb
WHERE gb.CompetitionId = @CompetitionId
  AND (gb.Team1Player1Id = @PlayerId OR gb.Team1Player2Id = @PlayerId 
       OR gb.Team2Player1Id = @PlayerId OR gb.Team2Player2Id = @PlayerId)
GROUP BY gb.Team1Name

UNION ALL

SELECT 
    gb.Team2Name,
    COUNT(*) AS BallsPlayed,
    COUNT(CASE WHEN WinnerTeam = 2 THEN 1 END) AS BallsWon,
    CAST(COUNT(CASE WHEN WinnerTeam = 2 THEN 1 END) AS FLOAT) / COUNT(*) * 100 AS WinPercentage,
    AVG(CASE WHEN WinnerTeam = 2 THEN Team2Score ELSE Team1Score END) AS AvgPointsScored,
    AVG(CASE WHEN WinnerTeam = 2 THEN Team1Score ELSE Team2Score END) AS AvgPointsConceded
FROM GameBalls gb
WHERE gb.CompetitionId = @CompetitionId
  AND (gb.Team1Player1Id = @PlayerId OR gb.Team1Player2Id = @PlayerId 
       OR gb.Team2Player1Id = @PlayerId OR gb.Team2Player2Id = @PlayerId);
```

### 3. Player Individual Statistics
```sql
-- Get individual player statistics across competitions
SELECT 
    u.Username AS PlayerName,
    COUNT(*) AS BallsPlayed,
    COUNT(CASE 
        WHEN (gb.Team1Player1Id = u.Id OR gb.Team1Player2Id = u.Id) AND WinnerTeam = 1 THEN 1
        WHEN (gb.Team2Player1Id = u.Id OR gb.Team2Player2Id = u.Id) AND WinnerTeam = 2 THEN 1
    END) AS BallsWon,
    COUNT(CASE WHEN HasThuneeDouble = 1 THEN 1 END) AS ThuneeDoubleBalls,
    COUNT(CASE WHEN HasKhanka = 1 THEN 1 END) AS KhankaBalls,
    COUNT(CASE WHEN SpecialCallResult = 'success' THEN 1 END) AS SuccessfulSpecialCalls
FROM Users u
JOIN GameBalls gb ON (gb.Team1Player1Id = u.Id OR gb.Team1Player2Id = u.Id 
                     OR gb.Team2Player1Id = u.Id OR gb.Team2Player2Id = u.Id)
WHERE gb.CompetitionId = @CompetitionId
GROUP BY u.Id, u.Username
ORDER BY BallsWon DESC;
```

### 4. Special Call Success Rates
```sql
-- Analyze success rates of special calls
SELECT 
    SpecialCallType,
    COUNT(*) AS TotalCalls,
    COUNT(CASE WHEN SpecialCallResult = 'success' THEN 1 END) AS SuccessfulCalls,
    CAST(COUNT(CASE WHEN SpecialCallResult = 'success' THEN 1 END) AS FLOAT) / COUNT(*) * 100 AS SuccessRate
FROM GameBalls
WHERE CompetitionId = @CompetitionId 
  AND SpecialCallType IS NOT NULL
GROUP BY SpecialCallType
ORDER BY SuccessRate DESC;
```

### 5. Trump Suit Analysis
```sql
-- Analyze performance by trump suit
SELECT 
    TrumpSuit,
    COUNT(*) AS BallsPlayed,
    AVG(CAST(Team1Score + Team2Score AS FLOAT)) AS AvgTotalPoints,
    COUNT(CASE WHEN HasThuneeDouble = 1 THEN 1 END) AS ThuneeDoubleCount,
    COUNT(CASE WHEN HasKhanka = 1 THEN 1 END) AS KhankaCount
FROM GameBalls
WHERE CompetitionId = @CompetitionId 
  AND TrumpSuit IS NOT NULL
GROUP BY TrumpSuit
ORDER BY BallsPlayed DESC;
```

### 6. Game Progression Analysis
```sql
-- Analyze how games progress (ball by ball)
SELECT 
    g.LobbyCode,
    gb.BallNumber,
    gb.WinnerTeam,
    gb.Team1BallsWon,
    gb.Team2BallsWon,
    gb.Team1Score,
    gb.Team2Score,
    gb.SpecialCallType,
    gb.CompletedAt
FROM Games g
JOIN GameBalls gb ON g.Id = gb.GameId
WHERE g.CompetitionId = @CompetitionId
ORDER BY g.LobbyCode, gb.BallNumber;
```

### 7. Hand-Level Analysis
```sql
-- Detailed hand analysis for balls
SELECT 
    gb.BallNumber,
    gb.Team1Name,
    gb.Team2Name,
    gh.HandNumber,
    u.Username AS HandWinner,
    gh.Points AS HandPoints,
    gb.WinnerTeam AS BallWinner
FROM GameBalls gb
JOIN GameHands gh ON gb.Id = gh.GameBallId
JOIN Users u ON gh.WinnerPlayerId = u.Id
WHERE gb.CompetitionId = @CompetitionId
ORDER BY gb.BallNumber, gh.HandNumber;
```

### 8. Competition Leaderboard with Ball Data
```sql
-- Enhanced leaderboard with ball-level statistics
SELECT 
    ct.TeamName,
    ct.Points AS LeaderboardPoints,
    ct.BonusPoints,
    COUNT(gb.Id) AS BallsPlayed,
    COUNT(CASE WHEN gb.WinnerTeam = 1 AND (gb.Team1Player1Id = ct.Player1Id OR gb.Team1Player2Id = ct.Player1Id)
               OR gb.WinnerTeam = 2 AND (gb.Team2Player1Id = ct.Player1Id OR gb.Team2Player2Id = ct.Player1Id) THEN 1 END) AS BallsWon,
    COUNT(CASE WHEN gb.HasThuneeDouble = 1 THEN 1 END) AS ThuneeDoubleBalls,
    COUNT(CASE WHEN gb.HasKhanka = 1 THEN 1 END) AS KhankaBalls
FROM CompetitionTeams ct
LEFT JOIN GameBalls gb ON gb.CompetitionId = ct.CompetitionId 
    AND ((gb.Team1Player1Id = ct.Player1Id OR gb.Team1Player2Id = ct.Player1Id OR gb.Team1Player1Id = ct.Player2Id OR gb.Team1Player2Id = ct.Player2Id)
         OR (gb.Team2Player1Id = ct.Player1Id OR gb.Team2Player2Id = ct.Player1Id OR gb.Team2Player1Id = ct.Player2Id OR gb.Team2Player2Id = ct.Player2Id))
WHERE ct.CompetitionId = @CompetitionId
GROUP BY ct.Id, ct.TeamName, ct.Points, ct.BonusPoints
ORDER BY ct.Points + ct.BonusPoints DESC;
```

## Usage Examples

### Get Competition Summary
```sql
DECLARE @CompetitionId UNIQUEIDENTIFIER = 'your-competition-id-here';
-- Run any of the above queries with this competition ID
```

### Get Player Statistics
```sql
DECLARE @CompetitionId UNIQUEIDENTIFIER = 'your-competition-id-here';
DECLARE @PlayerId UNIQUEIDENTIFIER = 'your-player-id-here';
-- Run player-specific queries
```

## Performance Considerations

1. **Indexes**: Consider adding indexes on frequently queried columns:
   ```sql
   CREATE INDEX IX_GameBalls_CompetitionId ON GameBalls(CompetitionId);
   CREATE INDEX IX_GameBalls_Players ON GameBalls(Team1Player1Id, Team1Player2Id, Team2Player1Id, Team2Player2Id);
   CREATE INDEX IX_GameBalls_SpecialCalls ON GameBalls(SpecialCallType, SpecialCallResult);
   ```

2. **Date Filtering**: Add date filters for large datasets:
   ```sql
   WHERE gb.CompletedAt >= '2024-01-01' AND gb.CompletedAt < '2025-01-01'
   ```

3. **Pagination**: Use OFFSET/FETCH for large result sets:
   ```sql
   ORDER BY gb.CompletedAt DESC
   OFFSET 0 ROWS FETCH NEXT 50 ROWS ONLY;
   ```
