# Ball Data Saving - Deployment Guide

## Overview

This guide covers the deployment steps required to implement ball data saving functionality for competition games.

## Prerequisites

- Database access to the production SQL Server
- API deployment access
- Node.js server deployment access

## Deployment Steps

### 1. Database Changes

#### Step 1.1: Run Migration Script
Execute the migration script to add new columns to existing tables:

```sql
-- Run this script on the production database
-- File: ThuneeAPI/ThuneeAPI.Infrastructure/Data/Scripts/Migration_AddBallResultFields.sql
```

**Important**: This script is safe to run multiple times as it checks for existing columns before adding them.

#### Step 1.2: Create Stored Procedure
Execute the stored procedure creation script:

```sql
-- Run this script on the production database
-- File: ThuneeAPI/ThuneeAPI.Infrastructure/Data/Scripts/SP_RecordBallResult.sql
```

#### Step 1.3: Verify Database Changes
Run these queries to verify the changes:

```sql
-- Check GameBalls table structure
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'GameBalls'
ORDER BY ORDINAL_POSITION;

-- Check GameHands table structure
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'GameHands'
ORDER BY ORDINAL_POSITION;

-- Verify stored procedure exists
SELECT name FROM sys.procedures WHERE name = 'SP_RecordBallResult';
```

### 2. API Deployment

#### Step 2.1: Build and Deploy API
1. Build the ThuneeAPI project with the new changes
2. Deploy to the production API server
3. Verify the new endpoint is available:

```
GET https://your-api-domain/swagger/index.html
```

Look for the new endpoint:
```
POST /api/games/{lobbyCode}/ball-result
```

#### Step 2.2: Test API Endpoint
Use a tool like Postman to test the new endpoint:

```json
POST /api/games/TEST123/ball-result
Authorization: Bearer {your-jwt-token}
Content-Type: application/json

{
  "ballNumber": 1,
  "winnerTeam": 1,
  "team1Score": 65,
  "team2Score": 40,
  "team1BallsWon": 1,
  "team2BallsWon": 0,
  "trumpSuit": "hearts",
  "hasThuneeDouble": false,
  "hasKhanka": false,
  "hands": []
}
```

### 3. Node.js Server Deployment

#### Step 3.1: Deploy Server Code
1. Copy the updated server files to the production server
2. Restart the Node.js server
3. Verify the server starts without errors

#### Step 3.2: Check Server Logs
Monitor the server logs for any errors related to the new ball saving functionality:

```bash
# Check for any errors in the logs
tail -f /path/to/server/logs/server.log | grep "BALL_DATA"
```

### 4. Verification

#### Step 4.1: Create Test Competition Game
1. Create a competition in the admin panel
2. Register teams for the competition
3. Start a competition game

#### Step 4.2: Play Through a Ball
1. Play through all 6 hands of a ball
2. Check the server logs for ball saving messages:
   ```
   [GAME_DATA] Ball result save initiated for competition game {lobbyCode} ball {ballNumber}
   ```

#### Step 4.3: Verify Database Records
Check that data was saved correctly:

```sql
-- Check latest ball records
SELECT TOP 10 * FROM GameBalls 
ORDER BY CreatedAt DESC;

-- Check hands linked to balls
SELECT gb.BallNumber, gb.WinnerTeam, gh.HandNumber, gh.WinnerPlayerId
FROM GameBalls gb
LEFT JOIN GameHands gh ON gb.Id = gh.GameBallId
WHERE gb.GameId = (SELECT TOP 1 Id FROM Games WHERE CompetitionId IS NOT NULL ORDER BY CreatedAt DESC)
ORDER BY gb.BallNumber, gh.HandNumber;
```

### 5. Monitoring

#### Step 5.1: Set Up Monitoring
Monitor these key metrics:
- Ball saving success rate
- API response times for ball-result endpoint
- Database performance for ball-related queries

#### Step 5.2: Error Monitoring
Watch for these potential issues:
- Failed API calls to ball-result endpoint
- Database timeout errors
- Missing ball data for competition games

### 6. Rollback Plan

If issues occur, follow this rollback procedure:

#### Step 6.1: Revert Node.js Server
1. Deploy the previous version of the server code
2. Restart the Node.js server
3. Verify games continue to work normally

#### Step 6.2: Disable Ball Saving (Alternative)
If you need to keep the new API but disable ball saving:

1. Edit `server/utils/gameDataUtils.js`
2. Modify the `saveBallEndResult` function to return early:

```javascript
function saveBallEndResult(lobby, ballData, hostPlayerId, lobbyCode) {
  // Temporarily disable ball saving
  console.log(`[GAME_DATA] Ball saving temporarily disabled for ${lobbyCode}`);
  return;
  
  // ... rest of function
}
```

#### Step 6.3: Database Rollback (Last Resort)
Only if absolutely necessary:

```sql
-- Remove foreign key constraint
ALTER TABLE GameHands DROP CONSTRAINT FK_GameHands_GameBalls;

-- Remove added columns (WARNING: This will lose data)
ALTER TABLE GameBalls DROP COLUMN Team1BallsWon;
ALTER TABLE GameBalls DROP COLUMN Team2BallsWon;
ALTER TABLE GameBalls DROP COLUMN TrumpSuit;
ALTER TABLE GameBalls DROP COLUMN HasThuneeDouble;
ALTER TABLE GameBalls DROP COLUMN HasKhanka;
ALTER TABLE GameBalls DROP COLUMN SpecialCallType;
ALTER TABLE GameBalls DROP COLUMN SpecialCallResult;
ALTER TABLE GameHands DROP COLUMN GameBallId;

-- Remove stored procedure
DROP PROCEDURE SP_RecordBallResult;
```

### 7. Post-Deployment Checklist

- [ ] Database migration completed successfully
- [ ] Stored procedure created
- [ ] API deployed and new endpoint available
- [ ] Node.js server deployed and running
- [ ] Test competition game created and played
- [ ] Ball data saving verified in database
- [ ] No errors in server logs
- [ ] Regular games still work normally
- [ ] Competition games save ball data correctly

### 8. Support

If you encounter issues during deployment:

1. Check the server logs for detailed error messages
2. Verify database connectivity from the API
3. Ensure all required columns exist in the database
4. Test the API endpoint independently
5. Verify the Node.js server can reach the API

For troubleshooting, the most common issues are:
- Missing database columns (run migration script)
- API connectivity issues (check network/firewall)
- Authentication problems (verify JWT tokens)
- Database permission issues (check SQL Server permissions)
