# Thunee Application Deployment Guide
## IIS Windows Server Deployment

This guide covers the complete deployment process for the Thunee application to your IIS Windows server.

## Overview

Your Thunee application consists of three main components:
1. **Frontend (React/Vite)** - Deployed to IIS on port 96
2. **ASP.NET Core API** - Deployed to IIS on port 8080
3. **Node.js Game Server** - Runs standalone on port 3001

## Prerequisites

- IIS Windows Server with Node.js installed
- ASP.NET Core Runtime installed
- Access to server directories:
  - `C:\inetpub\Thunee-Full` (Frontend)
  - `C:\inetpub\Thunee-Production` (Node.js Server)
  - API deployment location (as configured in IIS)

## Production URLs

- **Frontend**: `http://**************:96`
- **API**: `http://**************:8080/api`
- **Game Server**: `http://**************:3001`

## 1. Frontend Deployment

### Build Process
```bash
# From your development machine (C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE)
npm run build:production
```

### Deployment Steps
1. **Copy build files** to server:
   ```
   Source: dist/
   Destination: C:\inetpub\Thunee-Full\
   ```

2. **Verify environment configuration**:
   - Ensure `.env.production` contains:
     ```
     VITE_API_BASE_URL=http://**************:8080/api
     VITE_SOCKET_URL=http://**************:3001
     VITE_APP_ENV=production
     ```

3. **IIS Configuration**:
   - Site should be configured to serve from `C:\inetpub\Thunee-Full`
   - Port: 96
   - Ensure proper MIME types for .js, .css, .svg files

## 2. ASP.NET Core API Deployment

### Build Process
```bash
# From ThuneeAPI directory
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
```

### Deployment Steps
1. **Copy published files** to IIS directory
2. **Verify web.config** contains:
   ```xml
   <configuration>
     <system.webServer>
       <handlers>
         <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
       </handlers>
       <aspNetCore processPath=".\ThuneeAPI.exe" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess">
         <environmentVariables>
           <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Development" />
         </environmentVariables>
       </aspNetCore>
     </system.webServer>
   </configuration>
   ```

3. **IIS Site Configuration**:
   - Port: 8080
   - Application Pool: .NET Core
   - Physical Path: Points to published API files

## 3. Node.js Game Server Deployment

### Preparation
1. **Copy server files** to `C:\inetpub\Thunee-Production\server\`
2. **Install dependencies**:
   ```bash
   cd C:\inetpub\Thunee-Production\server
   npm install
   ```

### Environment Configuration
Create/update `C:\inetpub\Thunee-Production\server\.env`:
```env
NODE_ENV=production
API_BASE_URL=http://**************:8080
API_TIMEOUT=10000
JWT_SECRET=your_production_jwt_secret_here
LOG_LEVEL=info
LOBBY_TIMEOUT_MINUTES=30
GAME_TIMEOUT_MINUTES=60
MAX_RECONNECT_ATTEMPTS=3
```

**⚠️ IMPORTANT**: Ensure `API_BASE_URL` does NOT include `/api` suffix to avoid double path issues.

### Deployment Steps
1. **Stop existing server** (if running):
   ```bash
   # Find and kill existing Node.js processes if needed
   tasklist | findstr node
   taskkill /PID [process_id] /F
   ```

2. **Start the server**:
   ```bash
   cd C:\inetpub\Thunee-Production
   npm start
   ```

3. **Verify server startup**:
   - Should show: "Server running on port 3001"
   - Should show: "Competition service initialized"
   - No authentication errors in logs

## 4. Complete Deployment Process

### Step-by-Step Deployment

1. **Build Frontend**:
   ```bash
   cd C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE
   npm run build:production
   ```

2. **Deploy Frontend**:
   - Copy `dist/*` to `C:\inetpub\Thunee-Full\`

3. **Build API**:
   ```bash
   cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
   dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
   ```

4. **Deploy API**:
   - Copy published files to IIS API directory
   - Restart IIS site

5. **Deploy Node.js Server**:
   ```bash
   # Copy server files to C:\inetpub\Thunee-Production\server\
   cd C:\inetpub\Thunee-Production\server
   npm install
   # Update .env file with correct configuration
   cd ..
   npm start
   ```

## 5. Verification Steps

### Frontend Verification
1. Navigate to `http://**************:96`
2. Verify page loads correctly
3. Check browser console for errors

### API Verification
```powershell
Invoke-RestMethod -Uri 'http://**************:8080/api/competitions' -Method GET
```
Should return successful response.

### Game Server Verification
1. Check server logs for successful startup
2. Test authentication flow
3. Verify no connection errors in logs

### Integration Test
1. Login through frontend
2. Join/create a game lobby
3. Verify real-time communication works

## 6. Troubleshooting

### Common Issues

**Frontend not loading**:
- Check IIS site is running on port 96
- Verify MIME types are configured
- Check for build errors

**API connection errors**:
- Verify API is running on port 8080
- Check web.config configuration
- Verify database connectivity

**Game server authentication failures**:
- Check `.env` file configuration
- Verify `API_BASE_URL` doesn't have `/api` suffix
- Restart Node.js server after config changes

**CORS errors**:
- Verify API CORS settings allow frontend and game server origins
- Check that all URLs match exactly (no trailing slashes, correct ports)

## 7. Maintenance

### Regular Tasks
- Monitor server logs for errors
- Restart Node.js server if needed
- Update SSL certificates if using HTTPS
- Backup configuration files

### Log Locations
- **IIS Logs**: `C:\inetpub\logs\LogFiles\`
- **API Logs**: Check API application logs
- **Node.js Logs**: Console output from `npm start`

## 8. Security Notes

- Change default JWT secrets in production
- Implement proper SSL/TLS certificates
- Configure firewall rules for required ports
- Regular security updates for all components

## 9. Quick Deployment Commands

### Frontend Only
```bash
# Development machine
cd C:\Users\<USER>\source\repos\Thunee-fe\Thunee-FE
npm run build:production
# Copy dist/* to C:\inetpub\Thunee-Full\
```

### API Only
```bash
# Development machine
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
# Copy published files to IIS API directory and restart IIS site
```

### Node.js Server Only
```bash
# Production server
cd C:\inetpub\Thunee-Production
# Stop current server (Ctrl+C)
# Copy updated server files if needed
npm start
```

### Full Deployment
```bash
# 1. Build and deploy frontend
# 2. Build and deploy API
# 3. Deploy and restart Node.js server
# 4. Verify all components are working
```

## 10. Environment Files Reference

### Frontend (.env.production)
```env
VITE_API_BASE_URL=http://**************:8080/api
VITE_SOCKET_URL=http://**************:3001
VITE_APP_ENV=production
```

### Node.js Server (.env)
```env
NODE_ENV=production
API_BASE_URL=http://**************:8080
API_TIMEOUT=10000
JWT_SECRET=your_production_jwt_secret_here
LOG_LEVEL=info
LOBBY_TIMEOUT_MINUTES=30
GAME_TIMEOUT_MINUTES=60
MAX_RECONNECT_ATTEMPTS=3
```

---

**Last Updated**: June 2025
**Version**: 1.0
