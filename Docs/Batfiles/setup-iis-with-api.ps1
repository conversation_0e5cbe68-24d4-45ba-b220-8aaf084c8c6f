# PowerShell script to configure IIS for Thunee application with ASP.NET Core API
# Run this script as Administrator

param(
    [string]$NodeSitePath = "C:\inetpub\Thunee-Production",
    [string]$APISitePath = "C:\inetpub\ThuneeAPI",
    [string]$NodeSiteName = "Thunee",
    [string]$APISiteName = "ThuneeAPI",
    [int]$NodePort = 96,
    [int]$APIPort = 8080
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Thunee IIS Configuration Script with API" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

# Import WebAdministration module
Write-Host "Importing WebAdministration module..." -ForegroundColor Yellow
Import-Module WebAdministration -ErrorAction SilentlyContinue

if (-not (Get-Module WebAdministration)) {
    Write-Host "WebAdministration module not available. Installing IIS Management Tools..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-ManagementConsole -All
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-IIS6ManagementCompatibility -All
    Import-Module WebAdministration
}

# Enable required IIS features
Write-Host "Enabling required IIS features..." -ForegroundColor Yellow
$features = @(
    "IIS-WebServerRole",
    "IIS-WebServer",
    "IIS-CommonHttpFeatures",
    "IIS-HttpErrors",
    "IIS-HttpLogging",
    "IIS-RequestFiltering",
    "IIS-StaticContent",
    "IIS-DefaultDocument",
    "IIS-DirectoryBrowsing",
    "IIS-ASPNET45",
    "IIS-NetFxExtensibility45",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-WebSockets",
    "IIS-ApplicationDevelopment",
    "IIS-NetFxExtensibility",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-AspNetCoreModuleV2"
)

foreach ($feature in $features) {
    try {
        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
        Write-Host "Enabled: $feature" -ForegroundColor Green
    } catch {
        Write-Host "Warning: Could not enable $feature - $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Check for .NET 8.0 Runtime
Write-Host "Checking for .NET 8.0 Runtime..." -ForegroundColor Yellow
$dotnetVersion = dotnet --version 2>$null
if ($dotnetVersion -and $dotnetVersion.StartsWith("8.")) {
    Write-Host ".NET 8.0 Runtime found: $dotnetVersion" -ForegroundColor Green
} else {
    Write-Host "Warning: .NET 8.0 Runtime not found. Please install from:" -ForegroundColor Yellow
    Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Yellow
}

# Install IISNode if not already installed
Write-Host "Checking for IISNode..." -ForegroundColor Yellow
$iisNodePath = "${env:ProgramFiles}\iisnode\iisnode.dll"
if (-not (Test-Path $iisNodePath)) {
    Write-Host "IISNode not found. Please download and install IISNode from:" -ForegroundColor Red
    Write-Host "https://github.com/Azure/iisnode/releases" -ForegroundColor Red
    Write-Host "After installing IISNode, run this script again." -ForegroundColor Red
    exit 1
} else {
    Write-Host "IISNode found at: $iisNodePath" -ForegroundColor Green
}

# Create Node.js Application Pool
Write-Host "Creating Node.js Application Pool: ThuneeAppPool..." -ForegroundColor Yellow
try {
    Remove-WebAppPool -Name "ThuneeAppPool" -ErrorAction SilentlyContinue
    New-WebAppPool -Name "ThuneeAppPool" -Force
    
    # Configure Node.js Application Pool
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "enable32BitAppOnWin64" -Value $false
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "processModel.idleTimeout" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAppPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
    
    Write-Host "Node.js Application Pool created and configured successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating Node.js Application Pool: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create ASP.NET Core Application Pool
Write-Host "Creating ASP.NET Core Application Pool: ThuneeAPIAppPool..." -ForegroundColor Yellow
try {
    Remove-WebAppPool -Name "ThuneeAPIAppPool" -ErrorAction SilentlyContinue
    New-WebAppPool -Name "ThuneeAPIAppPool" -Force
    
    # Configure ASP.NET Core Application Pool
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAPIAppPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAPIAppPool" -Name "enable32BitAppOnWin64" -Value $false
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAPIAppPool" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAPIAppPool" -Name "processModel.idleTimeout" -Value "00:00:00"
    Set-ItemProperty -Path "IIS:\AppPools\ThuneeAPIAppPool" -Name "recycling.periodicRestart.time" -Value "00:00:00"
    
    Write-Host "ASP.NET Core Application Pool created and configured successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating ASP.NET Core Application Pool: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create Node.js Website
Write-Host "Creating Node.js Website: $NodeSiteName..." -ForegroundColor Yellow
try {
    Remove-Website -Name $NodeSiteName -ErrorAction SilentlyContinue
    New-Website -Name $NodeSiteName -Port $NodePort -PhysicalPath $NodeSitePath -ApplicationPool "ThuneeAppPool"
    
    Write-Host "Node.js Website created successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating Node.js Website: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create ASP.NET Core API Website
Write-Host "Creating ASP.NET Core API Website: $APISiteName..." -ForegroundColor Yellow
try {
    Remove-Website -Name $APISiteName -ErrorAction SilentlyContinue
    New-Website -Name $APISiteName -Port $APIPort -PhysicalPath $APISitePath -ApplicationPool "ThuneeAPIAppPool"
    
    Write-Host "ASP.NET Core API Website created successfully." -ForegroundColor Green
} catch {
    Write-Host "Error creating ASP.NET Core API Website: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Verify paths exist
Write-Host "Verifying deployment paths..." -ForegroundColor Yellow
if (-not (Test-Path $NodeSitePath)) {
    Write-Host "Warning: Node.js site path does not exist: $NodeSitePath" -ForegroundColor Yellow
}
if (-not (Test-Path $APISitePath)) {
    Write-Host "Warning: API site path does not exist: $APISitePath" -ForegroundColor Yellow
}

# Set permissions for Node.js site
Write-Host "Setting permissions for Node.js site..." -ForegroundColor Yellow
try {
    icacls $NodeSitePath /grant "IIS_IUSRS:(OI)(CI)F" /T
    icacls $NodeSitePath /grant "IUSR:(OI)(CI)R" /T
    icacls $NodeSitePath /grant "Everyone:(OI)(CI)R" /T
    Write-Host "Node.js site permissions set successfully." -ForegroundColor Green
} catch {
    Write-Host "Error setting Node.js site permissions: $($_.Exception.Message)" -ForegroundColor Red
}

# Set permissions for API site
Write-Host "Setting permissions for API site..." -ForegroundColor Yellow
try {
    icacls $APISitePath /grant "IIS_IUSRS:(OI)(CI)F" /T
    icacls $APISitePath /grant "IUSR:(OI)(CI)R" /T
    icacls $APISitePath /grant "Everyone:(OI)(CI)R" /T
    Write-Host "API site permissions set successfully." -ForegroundColor Green
} catch {
    Write-Host "Error setting API site permissions: $($_.Exception.Message)" -ForegroundColor Red
}

# Start Application Pools and Websites
Write-Host "Starting Application Pools and Websites..." -ForegroundColor Yellow
try {
    Start-WebAppPool -Name "ThuneeAppPool"
    Start-WebAppPool -Name "ThuneeAPIAppPool"
    Start-Website -Name $NodeSiteName
    Start-Website -Name $APISiteName
    Write-Host "All services started successfully." -ForegroundColor Green
} catch {
    Write-Host "Error starting services: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "IIS Configuration Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "DEPLOYMENT SUMMARY:" -ForegroundColor Cyan
Write-Host "Node.js App: http://localhost:$NodePort" -ForegroundColor White
Write-Host "ASP.NET Core API: http://localhost:$APIPort/api" -ForegroundColor White
Write-Host "API Documentation: http://localhost:$APIPort/api-docs" -ForegroundColor White
Write-Host ""
Write-Host "EXTERNAL ACCESS:" -ForegroundColor Cyan
Write-Host "Node.js App: http://**************:$NodePort" -ForegroundColor White
Write-Host "ASP.NET Core API: http://**************:$APIPort/api" -ForegroundColor White
Write-Host "API Documentation: http://**************:$APIPort/api-docs" -ForegroundColor White
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Test the Node.js application" -ForegroundColor White
Write-Host "2. Test the API endpoints" -ForegroundColor White
Write-Host "3. Check logs if there are any issues" -ForegroundColor White
Write-Host "4. Configure firewall to allow ports $NodePort and $APIPort" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
