# Ball Result API Troubleshooting Guide

## Overview

This guide helps resolve the 404 and 500 errors occurring when trying to save ball results to the API database during competition games.

## Current Issues Identified

### 1. 404 Error - Game Not Found
```
[API] ❌ Failed to record ball result for 33L2SS: Request failed with status code 404
```

**Root Cause**: The game with lobby code `33L2SS` is not found in the API database.

### 2. 500 Error - Hand Result Saving
```
[API] ❌ Failed to record hand result for 33L2SS: Request failed with status code 500
```

**Root Cause**: Internal server error when trying to save hand results, likely due to missing database schema.

## Diagnostic Steps

### Step 1: Verify Database Schema

Run the verification script to check if all required database objects exist:

```sql
-- Execute this script on your production database
-- File: ThuneeAPI/ThuneeAPI.Infrastructure/Data/Scripts/VerifyBallResultSchema.sql
```

This will check for:
- ✅ GameBalls table existence
- ✅ Required columns in GameBalls table
- ✅ GameHands table with GameBallId column
- ✅ SP_RecordBallResult stored procedure
- ✅ SP_RecordHandResult stored procedure

### Step 2: Check Game Creation

Verify that games are being created properly in the API database:

```sql
-- Check if games exist for the failing lobby codes
SELECT * FROM Games WHERE LobbyCode IN ('33L2SS', 'AAAYHY');

-- Check recent games
SELECT TOP 10 * FROM Games ORDER BY CreatedAt DESC;
```

### Step 3: Check API Logs

Look for these patterns in the API logs:
- `[GAME_REPOSITORY] ❌ No game found for lobby code: {lobbyCode}`
- `[GAME_SERVICE] ❌ Game not found for lobby code: {lobbyCode}`

## Quick Fix Solutions

### Solution 1: Database Schema Fix

If the verification script shows missing objects, run the quick fix:

```sql
-- Execute this script to create missing database objects
-- File: ThuneeAPI/ThuneeAPI.Infrastructure/Data/Scripts/QuickFixBallResults.sql
```

This script will:
- Create GameBalls table if missing
- Add missing columns to GameBalls table
- Create SP_RecordBallResult stored procedure if missing

### Solution 2: Manual Game Creation

If games are missing from the database, you can manually create them:

```sql
-- Example: Create a missing game record
INSERT INTO Games (
    Id, LobbyCode, Team1Name, Team2Name, Status, 
    CreatedAt, UpdatedAt, CurrentBall, CurrentHand,
    Team1Score, Team2Score, Team1BallsWon, Team2BallsWon
)
VALUES (
    NEWID(), '33L2SS', 'Team 1', 'Team 2', 'in-progress',
    GETUTCDATE(), GETUTCDATE(), 1, 1,
    0, 0, 0, 0
);
```

### Solution 3: API Restart

After making database changes:

1. Stop the API service
2. Rebuild and redeploy the API
3. Start the API service
4. Test ball result saving

## Prevention Measures

### 1. Ensure Proper Game Creation

Make sure competition games are properly created in the API database when lobbies are created:

```javascript
// In Node.js server - ensure this is working
gameDataService.saveGameCreation(lobby, hostSocketId)
```

### 2. Verify Merge Process

For competition lobby merging, ensure the merged lobby has a proper game ID:

```javascript
// Check that merged lobbies get proper game tracking
const gameId = gameDataService.savedGames.get(lobbyCode);
if (!gameId) {
    console.error(`Game ID missing for lobby ${lobbyCode}`);
}
```

### 3. Database Migration

Ensure all database migrations are run on production:

```bash
# Run these scripts on production database:
1. Migration_AddBallResultFields.sql
2. SP_RecordBallResult.sql
3. SP_RecordHandResult.sql (if missing)
```

## Testing Verification

After applying fixes, test the following:

### 1. Create Competition Game
```bash
# Test creating a competition game
curl -X POST https://your-api/api/Games \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "lobbyCode": "TEST01",
    "team1Name": "Test Team",
    "competitionId": "your-competition-id"
  }'
```

### 2. Test Ball Result Saving
```bash
# Test saving a ball result
curl -X POST https://your-api/api/Games/TEST01/ball-result \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "ballNumber": 1,
    "winnerTeam": 1,
    "team1Score": 105,
    "team2Score": 95,
    "team1BallsWon": 1,
    "team2BallsWon": 0
  }'
```

### 3. Verify Database Records
```sql
-- Check that records were created
SELECT * FROM Games WHERE LobbyCode = 'TEST01';
SELECT * FROM GameBalls WHERE GameId = (SELECT Id FROM Games WHERE LobbyCode = 'TEST01');
```

## Common Issues and Solutions

### Issue: "Game not found" for existing games
**Solution**: Check if the game was created with the correct lobby code and status.

### Issue: "Stored procedure not found"
**Solution**: Run the QuickFixBallResults.sql script to create missing procedures.

### Issue: "Column does not exist"
**Solution**: Run the Migration_AddBallResultFields.sql script to add missing columns.

### Issue: Competition lobby merge failures
**Solution**: Ensure the merge process properly transfers game IDs between lobbies.

## Monitoring

Set up monitoring for these key metrics:
- Game creation success rate
- Ball result save success rate
- Hand result save success rate
- API error rates for game-related endpoints

## Contact Information

If issues persist after following this guide:
1. Check API logs for detailed error messages
2. Verify database connectivity
3. Ensure all required stored procedures exist
4. Test with a simple non-competition game first
