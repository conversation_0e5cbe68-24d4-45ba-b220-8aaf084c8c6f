# Ball Data Saving Implementation for Competition Games

## Overview

This implementation adds comprehensive ball data saving functionality specifically for competition games in the Thunee system. When a ball is completed in a competition game, all relevant data including hand results, special calls, and ball outcomes are saved to the database.

## Features

### What Gets Saved
- **Ball Information**: Ball number, winner team, scores
- **Hand Details**: All 6 hands with winners and points
- **Special Calls**: Thunee Double, Khanka, Four Ball scenarios
- **Game State**: Trump suit, ball scores after completion
- **Call Results**: Success/failure status of special calls
- **Team Information**: Team names and all player IDs for both teams
- **Competition Linking**: Direct link to the competition this ball was played in

### When Data is Saved
Ball data is saved **only for competition games** when:
- A ball completes normally (after 6 hands)
- A Thunee Double succeeds or fails
- A Khanka call is processed
- A Four Ball scenario occurs

## Database Schema Changes

### Enhanced GameBalls Table
```sql
-- New columns added to GameBalls table
CompetitionId UNIQUEIDENTIFIER NULL -- Link to competition
Team1BallsWon INT NOT NULL DEFAULT 0
Team2BallsWon INT NOT NULL DEFAULT 0
-- Team 1 Players
Team1Player1Id UNIQUEIDENTIFIER NULL
Team1Player2Id UNIQUEIDENTIFIER NULL
-- Team 2 Players
Team2Player1Id UNIQUEIDENTIFIER NULL
Team2Player2Id UNIQUEIDENTIFIER NULL
-- Team Names
Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1'
Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2'
-- Game State
TrumpSuit NVARCHAR(10) NULL
HasThuneeDouble BIT NOT NULL DEFAULT 0
HasKhanka BIT NOT NULL DEFAULT 0
SpecialCallType NVARCHAR(20) NULL
SpecialCallResult NVARCHAR(20) NULL
```

### Enhanced GameHands Table
```sql
-- New column to link hands to balls
GameBallId UNIQUEIDENTIFIER NULL
-- Foreign key to GameBalls(Id)
```

## API Implementation

### New Endpoint
```
POST /api/games/{lobbyCode}/ball-result
```

### Request DTO
```csharp
public class RecordBallResultDto
{
    public string LobbyCode { get; set; }
    public Guid? CompetitionId { get; set; }
    public int BallNumber { get; set; }
    public int WinnerTeam { get; set; } // 1 or 2
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    // Team 1 Players
    public Guid? Team1Player1Id { get; set; }
    public Guid? Team1Player2Id { get; set; }
    // Team 2 Players
    public Guid? Team2Player1Id { get; set; }
    public Guid? Team2Player2Id { get; set; }
    // Team Names
    public string Team1Name { get; set; } = "Team 1";
    public string Team2Name { get; set; } = "Team 2";
    // Game State
    public string? TrumpSuit { get; set; }
    public bool HasThuneeDouble { get; set; }
    public bool HasKhanka { get; set; }
    public string? SpecialCallType { get; set; }
    public string? SpecialCallResult { get; set; }
    public List<BallHandDto> Hands { get; set; }
}
```

## Node.js Server Integration

### Ball Completion Scenarios

#### 1. Normal Ball Completion
```javascript
// After 6 hands, calculate winner and save ball data
const ballDataForSaving = {
  ballNumber: ballId,
  winnerTeam: ballResult.winner,
  team1Score: team1Points,
  team2Score: team2Points,
  team1BallsWon: ballResult.ballScores.team1,
  team2BallsWon: ballResult.ballScores.team2,
  trumpSuit: lobby.gameState?.trumpSuit,
  hands: lobby.hands || []
};
```

#### 2. Thunee Double Success
```javascript
const ballDataForSaving = {
  ballNumber: ballId,
  winnerTeam: thuneePlayerTeam,
  hasThuneeDouble: true,
  specialCallType: 'thunee',
  specialCallResult: 'success',
  hands: lobby.hands || []
};
```

#### 3. Khanka Call Processing
```javascript
const ballDataForSaving = {
  ballNumber: ballId,
  winnerTeam: khanakResult.winningTeam,
  hasKhanka: true,
  specialCallType: 'khanka',
  specialCallResult: khanakResult.success ? 'success' : 'failed',
  hands: lobby.hands || []
};
```

#### 4. Four Ball Scenarios
```javascript
const ballDataForSaving = {
  ballNumber: ballId,
  winnerTeam: winningTeam,
  specialCallType: 'four_ball',
  specialCallResult: data.option, // 'timeout', 'disconnect', etc.
  hands: lobby.hands || []
};
```

## Data Flow

1. **Ball Completion Detected** (server/index.js)
   - Normal completion after 6 hands
   - Special call scenarios (Thunee, Khanka, etc.)

2. **Data Preparation** (server/index.js)
   - Collect ball information
   - Gather hand results
   - Determine special call status

3. **API Call** (gameDataUtils.js → gameDataService.js → apiService.js)
   - Only for competition games
   - Async call to avoid disrupting gameplay
   - Retry mechanism for failed calls

4. **Database Storage** (ASP.NET Core API)
   - Save to GameBalls table
   - Link hands to ball via GameBallId
   - Update game current ball and scores

## Files Modified/Created

### API Layer (ASP.NET Core)
- `ThuneeAPI.Application/DTOs/GameDTOs.cs` - Added RecordBallResultDto
- `ThuneeAPI.Application/Interfaces/IGameService.cs` - Added RecordBallResultAsync
- `ThuneeAPI.Application/Interfaces/IGameRepository.cs` - Added RecordBallResultAsync
- `ThuneeAPI.Infrastructure/Services/GameService.cs` - Implemented ball saving logic
- `ThuneeAPI.Infrastructure/Data/Repositories/GameRepository.cs` - Added database methods
- `ThuneeAPI/Controllers/GamesController.cs` - Added ball-result endpoint
- `ThuneeAPI.Core/Entities/GameHand.cs` - Enhanced entities

### Database Scripts
- `ThuneeAPI.Infrastructure/Data/Scripts/SP_RecordBallResult.sql` - Stored procedure
- `ThuneeAPI.Infrastructure/Data/Scripts/Migration_AddBallResultFields.sql` - Migration

### Node.js Server
- `server/services/apiService.js` - Added recordBallResult method
- `server/services/gameDataService.js` - Added saveBallResult method
- `server/utils/gameDataUtils.js` - Added saveBallEndResult method
- `server/index.js` - Integrated ball saving in completion logic
- `server/handlers/fourBallHandler.js` - Added ball saving for four ball scenarios

## Usage

### For Competition Games
Ball data is automatically saved when any ball completes. No additional configuration required.

### For Regular Games
Ball data saving is skipped to avoid unnecessary database operations.

## Benefits

1. **Complete Game History**: Full record of all balls played in competition games
2. **Special Call Tracking**: Detailed tracking of Thunee, Khanka, and other special scenarios
3. **Competition Analytics**: Rich data for competition statistics and analysis
4. **Audit Trail**: Complete audit trail of game progression
5. **Performance Optimized**: Only saves data for competition games where it's needed
6. **Team Tracking**: Full team composition and player participation tracking
7. **Competition Linking**: Direct database relationships for competition analysis
8. **Player Statistics**: Individual player performance tracking across competitions

## Error Handling

- Failed API calls are queued for retry
- Errors don't disrupt gameplay
- Comprehensive logging for debugging
- Graceful degradation if API is unavailable

## Testing

To test the implementation:
1. Create a competition game
2. Play through a complete ball (6 hands)
3. Check the GameBalls and GameHands tables for saved data
4. Verify special call scenarios (Thunee, Khanka) are properly recorded
