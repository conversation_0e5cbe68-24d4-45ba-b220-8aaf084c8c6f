# ThuneeAPI Build and Deployment Guide

## Overview
This guide covers building and publishing the ASP.NET Core ThuneeAPI to the designated publish directory for IIS deployment.

## Prerequisites
- .NET 8.0 SDK installed
- Visual Studio or Visual Studio Code (optional)
- Access to the ThuneeAPI project directory
- Write permissions to `C:\Users\<USER>\Desktop\APIPUBLISH`

## Project Structure
```
ThuneeAPI/
├── ThuneeAPI/                 # Main API project
│   ├── Program.cs            # Application entry point
│   ├── ThuneeAPI.csproj      # Project file
│   ├── Controllers/          # API controllers
│   ├── Models/              # Data models
│   └── ...
├── ThuneeAPI.Core/          # Core business logic
├── ThuneeAPI.Infrastructure/ # Data access layer
└── ThuneeAPI.sln            # Solution file
```

## Build Commands

### Method 1: Using Command Line (Recommended)

#### Navigate to Project Directory
```bash
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
```

#### Clean Previous Builds
```bash
dotnet clean
```

#### Restore Dependencies
```bash
dotnet restore
```

#### Build in Release Mode
```bash
dotnet build -c Release
```

#### Publish to Target Directory
```bash
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
```

### Method 2: Single Command Publish
```bash
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH --self-contained false
```

### Method 3: Using Visual Studio
1. Open `ThuneeAPI.sln` in Visual Studio
2. Right-click on the `ThuneeAPI` project
3. Select "Publish..."
4. Choose "Folder" as publish target
5. Set target location: `C:\Users\<USER>\Desktop\APIPUBLISH`
6. Click "Publish"

## Detailed Step-by-Step Process

### Step 1: Prepare Environment
```powershell
# Open PowerShell as Administrator (if needed)
# Navigate to the API project directory
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
```

### Step 2: Verify Project Structure
```powershell
# List contents to verify you're in the right directory
dir
# Should show ThuneeAPI.sln and project folders
```

### Step 3: Clean and Restore
```powershell
# Clean any previous builds
dotnet clean

# Restore NuGet packages
dotnet restore
```

### Step 4: Build and Test
```powershell
# Build in Release configuration
dotnet build -c Release

# Optional: Run tests if available
dotnet test
```

### Step 5: Publish
```powershell
# Create publish directory if it doesn't exist
if (!(Test-Path "C:\Users\<USER>\Desktop\APIPUBLISH")) {
    New-Item -ItemType Directory -Path "C:\Users\<USER>\Desktop\APIPUBLISH"
}

# Publish the application
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
```

## Published Output Structure
After successful publishing, `C:\Users\<USER>\Desktop\APIPUBLISH` should contain:
```
APIPUBLISH/
├── ThuneeAPI.exe             # Main executable
├── ThuneeAPI.dll             # Main assembly
├── ThuneeAPI.deps.json       # Dependencies
├── ThuneeAPI.runtimeconfig.json
├── web.config                # IIS configuration
├── wwwroot/                  # Static files (if any)
├── appsettings.json          # Configuration
├── appsettings.Production.json
└── [Various .dll files]     # Dependencies
```

## Verification Steps

### Verify Build Success
```powershell
# Check if main executable exists
Test-Path "C:\Users\<USER>\Desktop\APIPUBLISH\ThuneeAPI.exe"

# List all files in publish directory
Get-ChildItem "C:\Users\<USER>\Desktop\APIPUBLISH" | Format-Table Name, Length
```

### Test Local Execution (Optional)
```powershell
cd C:\Users\<USER>\Desktop\APIPUBLISH
.\ThuneeAPI.exe
# Should start the API server (Ctrl+C to stop)
```

## Configuration Files

### appsettings.json
Ensure your `appsettings.json` contains production-ready settings:
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "DefaultConnection": "your_production_connection_string"
  }
}
```

### web.config
The publish process should generate a `web.config` file for IIS:
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath=".\ThuneeAPI.exe" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
```

## Troubleshooting

### Common Build Errors

**Error: Project not found**
```bash
# Ensure you're in the correct directory
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
ls *.sln  # Should show ThuneeAPI.sln
```

**Error: NuGet restore failed**
```bash
# Clear NuGet cache and retry
dotnet nuget locals all --clear
dotnet restore
```

**Error: Build failed**
```bash
# Check for detailed error information
dotnet build -c Release -v detailed
```

### Publish Directory Issues

**Permission Denied**
```powershell
# Run PowerShell as Administrator
# Or change publish directory to user-accessible location
dotnet publish -c Release -o C:\temp\APIPUBLISH
```

**Directory Not Empty**
```powershell
# Clear existing publish directory
Remove-Item "C:\Users\<USER>\Desktop\APIPUBLISH\*" -Recurse -Force
dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH
```

## Automation Script

### PowerShell Build Script
Create `build-and-publish.ps1`:
```powershell
# ThuneeAPI Build and Publish Script
param(
    [string]$PublishPath = "C:\Users\<USER>\Desktop\APIPUBLISH"
)

Write-Host "Starting ThuneeAPI build and publish process..." -ForegroundColor Green

# Navigate to project directory
Set-Location "C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI"

# Clean previous builds
Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
dotnet clean

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
dotnet restore

# Build in Release mode
Write-Host "Building in Release mode..." -ForegroundColor Yellow
dotnet build -c Release

if ($LASTEXITCODE -eq 0) {
    # Publish to target directory
    Write-Host "Publishing to $PublishPath..." -ForegroundColor Yellow
    dotnet publish -c Release -o $PublishPath
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build and publish completed successfully!" -ForegroundColor Green
        Write-Host "Published files are in: $PublishPath" -ForegroundColor Cyan
    } else {
        Write-Host "Publish failed!" -ForegroundColor Red
    }
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}
```

### Usage
```powershell
# Run the script
.\build-and-publish.ps1

# Or with custom publish path
.\build-and-publish.ps1 -PublishPath "C:\custom\path"
```

## Next Steps After Publishing

1. **Copy to IIS Server**: Transfer files from `APIPUBLISH` to your IIS server
2. **Configure IIS Site**: Set up IIS site pointing to the published files
3. **Update Connection Strings**: Ensure production database settings
4. **Test API Endpoints**: Verify API is responding correctly
5. **Monitor Logs**: Check for any startup or runtime errors

## Quick Reference Commands

```bash
# Full build and publish in one command
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI && dotnet publish -c Release -o C:\Users\<USER>\Desktop\APIPUBLISH

# Verify publish success
Test-Path "C:\Users\<USER>\Desktop\APIPUBLISH\ThuneeAPI.exe"

# Check published file size
Get-ChildItem "C:\Users\<USER>\Desktop\APIPUBLISH" | Measure-Object -Property Length -Sum
```

---

**Last Updated**: June 2025
**Version**: 1.0
