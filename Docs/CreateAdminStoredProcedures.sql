USE [GoldRushThunee]
GO

-- =============================================
-- Admin System Stored Procedures Installation
-- =============================================

-- Drop existing procedures if they exist
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllUsers')
    DROP PROCEDURE SP_GetAllUsers
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_UpdateUser')
    DROP PROCEDURE SP_UpdateUser
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteUser')
    DROP PROCEDURE SP_DeleteUser
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_ChangeUserPassword')
    DROP PROCEDURE SP_ChangeUserPassword
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllCompetitionsAdmin')
    DROP PROCEDURE SP_GetAllCompetitionsAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_UpdateCompetition')
    DROP PROCEDURE SP_UpdateCompetition
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteCompetition')
    DROP PROCEDURE SP_DeleteCompetition
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionTeamsAdmin')
    DROP PROCEDURE SP_GetCompetitionTeamsAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_DeleteCompetitionTeam')
    DROP PROCEDURE SP_DeleteCompetitionTeam
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionGamesAdmin')
    DROP PROCEDURE SP_GetCompetitionGamesAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAllGamesAdmin')
    DROP PROCEDURE SP_GetAllGamesAdmin
GO

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionPlayerEmails')
    DROP PROCEDURE SP_GetCompetitionPlayerEmails
GO

-- =============================================
-- User Management Procedures
-- =============================================

CREATE PROCEDURE SP_GetAllUsers
AS
BEGIN
    SELECT 
        Id,
        Username,
        Email,
        IsVerified,
        IsActive,
        IsAdmin,
        LastLoginAt,
        CreatedAt,
        UpdatedAt
    FROM Users
    ORDER BY CreatedAt DESC
END
GO

CREATE PROCEDURE SP_UpdateUser
    @Id UNIQUEIDENTIFIER,
    @Username NVARCHAR(50) = NULL,
    @Email NVARCHAR(255) = NULL,
    @IsVerified BIT = NULL,
    @IsActive BIT = NULL,
    @IsAdmin BIT = NULL
AS
BEGIN
    UPDATE Users 
    SET 
        Username = ISNULL(@Username, Username),
        Email = ISNULL(@Email, Email),
        IsVerified = ISNULL(@IsVerified, IsVerified),
        IsActive = ISNULL(@IsActive, IsActive),
        IsAdmin = ISNULL(@IsAdmin, IsAdmin),
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    SELECT 
        Id,
        Username,
        Email,
        IsVerified,
        IsActive,
        IsAdmin,
        LastLoginAt,
        CreatedAt,
        UpdatedAt
    FROM Users 
    WHERE Id = @Id
END
GO

CREATE PROCEDURE SP_DeleteUser
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if user exists
    IF NOT EXISTS (SELECT 1 FROM Users WHERE Id = @Id)
    BEGIN
        RAISERROR('User not found', 16, 1)
        RETURN
    END

    -- Delete related records first (if any foreign key constraints)
    -- Delete from CompetitionTeams where Player1Id or Player2Id = @Id
    DELETE FROM CompetitionTeams 
    WHERE Player1Id = @Id OR Player2Id = @Id

    -- Delete from Games where user is involved
    DELETE FROM Games 
    WHERE Team1Player1Id = @Id OR Team1Player2Id = @Id 
       OR Team2Player1Id = @Id OR Team2Player2Id = @Id

    -- Finally delete the user
    DELETE FROM Users WHERE Id = @Id

    SELECT 1 as Success
END
GO

CREATE PROCEDURE SP_ChangeUserPassword
    @Id UNIQUEIDENTIFIER,
    @NewPasswordHash NVARCHAR(255)
AS
BEGIN
    UPDATE Users 
    SET 
        PasswordHash = @NewPasswordHash,
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    SELECT @@ROWCOUNT as RowsAffected
END
GO

-- =============================================
-- Competition Management Procedures
-- =============================================

CREATE PROCEDURE SP_GetAllCompetitionsAdmin
AS
BEGIN
    SELECT 
        c.Id,
        c.Name,
        c.Description,
        c.StartDate,
        c.EndDate,
        c.Status,
        c.MaxTeams,
        c.CurrentTeams,
        c.EntryFee,
        c.PrizeFirst,
        c.PrizeSecond,
        c.PrizeThird,
        c.TotalPrizePool,
        c.Rules,
        c.IsPublic,
        c.AllowSpectators,
        c.MaxGamesPerTeam,
        c.CreatedAt,
        c.UpdatedAt
    FROM Competitions c
    ORDER BY c.CreatedAt DESC
END
GO

CREATE PROCEDURE SP_UpdateCompetition
    @Id UNIQUEIDENTIFIER,
    @Name NVARCHAR(255) = NULL,
    @Description NVARCHAR(MAX) = NULL,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @Status NVARCHAR(50) = NULL,
    @MaxTeams INT = NULL,
    @EntryFee DECIMAL(10,2) = NULL,
    @PrizeFirst DECIMAL(10,2) = NULL,
    @PrizeSecond DECIMAL(10,2) = NULL,
    @PrizeThird DECIMAL(10,2) = NULL,
    @Rules NVARCHAR(MAX) = NULL,
    @IsPublic BIT = NULL,
    @AllowSpectators BIT = NULL,
    @MaxGamesPerTeam INT = NULL
AS
BEGIN
    -- Calculate TotalPrizePool if any prize values are updated
    DECLARE @NewTotalPrizePool DECIMAL(10,2)
    
    SELECT @NewTotalPrizePool = 
        ISNULL(@PrizeFirst, PrizeFirst) + 
        ISNULL(@PrizeSecond, PrizeSecond) + 
        ISNULL(@PrizeThird, PrizeThird)
    FROM Competitions WHERE Id = @Id

    UPDATE Competitions 
    SET 
        Name = ISNULL(@Name, Name),
        Description = ISNULL(@Description, Description),
        StartDate = ISNULL(@StartDate, StartDate),
        EndDate = ISNULL(@EndDate, EndDate),
        Status = ISNULL(@Status, Status),
        MaxTeams = ISNULL(@MaxTeams, MaxTeams),
        EntryFee = ISNULL(@EntryFee, EntryFee),
        PrizeFirst = ISNULL(@PrizeFirst, PrizeFirst),
        PrizeSecond = ISNULL(@PrizeSecond, PrizeSecond),
        PrizeThird = ISNULL(@PrizeThird, PrizeThird),
        TotalPrizePool = @NewTotalPrizePool,
        Rules = ISNULL(@Rules, Rules),
        IsPublic = ISNULL(@IsPublic, IsPublic),
        AllowSpectators = ISNULL(@AllowSpectators, AllowSpectators),
        MaxGamesPerTeam = ISNULL(@MaxGamesPerTeam, MaxGamesPerTeam),
        UpdatedAt = GETUTCDATE()
    WHERE Id = @Id

    -- Return updated competition
    SELECT 
        Id,
        Name,
        Description,
        StartDate,
        EndDate,
        Status,
        MaxTeams,
        CurrentTeams,
        EntryFee,
        PrizeFirst,
        PrizeSecond,
        PrizeThird,
        TotalPrizePool,
        Rules,
        IsPublic,
        AllowSpectators,
        MaxGamesPerTeam,
        CreatedAt,
        UpdatedAt
    FROM Competitions 
    WHERE Id = @Id
END
GO

CREATE PROCEDURE SP_DeleteCompetition
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    -- Check if competition exists
    IF NOT EXISTS (SELECT 1 FROM Competitions WHERE Id = @Id)
    BEGIN
        RAISERROR('Competition not found', 16, 1)
        RETURN
    END

    -- Delete related records first
    DELETE FROM Games WHERE CompetitionId = @Id
    DELETE FROM CompetitionTeams WHERE CompetitionId = @Id

    -- Delete the competition
    DELETE FROM Competitions WHERE Id = @Id

    SELECT 1 as Success
END
GO
