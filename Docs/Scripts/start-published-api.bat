@echo off
echo ========================================
echo Starting Published Thunee API
echo ========================================
echo.

set PUBLISH_DIR=C:\Users\<USER>\Desktop\APIPUBLISH
set API_EXE=%PUBLISH_DIR%\ThuneeAPI.exe

REM Check if published files exist
if not exist "%API_EXE%" (
    echo ❌ Published API not found at: %API_EXE%
    echo Please run publish-api-production.bat first
    pause
    exit /b 1
)

echo API Location: %PUBLISH_DIR%
echo Executable: %API_EXE%
echo.

REM Change to publish directory
cd /d "%PUBLISH_DIR%"

REM Set environment to Production
set ASPNETCORE_ENVIRONMENT=Production

echo Environment: %ASPNETCORE_ENVIRONMENT%
echo Starting API on: http://**************:8080
echo.
echo Available endpoints:
echo - Health Check: http://**************:8080/health
echo - API Docs: http://**************:8080/api-docs
echo - Login: http://**************:8080/api/auth/login
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start the API with specific URL binding
ThuneeAPI.exe --urls="http://**************:8080"

pause
