@echo off
echo ========================================
echo    Starting Thunee Development Environment
echo    (Enhanced with ASP.NET Core API)
echo ========================================
echo.

REM Set console colors for better visibility
color 0A

REM Check if we're in the correct directory
if not exist "package.json" (
    echo Error: package.json not found. Please run this script from the Thunee-FE directory.
    pause
    exit /b 1
)

REM Check if .NET is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET SDK not found. Please install .NET 8.0 SDK.
    echo Download from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Check if ASP.NET API project exists
if not exist "..\ThuneeAPI\ThuneeAPI\ThuneeAPI.csproj" (
    echo Error: ASP.NET Core API project not found at ..\ThuneeAPI\ThuneeAPI\
    echo Please ensure the ThuneeAPI project is in the correct location.
    pause
    exit /b 1
)

echo [1/5] Building ASP.NET Core API...
echo ----------------------------------------
cd ..\ThuneeAPI\ThuneeAPI
echo Building API project...
dotnet build --configuration Debug
if errorlevel 1 (
    echo Error: Failed to build ASP.NET Core API
    pause
    exit /b 1
)
cd ..\..\Thunee-FE

echo [2/5] Starting ASP.NET Core API Server...
echo ----------------------------------------
start "Thunee ASP.NET API" cmd /k "cd ..\ThuneeAPI\ThuneeAPI && echo Starting ASP.NET Core API Server... && echo API will be available at: && echo   - HTTPS: https://localhost:57229 && echo   - HTTP:  http://localhost:57230 && echo   - Docs:  https://localhost:57229/api-docs && echo. && dotnet run"

REM Wait for API to start
echo Waiting for API server to start...
timeout /t 8 /nobreak >nul

echo [3/5] Starting Video Server...
echo ----------------------------------------
start "Thunee Video Server" cmd /k "cd server && echo Starting Video Server... && node videoServer.js"

REM Wait a moment for video server to start
timeout /t 2 /nobreak >nul

echo [4/5] Starting Game Server...
echo ----------------------------------------
start "Thunee Game Server" cmd /k "cd server && echo Starting Game Server... && node index.js"

REM Wait a moment for game server to start
timeout /t 2 /nobreak >nul

echo [5/5] Starting Frontend Development Server...
echo ----------------------------------------
echo Checking environment configuration...
if exist ".env.local" (
    echo ✓ Found .env.local - using local environment settings
) else (
    echo ⚠ No .env.local found - using default development settings
)
echo Starting Frontend on http://localhost:5173/
start "Thunee Frontend" cmd /k "echo Starting Frontend Development Server... && echo Frontend will connect to: && echo   - API: https://localhost:7001/api && echo   - Socket: https://localhost:7001 && echo. && npm run dev"

echo.
echo ========================================
echo    All Development Servers Started!
echo ========================================
echo.
echo Services running:
echo   - Frontend:     http://localhost:5173/
echo   - ASP.NET API:  https://localhost:57229/
echo   - API Docs:     https://localhost:57229/api-docs
echo   - Health Check: https://localhost:57229/health
echo   - Game Server:  WebSocket connection
echo   - Video Server: WebRTC connection
echo.
echo Environment Configuration:
echo   - API Base URL: https://localhost:57229/api
echo   - Socket URL:   https://localhost:57229
echo.
echo ========================================
echo    Quick Links
echo ========================================
echo   1. Open Application:  http://localhost:5173/
echo   2. API Documentation: https://localhost:57229/api-docs
echo   3. API Health Check:  https://localhost:57229/health
echo.
echo Press any key to open the application in your browser...
pause >nul

REM Open the application in default browser
start http://localhost:5173/

echo.
echo ========================================
echo    Development Environment Ready!
echo ========================================
echo.
echo The application should now be running in your browser.
echo.
echo To test the API connection:
echo   1. Try registering a new user
echo   2. Check the API documentation at https://localhost:57229/api-docs
echo   3. Monitor the console logs for any connection issues
echo.
echo Close this window or press Ctrl+C to stop all servers.
echo.
pause
