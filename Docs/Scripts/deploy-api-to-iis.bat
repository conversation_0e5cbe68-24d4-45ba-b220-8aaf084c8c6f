@echo off
echo ========================================
echo Deploying Thunee API to IIS Server
echo ========================================
echo.

set LOCAL_PUBLISH=C:\Users\<USER>\Desktop\APIPUBLISH
set SERVER_IP=**************
set SERVER_PATH=\\%SERVER_IP%\c$\inetpub\ThuneeAPI

echo Source: %LOCAL_PUBLISH%
echo Target: %SERVER_PATH%
echo.

REM Check if local published files exist
if not exist "%LOCAL_PUBLISH%\ThuneeAPI.exe" (
    echo ❌ Published API files not found at: %LOCAL_PUBLISH%
    echo Please run publish-api-production.bat first
    pause
    exit /b 1
)

echo Checking server connection...
ping -n 1 %SERVER_IP% >nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Cannot reach server %SERVER_IP%
    pause
    exit /b 1
)

echo ✅ Server is reachable

REM Create target directory if it doesn't exist
if not exist "%SERVER_PATH%" (
    echo Creating directory on server...
    mkdir "%SERVER_PATH%"
)

echo.
echo Stopping IIS application pool (if exists)...
REM You may need to run this on the server directly
echo psexec \\%SERVER_IP% -u Administrator cmd /c "C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:ThuneeAPI"

echo.
echo Copying files to server...
robocopy "%LOCAL_PUBLISH%" "%SERVER_PATH%" /E /R:3 /W:5 /MT:8

if %ERRORLEVEL% lss 8 (
    echo ✅ Files copied successfully
) else (
    echo ❌ Error copying files
    pause
    exit /b 1
)

echo.
echo Starting IIS application pool...
echo psexec \\%SERVER_IP% -u Administrator cmd /c "C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:ThuneeAPI"

echo.
echo ========================================
echo Deployment Instructions:
echo ========================================
echo 1. Files copied to: %SERVER_PATH%
echo 2. On the IIS server, configure:
echo    - Application Pool: ThuneeAPI
echo    - Site: ThuneeAPI (Port 8080)
echo    - Physical Path: C:\inetpub\ThuneeAPI
echo    - .NET Version: .NET 8.0
echo.
echo 3. Test endpoints:
echo    - http://**************:8080/health
echo    - http://**************:8080/api-docs
echo    - http://**************:8080/api/auth/login
echo ========================================

pause
