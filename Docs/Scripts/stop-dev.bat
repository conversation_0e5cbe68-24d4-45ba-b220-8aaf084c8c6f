@echo off
echo ========================================
echo    Stopping Thunee Development Servers
echo ========================================
echo.

REM Set console colors
color 0C

echo Stopping all Node.js processes...

REM Kill all node processes (be careful with this in production!)
taskkill /f /im node.exe >nul 2>&1
if errorlevel 1 (
    echo No Node.js processes found running.
) else (
    echo Node.js processes stopped.
)

REM Kill any remaining npm processes
taskkill /f /im npm.exe >nul 2>&1

REM Kill any nodemon processes
taskkill /f /im nodemon.exe >nul 2>&1

REM Try to kill specific processes by window title (if they were started with titles)
taskkill /f /fi "WindowTitle eq Thunee API Server*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Thunee Video Server*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Thunee Game Server*" >nul 2>&1
taskkill /f /fi "WindowTitle eq Thunee Frontend*" >nul 2>&1

echo.
echo ========================================
echo    All Development Servers Stopped
echo ========================================
echo.
echo All Thunee development processes have been terminated.
echo You can now restart the development environment using start-dev.bat
echo.
pause
