@echo off
echo ========================================
echo  PATCHING PRODUCTION SERVER PORT ISSUE
echo ========================================
echo.
echo This will fix the port 80 binding issue on your IIS server.
echo.

echo Creating server patch script...
(
echo @echo off
echo echo Patching Node.js server for IIS deployment...
echo.
echo cd /d "C:\inetpub\Thunee-Production\server"
echo.
echo echo Step 1: Stopping Node.js processes...
echo taskkill /f /im node.exe 2^>nul
echo.
echo echo Step 2: Creating production environment file...
echo ^(
echo echo NODE_ENV=production
echo echo API_BASE_URL=http://**************:8080
echo echo API_TIMEOUT=10000
echo echo JWT_SECRET=your_production_jwt_secret_here
echo echo LOG_LEVEL=info
echo echo LOBBY_TIMEOUT_MINUTES=30
echo echo GAME_TIMEOUT_MINUTES=60
echo echo MAX_RECONNECT_ATTEMPTS=3
echo ^) ^> .env
echo.
echo echo Step 3: Creating server startup patch...
echo ^(
echo echo // IIS Production Server Patch
echo echo const originalStartServer = startServer;
echo echo.
echo echo const startServerForIIS = async ^(^) =^> {
echo echo   console.log^('🔧 Starting server for IIS deployment...'^);
echo echo   
echo echo   const port = process.env.PORT;
echo echo   console.log^('Environment check:'^);
echo echo   console.log^('- NODE_ENV:', process.env.NODE_ENV^);
echo echo   console.log^('- PORT:', port^);
echo echo   
echo echo   if ^(!port^) {
echo echo     console.error^('❌ PORT environment variable not set by IISNode'^);
echo echo     console.error^('This app should run through IIS, not directly'^);
echo echo     process.exit^(1^);
echo echo   }
echo echo   
echo echo   if ^(port.toString^(^).startsWith^('\\\\\\\\.\\\\.\\\\pipe\\\\'^)^) {
echo echo     console.log^('✅ Detected IISNode named pipe:', port^);
echo echo     server.listen^(port, ^(^) =^> {
echo echo       console.log^('🚀 Server started on IISNode named pipe'^);
echo echo       console.log^('🌐 Accessible at: http://**************:96'^);
echo echo     }^);
echo echo   } else {
echo echo     console.log^('⚠️ Using port number instead of named pipe'^);
echo echo     const portNumber = parseInt^(port^) ^|^| 3001;
echo echo     server.listen^(portNumber, ^(^) =^> {
echo echo       console.log^(`🚀 Server running on port ${portNumber}`^);
echo echo     }^);
echo echo   }
echo echo };
echo echo.
echo echo // Replace the original startServer call
echo echo startServerForIIS^(^);
echo ^) ^> server-patch.js
echo.
echo echo Step 4: Applying patch to main server file...
echo echo. ^>^> index.js
echo echo // === IIS PRODUCTION PATCH === ^>^> index.js
echo type server-patch.js ^>^> index.js
echo.
echo echo Step 5: Setting permissions...
echo icacls "." /grant "IIS_IUSRS:^(OI^)^(CI^)F" /T /Q
echo.
echo echo Step 6: Restarting IIS...
echo iisreset
echo.
echo echo ========================================
echo echo  PATCH COMPLETE
echo echo ========================================
echo echo.
echo echo The server should now work properly with IIS.
echo echo Access via: http://**************:96
echo echo.
echo pause
) > patch-server-on-iis.bat

echo.
echo ========================================
echo  PATCH SCRIPT CREATED
echo ========================================
echo.
echo Transfer this file to your IIS server and run as Administrator:
echo   patch-server-on-iis.bat
echo.
echo This will fix the port binding issue permanently.
echo.
pause
