@echo off
echo ========================================
echo Publishing Thunee API for Production
echo ========================================
echo.

set PUBLISH_DIR=C:\Users\<USER>\Desktop\APIPUBLISH
set PROJECT_DIR=ThuneeAPI\ThuneeAPI

echo Target Directory: %PUBLISH_DIR%
echo Project Directory: %PROJECT_DIR%
echo.

REM Create publish directory if it doesn't exist
if not exist "%PUBLISH_DIR%" (
    echo Creating publish directory...
    mkdir "%PUBLISH_DIR%"
)

REM Clean existing files in publish directory
echo Cleaning existing files in publish directory...
if exist "%PUBLISH_DIR%\*" (
    del /Q "%PUBLISH_DIR%\*"
    for /d %%x in ("%PUBLISH_DIR%\*") do rd /s /q "%%x"
)

echo.
echo Building and publishing API...
cd %PROJECT_DIR%

REM Build and publish the application
dotnet publish ^
    --configuration Release ^
    --output "%PUBLISH_DIR%" ^
    --runtime win-x64 ^
    --self-contained false ^
    --verbosity normal

if %ERRORLEVEL% neq 0 (
    echo.
    echo ❌ Publish failed!
    cd ..\..
    pause
    exit /b 1
)

cd ..\..

echo.
echo ✅ API published successfully!
echo.
echo Published files location: %PUBLISH_DIR%
echo.

REM Copy production configuration
echo Copying production configuration...
copy "appsettings.Production.json" "%PUBLISH_DIR%\appsettings.Production.json" /Y

if exist "%PUBLISH_DIR%\appsettings.Production.json" (
    echo ✅ Production configuration copied successfully
) else (
    echo ❌ Failed to copy production configuration
)

echo.
echo ========================================
echo Publication Summary:
echo ========================================
echo Location: %PUBLISH_DIR%
echo Configuration: Production
echo Runtime: win-x64
echo Self-contained: No
echo.
echo To run the API:
echo 1. Navigate to: %PUBLISH_DIR%
echo 2. Set environment: set ASPNETCORE_ENVIRONMENT=Production
echo 3. Run: ThuneeAPI.exe --urls="http://**************:8080"
echo.
echo Or use the start script: start-published-api.bat
echo ========================================

pause
