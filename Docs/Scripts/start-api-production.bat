@echo off
echo Starting Thunee API in Production Mode...
echo.

cd ThuneeAPI\ThuneeAPI

echo Setting environment to Production...
set ASPNETCORE_ENVIRONMENT=Production

echo Building the API...
dotnet build --configuration Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting API server on port 8080...
echo API will be available at: http://160.119.252.81:8080
echo Swagger documentation: http://160.119.252.81:8080/api-docs
echo Health check: http://160.119.252.81:8080/health
echo.

dotnet run --configuration Release --launch-profile ThuneeAPI-Production

pause
