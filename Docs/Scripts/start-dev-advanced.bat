@echo off
setlocal enabledelayedexpansion

REM Advanced Development Environment Startup Script for Thunee
REM This script includes dependency checks, error handling, and logging

echo ========================================
echo    Thunee Advanced Development Startup
echo ========================================
echo.

REM Set console colors
color 0A

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set log file with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%"
set "logfile=logs\dev-startup_%datestamp%.log"

echo Starting development environment at %date% %time% > "%logfile%"

REM Function to log messages
set "log_msg=echo [%time%] "

REM Check if we're in the correct directory
if not exist "package.json" (
    %log_msg% ERROR: package.json not found >> "%logfile%"
    echo ERROR: package.json not found. Please run this script from the Thunee-FE directory.
    pause
    exit /b 1
)

REM Check Node.js installation
node --version >nul 2>&1
if errorlevel 1 (
    %log_msg% ERROR: Node.js not found >> "%logfile%"
    echo ERROR: Node.js is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check npm installation
npm --version >nul 2>&1
if errorlevel 1 (
    %log_msg% ERROR: npm not found >> "%logfile%"
    echo ERROR: npm is not installed or not in PATH.
    pause
    exit /b 1
)

echo Node.js and npm are available.
%log_msg% Node.js and npm checks passed >> "%logfile%"

REM Check if node_modules exists in main directory
if not exist "node_modules" (
    echo Installing frontend dependencies...
    %log_msg% Installing frontend dependencies >> "%logfile%"
    npm install
    if errorlevel 1 (
        %log_msg% ERROR: Frontend npm install failed >> "%logfile%"
        echo ERROR: Failed to install frontend dependencies.
        pause
        exit /b 1
    )
)

REM Check if node_modules exists in api directory
if not exist "api\node_modules" (
    echo Installing API dependencies...
    %log_msg% Installing API dependencies >> "%logfile%"
    cd api
    npm install
    if errorlevel 1 (
        %log_msg% ERROR: API npm install failed >> "%logfile%"
        echo ERROR: Failed to install API dependencies.
        pause
        exit /b 1
    )
    cd ..
)

REM Check if node_modules exists in server directory
if not exist "server\node_modules" (
    echo Installing server dependencies...
    %log_msg% Installing server dependencies >> "%logfile%"
    cd server
    npm install
    if errorlevel 1 (
        %log_msg% ERROR: Server npm install failed >> "%logfile%"
        echo ERROR: Failed to install server dependencies.
        pause
        exit /b 1
    )
    cd ..
)

echo.
echo All dependencies are ready!
%log_msg% All dependencies verified >> "%logfile%"

echo.
echo [1/4] Starting API Server...
echo ----------------------------------------
%log_msg% Starting API Server >> "%logfile%"
start "Thunee API Server" cmd /k "title Thunee API Server && cd api && echo Starting API Server on port 3000... && npm run dev"

REM Wait for API to start
timeout /t 4 /nobreak >nul

echo [2/4] Starting Video Server...
echo ----------------------------------------
%log_msg% Starting Video Server >> "%logfile%"
start "Thunee Video Server" cmd /k "title Thunee Video Server && cd server && echo Starting Video Server... && node videoServer.js"

REM Wait for video server to start
timeout /t 3 /nobreak >nul

echo [3/4] Starting Game Server...
echo ----------------------------------------
%log_msg% Starting Game Server >> "%logfile%"
start "Thunee Game Server" cmd /k "title Thunee Game Server && cd server && echo Starting Game Server... && node index.js"

REM Wait for game server to start
timeout /t 3 /nobreak >nul

echo [4/4] Starting Frontend Development Server...
echo ----------------------------------------
%log_msg% Starting Frontend Server >> "%logfile%"
echo Starting Frontend on http://localhost:5173/
start "Thunee Frontend" cmd /k "title Thunee Frontend && echo Starting Frontend Development Server... && npm run dev"

echo.
echo ========================================
echo    All Development Servers Started!
echo ========================================
echo.
echo Services running:
echo   - Frontend:     http://localhost:5173/
echo   - API Server:   http://localhost:3000/
echo   - API Docs:     http://localhost:3000/api-docs/
echo   - Game Server:  WebSocket connection
echo   - Video Server: WebRTC connection
echo.
echo Log file: %logfile%
echo.

%log_msg% All servers started successfully >> "%logfile%"

REM Wait a moment for all servers to fully initialize
timeout /t 5 /nobreak >nul

echo Opening application in browser...
start http://localhost:5173/

echo.
echo ========================================
echo    Development Environment Ready!
echo ========================================
echo.
echo To stop all servers:
echo   1. Close all the opened terminal windows
echo   2. Or press Ctrl+C in each terminal
echo.
echo Press any key to exit this startup script...
pause >nul

%log_msg% Startup script completed >> "%logfile%"
