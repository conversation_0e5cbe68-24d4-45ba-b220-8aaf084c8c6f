using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using System.Collections.Concurrent;
using ThuneeAPI.Models.VideoSignaling;

namespace ThuneeAPI.Hubs
{
    // Remove [Authorize] for now to test basic functionality
    // [Authorize]
    public class VideoSignalingHub : Hub
    {
        // Thread-safe collections to store user connections and rooms
        private static readonly ConcurrentDictionary<string, UserConnection> _connections = new();
        private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, RoomUser>> _rooms = new();

        public override async Task OnConnectedAsync()
        {
            var userId = Context.UserIdentifier ?? Context.ConnectionId;
            var userName = Context.User?.Identity?.Name ?? "Anonymous";

            var connection = new UserConnection
            {
                ConnectionId = Context.ConnectionId,
                UserId = userId,
                UserName = userName,
                ConnectedAt = DateTime.UtcNow
            };

            _connections.TryAdd(Context.ConnectionId, connection);
            Console.WriteLine($"Video Hub: User {userName} ({userId}) connected with connection {Context.ConnectionId}");

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            if (_connections.TryRemove(Context.ConnectionId, out var connection))
            {
                // Remove user from all rooms
                foreach (var room in _rooms.Values)
                {
                    if (room.TryRemove(Context.ConnectionId, out var removedUser))
                    {
                        // Notify other users in the room
                        await Clients.Group(connection.RoomId).SendAsync("UserLeft", new
                        {
                            ConnectionId = Context.ConnectionId,
                            UserId = connection.UserId,
                            UserName = connection.UserName
                        });

                        Console.WriteLine($"Video Hub: User {connection.UserName} left room {connection.RoomId}");
                    }
                }

                Console.WriteLine($"Video Hub: User {connection.UserName} disconnected");
            }

            await base.OnDisconnectedAsync(exception);
        }

        public async Task JoinRoom(JoinRoomRequest request)
        {
            try
            {
                if (!_connections.TryGetValue(Context.ConnectionId, out var connection))
                {
                    await Clients.Caller.SendAsync("Error", "Connection not found");
                    return;
                }

                // Update connection with room info
                connection.RoomId = request.RoomId;
                connection.UserName = request.UserName;

                // Add to SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, request.RoomId);

                // Add to room tracking
                var room = _rooms.GetOrAdd(request.RoomId, _ => new ConcurrentDictionary<string, RoomUser>());
                
                var roomUser = new RoomUser
                {
                    ConnectionId = Context.ConnectionId,
                    UserId = connection.UserId,
                    UserName = request.UserName
                };

                room.TryAdd(Context.ConnectionId, roomUser);

                // Get other users in the room
                var otherUsers = room.Values
                    .Where(u => u.ConnectionId != Context.ConnectionId)
                    .Select(u => new { u.ConnectionId, u.UserId, u.UserName })
                    .ToList();

                // Send list of existing users to the new user
                await Clients.Caller.SendAsync("RoomUsers", otherUsers);

                // Notify other users about the new user
                await Clients.GroupExcept(request.RoomId, Context.ConnectionId).SendAsync("UserJoined", new
                {
                    ConnectionId = Context.ConnectionId,
                    UserId = connection.UserId,
                    UserName = request.UserName
                });

                Console.WriteLine($"Video Hub: User {request.UserName} joined room {request.RoomId}. Room now has {room.Count} users.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Video Hub: Error in JoinRoom: {ex.Message}");
                await Clients.Caller.SendAsync("Error", $"Failed to join room: {ex.Message}");
            }
        }

        public async Task LeaveRoom(string roomId)
        {
            try
            {
                if (!_connections.TryGetValue(Context.ConnectionId, out var connection))
                {
                    return;
                }

                // Remove from SignalR group
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, roomId);

                // Remove from room tracking
                if (_rooms.TryGetValue(roomId, out var room))
                {
                    if (room.TryRemove(Context.ConnectionId, out var removedUser))
                    {
                        // Notify other users
                        await Clients.Group(roomId).SendAsync("UserLeft", new
                        {
                            ConnectionId = Context.ConnectionId,
                            UserId = connection.UserId,
                            UserName = connection.UserName
                        });

                        Console.WriteLine($"Video Hub: User {connection.UserName} left room {roomId}");
                    }

                    // Clean up empty room
                    if (room.IsEmpty)
                    {
                        _rooms.TryRemove(roomId, out _);
                        Console.WriteLine($"Video Hub: Room {roomId} deleted (empty)");
                    }
                }

                // Clear room from connection
                connection.RoomId = string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Video Hub: Error in LeaveRoom: {ex.Message}");
                await Clients.Caller.SendAsync("Error", $"Failed to leave room: {ex.Message}");
            }
        }

        public async Task SendSignal(SignalMessage message)
        {
            try
            {
                if (!_connections.TryGetValue(Context.ConnectionId, out var senderConnection))
                {
                    await Clients.Caller.SendAsync("Error", "Sender connection not found");
                    return;
                }

                // Validate that both users are in the same room
                if (!_rooms.TryGetValue(message.RoomId, out var room))
                {
                    await Clients.Caller.SendAsync("Error", "Room not found");
                    return;
                }

                if (!room.ContainsKey(Context.ConnectionId))
                {
                    await Clients.Caller.SendAsync("Error", "Sender not in room");
                    return;
                }

                if (!room.ContainsKey(message.ToUser))
                {
                    await Clients.Caller.SendAsync("Error", "Recipient not in room");
                    return;
                }

                // Forward the signal to the recipient
                await Clients.Client(message.ToUser).SendAsync("ReceiveSignal", new
                {
                    Type = message.Type,
                    FromUser = Context.ConnectionId,
                    Payload = message.Payload,
                    SenderName = senderConnection.UserName
                });

                Console.WriteLine($"Video Hub: Signal {message.Type} forwarded from {senderConnection.UserName} to {message.ToUser}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Video Hub: Error in SendSignal: {ex.Message}");
                await Clients.Caller.SendAsync("Error", $"Failed to send signal: {ex.Message}");
            }
        }

        // Get current room statistics (for debugging)
        public async Task GetRoomInfo(string roomId)
        {
            try
            {
                if (_rooms.TryGetValue(roomId, out var room))
                {
                    var users = room.Values.Select(u => new { u.ConnectionId, u.UserName }).ToList();
                    await Clients.Caller.SendAsync("RoomInfo", new
                    {
                        RoomId = roomId,
                        UserCount = room.Count,
                        Users = users
                    });
                }
                else
                {
                    await Clients.Caller.SendAsync("RoomInfo", new
                    {
                        RoomId = roomId,
                        UserCount = 0,
                        Users = new List<object>()
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Video Hub: Error in GetRoomInfo: {ex.Message}");
                await Clients.Caller.SendAsync("Error", $"Failed to get room info: {ex.Message}");
            }
        }
    }
}
