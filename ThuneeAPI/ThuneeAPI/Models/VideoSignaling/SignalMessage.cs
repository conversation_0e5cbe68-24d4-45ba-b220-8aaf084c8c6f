namespace ThuneeAPI.Models.VideoSignaling
{
    public class SignalMessage
    {
        public string Type { get; set; } = string.Empty; // "offer", "answer", "ice-candidate"
        public string FromUser { get; set; } = string.Empty;
        public string ToUser { get; set; } = string.Empty;
        public object Payload { get; set; } = new();
        public string RoomId { get; set; } = string.Empty;
    }

    public class JoinRoomRequest
    {
        public string RoomId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
    }

    public class UserConnection
    {
        public string ConnectionId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string RoomId { get; set; } = string.Empty;
        public DateTime ConnectedAt { get; set; } = DateTime.UtcNow;
    }

    public class RoomUser
    {
        public string ConnectionId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
    }
}
