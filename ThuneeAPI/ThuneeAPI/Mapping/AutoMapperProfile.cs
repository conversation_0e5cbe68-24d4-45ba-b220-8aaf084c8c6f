using AutoMapper;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Mapping;

public class AutoMapperProfile : Profile
{
    public AutoMapperProfile()
    {
        // User mappings
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.Statistics, opt => opt.Ignore()); // Will be populated separately

        CreateMap<RegisterUserDto, User>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.PasswordHash, opt => opt.Ignore())
            .ForMember(dest => dest.IsVerified, opt => opt.MapFrom(src => false))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.IsAdmin, opt => opt.MapFrom(src => false))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        // Competition mappings
        CreateMap<Competition, CompetitionDto>()
            .ForMember(dest => dest.Prizes, opt => opt.MapFrom(src => new PrizesDto
            {
                First = src.PrizeFirst,
                Second = src.PrizeSecond,
                Third = src.PrizeThird
            }));

        CreateMap<CreateCompetitionDto, Competition>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CurrentTeams, opt => opt.MapFrom(src => 0))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => "upcoming"))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        // Game mappings
        CreateMap<Game, GameDto>()
            .ForMember(dest => dest.Team1Players, opt => opt.Ignore()) // Will be populated manually
            .ForMember(dest => dest.Team2Players, opt => opt.Ignore()); // Will be populated manually

        CreateMap<CreateGameDto, Game>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.LobbyCode, opt => opt.Ignore()) // Will be generated
            .ForMember(dest => dest.Team2Name, opt => opt.MapFrom(src => "Team 2"))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => "waiting"))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow));

        // GameHand mappings
        CreateMap<GameHand, GameHandDto>()
            .ForMember(dest => dest.WinnerPlayerName, opt => opt.MapFrom(src => src.WinnerPlayer.Username));
            // PlayedCards mapping removed - no longer tracking individual cards

        // CompetitionTeam mappings
        CreateMap<CompetitionTeam, CompetitionTeamDto>()
            .ForMember(dest => dest.Player1, opt => opt.MapFrom(src => new PlayerDto 
            { 
                Id = src.Player1.Id, 
                Username = src.Player1.Username 
            }))
            .ForMember(dest => dest.Player2, opt => opt.MapFrom(src => new PlayerDto 
            { 
                Id = src.Player2.Id, 
                Username = src.Player2.Username 
            }));
    }
}
