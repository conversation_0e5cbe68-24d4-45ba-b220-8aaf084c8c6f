using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Text;
using ThuneeAPI.Infrastructure.Data;
using ThuneeAPI.Infrastructure.Data.Repositories;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Infrastructure.Services;
using ThuneeAPI.Core.Entities;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.File("logs/thunee-api-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();

// Database - Use Dapper with SQL Server
builder.Services.AddSingleton<IDbConnectionFactory, SqlConnectionFactory>();

// Repository registration
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IGameRepository, GameRepository>();
builder.Services.AddScoped<ICompetitionRepository, CompetitionRepository>();
builder.Services.AddScoped<ICompetitionTeamRepository, CompetitionTeamRepository>();

// CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins("http://localhost:5173", "http://localhost:3000")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

// JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var secretKey = jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey not configured");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey)),
        ClockSkew = TimeSpan.Zero
    };
});

// AutoMapper
builder.Services.AddAutoMapper(typeof(Program));

// Application Services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IGameService, GameService>();
builder.Services.AddScoped<ICompetitionService, CompetitionService>();
builder.Services.AddScoped<ILeaderboardService, LeaderboardService>();
builder.Services.AddScoped<ICompetitionPhaseService, CompetitionPhaseService>();
builder.Services.AddScoped<IEmailService, EmailService>();

// Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Thunee API",
        Version = "v1",
        Description = "A comprehensive API for the Thunee multiplayer card game",
        Contact = new OpenApiContact
        {
            Name = "Thunee Team",
            Email = "<EMAIL>"
        }
    });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Include XML comments
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Thunee API v1");
        c.RoutePrefix = "api-docs";
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Health check endpoint
app.MapGet("/health", () => new
{
    Status = "Healthy",
    Timestamp = DateTime.UtcNow,
    Version = "1.0.0",
    Environment = app.Environment.EnvironmentName
});

// Root endpoint
app.MapGet("/", () => new
{
    Message = "Thunee API Server",
    Version = "1.0.0",
    Documentation = "/api-docs",
    Health = "/health"
});

// Seed data in development - temporarily disabled for testing
// if (app.Environment.IsDevelopment())
// {
//     using var scope = app.Services.CreateScope();
//     var userRepository = scope.ServiceProvider.GetRequiredService<IUserRepository>();
//     var competitionRepository = scope.ServiceProvider.GetRequiredService<ICompetitionRepository>();
//     await SeedDevelopmentData(userRepository, competitionRepository);
// }

try
{
    Log.Information("Starting Thunee API Server");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}

// Development data seeding method
static async Task SeedDevelopmentData(IUserRepository userRepository, ICompetitionRepository competitionRepository)
{
    // Check if data already exists
    var existingUsers = await userRepository.GetAllAsync(1, 1);
    if (existingUsers.Any()) return; // Data already seeded

    // Create test users
    var users = new[]
    {
        new User
        {
            Id = Guid.NewGuid(),
            Username = "dean",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password"),
            IsVerified = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        },
        new User
        {
            Id = Guid.NewGuid(),
            Username = "testuser1",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            IsVerified = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        },
        new User
        {
            Id = Guid.NewGuid(),
            Username = "testuser2",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("password123"),
            IsVerified = true,
            IsActive = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        },
        new User
        {
            Id = Guid.NewGuid(),
            Username = "admin",
            Email = "<EMAIL>",
            PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
            IsVerified = true,
            IsActive = true,
            IsAdmin = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        }
    };

    // Create users
    foreach (var user in users)
    {
        await userRepository.CreateAsync(user);
    }

    // Create test competitions
    var competitions = new[]
    {
        new Competition
        {
            Id = Guid.NewGuid(),
            Name = "Summer Championship 2025",
            Description = "The biggest Thunee tournament of the summer!",
            StartDate = DateTime.UtcNow.AddDays(7),
            EndDate = DateTime.UtcNow.AddDays(37),
            Status = "upcoming",
            MaxTeams = 32,
            CurrentTeams = 5,
            EntryFee = 10.00m,
            PrizeFirst = "$500",
            PrizeSecond = "$250",
            PrizeThird = "$100",
            TotalPrizePool = 850.00m,
            IsPublic = true,
            AllowSpectators = true,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        },
        new Competition
        {
            Id = Guid.NewGuid(),
            Name = "Weekly Challenge",
            Description = "Weekly competition for all skill levels",
            StartDate = DateTime.UtcNow.AddDays(-3),
            EndDate = DateTime.UtcNow.AddDays(4),
            Status = "active",
            MaxTeams = 16,
            CurrentTeams = 12,
            EntryFee = 5.00m,
            PrizeFirst = "$100",
            PrizeSecond = "$50",
            PrizeThird = "$25",
            TotalPrizePool = 175.00m,
            IsPublic = true,
            AllowSpectators = true,
            CreatedAt = DateTime.UtcNow.AddDays(-5),
            UpdatedAt = DateTime.UtcNow
        }
    };

    // Create competitions
    foreach (var competition in competitions)
    {
        await competitionRepository.CreateAsync(competition);
    }

    Log.Information("Development data seeded successfully");
}
