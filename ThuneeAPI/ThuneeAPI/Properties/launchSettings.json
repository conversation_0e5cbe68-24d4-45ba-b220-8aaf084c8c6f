{"profiles": {"ThuneeAPI": {"commandName": "Project", "launchBrowser": true, "launchUrl": "api-docs", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:57229;http://localhost:57230"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:57561/", "sslPort": 44349}}}