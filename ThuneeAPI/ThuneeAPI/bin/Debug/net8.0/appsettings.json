{"ConnectionStrings": {"DefaultConnection": "Server=192.168.20.121; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "ThuneeAPI", "Audience": "ThuneeClient", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/thunee-api-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*"}