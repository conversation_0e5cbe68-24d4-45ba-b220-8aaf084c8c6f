{"ConnectionStrings": {"DefaultConnection": "Server=192.168.20.121; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "ThuneeAPI", "Audience": "ThuneeClient", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "File": {"Path": "logs/thunee-api-.txt", "LogLevel": {"Default": "Information"}}, "Cors": {"AllowedOrigins": ["http://**************:96", "http://**************:3001", "http://localhost:96", "http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]}, "ApiSettings": {"BaseUrl": "http://**************:8080", "Environment": "Production"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "logs/thunee-api-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7}}], "Enrich": ["FromLogContext"]}, "AllowedHosts": "*", "EmailSettings": {"displayName": "EasyGames Admin", "from": "<EMAIL>", "Host": "smtp.gmail.com", "Password": "obzh qvkf girv qtrw", "Port": 465, "UserName": "<EMAIL>", "To": "<EMAIL>", "UseSSL": true, "UseStartTls": false}}