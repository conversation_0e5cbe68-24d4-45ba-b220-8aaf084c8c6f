using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class CompetitionsController : ControllerBase
{
    private readonly ICompetitionService _competitionService;
    private readonly ILogger<CompetitionsController> _logger;

    public CompetitionsController(ICompetitionService competitionService, ILogger<CompetitionsController> logger)
    {
        _competitionService = competitionService;
        _logger = logger;
    }

    /// <summary>
    /// Get all competitions
    /// </summary>
    /// <returns>List of competitions</returns>
    [HttpGet]
    [ProducesResponseType(typeof(List<CompetitionDto>), 200)]
    public async Task<ActionResult<List<CompetitionDto>>> GetCompetitions()
    {
        try
        {
            var competitions = await _competitionService.GetCompetitionsAsync();
            
            return Ok(new
            {
                success = true,
                data = competitions
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competitions");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get competition by ID
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>Competition details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(CompetitionDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CompetitionDto>> GetCompetition(Guid id)
    {
        try
        {
            var competition = await _competitionService.GetCompetitionByIdAsync(id);
            
            return Ok(new
            {
                success = true,
                data = competition
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Create a new competition (Admin only)
    /// </summary>
    /// <param name="createDto">Competition creation details</param>
    /// <returns>Created competition</returns>
    [HttpPost]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionDto), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<CompetitionDto>> CreateCompetition([FromBody] CreateCompetitionDto createDto)
    {
        try
        {
            var competition = await _competitionService.CreateCompetitionAsync(createDto);
            
            _logger.LogInformation("Competition created: {CompetitionId} - {Name}", competition.Id, competition.Name);
            
            return CreatedAtAction(nameof(GetCompetition), new { id = competition.Id }, new
            {
                success = true,
                data = competition,
                message = "Competition created successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating competition");
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Create a team for a competition
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <param name="createDto">Team creation details</param>
    /// <returns>Team details with invite code</returns>
    [HttpPost("{id}/teams/create")]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionTeamDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CompetitionTeamDto>> CreateCompetitionTeam(Guid id, [FromBody] CreateCompetitionTeamDto createDto)
    {
        try
        {
            var userId = GetCurrentUserId();

            var team = await _competitionService.CreateCompetitionTeamAsync(id, createDto, userId);

            _logger.LogInformation("User {UserId} created team for competition {CompetitionId}", userId, id);

            return Ok(new
            {
                success = true,
                data = team,
                message = "Team created successfully. Share the invite code with your partner."
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating team for competition {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Join a team using invite code
    /// </summary>
    /// <param name="joinDto">Join team details</param>
    /// <returns>Team details</returns>
    [HttpPost("teams/join")]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionTeamDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<CompetitionTeamDto>> JoinCompetitionTeam([FromBody] JoinCompetitionTeamDto joinDto)
    {
        try
        {
            var userId = GetCurrentUserId();

            var team = await _competitionService.JoinCompetitionTeamAsync(joinDto, userId);

            _logger.LogInformation("User {UserId} joined team using invite code", userId);

            return Ok(new
            {
                success = true,
                data = team,
                message = "Successfully joined the team"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining team with invite code");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get competition status for current user
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>Competition status</returns>
    [HttpGet("{id}/status")]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionStatusDto), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CompetitionStatusDto>> GetCompetitionStatus(Guid id)
    {
        try
        {
            var userId = GetCurrentUserId();
            var status = await _competitionService.GetCompetitionStatusAsync(id, userId);

            return Ok(new
            {
                success = true,
                data = status
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition status: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Join a competition (legacy endpoint)
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <param name="joinDto">Join competition details</param>
    /// <returns>Team registration details</returns>
    [HttpPost("{id}/join")]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionTeamDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CompetitionTeamDto>> JoinCompetition(Guid id, [FromBody] JoinCompetitionDto joinDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            joinDto.CompetitionId = id;
            
            var team = await _competitionService.JoinCompetitionAsync(joinDto, userId);
            
            _logger.LogInformation("User {UserId} joined competition {CompetitionId} with team {TeamName}", 
                userId, id, joinDto.TeamName);
            
            return Ok(new
            {
                success = true,
                data = team,
                message = "Successfully joined competition"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining competition: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get competition teams
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>List of registered teams</returns>
    [HttpGet("{id}/teams")]
    [ProducesResponseType(typeof(List<CompetitionTeamDto>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<List<CompetitionTeamDto>>> GetCompetitionTeams(Guid id)
    {
        try
        {
            var teams = await _competitionService.GetCompetitionTeamsAsync(id);
            
            return Ok(new
            {
                success = true,
                data = teams
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition teams: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get competition leaderboard
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Items per page</param>
    /// <returns>Competition leaderboard</returns>
    [HttpGet("{id}/leaderboard")]
    [ProducesResponseType(typeof(LeaderboardResponseDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<LeaderboardResponseDto>> GetCompetitionLeaderboard(
        Guid id, 
        [FromQuery] int page = 1, 
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var leaderboard = await _competitionService.GetCompetitionLeaderboardAsync(id, page, pageSize);
            
            return Ok(new
            {
                success = true,
                data = leaderboard
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition leaderboard: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Update competition (Admin only)
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <param name="updateDto">Update details</param>
    /// <returns>Updated competition</returns>
    [HttpPut("{id}")]
    [Authorize]
    [ProducesResponseType(typeof(CompetitionDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<CompetitionDto>> UpdateCompetition(Guid id, [FromBody] UpdateCompetitionDto updateDto)
    {
        try
        {
            var competition = await _competitionService.UpdateCompetitionAsync(id, updateDto);
            
            _logger.LogInformation("Competition updated: {CompetitionId}", id);
            
            return Ok(new
            {
                success = true,
                data = competition,
                message = "Competition updated successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating competition: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Process game result for competition scoring
    /// </summary>
    /// <param name="gameResult">Game result details</param>
    /// <returns>Success message</returns>
    [HttpPost("games/result")]
    [Authorize]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult> ProcessGameResult([FromBody] CompetitionGameResultDto gameResult)
    {
        try
        {
            await _competitionService.ProcessGameResultAsync(gameResult);

            _logger.LogInformation("Game result processed for competition {CompetitionId}", gameResult.CompetitionId);

            return Ok(new
            {
                success = true,
                message = "Game result processed successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing game result for competition {CompetitionId}", gameResult.CompetitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Delete competition (Admin only)
    /// </summary>
    /// <param name="id">Competition ID</param>
    /// <returns>Success message</returns>
    [HttpDelete("{id}")]
    [Authorize]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteCompetition(Guid id)
    {
        try
        {
            var deleted = await _competitionService.DeleteCompetitionAsync(id);
            
            if (!deleted)
            {
                return NotFound(new
                {
                    success = false,
                    error = "Competition not found"
                });
            }
            
            _logger.LogInformation("Competition deleted: {CompetitionId}", id);
            
            return Ok(new
            {
                success = true,
                message = "Competition deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition: {CompetitionId}", id);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid token");
        }
        return userId;
    }
}
