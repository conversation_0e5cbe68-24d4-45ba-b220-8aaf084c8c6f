using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class GameSettingsController : ControllerBase
{
    private readonly IGameSettingsService _gameSettingsService;
    private readonly ILogger<GameSettingsController> _logger;

    public GameSettingsController(IGameSettingsService gameSettingsService, ILogger<GameSettingsController> logger)
    {
        _gameSettingsService = gameSettingsService;
        _logger = logger;
    }

    /// <summary>
    /// Get current game settings
    /// </summary>
    /// <returns>Current game settings</returns>
    [HttpGet]
    [ProducesResponseType(typeof(GameSettingsDto), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<GameSettingsDto>> GetGameSettings()
    {
        try
        {
            var settings = await _gameSettingsService.GetGameSettingsAsync();
            
            _logger.LogInformation("Game settings retrieved successfully");
            
            return Ok(new
            {
                success = true,
                data = settings,
                message = "Game settings retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving game settings");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred while retrieving game settings"
            });
        }
    }

    /// <summary>
    /// Update game settings
    /// </summary>
    /// <param name="updateDto">Settings to update</param>
    /// <returns>Updated game settings</returns>
    [HttpPut]
    [ProducesResponseType(typeof(GameSettingsDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<GameSettingsDto>> UpdateGameSettings([FromBody] UpdateGameSettingsDto updateDto)
    {
        try
        {
            // Get current user ID if authenticated (optional for now)
            Guid? userId = null;
            if (User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(userIdClaim, out var parsedUserId))
                {
                    userId = parsedUserId;
                }
            }

            var updatedSettings = await _gameSettingsService.UpdateGameSettingsAsync(updateDto, userId);
            
            _logger.LogInformation("Game settings updated successfully by user {UserId}", userId?.ToString() ?? "anonymous");
            
            return Ok(new
            {
                success = true,
                data = updatedSettings,
                message = "Game settings updated successfully"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid game settings update request");
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating game settings");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred while updating game settings"
            });
        }
    }

    /// <summary>
    /// Reset game settings to defaults
    /// </summary>
    /// <returns>Reset game settings</returns>
    [HttpPost("reset")]
    [ProducesResponseType(typeof(GameSettingsDto), 200)]
    [ProducesResponseType(500)]
    public async Task<ActionResult<GameSettingsDto>> ResetGameSettings()
    {
        try
        {
            // Get current user ID if authenticated (optional for now)
            Guid? userId = null;
            if (User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(userIdClaim, out var parsedUserId))
                {
                    userId = parsedUserId;
                }
            }

            var resetSettings = await _gameSettingsService.ResetGameSettingsAsync(userId);
            
            _logger.LogInformation("Game settings reset to defaults by user {UserId}", userId?.ToString() ?? "anonymous");
            
            return Ok(new
            {
                success = true,
                data = resetSettings,
                message = "Game settings reset to defaults successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting game settings");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred while resetting game settings"
            });
        }
    }
}
