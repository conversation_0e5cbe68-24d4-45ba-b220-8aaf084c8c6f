using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class LeaderboardController : ControllerBase
{
    private readonly ILeaderboardService _leaderboardService;
    private readonly ILogger<LeaderboardController> _logger;

    public LeaderboardController(ILeaderboardService leaderboardService, ILogger<LeaderboardController> logger)
    {
        _leaderboardService = leaderboardService;
        _logger = logger;
    }

    /// <summary>
    /// Get global leaderboard
    /// </summary>
    /// <param name="timeFrame">Time frame filter (all, weekly, monthly)</param>
    /// <param name="sortBy">Sort criteria (score, winRate, gamesPlayed)</param>
    /// <param name="page">Page number</param>
    /// <param name="limit">Items per page</param>
    /// <returns>Global leaderboard</returns>
    [HttpGet("global")]
    [ProducesResponseType(typeof(LeaderboardResponseDto), 200)]
    public async Task<ActionResult<LeaderboardResponseDto>> GetGlobalLeaderboard(
        [FromQuery] string timeFrame = "all",
        [FromQuery] string sortBy = "score",
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        try
        {
            var leaderboard = await _leaderboardService.GetGlobalLeaderboardAsync(timeFrame, sortBy, page, limit);
            
            return Ok(new
            {
                success = true,
                data = leaderboard
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting global leaderboard");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get weekly leaderboard
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="limit">Items per page</param>
    /// <returns>Weekly leaderboard</returns>
    [HttpGet("weekly")]
    [ProducesResponseType(typeof(LeaderboardResponseDto), 200)]
    public async Task<ActionResult<LeaderboardResponseDto>> GetWeeklyLeaderboard(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        try
        {
            var leaderboard = await _leaderboardService.GetWeeklyLeaderboardAsync(page, limit);
            
            return Ok(new
            {
                success = true,
                data = leaderboard
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting weekly leaderboard");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get monthly leaderboard
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="limit">Items per page</param>
    /// <returns>Monthly leaderboard</returns>
    [HttpGet("monthly")]
    [ProducesResponseType(typeof(LeaderboardResponseDto), 200)]
    public async Task<ActionResult<LeaderboardResponseDto>> GetMonthlyLeaderboard(
        [FromQuery] int page = 1,
        [FromQuery] int limit = 20)
    {
        try
        {
            var leaderboard = await _leaderboardService.GetMonthlyLeaderboardAsync(page, limit);
            
            return Ok(new
            {
                success = true,
                data = leaderboard
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting monthly leaderboard");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }
}
