using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class AuthController : ControllerBase
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService;
        _logger = logger;
    }

    /// <summary>
    /// Register a new user
    /// </summary>
    /// <param name="registerDto">User registration details</param>
    /// <returns>Authentication response with user details and token</returns>
    [HttpPost("register")]
    [ProducesResponseType(typeof(AuthResponseDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(409)]
    public async Task<ActionResult<AuthResponseDto>> Register([FromBody] RegisterUserDto registerDto)
    {
        try
        {
            _logger.LogInformation("User registration attempt for username: {Username}", registerDto.Username);
            
            var result = await _authService.RegisterAsync(registerDto);
            
            _logger.LogInformation("User registered successfully: {UserId}", result.User.Id);
            
            return Ok(new
            {
                success = true,
                data = result,
                message = "User registered successfully"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Registration failed for username {Username}: {Error}", registerDto.Username, ex.Message);
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Registration conflict for username {Username}: {Error}", registerDto.Username, ex.Message);
            return Conflict(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during registration for username: {Username}", registerDto.Username);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred during registration"
            });
        }
    }

    /// <summary>
    /// Validate JWT token
    /// </summary>
    /// <returns>Token validation result</returns>
    [HttpGet("validate")]
    [Authorize]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    public ActionResult ValidateToken()
    {
        try
        {
            var userId = GetCurrentUserId();
            _logger.LogInformation("Token validated for user: {UserId}", userId);

            return Ok(new
            {
                success = true,
                message = "Token is valid",
                userId = userId
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Token validation failed");
            return Unauthorized(new
            {
                success = false,
                message = "Invalid token"
            });
        }
    }

    /// <summary>
    /// Get current user information
    /// </summary>
    /// <returns>Current user details</returns>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(UserDto), 200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<UserDto>> GetCurrentUser()
    {
        try
        {
            var userId = GetCurrentUserId();
            var user = await _authService.GetUserByIdAsync(userId);

            if (user == null)
            {
                return NotFound(new
                {
                    success = false,
                    message = "User not found"
                });
            }

            _logger.LogInformation("User info retrieved: {UserId}", userId);

            return Ok(new
            {
                success = true,
                data = user,
                message = "User information retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current user");
            return StatusCode(500, new
            {
                success = false,
                message = "Failed to retrieve user information"
            });
        }
    }

    /// <summary>
    /// Login user
    /// </summary>
    /// <param name="loginDto">User login credentials</param>
    /// <returns>Authentication response with user details and token</returns>
    [HttpPost("login")]
    [ProducesResponseType(typeof(AuthResponseDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<AuthResponseDto>> Login([FromBody] LoginUserDto loginDto)
    {
        try
        {
            _logger.LogInformation("Login attempt for username: {Username}", loginDto.Username);
            
            var result = await _authService.LoginAsync(loginDto);
            
            _logger.LogInformation("User logged in successfully: {UserId}", result.User.Id);
            
            return Ok(new
            {
                success = true,
                data = result,
                message = "Login successful"
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Login failed for username {Username}: {Error}", loginDto.Username, ex.Message);
            return Unauthorized(new
            {
                success = false,
                error = "Invalid username or password"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Login failed for username {Username}: {Error}", loginDto.Username, ex.Message);
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during login for username: {Username}", loginDto.Username);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred during login"
            });
        }
    }



    /// <summary>
    /// Verify OTP (Development: use "1234" as the correct OTP)
    /// </summary>
    /// <param name="verifyOtpDto">OTP verification details</param>
    /// <returns>Success message</returns>
    [HttpPost("verify-otp")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public Task<ActionResult> VerifyOtp([FromBody] VerifyOtpDto verifyOtpDto)
    {
        try
        {
            // For development, hardcode the correct OTP as "1234"
            const string correctOtp = "1234";

            if (verifyOtpDto.Otp != correctOtp)
            {
                return Task.FromResult<ActionResult>(BadRequest(new
                {
                    success = false,
                    error = "Invalid OTP"
                }));
            }

            _logger.LogInformation("OTP verified successfully for email: {Email}", verifyOtpDto.Email);

            return Task.FromResult<ActionResult>(Ok(new
            {
                success = true,
                message = "OTP verified successfully"
            }));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during OTP verification");
            return Task.FromResult<ActionResult>(StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred during OTP verification"
            }));
        }
    }

    /// <summary>
    /// Logout user
    /// </summary>
    /// <returns>Success message</returns>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    public async Task<ActionResult> Logout()
    {
        try
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
            {
                return Unauthorized(new
                {
                    success = false,
                    error = "Invalid token"
                });
            }

            await _authService.LogoutAsync(userId);

            _logger.LogInformation("User logged out successfully: {UserId}", userId);

            return Ok(new
            {
                success = true,
                message = "Logout successful"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred during logout"
            });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid token");
        }
        return userId;
    }
}
