using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[Authorize]
public class GamesController : ControllerBase
{
    private readonly IGameService _gameService;
    private readonly ILogger<GamesController> _logger;

    public GamesController(IGameService gameService, ILogger<GamesController> logger)
    {
        _gameService = gameService;
        _logger = logger;
    }

    /// <summary>
    /// Create a new game lobby
    /// </summary>
    /// <param name="createGameDto">Game creation details</param>
    /// <returns>Created game information</returns>
    [HttpPost]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<GameDto>> CreateGame([FromBody] CreateGameDto createGameDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            var game = await _gameService.CreateGameAsync(createGameDto, userId);
            
            _logger.LogInformation("Game created successfully: {LobbyCode} by user {UserId}", game.LobbyCode, userId);
            
            return Ok(new
            {
                success = true,
                data = game,
                message = "Game created successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating game");
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
    }

    /// <summary>
    /// Get game information by lobby code
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <returns>Game information</returns>
    [HttpGet("{lobbyCode}")]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> GetGame(string lobbyCode)
    {
        try
        {
            var game = await _gameService.GetGameByLobbyCodeAsync(lobbyCode);
            
            return Ok(new
            {
                success = true,
                data = game
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Join an existing game
    /// </summary>
    /// <param name="lobbyCode">The lobby code to join</param>
    /// <param name="joinGameDto">Join game details</param>
    /// <returns>Updated game information</returns>
    [HttpPost("{lobbyCode}/join")]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> JoinGame(string lobbyCode, [FromBody] JoinGameDto joinGameDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            joinGameDto.LobbyCode = lobbyCode;
            
            var game = await _gameService.JoinGameAsync(joinGameDto, userId);
            
            _logger.LogInformation("User {UserId} joined game {LobbyCode}", userId, lobbyCode);
            
            return Ok(new
            {
                success = true,
                data = game,
                message = "Successfully joined game"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Start a game (host only)
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <returns>Updated game information</returns>
    [HttpPost("{lobbyCode}/start")]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> StartGame(string lobbyCode)
    {
        try
        {
            var userId = GetCurrentUserId();
            var game = await _gameService.StartGameAsync(lobbyCode, userId);
            
            _logger.LogInformation("Game started: {LobbyCode} by user {UserId}", lobbyCode, userId);
            
            return Ok(new
            {
                success = true,
                data = game,
                message = "Game started successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Unauthorized(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Record the result of a hand
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <param name="handResultDto">Hand result details</param>
    /// <returns>Updated game information</returns>
    [HttpPost("{lobbyCode}/hand-result")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> RecordHandResult(string lobbyCode, [FromBody] RecordHandResultDto handResultDto)
    {
        try
        {
            handResultDto.LobbyCode = lobbyCode;
            var game = await _gameService.RecordHandResultAsync(handResultDto);
            
            _logger.LogInformation("Hand result recorded for game {LobbyCode}: Ball {Ball}, Hand {Hand}", 
                lobbyCode, handResultDto.BallNumber, handResultDto.HandNumber);
            
            return Ok(new
            {
                success = true,
                data = game,
                message = "Hand result recorded successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording hand result for game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Record the result of a ball
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <param name="ballResultDto">Ball result details</param>
    /// <returns>Updated game information</returns>
    [HttpPost("{lobbyCode}/ball-result")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> RecordBallResult(string lobbyCode, [FromBody] RecordBallResultDto ballResultDto)
    {
        try
        {
            _logger.LogInformation("Recording ball result for lobby {LobbyCode}: Ball {Ball}, Winner: Team {Winner}, Competition: {Competition}",
                lobbyCode, ballResultDto.BallNumber, ballResultDto.WinnerTeam, ballResultDto.CompetitionId);

            ballResultDto.LobbyCode = lobbyCode;
            var game = await _gameService.RecordBallResultAsync(ballResultDto);

            _logger.LogInformation("Ball result recorded for game {LobbyCode}: Ball {Ball}, Winner: Team {Winner}",
                lobbyCode, ballResultDto.BallNumber, ballResultDto.WinnerTeam);

            return Ok(new
            {
                success = true,
                data = game,
                message = "Ball result recorded successfully"
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Ball result recording failed for lobby {LobbyCode}: {Error}", lobbyCode, ex.Message);
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording ball result for game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Record the final game result
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <param name="gameResultDto">Game result details</param>
    /// <returns>Updated game information</returns>
    [HttpPost("{lobbyCode}/game-result")]
    [ProducesResponseType(typeof(GameDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GameDto>> RecordGameResult(string lobbyCode, [FromBody] GameResultDto gameResultDto)
    {
        try
        {
            gameResultDto.LobbyCode = lobbyCode;
            var game = await _gameService.RecordGameResultAsync(gameResultDto);
            
            _logger.LogInformation("Game completed: {LobbyCode}, Winner: Team {WinnerTeam}", 
                lobbyCode, gameResultDto.WinnerTeam);
            
            return Ok(new
            {
                success = true,
                data = game,
                message = "Game result recorded successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording game result for game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get user's game history
    /// </summary>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Items per page</param>
    /// <returns>List of user's games</returns>
    [HttpGet("user/history")]
    [ProducesResponseType(typeof(List<GameDto>), 200)]
    public async Task<ActionResult<List<GameDto>>> GetUserGames([FromQuery] int page = 1, [FromQuery] int pageSize = 20)
    {
        try
        {
            var userId = GetCurrentUserId();
            var games = await _gameService.GetUserGamesAsync(userId, page, pageSize);
            
            return Ok(new
            {
                success = true,
                data = games,
                pagination = new
                {
                    currentPage = page,
                    itemsPerPage = pageSize
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user games");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get detailed hand history for a game
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <returns>List of game hands</returns>
    [HttpGet("{lobbyCode}/hands")]
    [ProducesResponseType(typeof(List<GameHandDto>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<List<GameHandDto>>> GetGameHands(string lobbyCode)
    {
        try
        {
            var hands = await _gameService.GetGameHandsAsync(lobbyCode);

            return Ok(new
            {
                success = true,
                data = hands
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting game hands: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Get current ball scores for a game
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <returns>Current ball scores</returns>
    [HttpGet("{lobbyCode}/ball-scores")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> GetBallScores(string lobbyCode)
    {
        try
        {
            var ballScores = await _gameService.GetBallScoresAsync(lobbyCode);

            return Ok(new
            {
                success = true,
                data = ballScores
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting ball scores: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    // Card play recording removed - no longer tracking individual cards

    // Jordhi call recording removed - no longer tracking individual calls



    /// <summary>
    /// Get active games/lobbies
    /// </summary>
    /// <returns>List of active games</returns>
    [HttpGet("active")]
    [ProducesResponseType(typeof(List<GameDto>), 200)]
    public async Task<ActionResult<List<GameDto>>> GetActiveGames()
    {
        try
        {
            var games = await _gameService.GetActiveGamesAsync();

            return Ok(new
            {
                success = true,
                data = games,
                message = "Active games retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active games");
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Leave a game
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <returns>Success response</returns>
    [HttpPost("{lobbyCode}/leave")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> LeaveGame(string lobbyCode)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _gameService.LeaveGameAsync(lobbyCode, userId);

            _logger.LogInformation("User {UserId} left game {LobbyCode}", userId, lobbyCode);

            return Ok(new
            {
                success = true,
                message = "Left game successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving game: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    /// <summary>
    /// Set player ready status
    /// </summary>
    /// <param name="lobbyCode">The lobby code</param>
    /// <param name="readyDto">Ready status</param>
    /// <returns>Success response</returns>
    [HttpPost("{lobbyCode}/ready")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> SetPlayerReady(string lobbyCode, [FromBody] SetPlayerReadyDto readyDto)
    {
        try
        {
            var userId = GetCurrentUserId();
            await _gameService.SetPlayerReadyAsync(lobbyCode, userId, readyDto.Ready);

            _logger.LogInformation("User {UserId} set ready status to {Ready} in game {LobbyCode}",
                userId, readyDto.Ready, lobbyCode);

            return Ok(new
            {
                success = true,
                message = "Ready status updated successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting player ready: {LobbyCode}", lobbyCode);
            return StatusCode(500, new
            {
                success = false,
                error = "An unexpected error occurred"
            });
        }
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid token");
        }
        return userId;
    }
}
