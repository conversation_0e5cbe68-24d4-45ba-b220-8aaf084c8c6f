[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_7", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType9`4[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "ThuneeAPI.Application.DTOs.LoginUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.AuthResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.UserDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "ThuneeAPI.Application.DTOs.RegisterUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.AuthResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 409}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.AuthController", "Method": "VerifyOtp", "RelativePath": "api/Auth/verify-otp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "verifyOtpDto", "Type": "ThuneeAPI.Application.DTOs.VerifyOtpDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "GetCompetitions", "RelativePath": "api/Competitions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ThuneeAPI.Application.DTOs.CompetitionDto, ThuneeAPI.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "CreateCompetition", "RelativePath": "api/Competitions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "ThuneeAPI.Application.DTOs.CreateCompetitionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionDto", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "GetCompetition", "RelativePath": "api/Competitions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "UpdateCompetition", "RelativePath": "api/Competitions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "updateDto", "Type": "ThuneeAPI.Application.DTOs.UpdateCompetitionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "DeleteCompetition", "RelativePath": "api/Competitions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "JoinCompetition", "RelativePath": "api/Competitions/{id}/join", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "joinDto", "Type": "ThuneeAPI.Application.DTOs.JoinCompetitionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionTeamDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "GetCompetitionLeaderboard", "RelativePath": "api/Competitions/{id}/leaderboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.LeaderboardResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "GetCompetitionStatus", "RelativePath": "api/Competitions/{id}/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionStatusDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "GetCompetitionTeams", "RelativePath": "api/Competitions/{id}/teams", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ThuneeAPI.Application.DTOs.CompetitionTeamDto, ThuneeAPI.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "CreateCompetitionTeam", "RelativePath": "api/Competitions/{id}/teams/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "createDto", "Type": "ThuneeAPI.Application.DTOs.CreateCompetitionTeamDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionTeamDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "ProcessGameResult", "RelativePath": "api/Competitions/games/result", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "gameResult", "Type": "ThuneeAPI.Application.DTOs.CompetitionGameResultDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.CompetitionsController", "Method": "JoinCompetitionTeam", "RelativePath": "api/Competitions/teams/join", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "joinDto", "Type": "ThuneeAPI.Application.DTOs.JoinCompetitionTeamDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.CompetitionTeamDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "CreateGame", "RelativePath": "api/Games", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createGameDto", "Type": "ThuneeAPI.Application.DTOs.CreateGameDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "GetGame", "RelativePath": "api/Games/{lobbyCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "RecordGameResult", "RelativePath": "api/Games/{lobbyCode}/game-result", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}, {"Name": "gameResultDto", "Type": "ThuneeAPI.Application.DTOs.GameResultDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "RecordHandResult", "RelativePath": "api/Games/{lobbyCode}/hand-result", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}, {"Name": "handResultDto", "Type": "ThuneeAPI.Application.DTOs.RecordHandResultDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "GetGameHands", "RelativePath": "api/Games/{lobbyCode}/hands", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ThuneeAPI.Application.DTOs.GameHandDto, ThuneeAPI.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Games/{lobbyCode}/join", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}, {"Name": "joinGameDto", "Type": "ThuneeAPI.Application.DTOs.JoinGameDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "LeaveGame", "RelativePath": "api/Games/{lobbyCode}/leave", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "SetPlayerReady", "RelativePath": "api/Games/{lobbyCode}/ready", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}, {"Name": "readyDto", "Type": "ThuneeAPI.Application.DTOs.SetPlayerReadyDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "StartGame", "RelativePath": "api/Games/{lobbyCode}/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "lobbyCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 404}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "GetActiveGames", "RelativePath": "api/Games/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ThuneeAPI.Application.DTOs.GameDto, ThuneeAPI.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "RecordCardPlay", "RelativePath": "api/Games/card-play", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cardPlayDto", "Type": "ThuneeAPI.Application.DTOs.CardPlayDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "RecordJordhiCall", "RelativePath": "api/Games/jordhi-call", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "jord<PERSON><PERSON><PERSON>", "Type": "ThuneeAPI.Application.DTOs.JordhiCallDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}]}, {"ContainingType": "ThuneeAPI.Controllers.GamesController", "Method": "GetUserGames", "RelativePath": "api/Games/user/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ThuneeAPI.Application.DTOs.GameDto, ThuneeAPI.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.GameSettingsController", "Method": "GetGameSettings", "RelativePath": "api/GameSettings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameSettingsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ThuneeAPI.Controllers.GameSettingsController", "Method": "UpdateGameSettings", "RelativePath": "api/GameSettings", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "ThuneeAPI.Application.DTOs.UpdateGameSettingsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameSettingsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["application/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ThuneeAPI.Controllers.GameSettingsController", "Method": "ResetGameSettings", "RelativePath": "api/GameSettings/reset", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.GameSettingsDto", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "ThuneeAPI.Controllers.LeaderboardController", "Method": "GetGlobalLeaderboard", "RelativePath": "api/Leaderboard/global", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "timeFrame", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.LeaderboardResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.LeaderboardController", "Method": "GetMonthlyLeaderboard", "RelativePath": "api/Leaderboard/monthly", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.LeaderboardResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "ThuneeAPI.Controllers.LeaderboardController", "Method": "GetWeeklyLeaderboard", "RelativePath": "api/Leaderboard/weekly", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "ThuneeAPI.Application.DTOs.LeaderboardResponseDto", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c__DisplayClass0_0", "Method": "<<Main>$>b__6", "RelativePath": "health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType8`4[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}]