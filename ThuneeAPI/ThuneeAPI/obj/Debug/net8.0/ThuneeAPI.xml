<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ThuneeAPI</name>
    </assembly>
    <members>
        <member name="M:ThuneeAPI.Controllers.AuthController.Register(ThuneeAPI.Application.DTOs.RegisterUserDto)">
            <summary>
            Register a new user
            </summary>
            <param name="registerDto">User registration details</param>
            <returns>Authentication response with user details and token</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.ValidateToken">
            <summary>
            Validate JWT token
            </summary>
            <returns>Token validation result</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get current user information
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.Login(ThuneeAPI.Application.DTOs.LoginUserDto)">
            <summary>
            Login user
            </summary>
            <param name="loginDto">User login credentials</param>
            <returns>Authentication response with user details and token</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.VerifyOtp(ThuneeAPI.Application.DTOs.VerifyOtpDto)">
            <summary>
            Verify OTP (Development: use "1234" as the correct OTP)
            </summary>
            <param name="verifyOtpDto">OTP verification details</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.Logout">
            <summary>
            Logout user
            </summary>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(System.Guid,ThuneeAPI.Controllers.AdvancePhaseDto)">
            <summary>
            Advance competition to next phase (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetEligibleTeams(System.Guid)">
            <summary>
            Get teams eligible for next phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetTeamsForPhase(System.Guid,System.String)">
            <summary>
            Get teams for current phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CreatePhaseLobby(System.Guid,ThuneeAPI.Application.DTOs.CreateCompetitionPhaseLobbyDto)">
            <summary>
            Create a knockout phase lobby
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseLobbies(System.Guid,System.String)">
            <summary>
            Get all lobbies for a specific phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetLobbyByCode(System.String)">
            <summary>
            Get lobby details by lobby code
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetLobbyById(System.Guid)">
            <summary>
            Get lobby details by lobby ID
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.ScheduleMatch(System.Guid,ThuneeAPI.Application.DTOs.MatchSchedulingDto)">
            <summary>
            Schedule a match for a lobby
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.ProcessGameResult(System.Guid,System.Guid)">
            <summary>
            Process game result for knockout match
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetMatchResult(System.Guid)">
            <summary>
            Get match result evaluation for a lobby
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.DeletePhaseLobby(System.Guid)">
            <summary>
            Delete a phase lobby
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.SetLobbyWinner(System.Guid,ThuneeAPI.Controllers.SetLobbyWinnerDto)">
            <summary>
            Set lobby winner
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetAdminLobbyView(System.Guid,System.String)">
            <summary>
            Get admin lobby view for competition management
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseWinners(System.Guid,System.String)">
            <summary>
            Get winners from a completed phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(System.Guid,System.String,System.String)">
            <summary>
            Advance teams to next phase (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CanAdvancePhase(System.Guid,System.String)">
            <summary>
            Check if phase can be advanced
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.SendMatchNotification(System.Guid,System.String)">
            <summary>
            Send match notification for a lobby
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CreateMultipleLobbies(System.Guid,System.String,ThuneeAPI.Application.DTOs.CreateMultipleLobbiesDto)">
            <summary>
            Create multiple lobbies for a phase (bulk creation)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CreateAllPhaseMatches(System.Guid,System.String,ThuneeAPI.Application.DTOs.CreateAllPhaseMatchesDto)">
            <summary>
            Create all knockout matches for a phase automatically
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GenerateBracket(System.Guid,System.String)">
            <summary>
            Generate bracket for phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetCompetitionBrackets(System.Guid)">
            <summary>
            Get competition brackets
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.ProcessPhaseEnd(System.Guid)">
            <summary>
            Process phase end
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CanAdvanceToNextPhase(System.Guid)">
            <summary>
            Check if competition can advance to next phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CalculatePhaseRankings(System.Guid,System.String)">
            <summary>
            Calculate phase rankings
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitions">
            <summary>
            Get all competitions
            </summary>
            <returns>List of competitions</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetition(System.Guid)">
            <summary>
            Get competition by ID
            </summary>
            <param name="id">Competition ID</param>
            <returns>Competition details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.CreateCompetition(ThuneeAPI.Application.DTOs.CreateCompetitionDto)">
            <summary>
            Create a new competition (Admin only)
            </summary>
            <param name="createDto">Competition creation details</param>
            <returns>Created competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.CreateCompetitionTeam(System.Guid,ThuneeAPI.Application.DTOs.CreateCompetitionTeamDto)">
            <summary>
            Create a team for a competition
            </summary>
            <param name="id">Competition ID</param>
            <param name="createDto">Team creation details</param>
            <returns>Team details with invite code</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.JoinCompetitionTeam(ThuneeAPI.Application.DTOs.JoinCompetitionTeamDto)">
            <summary>
            Join a team using invite code
            </summary>
            <param name="joinDto">Join team details</param>
            <returns>Team details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionStatus(System.Guid)">
            <summary>
            Get competition status for current user
            </summary>
            <param name="id">Competition ID</param>
            <returns>Competition status</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.JoinCompetition(System.Guid,ThuneeAPI.Application.DTOs.JoinCompetitionDto)">
            <summary>
            Join a competition (legacy endpoint)
            </summary>
            <param name="id">Competition ID</param>
            <param name="joinDto">Join competition details</param>
            <returns>Team registration details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionTeams(System.Guid)">
            <summary>
            Get competition teams
            </summary>
            <param name="id">Competition ID</param>
            <returns>List of registered teams</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetAdvancedCompetitionTeams(System.Guid)">
            <summary>
            Get teams that have advanced to the next phase for a competition
            </summary>
            <param name="id">Competition ID</param>
            <returns>List of teams that have AdvancedToNextPhase = true</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionLeaderboard(System.Guid,System.Int32,System.Int32)">
            <summary>
            Get competition leaderboard
            </summary>
            <param name="id">Competition ID</param>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>Competition leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.UpdateCompetition(System.Guid,ThuneeAPI.Application.DTOs.UpdateCompetitionDto)">
            <summary>
            Update competition (Admin only)
            </summary>
            <param name="id">Competition ID</param>
            <param name="updateDto">Update details</param>
            <returns>Updated competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.ProcessGameResult(ThuneeAPI.Application.DTOs.CompetitionGameResultDto)">
            <summary>
            Process game result for competition scoring
            </summary>
            <param name="gameResult">Game result details</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.DeleteCompetition(System.Guid)">
            <summary>
            Delete competition (Admin only)
            </summary>
            <param name="id">Competition ID</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionGames(System.Guid)">
            <summary>
            Get competition games (Admin only)
            </summary>
            <param name="id">Competition ID</param>
            <returns>List of games for the competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.CreateGame(ThuneeAPI.Application.DTOs.CreateGameDto)">
            <summary>
            Create a new game lobby
            </summary>
            <param name="createGameDto">Game creation details</param>
            <returns>Created game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetGame(System.String)">
            <summary>
            Get game information by lobby code
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.JoinGame(System.String,ThuneeAPI.Application.DTOs.JoinGameDto)">
            <summary>
            Join an existing game
            </summary>
            <param name="lobbyCode">The lobby code to join</param>
            <param name="joinGameDto">Join game details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.StartGame(System.String)">
            <summary>
            Start a game (host only)
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordHandResult(System.String,ThuneeAPI.Application.DTOs.RecordHandResultDto)">
            <summary>
            Record the result of a hand
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="handResultDto">Hand result details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordGameResult(System.String,ThuneeAPI.Application.DTOs.GameResultDto)">
            <summary>
            Record the final game result
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="gameResultDto">Game result details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetUserGames(System.Int32,System.Int32)">
            <summary>
            Get user's game history
            </summary>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>List of user's games</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetGameHands(System.String)">
            <summary>
            Get detailed hand history for a game
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>List of game hands</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordCardPlay(ThuneeAPI.Application.DTOs.CardPlayDto)">
            <summary>
            Record a card play
            </summary>
            <param name="cardPlayDto">Card play details</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordJordhiCall(ThuneeAPI.Application.DTOs.JordhiCallDto)">
            <summary>
            Record a Jordhi call
            </summary>
            <param name="jordhiDto">Jordhi call details</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetActiveGames">
            <summary>
            Get active games/lobbies
            </summary>
            <returns>List of active games</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.LeaveGame(System.String)">
            <summary>
            Leave a game
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.SetPlayerReady(System.String,ThuneeAPI.Application.DTOs.SetPlayerReadyDto)">
            <summary>
            Set player ready status
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="readyDto">Ready status</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetGlobalLeaderboard(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get global leaderboard
            </summary>
            <param name="timeFrame">Time frame filter (all, weekly, monthly)</param>
            <param name="sortBy">Sort criteria (score, winRate, gamesPlayed)</param>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Global leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetWeeklyLeaderboard(System.Int32,System.Int32)">
            <summary>
            Get weekly leaderboard
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Weekly leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetMonthlyLeaderboard(System.Int32,System.Int32)">
            <summary>
            Get monthly leaderboard
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Monthly leaderboard</returns>
        </member>
    </members>
</doc>
