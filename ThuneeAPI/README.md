# Thunee API - ASP.NET Core Web API

A comprehensive ASP.NET Core Web API for the Thunee multiplayer card game, built with layered architecture (N-Tier) and Entity Framework Core with SQL Server.

## 🏗️ Architecture

This API follows the **Clean Architecture** pattern with clear separation of concerns:

```
ThuneeAPI/
├── ThuneeAPI/                  # Presentation Layer (Controllers, Middleware)
├── ThuneeAPI.Application/      # Application Layer (DTOs, Interfaces, Business Logic)
├── ThuneeAPI.Infrastructure/   # Infrastructure Layer (Data Access, External Services)
└── ThuneeAPI.Core/            # Domain Layer (Entities, Domain Logic)
```

### Layers Explained

- **Core (Domain)**: Contains business entities and domain logic
- **Application**: Contains business logic, DTOs, and service interfaces
- **Infrastructure**: Contains data access, external services, and implementations
- **Presentation**: Contains controllers, middleware, and API configuration

## 🚀 Features

- **Authentication & Authorization**: JWT-based authentication with refresh tokens
- **User Management**: Registration, login, profile management
- **Game Management**: Create/join games, record hand results, game history
- **Competition System**: Tournament management with leaderboards
- **Leaderboard System**: Global, weekly, and monthly rankings
- **Real-time Updates**: Ready for SignalR integration
- **Database Integration**: Entity Framework Core with SQL Server
- **API Documentation**: Swagger/OpenAPI integration
- **Logging**: Structured logging with Serilog
- **CORS Support**: Configured for frontend integration

## 🛠️ Technology Stack

- **Framework**: ASP.NET Core 8.0
- **Database**: SQL Server with Entity Framework Core
- **Authentication**: JWT Bearer tokens
- **Documentation**: Swagger/OpenAPI
- **Logging**: Serilog
- **Validation**: FluentValidation
- **Mapping**: AutoMapper
- **Password Hashing**: BCrypt.NET

## 📋 Prerequisites

- .NET 8.0 SDK
- SQL Server (LocalDB, Express, or Full)
- Visual Studio 2022 or VS Code
- SQL Server Management Studio (optional)

## 🚀 Getting Started

### 1. Clone and Setup

```bash
cd C:\Users\<USER>\source\repos\Thunee-fe\ThuneeAPI
```

### 2. Database Setup

Update the connection string in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"
  }
}
```

### 3. Create Database

```bash
# Navigate to the main API project
cd ThuneeAPI

# Add migration
dotnet ef migrations add InitialCreate

# Update database
dotnet ef database update
```

### 4. Run the API

```bash
# Development mode
dotnet run

# Or with hot reload
dotnet watch run
```

The API will be available at:
- **HTTPS**: https://localhost:7001
- **HTTP**: http://localhost:5001
- **Swagger UI**: https://localhost:7001/api-docs

## 📚 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Games
- `POST /api/games` - Create new game
- `GET /api/games/{lobbyCode}` - Get game by lobby code
- `POST /api/games/{lobbyCode}/join` - Join game
- `POST /api/games/{lobbyCode}/start` - Start game
- `POST /api/games/{lobbyCode}/hand-result` - Record hand result
- `POST /api/games/{lobbyCode}/game-result` - Record final game result
- `GET /api/games/user/{userId}` - Get user's games

### Competitions
- `GET /api/competitions` - Get all competitions
- `GET /api/competitions/{id}` - Get competition by ID
- `POST /api/competitions` - Create competition (Admin)
- `POST /api/competitions/{id}/join` - Join competition
- `GET /api/competitions/{id}/leaderboard` - Get competition leaderboard

### Leaderboards
- `GET /api/leaderboard/global` - Global leaderboard
- `GET /api/leaderboard/weekly` - Weekly leaderboard
- `GET /api/leaderboard/monthly` - Monthly leaderboard

## 🎮 Game Flow Integration

### Hand-by-Hand Tracking
After each hand is played, call:
```http
POST /api/games/{lobbyCode}/hand-result
{
  "ballNumber": 1,
  "handNumber": 1,
  "winnerPlayerId": "guid",
  "points": 10,
  "playedCards": [
    {
      "playerId": "guid",
      "cardSuit": "hearts",
      "cardValue": "A",
      "playOrder": 1
    }
  ]
}
```

### Game Completion
When the game ends:
```http
POST /api/games/{lobbyCode}/game-result
{
  "winnerTeam": 1,
  "team1FinalScore": 100,
  "team2FinalScore": 80
}
```

## 🔧 Configuration

### JWT Settings
```json
{
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "ThuneeAPI",
    "Audience": "ThuneeClient",
    "ExpiryInMinutes": 60,
    "RefreshTokenExpiryInDays": 30
  }
}
```

### Database Connection
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"
  }
}
```

## 🧪 Testing

### Using Swagger UI
1. Navigate to https://localhost:7001/api-docs
2. Register a new user
3. Login to get JWT token
4. Click "Authorize" and enter: `Bearer {your-token}`
5. Test protected endpoints

### Using Postman
Import the API collection from the Swagger JSON endpoint.

## 📊 Database Schema

The database includes these main tables:
- **Users** - User accounts and authentication
- **Competitions** - Tournament information
- **CompetitionTeams** - Teams registered for competitions
- **Games** - Individual game records
- **GameBalls** - Ball-by-ball game results
- **GameHands** - Hand-by-hand game details
- **PlayedCards** - Individual card plays

## 🔒 Security Features

- JWT authentication with configurable expiry
- Password hashing with BCrypt
- CORS configuration for frontend integration
- Input validation and sanitization
- Structured logging for security monitoring

## 🚀 Deployment

### Development
```bash
dotnet run --environment Development
```

### Production
```bash
dotnet publish -c Release
# Deploy to IIS, Azure, or your preferred hosting platform
```

## 📝 Frontend Integration

Update your frontend environment variables:
```env
VITE_API_BASE_URL=https://localhost:7001/api
VITE_SOCKET_URL=https://localhost:7001
```

The API is configured to accept requests from:
- http://localhost:5173 (Vite dev server)
- http://localhost:3000 (Alternative dev server)

## 🤝 Contributing

1. Follow the established architecture patterns
2. Add proper validation and error handling
3. Include XML documentation for new endpoints
4. Write unit tests for business logic
5. Update this README for new features

## 📞 Support

For issues and questions:
- Check the Swagger documentation at `/api-docs`
- Review the logs in the `logs/` directory
- Check the health endpoint at `/health`
