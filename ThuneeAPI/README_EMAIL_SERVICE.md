# Email Service Implementation

## Overview

The Email Service has been implemented to send welcome emails to players when they register and team pairing emails when players pair up with partners in competitions.

## Architecture

### 1. Interface Definition
**File:** `ThuneeAPI.Application/Interfaces/IEmailService.cs`

```csharp
public interface IEmailService
{
    Task SendEmailAsync(string to, string subject, string body);
    Task SendWelcomeEmailAsync(string to, string username);
    Task SendTeamPairingEmailAsync(string to, string username, string teamName, string partnerName);
}
```

### 2. Email Service Implementation
**File:** `ThuneeAPI.Infrastructure/Services/EmailService.cs`

The service implements:
- Generic email sending functionality using SMTP
- Welcome email template for new user registration
- Team pairing email template when players form complete teams
- HTML email templates with professional styling

### 3. Configuration
**Files:** `appsettings.json` and `appsettings.Development.json`

```json
"EmailSettings": {
  "DisplayName": "Thunee Competition",
  "From": "<EMAIL>",
  "Host": "smtp.gmail.com",
  "Password": "your-app-password",
  "Port": 587,
  "Username": "<EMAIL>",
  "UseSSL": true
}
```

### 4. Dependency Injection
**File:** `Program.cs`

```csharp
builder.Services.AddScoped<IEmailService, EmailService>();
```

## Integration Points

### 1. User Registration
**File:** `AuthService.cs` - `RegisterAsync()` method

- Sends welcome email after successful user registration
- Email is sent asynchronously to avoid blocking the registration process
- Errors are logged but don't fail the registration

### 2. Team Pairing
**File:** `CompetitionService.cs` - `JoinCompetitionTeamAsync()` method

- Sends team pairing emails to both players when a team becomes complete
- Player2 (who just joined) receives email about joining the team
- Player1 (team creator) receives email about their partner joining
- Emails are sent asynchronously to avoid blocking the team joining process

## Email Templates

### Welcome Email
- Professional HTML template with Thunee branding
- Welcomes new users to the platform
- Provides guidance on next steps (finding partners, joining competitions)
- Includes call-to-action button

### Team Pairing Email
- Congratulates players on forming a complete team
- Shows team details (team name, partner name)
- Encourages players to start competing
- Includes call-to-action button

## Configuration Setup

### For Gmail SMTP:
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password for the application
3. Update the configuration:
   ```json
   "EmailSettings": {
     "DisplayName": "Thunee Competition",
     "From": "<EMAIL>",
     "Host": "smtp.gmail.com",
     "Password": "your-16-character-app-password",
     "Port": 587,
     "Username": "<EMAIL>",
     "UseSSL": true
   }
   ```

### For Other SMTP Providers:
Update the Host, Port, and SSL settings according to your provider's specifications.

## Error Handling

- Email sending is performed asynchronously using `Task.Run()`
- Errors are logged to console but don't interrupt the main business flow
- Registration and team pairing continue to work even if email sending fails

## Testing

### Development Testing:
1. Update `appsettings.Development.json` with valid SMTP credentials
2. Register a new user - check for welcome email
3. Create a competition team and have another user join - check for team pairing emails

### Production Deployment:
1. Update `appsettings.json` with production SMTP credentials
2. Ensure SMTP server is accessible from production environment
3. Test email delivery in production environment

## Future Enhancements

Potential improvements that could be added:
- Email templates stored in database for easy customization
- Email queue system for better reliability
- Email tracking and delivery confirmation
- Unsubscribe functionality
- Different email templates for different competition types
- Email notifications for game results and achievements

## Security Considerations

- Store SMTP passwords securely (consider using Azure Key Vault or similar)
- Use App Passwords instead of main account passwords
- Validate email addresses before sending
- Implement rate limiting to prevent email spam
- Consider using dedicated email service providers (SendGrid, AWS SES) for production

## Troubleshooting

### Common Issues:
1. **Authentication Failed**: Check username/password and ensure App Password is used for Gmail
2. **Connection Timeout**: Verify SMTP host and port settings
3. **SSL/TLS Errors**: Ensure UseSSL setting matches server requirements
4. **Emails Not Received**: Check spam folders, verify email addresses, check SMTP server logs

### Debugging:
- Check console output for error messages
- Verify SMTP settings are correctly loaded from configuration
- Test SMTP connection independently
- Check email server logs if available
