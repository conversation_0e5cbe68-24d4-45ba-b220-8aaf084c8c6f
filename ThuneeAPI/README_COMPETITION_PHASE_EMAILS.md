# Competition Phase Email Notifications

## Overview

The Competition Phase Management system now includes automatic email notifications to keep players informed about knockout matches, phase advancements, and eliminations. This document outlines the email integration features.

## Email Types

### 1. Knockout Lobby Notification Email

**Triggered when:** <PERSON><PERSON> creates knockout lobbies for Top16, Top8, Top4, or Final phases

**Recipients:** All players in the teams assigned to the lobby

**Content includes:**
- Competition name and phase
- Team names and opponent information
- **Lobby code prominently displayed**
- Knockout rules and important reminders
- Match preparation instructions

**Template:** Professional HTML email with Thunee branding

### 2. Phase Advancement Email

**Triggered when:** Phase end is processed and teams advance to the next phase

**Recipients:** All players in teams that successfully advance

**Content includes:**
- Congratulations message
- Competition and team details
- New phase information
- Next steps and expectations
- Tournament bracket link

### 3. Phase Elimination Email

**Triggered when:** Phase end is processed and teams are eliminated

**Recipients:** All players in teams that are eliminated

**Content includes:**
- Respectful elimination notification
- Tournament summary
- Encouragement for future competitions
- Links to upcoming competitions
- Community engagement messaging

## Implementation Details

### EmailService Extensions

New methods added to `IEmailService`:

```csharp
Task SendKnockoutLobbyEmailAsync(string to, string username, string teamName, 
    string competitionName, string phase, string lobbyCode, string opponentTeamName);

Task SendPhaseAdvancementEmailAsync(string to, string username, string teamName, 
    string competitionName, string newPhase, bool isEliminated);
```

### Integration Points

1. **CompetitionPhaseService.CreatePhaseLobbyAsync()**
   - Sends knockout lobby emails to all players in both teams
   - Includes lobby code and opponent information
   - Runs in background to avoid blocking lobby creation

2. **CompetitionPhaseService.ProcessPhaseEndAsync()**
   - Sends advancement emails to qualifying teams
   - Sends elimination emails to eliminated teams
   - Processes all emails in background

### Email Templates

All emails use professional HTML templates with:
- Thunee branding and colors (#E1C760 gold theme)
- Responsive design for mobile devices
- Clear call-to-action buttons
- Professional typography and layout
- Consistent footer with company information

## User Experience

### Admin Experience

When admins perform phase management actions:

1. **Creating Knockout Lobbies:**
   - Success message includes "Email notifications sent to all players!"
   - Confirmation that players have been notified
   - No additional steps required

2. **Processing Phase End:**
   - Success message confirms email notifications sent
   - Automatic handling of both advancement and elimination emails
   - Background processing prevents delays

### Player Experience

Players receive timely notifications for:

1. **Knockout Match Ready:**
   - Clear lobby code display
   - Opponent team information
   - Match importance and rules
   - Direct link to join lobby

2. **Phase Advancement:**
   - Celebration of achievement
   - Next phase information
   - Preparation guidance
   - Tournament progress tracking

3. **Elimination Notification:**
   - Respectful and encouraging message
   - Tournament summary
   - Future opportunities
   - Community engagement

## Configuration

Email settings are configured in `appsettings.json`:

```json
{
  "EmailSettings": {
    "DisplayName": "Thunee Competition",
    "From": "<EMAIL>",
    "Host": "smtp.gmail.com",
    "Password": "your-app-password",
    "Port": 587,
    "Username": "<EMAIL>",
    "UseSSL": true
  }
}
```

## Testing

### Email Test Endpoints

New test endpoints available for development:

1. **POST** `/api/EmailTest/send-knockout-lobby`
   - Tests knockout lobby email template
   - Parameters: email, username, teamName, competitionName, phase, lobbyCode, opponentTeamName

2. **POST** `/api/EmailTest/send-phase-advancement`
   - Tests phase advancement/elimination email templates
   - Parameters: email, username, teamName, competitionName, newPhase, isEliminated

### Testing Examples

```bash
# Test knockout lobby email
POST /api/EmailTest/send-knockout-lobby?email=<EMAIL>&username=TestUser&teamName=TestTeam

# Test advancement email
POST /api/EmailTest/send-phase-advancement?email=<EMAIL>&username=TestUser&teamName=TestTeam&isEliminated=false

# Test elimination email
POST /api/EmailTest/send-phase-advancement?email=<EMAIL>&username=TestUser&teamName=TestTeam&isEliminated=true
```

## Error Handling

- Email failures are logged but don't block phase management operations
- Background email processing prevents UI delays
- Graceful degradation if email service is unavailable
- Comprehensive logging for troubleshooting

## Security Considerations

- Email addresses are retrieved from user database (not hardcoded)
- No sensitive game data included in emails
- Lobby codes are temporary and expire
- Email content is sanitized and validated

## Future Enhancements

Potential improvements:
- Email preferences for players
- Email delivery status tracking
- Rich text formatting options
- Multilingual email templates
- Email analytics and metrics

## Deployment Notes

1. Ensure email settings are properly configured in production
2. Test email delivery in staging environment
3. Monitor email logs for delivery issues
4. Consider email rate limiting for large competitions
5. Backup email service configuration recommended
