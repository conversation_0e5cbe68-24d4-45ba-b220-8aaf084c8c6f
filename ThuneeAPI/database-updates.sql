-- Database updates to support Competition Phase Management
-- Run these SQL commands in SSMS to add the missing columns

-- First, let's check what tables exist and their columns
SELECT
    t.name AS TableName,
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.is_nullable
FROM sys.tables t
INNER JOIN sys.columns c ON t.object_id = c.object_id
INNER JOIN sys.types ty ON c.user_type_id = ty.user_type_id
WHERE t.name IN ('Competitions', 'CompetitionTeams')
ORDER BY t.name, c.column_id;

-- Add Phase Management columns to Competitions table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Competitions') AND name = 'Phase')
BEGIN
    ALTER TABLE dbo.Competitions ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    PRINT 'Added Phase column to Competitions table';
END
ELSE
BEGIN
    PRINT 'Phase column already exists in Competitions table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Competitions') AND name = 'PhaseEndDate')
BEGIN
    ALTER TABLE dbo.Competitions ADD PhaseEndDate DATETIME2 NULL;
    PRINT 'Added PhaseEndDate column to Competitions table';
END
ELSE
BEGIN
    PRINT 'PhaseEndDate column already exists in Competitions table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.Competitions') AND name = 'MaxGamesPerPhase')
BEGIN
    ALTER TABLE dbo.Competitions ADD MaxGamesPerPhase BIGINT NULL;
    PRINT 'Added MaxGamesPerPhase column to Competitions table';
END
ELSE
BEGIN
    PRINT 'MaxGamesPerPhase column already exists in Competitions table';
END

-- Add Phase Management columns to CompetitionTeams table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.CompetitionTeams') AND name = 'Phase')
BEGIN
    ALTER TABLE dbo.CompetitionTeams ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    PRINT 'Added Phase column to CompetitionTeams table';
END
ELSE
BEGIN
    PRINT 'Phase column already exists in CompetitionTeams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.CompetitionTeams') AND name = 'IsEliminated')
BEGIN
    ALTER TABLE dbo.CompetitionTeams ADD IsEliminated BIT NOT NULL DEFAULT 0;
    PRINT 'Added IsEliminated column to CompetitionTeams table';
END
ELSE
BEGIN
    PRINT 'IsEliminated column already exists in CompetitionTeams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.CompetitionTeams') AND name = 'AdvancedToNextPhase')
BEGIN
    ALTER TABLE dbo.CompetitionTeams ADD AdvancedToNextPhase BIT NOT NULL DEFAULT 0;
    PRINT 'Added AdvancedToNextPhase column to CompetitionTeams table';
END
ELSE
BEGIN
    PRINT 'AdvancedToNextPhase column already exists in CompetitionTeams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('dbo.CompetitionTeams') AND name = 'PhaseEliminatedAt')
BEGIN
    ALTER TABLE dbo.CompetitionTeams ADD PhaseEliminatedAt DATETIME2 NULL;
    PRINT 'Added PhaseEliminatedAt column to CompetitionTeams table';
END
ELSE
BEGIN
    PRINT 'PhaseEliminatedAt column already exists in CompetitionTeams table';
END

-- Update existing competitions to have default phase
UPDATE dbo.Competitions SET Phase = 'Leaderboard' WHERE Phase IS NULL OR Phase = '';
PRINT 'Updated existing competitions with default phase';

-- Update existing competition teams to have default phase
UPDATE dbo.CompetitionTeams SET Phase = 'Leaderboard' WHERE Phase IS NULL OR Phase = '';
PRINT 'Updated existing competition teams with default phase';

PRINT 'Database schema updated successfully for Competition Phase Management';

-- Show final table structure
SELECT
    t.name AS TableName,
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.is_nullable
FROM sys.tables t
INNER JOIN sys.columns c ON t.object_id = c.object_id
INNER JOIN sys.types ty ON c.user_type_id = ty.user_type_id
WHERE t.name IN ('Competitions', 'CompetitionTeams')
  AND c.name IN ('Phase', 'PhaseEndDate', 'MaxGamesPerPhase', 'IsEliminated', 'AdvancedToNextPhase', 'PhaseEliminatedAt')
ORDER BY t.name, c.column_id;
