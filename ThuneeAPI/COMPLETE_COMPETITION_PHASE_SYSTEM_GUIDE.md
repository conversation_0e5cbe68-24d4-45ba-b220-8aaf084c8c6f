# Complete Competition Phase System Guide

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Database Architecture](#database-architecture)
3. [Phase Progression Flow](#phase-progression-flow)
4. [Frontend Components](#frontend-components)
5. [Admin Management](#admin-management)
6. [Email Notifications](#email-notifications)
7. [API Endpoints](#api-endpoints)
8. [Phase Advancement Process](#phase-advancement-process)
9. [Knockout System](#knockout-system)
10. [Implementation Checklist](#implementation-checklist)

---

## 🎯 System Overview

The Competition Phase System transforms traditional leaderboard competitions into structured tournaments with multiple elimination phases, culminating in knockout rounds and a final championship match.

### Phase Structure
```
Leaderboard → Top32 → Top16 → Top8 → Top4 → Final → Completed
```

### Competition Types
- **Leaderboard Phase**: All teams compete for points-based ranking
- **Qualification Phases**: Top32 - teams still compete for points but with limited spots
- **Knockout Phases**: Top16, Top8, Top4, Final - single elimination matches

---

## 🗃️ Database Architecture

### Modified Existing Tables

#### Competitions Table - New Columns
| Column | Type | Description |
|--------|------|-------------|
| `Phase` | NVARCHAR(40) | Current competition phase |
| `PhaseEndDate` | DATETIME2 | When current phase ends |
| `MaxGamesPerPhase` | INT | Max games per team in current phase |

#### CompetitionTeams Table - New Columns
| Column | Type | Description |
|--------|------|-------------|
| `Phase` | NVARCHAR(40) | Current phase team is in |
| `IsEliminated` | BIT | Whether team has been eliminated |
| `AdvancedToNextPhase` | BIT | Whether team advanced to next phase |
| `PhaseEliminatedAt` | DATETIME2 | When team was eliminated |

### New Tables

#### CompetitionPhaseLobbies
Stores knockout match lobbies for elimination rounds.

| Column | Type | Description |
|--------|------|-------------|
| `Id` | UNIQUEIDENTIFIER | Primary key |
| `CompetitionId` | UNIQUEIDENTIFIER | FK to Competitions |
| `Phase` | NVARCHAR(40) | Phase (Top16, Top8, Top4, Final) |
| `LobbyCode` | NVARCHAR(12) | Unique lobby code for players |
| `CreatedByAdminId` | UNIQUEIDENTIFIER | Admin who created lobby |
| `CreatedAt` | DATETIME2 | Creation timestamp |

#### CompetitionPhaseLobbyTeams
Links teams to their knockout lobbies.

| Column | Type | Description |
|--------|------|-------------|
| `Id` | UNIQUEIDENTIFIER | Primary key |
| `LobbyId` | UNIQUEIDENTIFIER | FK to CompetitionPhaseLobbies |
| `CompetitionTeamId` | UNIQUEIDENTIFIER | FK to CompetitionTeams |
| `IsWinner` | BIT | Whether team won the match |
| `EliminatedAt` | DATETIME2 | When team was eliminated |

#### CompetitionTeamPhaseStats
Tracks team performance across different phases.

| Column | Type | Description |
|--------|------|-------------|
| `Id` | UNIQUEIDENTIFIER | Primary key |
| `CompetitionTeamId` | UNIQUEIDENTIFIER | FK to CompetitionTeams |
| `Phase` | NVARCHAR(40) | Phase name |
| `Points` | INT | Points earned in this phase |
| `BonusPoints` | INT | Bonus points in this phase |
| `GamesPlayed` | INT | Games played in this phase |
| `BallsWon` | INT | Total balls won in this phase |

---

## 🔄 Phase Progression Flow

### 1. Leaderboard Phase (Initial)
- **Duration**: Configurable (default 30 days)
- **Teams**: All registered teams
- **Objective**: Accumulate points through regular gameplay
- **Advancement**: Top 32 teams by total points (Points + BonusPoints)
- **Elimination**: Teams not in top 32

### 2. Top32 Phase (Qualification)
- **Duration**: 7 days
- **Teams**: Top 32 from Leaderboard
- **Objective**: Continue point accumulation with limited time
- **Advancement**: Top 16 teams by total points
- **Elimination**: Bottom 16 teams

### 3. Top16 Phase (First Knockout)
- **Duration**: 3 days
- **Teams**: Top 16 from Top32
- **Format**: Single elimination matches
- **Lobbies**: 8 lobbies, 2 teams each
- **Advancement**: 8 winners
- **Elimination**: 8 losers

### 4. Top8 Phase (Quarter-Finals)
- **Duration**: 2 days
- **Teams**: 8 winners from Top16
- **Format**: Single elimination matches
- **Lobbies**: 4 lobbies, 2 teams each
- **Advancement**: 4 winners
- **Elimination**: 4 losers

### 5. Top4 Phase (Semi-Finals)
- **Duration**: 1 day
- **Teams**: 4 winners from Top8
- **Format**: Single elimination matches
- **Lobbies**: 2 lobbies, 2 teams each
- **Advancement**: 2 winners
- **Elimination**: 2 losers

### 6. Final Phase (Championship)
- **Duration**: 12 hours
- **Teams**: 2 winners from Top4
- **Format**: Single elimination match
- **Lobbies**: 1 lobby, 2 teams
- **Result**: 1 champion, 1 runner-up

### 7. Completed Phase
- **Competition finished**
- **Final rankings determined**
- **Prizes distributed**

---

## 🎮 Frontend Components

### Public Components

#### 1. CompetitionBrackets.tsx
**Location**: `/competitions/{id}/brackets`

**Features**:
- Tournament bracket visualization
- Phase selector (Top16, Top8, Top4, Final)
- Real-time match status updates
- Auto-refresh every 30 seconds
- Team information display
- Winner highlighting

**Usage**:
```jsx
<CompetitionBracket 
  competitionId={competitionId}
  brackets={brackets}
  isLoading={false}
/>
```

#### 2. CompetitionBracket.tsx
**Core bracket visualization component**

**Features**:
- Interactive phase selection
- Match cards with team details
- Winner/loser indication
- Completion status badges
- Responsive grid layout

### Admin Components

#### 3. CompetitionPhaseManagement.tsx
**Location**: `/admin/competitions/{id}/phases`

**Features**:
- Phase overview dashboard
- Team advancement controls
- Phase end processing
- Bracket management
- Email notification status

**Key Functions**:
- `handleAdvancePhase()` - Move to next phase
- `handleProcessPhaseEnd()` - Auto-advance eligible teams
- `loadPhaseTeams()` - Get current phase teams
- `loadEligibleTeams()` - Get advancement candidates

#### 4. CreateKnockoutLobby.tsx
**Knockout lobby creation interface**

**Features**:
- Manual team selection (2 teams)
- Bulk lobby creation (all teams)
- Team pairing interface
- Automatic email notifications
- Lobby code generation

**Usage**:
```jsx
<CreateKnockoutLobby 
  competitionId={competitionId}
  phase={phase}
  teams={teams}
  onLobbyCreated={onLobbyCreated}
/>
```

---

## 👨‍💼 Admin Management

### Phase Management Dashboard

#### Current Phase Overview
- Active teams count
- Eligible teams for next phase
- Phase end date
- Progress indicators

#### Phase Actions
1. **Process Phase End**
   - Automatically determine eligible teams
   - Eliminate non-advancing teams
   - Advance qualifying teams
   - Send email notifications

2. **Manual Phase Advancement**
   - Force advance to next phase
   - Override automatic progression
   - Set custom phase end dates

3. **Knockout Lobby Management**
   - Create individual match lobbies
   - Bulk create all phase lobbies
   - Set match winners
   - Monitor lobby status

### Team Management

#### Team Status Tracking
- Current phase
- Elimination status
- Advancement history
- Phase statistics

#### Manual Interventions
- Force team elimination
- Manual team advancement
- Phase reassignment
- Statistics adjustment

---

## 📧 Email Notifications

### Automatic Email Types

#### 1. Knockout Lobby Notification
**Triggered**: When admin creates knockout lobbies
**Recipients**: All players in assigned teams
**Content**:
- Competition name and phase
- Lobby code (prominently displayed)
- Opponent team information
- Knockout rules and instructions
- Match preparation guidelines

#### 2. Phase Advancement Email
**Triggered**: When teams advance to next phase
**Recipients**: All players in advancing teams
**Content**:
- Congratulations message
- New phase information
- Tournament progress update
- Next steps and expectations

#### 3. Phase Elimination Email
**Triggered**: When teams are eliminated
**Recipients**: All players in eliminated teams
**Content**:
- Respectful elimination notification
- Tournament summary
- Encouragement for future competitions
- Community engagement links

### Email Templates
- Professional HTML design
- Thunee branding (#E1C760 gold theme)
- Responsive mobile layout
- Clear call-to-action buttons

---

## 🔌 API Endpoints

### Phase Management
```
POST /api/competitions/{id}/phases/advance
GET  /api/competitions/{id}/phases/{phase}/teams
GET  /api/competitions/{id}/phases/eligible
POST /api/competitions/{id}/phases/process-end
```

### Lobby Management
```
POST /api/competitions/{id}/phases/{phase}/lobbies
GET  /api/competitions/{id}/phases/{phase}/lobbies
GET  /api/competitions/{id}/phases/lobbies/{lobbyId}
POST /api/competitions/{id}/phases/lobbies/{lobbyId}/winner
DELETE /api/competitions/{id}/phases/lobbies/{lobbyId}
```

### Bracket Visualization
```
GET /api/competitions/{id}/phases/{phase}/bracket
GET /api/competitions/{id}/phases/brackets
GET /api/competitions/{id}/phases/{phase}/rankings
```

### Statistics
```
GET /api/competitions/{id}/teams/{teamId}/phase-stats
POST /api/competitions/{id}/teams/{teamId}/phase-stats
```

---

## ⚡ Phase Advancement Process

### Automatic Phase Progression

#### 1. Determine Eligible Teams
```sql
-- Get top N teams by total points
SELECT TOP (@AdvancingCount)
    ct.*,
    (ct.Points + ct.BonusPoints) as TotalPoints
FROM CompetitionTeams ct
WHERE ct.CompetitionId = @CompetitionId 
  AND ct.Phase = @CurrentPhase 
  AND ct.IsEliminated = 0
ORDER BY TotalPoints DESC, ct.Points DESC, ct.GamesPlayed ASC;
```

#### 2. Advance Qualifying Teams
```sql
EXEC SP_AdvanceTeamsToNextPhase @EligibleTeamIds, @NextPhase;
```

#### 3. Eliminate Non-Advancing Teams
```sql
EXEC SP_EliminateTeams @EliminatedTeamIds;
```

#### 4. Update Competition Phase
```sql
EXEC SP_AdvanceCompetitionPhase @CompetitionId, @NextPhase, @PhaseEndDate;
```

#### 5. Send Email Notifications
- Advancement emails to qualifying teams
- Elimination emails to eliminated teams
- Background processing to avoid delays

### Manual Phase Management

#### Admin Controls
1. **Override Automatic Progression**
   - Custom team selection
   - Manual elimination/advancement
   - Phase date adjustments

2. **Emergency Interventions**
   - Rollback phase changes
   - Restore eliminated teams
   - Adjust team statistics

---

## 🥊 Knockout System

### Lobby Creation Process

#### 1. Admin Creates Lobbies
- Select 2 teams for each match
- Generate unique lobby codes
- Assign teams to lobbies
- Send email notifications

#### 2. Players Join Lobbies
- Receive lobby codes via email
- Join using lobby code
- Wait for opponent team
- Start knockout match

#### 3. Match Completion
- Game determines winner
- Admin sets lobby winner
- Loser team eliminated
- Winner advances to next phase

### Bracket Generation

#### Match Pairing Logic
```javascript
// Pair teams for knockout matches
for (let i = 0; i < teams.length; i += 2) {
  const team1 = teams[i];
  const team2 = teams[i + 1];
  
  createMatch(team1, team2, phase);
}
```

#### Bracket Visualization
- Tournament tree structure
- Real-time match updates
- Winner progression tracking
- Phase-by-phase display

---

## ✅ Implementation Checklist

### Database Setup
- [x] Run schema migration script
- [x] Create stored procedures
- [x] Verify table structure
- [x] Test stored procedures

### Backend Implementation
- [ ] Register CompetitionPhaseService in DI
- [ ] Update existing competition endpoints
- [ ] Implement phase management controllers
- [ ] Add email service integration
- [ ] Test API endpoints

### Frontend Implementation
- [ ] Add phase management routes
- [ ] Implement bracket components
- [ ] Create admin phase dashboard
- [ ] Add knockout lobby creation
- [ ] Test user workflows

### Email System
- [ ] Configure email templates
- [ ] Test email delivery
- [ ] Verify notification triggers
- [ ] Monitor email logs

### Testing & Deployment
- [ ] Unit test phase logic
- [ ] Integration test workflows
- [ ] User acceptance testing
- [ ] Production deployment
- [ ] Monitor system performance

---

## 🚀 Getting Started

### 1. Verify Database Setup
```sql
-- Check tables exist
SELECT name FROM sys.tables WHERE name LIKE 'Competition%';

-- Check stored procedures
SELECT name FROM sys.procedures WHERE name LIKE 'SP_%';

-- Verify data
SELECT Phase, COUNT(*) FROM Competitions GROUP BY Phase;
```

### 2. Configure Application
```csharp
// Register services in Program.cs
services.AddScoped<ICompetitionPhaseService, CompetitionPhaseService>();
services.AddScoped<IEmailService, EmailService>();
```

### 3. Test Phase System
```csharp
// Test phase advancement
var result = await _phaseService.AdvanceCompetitionPhaseAsync(
    competitionId, "Top16");

// Test lobby creation
var lobby = await _phaseService.CreatePhaseLobbyAsync(
    createDto, adminId);
```

### 4. Deploy Frontend
- Build React components
- Update routing configuration
- Deploy to production server
- Test user workflows

---

## 📞 Support & Troubleshooting

### Common Issues
1. **Phase advancement fails**: Check team eligibility and database constraints
2. **Email notifications not sent**: Verify email service configuration
3. **Bracket not displaying**: Check API endpoints and data format
4. **Lobby creation errors**: Validate team selection and admin permissions

### Monitoring
- Database query performance
- Email delivery rates
- User engagement metrics
- System error logs

### Maintenance
- Regular database cleanup
- Email template updates
- Performance optimization
- Feature enhancements

---

## 🎯 Detailed Elimination Process

### Leaderboard to Top32 Elimination

#### Ranking Calculation
```sql
-- Teams ranked by total points (Points + BonusPoints)
SELECT
    ct.*,
    (ct.Points + ct.BonusPoints) as TotalPoints,
    ROW_NUMBER() OVER (
        ORDER BY (ct.Points + ct.BonusPoints) DESC,
                 ct.Points DESC,
                 ct.GamesPlayed ASC,
                 ct.RegisteredAt ASC
    ) as Rank
FROM CompetitionTeams ct
WHERE ct.CompetitionId = @CompetitionId
  AND ct.Phase = 'Leaderboard'
  AND ct.IsEliminated = 0
```

#### Elimination Criteria
1. **Primary**: Total Points (Points + BonusPoints) - Descending
2. **Tiebreaker 1**: Regular Points - Descending
3. **Tiebreaker 2**: Games Played - Ascending (fewer games = higher rank)
4. **Tiebreaker 3**: Registration Date - Ascending (earlier = higher rank)

#### Process
1. Calculate final rankings after phase end date
2. Select top 32 teams
3. Mark remaining teams as eliminated
4. Send advancement emails to top 32
5. Send elimination emails to remaining teams
6. Advance competition to Top32 phase

### Top32 to Top16 Elimination

#### Same ranking system as Leaderboard
- Top 16 teams advance to knockout rounds
- Bottom 16 teams eliminated
- 7-day qualification period

### Knockout Phase Eliminations (Top16 → Top8 → Top4 → Final)

#### Single Elimination Format
- Each match eliminates exactly 1 team
- Winner advances, loser eliminated immediately
- No second chances or consolation rounds

#### Match Results Processing
```sql
-- Set lobby winner and eliminate loser
EXEC SP_SetLobbyWinner @LobbyId, @WinnerTeamId;

-- This automatically:
-- 1. Sets IsWinner = 1 for winning team
-- 2. Sets EliminatedAt = GETUTCDATE() for losing team
-- 3. Updates team elimination status
```

---

## 🔧 Technical Implementation Details

### Service Layer Architecture

#### CompetitionPhaseService Methods

```csharp
// Phase Management
Task<CompetitionDto> AdvanceCompetitionPhaseAsync(Guid competitionId, string newPhase);
Task<List<CompetitionTeamDto>> GetEligibleTeamsForNextPhaseAsync(Guid competitionId);
Task EliminateTeamsAsync(Guid competitionId, List<Guid> teamIds);
Task AdvanceTeamsToNextPhaseAsync(Guid competitionId, List<Guid> teamIds);

// Lobby Management
Task<CompetitionPhaseLobbyDto> CreatePhaseLobbyAsync(CreateCompetitionPhaseLobbyDto createDto, Guid adminId);
Task<List<CompetitionPhaseLobbyDto>> GetPhaseLobbiesAsync(Guid competitionId, string phase);
Task SetLobbyWinnerAsync(Guid lobbyId, Guid winnerTeamId);

// Bracket Generation
Task<BracketDto> GenerateBracketAsync(Guid competitionId, string phase);
Task<List<BracketDto>> GetCompetitionBracketsAsync(Guid competitionId);

// Automated Processing
Task ProcessPhaseEndAsync(Guid competitionId);
Task<bool> CanAdvanceToNextPhaseAsync(Guid competitionId);
```

### Frontend State Management

#### Competition Store Updates
```typescript
interface CompetitionState {
  // Existing properties
  competitions: Competition[];
  selectedCompetition: Competition | null;

  // New phase system properties
  currentPhase: string;
  phaseTeams: CompetitionTeam[];
  eligibleTeams: CompetitionTeam[];
  phaseLobbies: CompetitionPhaseLobby[];
  brackets: Bracket[];

  // Loading states
  isLoadingPhase: boolean;
  isProcessingPhase: boolean;
  isCreatingLobby: boolean;
}
```

#### Phase Management Actions
```typescript
// Phase progression
const advancePhase = async (competitionId: string, newPhase: string) => {
  setIsProcessingPhase(true);
  try {
    const result = await phaseService.advancePhase(competitionId, newPhase);
    updateCompetitionPhase(result);
    showSuccessNotification('Phase advanced successfully');
  } catch (error) {
    showErrorNotification('Failed to advance phase');
  } finally {
    setIsProcessingPhase(false);
  }
};

// Lobby creation
const createKnockoutLobby = async (teamIds: string[]) => {
  setIsCreatingLobby(true);
  try {
    const lobby = await phaseService.createLobby(competitionId, phase, teamIds);
    addPhaseLobby(lobby);
    showSuccessNotification('Lobby created and emails sent!');
  } catch (error) {
    showErrorNotification('Failed to create lobby');
  } finally {
    setIsCreatingLobby(false);
  }
};
```

### Database Triggers and Automation

#### Automatic Phase Statistics
```sql
-- Trigger to update phase stats when game completes
CREATE TRIGGER TR_UpdatePhaseStats
ON Games
AFTER UPDATE
AS
BEGIN
    IF UPDATE(Status) AND EXISTS (SELECT 1 FROM inserted WHERE Status = 'completed')
    BEGIN
        -- Update team phase statistics
        -- Calculate points and bonus points
        -- Update games played count
    END
END
```

#### Phase End Monitoring
```sql
-- Scheduled job to check for phase end dates
CREATE PROCEDURE SP_CheckPhaseEndDates
AS
BEGIN
    DECLARE @CompetitionId UNIQUEIDENTIFIER;

    DECLARE phase_cursor CURSOR FOR
    SELECT Id FROM Competitions
    WHERE PhaseEndDate <= GETUTCDATE()
      AND Phase NOT IN ('Completed', 'Final');

    OPEN phase_cursor;
    FETCH NEXT FROM phase_cursor INTO @CompetitionId;

    WHILE @@FETCH_STATUS = 0
    BEGIN
        EXEC SP_ProcessPhaseEnd @CompetitionId;
        FETCH NEXT FROM phase_cursor INTO @CompetitionId;
    END

    CLOSE phase_cursor;
    DEALLOCATE phase_cursor;
END
```

---

## 📊 Reporting and Analytics

### Phase Performance Metrics

#### Team Performance Tracking
```sql
-- Get team performance across all phases
SELECT
    ct.TeamName,
    ct.Phase,
    ct.Points,
    ct.BonusPoints,
    ct.GamesPlayed,
    ct.IsEliminated,
    ct.PhaseEliminatedAt,
    CASE
        WHEN ct.IsEliminated = 0 THEN 'Active'
        ELSE 'Eliminated in ' + ct.Phase
    END as Status
FROM CompetitionTeams ct
WHERE ct.CompetitionId = @CompetitionId
ORDER BY ct.Phase, (ct.Points + ct.BonusPoints) DESC;
```

#### Competition Analytics
```sql
-- Competition phase summary
SELECT
    c.Name,
    c.Phase,
    c.PhaseEndDate,
    COUNT(ct.Id) as TotalTeams,
    COUNT(CASE WHEN ct.IsEliminated = 0 THEN 1 END) as ActiveTeams,
    COUNT(CASE WHEN ct.IsEliminated = 1 THEN 1 END) as EliminatedTeams
FROM Competitions c
LEFT JOIN CompetitionTeams ct ON c.Id = ct.CompetitionId
GROUP BY c.Id, c.Name, c.Phase, c.PhaseEndDate;
```

### Admin Dashboard Metrics

#### Real-time Statistics
- Teams per phase
- Elimination rates
- Average points per phase
- Game completion rates
- Email delivery status

#### Historical Analysis
- Phase duration tracking
- Team advancement patterns
- Competition engagement metrics
- Performance trends

---

## 🔐 Security and Permissions

### Admin Access Control
```csharp
[Authorize(Roles = "Admin")]
public class CompetitionPhaseController : ControllerBase
{
    // Phase management endpoints
    // Only accessible to admin users
}
```

### Team Data Protection
- Teams can only view their own statistics
- Public bracket viewing allowed
- Sensitive team information protected
- Audit logging for admin actions

### API Rate Limiting
```csharp
[EnableRateLimiting("PhaseManagement")]
public async Task<ActionResult> ProcessPhaseEnd(Guid competitionId)
{
    // Prevent abuse of phase management endpoints
}
```

---

## 🚀 Performance Optimization

### Database Indexing Strategy
```sql
-- Optimized indexes for phase queries
CREATE INDEX IX_CompetitionTeams_Points_Ranking
ON CompetitionTeams(CompetitionId, Phase, Points DESC, BonusPoints DESC);

CREATE INDEX IX_CompetitionTeams_Phase_Status
ON CompetitionTeams(Phase, IsEliminated)
INCLUDE (CompetitionId, Points, BonusPoints);

CREATE INDEX IX_Competitions_Phase_EndDate
ON Competitions(Phase, PhaseEndDate)
WHERE PhaseEndDate IS NOT NULL;
```

### Caching Strategy
```csharp
// Cache frequently accessed data
[MemoryCache(Duration = 300)] // 5 minutes
public async Task<List<CompetitionTeamDto>> GetPhaseTeamsAsync(Guid competitionId, string phase)

[MemoryCache(Duration = 60)] // 1 minute
public async Task<BracketDto> GetPhaseBracketAsync(Guid competitionId, string phase)
```

### Background Processing
```csharp
// Use background services for heavy operations
public class PhaseProcessingService : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await CheckPhaseEndDates();
            await ProcessPendingEmails();
            await UpdateBrackets();

            await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
        }
    }
}
```

---

This comprehensive system provides a complete tournament management solution with automated phase progression, knockout brackets, and professional communication throughout the competition lifecycle.
