-- =============================================
-- Competition Phase Management Stored Procedures
-- Database: GoldRushThunee
-- Author: System Generated
-- Create Date: 2025-07-29
-- Description: Stored procedures for managing competition phases,
--              team advancement, and elimination
-- =============================================

USE [GoldRushThunee]
GO

-- =============================================
-- SP_AdvanceCompetitionPhase
-- Advances competition to new phase and manages team transitions
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_AdvanceCompetitionPhase]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_AdvanceCompetitionPhase]
GO

CREATE PROCEDURE [dbo].[SP_AdvanceCompetitionPhase]
    @CompetitionId UNIQUEIDENTIFIER,
    @NewPhase NVARCHAR(40),
    @EligibleTeamIds NVARCHAR(MAX), -- Comma-separated list of team IDs
    @PhaseEndDate DATETIME2 = NULL,
    @MaxGamesPerPhase BIGINT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- Update the competition phase
        UPDATE Competitions 
        SET Phase = @NewPhase,
            PhaseEndDate = @PhaseEndDate,
            MaxGamesPerPhase = @MaxGamesPerPhase,
            UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;
        
        -- Parse eligible team IDs
        DECLARE @EligibleTeamTable TABLE (TeamId UNIQUEIDENTIFIER);
        
        IF @EligibleTeamIds IS NOT NULL AND @EligibleTeamIds != ''
        BEGIN
            INSERT INTO @EligibleTeamTable (TeamId)
            SELECT CAST(value AS UNIQUEIDENTIFIER)
            FROM STRING_SPLIT(@EligibleTeamIds, ',')
            WHERE value != '';
        END
        
        -- Advance eligible teams to the new phase
        UPDATE CompetitionTeams 
        SET Phase = @NewPhase,
            AdvancedToNextPhase = 1
        WHERE CompetitionId = @CompetitionId
          AND Id IN (SELECT TeamId FROM @EligibleTeamTable);
        
        -- Eliminate teams that are not eligible
        UPDATE CompetitionTeams 
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE CompetitionId = @CompetitionId
          AND Phase != @NewPhase  -- Teams not in the new phase
          AND IsEliminated = 0;   -- Only eliminate teams that aren't already eliminated
        
        COMMIT TRANSACTION;
        
        -- Return success message
        SELECT 'Success' AS Result, 
               @@ROWCOUNT AS TeamsAffected,
               @NewPhase AS NewPhase;
               
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        -- Return error information
        SELECT 'Error' AS Result,
               ERROR_MESSAGE() AS ErrorMessage,
               ERROR_NUMBER() AS ErrorNumber;
               
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_GetEligibleTeamsForPhase
-- Gets teams eligible for advancement to next phase
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetEligibleTeamsForPhase]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_GetEligibleTeamsForPhase]
GO

CREATE PROCEDURE [dbo].[SP_GetEligibleTeamsForPhase]
    @CompetitionId UNIQUEIDENTIFIER,
    @CurrentPhase NVARCHAR(40),
    @MaxTeams INT = 32
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@MaxTeams)
        ct.Id,
        ct.TeamName,
        ct.Player1Id,
        ct.Player2Id,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        ct.IsActive,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt,
        ct.Phase,
        ct.IsEliminated,
        ct.AdvancedToNextPhase,
        ct.PhaseEliminatedAt,
        u1.Username AS Player1Username,
        u2.Username AS Player2Username
    FROM CompetitionTeams ct
    LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId
      AND ct.Phase = @CurrentPhase
      AND ct.IsEliminated = 0
      AND ct.IsComplete = 1  -- Only complete teams
    ORDER BY (ct.Points + ct.BonusPoints) DESC,
             ct.Points DESC,
             ct.TeamName ASC;
END
GO

-- =============================================
-- SP_GetTeamsByPhase
-- Gets all teams in a specific phase
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetTeamsByPhase]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_GetTeamsByPhase]
GO

CREATE PROCEDURE [dbo].[SP_GetTeamsByPhase]
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ct.Id,
        ct.TeamName,
        ct.Player1Id,
        ct.Player2Id,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        ct.IsActive,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt,
        ct.Phase,
        ct.IsEliminated,
        ct.AdvancedToNextPhase,
        ct.PhaseEliminatedAt,
        u1.Username AS Player1Username,
        u2.Username AS Player2Username
    FROM CompetitionTeams ct
    LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId
      AND ct.Phase = @Phase
      AND ct.IsEliminated = 0
    ORDER BY (ct.Points + ct.BonusPoints) DESC,
             ct.Points DESC,
             ct.TeamName ASC;
END
GO

-- =============================================
-- SP_CheckTeamEligibilityForGames
-- Checks if a team is eligible to play games (not eliminated)
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_CheckTeamEligibilityForGames]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_CheckTeamEligibilityForGames]
GO

CREATE PROCEDURE [dbo].[SP_CheckTeamEligibilityForGames]
    @TeamId UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ct.Id,
        ct.TeamName,
        ct.Phase,
        ct.IsEliminated,
        ct.IsActive,
        ct.IsComplete,
        ct.GamesPlayed,
        ct.MaxGames,
        c.Phase AS CompetitionPhase,
        c.Status AS CompetitionStatus,
        c.PhaseEndDate,
        CASE 
            WHEN ct.IsEliminated = 1 THEN 0
            WHEN ct.IsActive = 0 THEN 0
            WHEN c.Status != 'active' THEN 0
            WHEN c.PhaseEndDate IS NOT NULL AND GETUTCDATE() > c.PhaseEndDate THEN 0
            WHEN ct.GamesPlayed >= ct.MaxGames THEN 0
            ELSE 1
        END AS IsEligibleForGames,
        CASE 
            WHEN ct.IsEliminated = 1 THEN 'Team has been eliminated from the competition'
            WHEN ct.IsActive = 0 THEN 'Team is not active'
            WHEN c.Status != 'active' THEN 'Competition is not active'
            WHEN c.PhaseEndDate IS NOT NULL AND GETUTCDATE() > c.PhaseEndDate THEN 'Competition phase has ended'
            WHEN ct.GamesPlayed >= ct.MaxGames THEN 'Team has reached maximum games for this phase'
            ELSE 'Team is eligible to play games'
        END AS EligibilityReason
    FROM CompetitionTeams ct
    INNER JOIN Competitions c ON ct.CompetitionId = c.Id
    WHERE ct.Id = @TeamId
      AND ct.CompetitionId = @CompetitionId;
END
GO

-- =============================================
-- SP_CreateCompetitionPhaseLobby
-- Creates a lobby for knockout phase matches
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_CreateCompetitionPhaseLobby]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_CreateCompetitionPhaseLobby]
GO

CREATE PROCEDURE [dbo].[SP_CreateCompetitionPhaseLobby]
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @LobbyCode NVARCHAR(20),
    @CreatedByAdminId UNIQUEIDENTIFIER,
    @TeamIds NVARCHAR(MAX) -- Comma-separated list of team IDs
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    BEGIN TRY
        DECLARE @LobbyId UNIQUEIDENTIFIER = NEWID();

        -- Create the phase lobby
        INSERT INTO CompetitionPhaseLobbies (Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId, CreatedAt)
        VALUES (@LobbyId, @CompetitionId, @Phase, @LobbyCode, @CreatedByAdminId, GETUTCDATE());

        -- Parse team IDs and add them to the lobby
        IF @TeamIds IS NOT NULL AND @TeamIds != ''
        BEGIN
            INSERT INTO CompetitionPhaseLobbyTeams (Id, LobbyId, CompetitionTeamId, IsWinner, EliminatedAt)
            SELECT NEWID(), @LobbyId, CAST(value AS UNIQUEIDENTIFIER), 0, NULL
            FROM STRING_SPLIT(@TeamIds, ',')
            WHERE value != '';
        END

        COMMIT TRANSACTION;

        -- Return the created lobby
        SELECT
            cpl.Id,
            cpl.CompetitionId,
            cpl.Phase,
            cpl.LobbyCode,
            cpl.CreatedByAdminId,
            cpl.CreatedAt,
            u.Username AS CreatedByAdminUsername
        FROM CompetitionPhaseLobbies cpl
        LEFT JOIN Users u ON cpl.CreatedByAdminId = u.Id
        WHERE cpl.Id = @LobbyId;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_SetPhaseLobbyWinner
-- Sets the winner of a knockout phase lobby
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_SetPhaseLobbyWinner]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[SP_SetPhaseLobbyWinner]
GO

CREATE PROCEDURE [dbo].[SP_SetPhaseLobbyWinner]
    @LobbyId UNIQUEIDENTIFIER,
    @WinnerTeamId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    BEGIN TRY
        -- Set the winner
        UPDATE CompetitionPhaseLobbyTeams
        SET IsWinner = 1
        WHERE LobbyId = @LobbyId
          AND CompetitionTeamId = @WinnerTeamId;

        -- Mark other teams as eliminated
        UPDATE CompetitionPhaseLobbyTeams
        SET EliminatedAt = GETUTCDATE()
        WHERE LobbyId = @LobbyId
          AND CompetitionTeamId != @WinnerTeamId;

        -- Get the competition and phase info
        DECLARE @CompetitionId UNIQUEIDENTIFIER;
        DECLARE @CurrentPhase NVARCHAR(40);
        DECLARE @NextPhase NVARCHAR(40);

        SELECT @CompetitionId = cpl.CompetitionId, @CurrentPhase = cpl.Phase
        FROM CompetitionPhaseLobbies cpl
        WHERE cpl.Id = @LobbyId;

        -- Determine next phase
        SET @NextPhase = CASE @CurrentPhase
            WHEN 'Top16' THEN 'Top8'
            WHEN 'Top8' THEN 'Top4'
            WHEN 'Top4' THEN 'Final'
            WHEN 'Final' THEN 'Completed'
            ELSE @CurrentPhase
        END;

        -- Advance winner to next phase (if not final)
        IF @NextPhase != 'Completed'
        BEGIN
            UPDATE CompetitionTeams
            SET Phase = @NextPhase,
                AdvancedToNextPhase = 1
            WHERE Id = @WinnerTeamId;
        END

        -- Eliminate losing teams
        UPDATE CompetitionTeams
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE Id IN (
            SELECT cplt.CompetitionTeamId
            FROM CompetitionPhaseLobbyTeams cplt
            WHERE cplt.LobbyId = @LobbyId
              AND cplt.CompetitionTeamId != @WinnerTeamId
        );

        COMMIT TRANSACTION;

        SELECT 'Success' AS Result, @WinnerTeamId AS WinnerTeamId, @NextPhase AS NextPhase;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

PRINT 'Competition Phase Management stored procedures created successfully!'
PRINT 'Created procedures:'
PRINT '- SP_AdvanceCompetitionPhase'
PRINT '- SP_GetEligibleTeamsForPhase'
PRINT '- SP_GetTeamsByPhase'
PRINT '- SP_CheckTeamEligibilityForGames'
PRINT '- SP_CreateCompetitionPhaseLobby'
PRINT '- SP_SetPhaseLobbyWinner'
