using System.Resources;
using System.Reflection;

namespace ThuneeAPI.Infrastructure.Helpers;

/// <summary>
/// Helper class for reading stored procedures from resource files
/// </summary>
public static class StoredProcedureHelper
{
    private static readonly ResourceManager _resourceManager = 
        new("ThuneeAPI.Infrastructure.Resources.StoredProcedures", Assembly.GetExecutingAssembly());

    /// <summary>
    /// Gets a stored procedure call command (not the CREATE statement)
    /// </summary>
    /// <param name="procedureName">The name of the stored procedure resource</param>
    /// <returns>The SQL command to execute the stored procedure</returns>
    public static string GetStoredProcedure(string procedureName)
    {
        // Check if the stored procedure exists in resources (for validation)
        var sql = _resourceManager.GetString(procedureName);
        if (string.IsNullOrEmpty(sql))
        {
            throw new InvalidOperationException($"Stored procedure '{procedureName}' not found in resources");
        }

        // Return the EXEC command instead of the CREATE statement
        return procedureName;
    }

    /// <summary>
    /// Checks if a stored procedure exists in the resource file
    /// </summary>
    /// <param name="procedureName">The name of the stored procedure resource</param>
    /// <returns>True if the stored procedure exists, false otherwise</returns>
    public static bool StoredProcedureExists(string procedureName)
    {
        try
        {
            var sql = _resourceManager.GetString(procedureName);
            return !string.IsNullOrEmpty(sql);
        }
        catch
        {
            return false;
        }
    }
}
