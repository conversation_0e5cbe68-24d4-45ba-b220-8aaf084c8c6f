-- Migration: Add Competition Phase System
-- Description: Adds phase management, knockout lobbies, and phase statistics tracking

-- Add phase management columns to Competitions table
ALTER TABLE Competitions 
ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard',
    PhaseEndDate DATETIME2 NULL,
    MaxGamesPerPhase INT NULL;

-- Add phase tracking columns to CompetitionTeams table
ALTER TABLE CompetitionTeams 
ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard',
    IsEliminated BIT NOT NULL DEFAULT 0,
    AdvancedToNextPhase BIT NOT NULL DEFAULT 0,
    PhaseEliminatedAt DATETIME2 NULL;

-- Create CompetitionPhaseLobbies table
CREATE TABLE CompetitionPhaseLobbies (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompetitionId UNIQUEIDENTIFIER NOT NULL,
    Phase NVARCHAR(40) NOT NULL,
    LobbyCode NVARCHAR(12) NOT NULL,
    CreatedByAdminId UNIQUEIDENTIFIER NOT NULL,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_CompetitionPhaseLobbies_Competition 
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
    CONSTRAINT FK_CompetitionPhaseLobbies_Admin 
        FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id)
);

-- Create CompetitionPhaseLobbyTeams table
CREATE TABLE CompetitionPhaseLobbyTeams (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LobbyId UNIQUEIDENTIFIER NOT NULL,
    CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
    IsWinner BIT NOT NULL DEFAULT 0,
    EliminatedAt DATETIME2 NULL,
    
    CONSTRAINT FK_CompetitionPhaseLobbyTeams_Lobby 
        FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id),
    CONSTRAINT FK_CompetitionPhaseLobbyTeams_Team 
        FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);

-- Create CompetitionTeamPhaseStats table
CREATE TABLE CompetitionTeamPhaseStats (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
    Phase NVARCHAR(40) NOT NULL,
    Points INT NOT NULL DEFAULT 0,
    BonusPoints INT NOT NULL DEFAULT 0,
    GamesPlayed INT NOT NULL DEFAULT 0,
    BallsWon INT NOT NULL DEFAULT 0,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    CONSTRAINT FK_CompetitionTeamPhaseStats_Team 
        FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
);

-- Add indexes for performance
CREATE INDEX IX_Competitions_Phase ON Competitions(Phase);
CREATE INDEX IX_CompetitionTeams_Phase ON CompetitionTeams(Phase);
CREATE INDEX IX_CompetitionTeams_IsEliminated ON CompetitionTeams(IsEliminated);
CREATE INDEX IX_CompetitionPhaseLobbies_Competition_Phase ON CompetitionPhaseLobbies(CompetitionId, Phase);
CREATE INDEX IX_CompetitionPhaseLobbyTeams_Lobby ON CompetitionPhaseLobbyTeams(LobbyId);
CREATE INDEX IX_CompetitionTeamPhaseStats_Team_Phase ON CompetitionTeamPhaseStats(CompetitionTeamId, Phase);

-- Add unique constraints
ALTER TABLE CompetitionPhaseLobbies 
ADD CONSTRAINT UQ_CompetitionPhaseLobbies_LobbyCode UNIQUE (LobbyCode);

-- Update existing competitions to have default phase values
UPDATE Competitions 
SET Phase = 'Leaderboard', 
    PhaseEndDate = DATEADD(DAY, 30, GETUTCDATE()),
    MaxGamesPerPhase = 10
WHERE Phase IS NULL OR Phase = '';

-- Update existing competition teams to have default phase values
UPDATE CompetitionTeams 
SET Phase = 'Leaderboard',
    IsEliminated = 0,
    AdvancedToNextPhase = 0
WHERE Phase IS NULL OR Phase = '';

-- Create stored procedures for phase management

-- Stored procedure to advance competition phase
CREATE OR ALTER PROCEDURE SP_AdvanceCompetitionPhase
    @CompetitionId UNIQUEIDENTIFIER,
    @NewPhase NVARCHAR(40),
    @PhaseEndDate DATETIME2 = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE Competitions 
    SET Phase = @NewPhase,
        PhaseEndDate = @PhaseEndDate,
        UpdatedAt = GETUTCDATE()
    WHERE Id = @CompetitionId;
    
    SELECT * FROM Competitions WHERE Id = @CompetitionId;
END;

-- Stored procedure to eliminate teams
CREATE OR ALTER PROCEDURE SP_EliminateTeams
    @TeamIds NVARCHAR(MAX) -- Comma-separated list of team IDs
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE CompetitionTeams 
    SET IsEliminated = 1,
        PhaseEliminatedAt = GETUTCDATE()
    WHERE Id IN (
        SELECT value 
        FROM STRING_SPLIT(@TeamIds, ',')
        WHERE value != ''
    );
END;

-- Stored procedure to advance teams to next phase
CREATE OR ALTER PROCEDURE SP_AdvanceTeamsToNextPhase
    @TeamIds NVARCHAR(MAX), -- Comma-separated list of team IDs
    @NextPhase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE CompetitionTeams 
    SET Phase = @NextPhase,
        AdvancedToNextPhase = 1
    WHERE Id IN (
        SELECT value 
        FROM STRING_SPLIT(@TeamIds, ',')
        WHERE value != ''
    );
END;

-- Stored procedure to create phase lobby
CREATE OR ALTER PROCEDURE SP_CreatePhaseLobby
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @LobbyCode NVARCHAR(12),
    @CreatedByAdminId UNIQUEIDENTIFIER,
    @TeamIds NVARCHAR(MAX) -- Comma-separated list of team IDs
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @LobbyId UNIQUEIDENTIFIER = NEWID();
    
    -- Create the lobby
    INSERT INTO CompetitionPhaseLobbies (Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId)
    VALUES (@LobbyId, @CompetitionId, @Phase, @LobbyCode, @CreatedByAdminId);
    
    -- Add teams to the lobby
    INSERT INTO CompetitionPhaseLobbyTeams (LobbyId, CompetitionTeamId)
    SELECT @LobbyId, value
    FROM STRING_SPLIT(@TeamIds, ',')
    WHERE value != '';
    
    -- Return the created lobby with teams
    SELECT 
        l.*,
        u.Username as CreatedByAdminUsername
    FROM CompetitionPhaseLobbies l
    JOIN Users u ON l.CreatedByAdminId = u.Id
    WHERE l.Id = @LobbyId;
END;

-- Stored procedure to get phase teams with rankings
CREATE OR ALTER PROCEDURE SP_GetPhaseTeamsWithRankings
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ct.*,
        u1.Username as Player1Username,
        u1.Email as Player1Email,
        u2.Username as Player2Username,
        u2.Email as Player2Email,
        (ct.Points + ct.BonusPoints) as TotalPoints,
        ROW_NUMBER() OVER (
            ORDER BY (ct.Points + ct.BonusPoints) DESC, 
                     ct.Points DESC, 
                     ct.GamesPlayed ASC
        ) as Rank
    FROM CompetitionTeams ct
    JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId 
      AND ct.Phase = @Phase 
      AND ct.IsEliminated = 0
    ORDER BY TotalPoints DESC, ct.Points DESC, ct.GamesPlayed ASC;
END;
