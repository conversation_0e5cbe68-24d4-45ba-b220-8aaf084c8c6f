USE GoldRushThunee;

-- =============================================
-- COMPETITION PHASE SYSTEM DEPLOYMENT - FINAL VERSION
-- Description: Final deployment script using dynamic SQL to avoid parsing issues
-- Version: 1.3
-- Date: 2025-01-02
-- =============================================

PRINT '========================================';
PRINT 'STARTING COMPETITION PHASE SYSTEM DEPLOYMENT (FINAL)';
PRINT '========================================';
PRINT '';

-- Check if we're connected to the correct database
PRINT 'Current Database: ' + DB_NAME();
PRINT 'Server: ' + @@SERVERNAME;
PRINT 'Current Time: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';

-- Verify required tables exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Competitions')
BEGIN
    PRINT 'ERROR: Competitions table not found. Please ensure you are connected to the correct database.';
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionTeams')
BEGIN
    PRINT 'ERROR: CompetitionTeams table not found. Please ensure you are connected to the correct database.';
    RETURN;
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Users')
BEGIN
    PRINT 'ERROR: Users table not found. Please ensure you are connected to the correct database.';
    RETURN;
END

PRINT 'Database validation passed. Proceeding with deployment...';
PRINT '';

-- =============================================
-- STEP 1: ADD COLUMNS TO COMPETITIONS TABLE
-- =============================================

PRINT '========================================';
PRINT 'STEP 1: ADDING COLUMNS TO COMPETITIONS TABLE';
PRINT '========================================';

-- Add Phase column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'Phase')
BEGIN
    ALTER TABLE Competitions ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    PRINT '  ✓ Added Phase column to Competitions';
END
ELSE
    PRINT '  ✓ Phase column already exists in Competitions';

-- Add PhaseEndDate column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'PhaseEndDate')
BEGIN
    ALTER TABLE Competitions ADD PhaseEndDate DATETIME2 NULL;
    PRINT '  ✓ Added PhaseEndDate column to Competitions';
END
ELSE
    PRINT '  ✓ PhaseEndDate column already exists in Competitions';

-- Add MaxGamesPerPhase column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'MaxGamesPerPhase')
BEGIN
    ALTER TABLE Competitions ADD MaxGamesPerPhase INT NULL;
    PRINT '  ✓ Added MaxGamesPerPhase column to Competitions';
END
ELSE
    PRINT '  ✓ MaxGamesPerPhase column already exists in Competitions';

PRINT 'Competitions table updates completed.';
PRINT '';

-- =============================================
-- STEP 2: ADD COLUMNS TO COMPETITIONTEAMS TABLE
-- =============================================

PRINT '========================================';
PRINT 'STEP 2: ADDING COLUMNS TO COMPETITIONTEAMS TABLE';
PRINT '========================================';

-- Add Phase column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'Phase')
BEGIN
    ALTER TABLE CompetitionTeams ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    PRINT '  ✓ Added Phase column to CompetitionTeams';
END
ELSE
    PRINT '  ✓ Phase column already exists in CompetitionTeams';

-- Add IsEliminated column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'IsEliminated')
BEGIN
    ALTER TABLE CompetitionTeams ADD IsEliminated BIT NOT NULL DEFAULT 0;
    PRINT '  ✓ Added IsEliminated column to CompetitionTeams';
END
ELSE
    PRINT '  ✓ IsEliminated column already exists in CompetitionTeams';

-- Add AdvancedToNextPhase column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'AdvancedToNextPhase')
BEGIN
    ALTER TABLE CompetitionTeams ADD AdvancedToNextPhase BIT NOT NULL DEFAULT 0;
    PRINT '  ✓ Added AdvancedToNextPhase column to CompetitionTeams';
END
ELSE
    PRINT '  ✓ AdvancedToNextPhase column already exists in CompetitionTeams';

-- Add PhaseEliminatedAt column
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'PhaseEliminatedAt')
BEGIN
    ALTER TABLE CompetitionTeams ADD PhaseEliminatedAt DATETIME2 NULL;
    PRINT '  ✓ Added PhaseEliminatedAt column to CompetitionTeams';
END
ELSE
    PRINT '  ✓ PhaseEliminatedAt column already exists in CompetitionTeams';

PRINT 'CompetitionTeams table updates completed.';
PRINT '';

-- =============================================
-- STEP 3: UPDATE EXISTING DATA USING DYNAMIC SQL
-- =============================================

PRINT '========================================';
PRINT 'STEP 3: UPDATING EXISTING DATA';
PRINT '========================================';

-- Update Competitions table with default values using dynamic SQL
DECLARE @CompetitionsUpdated INT = 0;
DECLARE @SQL NVARCHAR(MAX);

-- Check if both columns exist before updating
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'PhaseEndDate')
   AND EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'MaxGamesPerPhase')
BEGIN
    SET @SQL = N'
    UPDATE Competitions 
    SET PhaseEndDate = CASE WHEN PhaseEndDate IS NULL THEN DATEADD(DAY, 30, GETUTCDATE()) ELSE PhaseEndDate END,
        MaxGamesPerPhase = CASE WHEN MaxGamesPerPhase IS NULL THEN 10 ELSE MaxGamesPerPhase END';
    
    EXEC sp_executesql @SQL;
    
    -- Get count of competitions
    SELECT @CompetitionsUpdated = COUNT(*) FROM Competitions;
    PRINT 'Updated ' + CAST(@CompetitionsUpdated AS VARCHAR) + ' competitions with default phase values.';
END
ELSE
    PRINT 'Skipping competitions update - columns not ready yet.';

-- Count existing data
DECLARE @CompetitionCount INT = 0;
DECLARE @TeamCount INT = 0;

SELECT @CompetitionCount = COUNT(*) FROM Competitions;
SELECT @TeamCount = COUNT(*) FROM CompetitionTeams;

PRINT 'Found ' + CAST(@CompetitionCount AS VARCHAR) + ' competitions in database.';
PRINT 'Found ' + CAST(@TeamCount AS VARCHAR) + ' teams in database.';
PRINT '';

-- =============================================
-- STEP 4: CREATE NEW TABLES
-- =============================================

PRINT '========================================';
PRINT 'STEP 4: CREATING NEW TABLES';
PRINT '========================================';

-- CompetitionPhaseLobbies table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbies')
BEGIN
    CREATE TABLE CompetitionPhaseLobbies (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionId UNIQUEIDENTIFIER NOT NULL,
        Phase NVARCHAR(40) NOT NULL,
        LobbyCode NVARCHAR(12) NOT NULL,
        CreatedByAdminId UNIQUEIDENTIFIER NOT NULL,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_CompetitionPhaseLobbies_Competition 
            FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
        CONSTRAINT FK_CompetitionPhaseLobbies_Admin 
            FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id)
    );
    PRINT '  ✓ Created CompetitionPhaseLobbies table';
END
ELSE
    PRINT '  ✓ CompetitionPhaseLobbies table already exists';

-- CompetitionPhaseLobbyTeams table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbyTeams')
BEGIN
    CREATE TABLE CompetitionPhaseLobbyTeams (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        LobbyId UNIQUEIDENTIFIER NOT NULL,
        CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
        IsWinner BIT NOT NULL DEFAULT 0,
        EliminatedAt DATETIME2 NULL,
        
        CONSTRAINT FK_CompetitionPhaseLobbyTeams_Lobby 
            FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id),
        CONSTRAINT FK_CompetitionPhaseLobbyTeams_Team 
            FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
    );
    PRINT '  ✓ Created CompetitionPhaseLobbyTeams table';
END
ELSE
    PRINT '  ✓ CompetitionPhaseLobbyTeams table already exists';

-- CompetitionTeamPhaseStats table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionTeamPhaseStats')
BEGIN
    CREATE TABLE CompetitionTeamPhaseStats (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
        Phase NVARCHAR(40) NOT NULL,
        Points INT NOT NULL DEFAULT 0,
        BonusPoints INT NOT NULL DEFAULT 0,
        GamesPlayed INT NOT NULL DEFAULT 0,
        BallsWon INT NOT NULL DEFAULT 0,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        CONSTRAINT FK_CompetitionTeamPhaseStats_Team 
            FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
    );
    PRINT '  ✓ Created CompetitionTeamPhaseStats table';
END
ELSE
    PRINT '  ✓ CompetitionTeamPhaseStats table already exists';

PRINT 'New tables created successfully.';
PRINT '';

-- =============================================
-- STEP 5: CREATE INDEXES AND CONSTRAINTS
-- =============================================

PRINT '========================================';
PRINT 'STEP 5: CREATING INDEXES AND CONSTRAINTS';
PRINT '========================================';

-- Create indexes for performance (with error handling)
BEGIN TRY
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Competitions_Phase')
    BEGIN
        CREATE INDEX IX_Competitions_Phase ON Competitions(Phase);
        PRINT '  ✓ Created IX_Competitions_Phase';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_Phase ON CompetitionTeams(Phase);
        PRINT '  ✓ Created IX_CompetitionTeams_Phase';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_IsEliminated')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_IsEliminated ON CompetitionTeams(IsEliminated);
        PRINT '  ✓ Created IX_CompetitionTeams_IsEliminated';
    END

    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_Competition_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_Competition_Phase ON CompetitionTeams(CompetitionId, Phase);
        PRINT '  ✓ Created IX_CompetitionTeams_Competition_Phase';
    END

    -- Add unique constraints
    IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_CompetitionPhaseLobbies_LobbyCode')
    BEGIN
        ALTER TABLE CompetitionPhaseLobbies 
        ADD CONSTRAINT UQ_CompetitionPhaseLobbies_LobbyCode UNIQUE (LobbyCode);
        PRINT '  ✓ Added UQ_CompetitionPhaseLobbies_LobbyCode';
    END

    IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_CompetitionTeamPhaseStats_Team_Phase')
    BEGIN
        ALTER TABLE CompetitionTeamPhaseStats 
        ADD CONSTRAINT UQ_CompetitionTeamPhaseStats_Team_Phase UNIQUE (CompetitionTeamId, Phase);
        PRINT '  ✓ Added UQ_CompetitionTeamPhaseStats_Team_Phase';
    END

    PRINT 'Indexes and constraints created successfully.';
    
END TRY
BEGIN CATCH
    PRINT 'Warning: Some indexes may not have been created. Error: ' + ERROR_MESSAGE();
END CATCH

PRINT '';

-- =============================================
-- DEPLOYMENT SUMMARY
-- =============================================

PRINT '========================================';
PRINT 'DEPLOYMENT SUMMARY';
PRINT '========================================';
PRINT 'Competition Phase System deployment completed successfully!';
PRINT '';
PRINT 'What was deployed:';
PRINT '✓ Phase management columns added to Competitions table';
PRINT '✓ Phase tracking columns added to CompetitionTeams table';
PRINT '✓ CompetitionPhaseLobbies table created';
PRINT '✓ CompetitionPhaseLobbyTeams table created';
PRINT '✓ CompetitionTeamPhaseStats table created';
PRINT '✓ Performance indexes created';
PRINT '✓ Unique constraints added';
PRINT '✓ Existing data preserved';
PRINT '';
PRINT 'Next steps:';
PRINT '1. Run CompetitionPhaseSystem_StoredProcedures.sql to create stored procedures';
PRINT '2. Update your application configuration to use the new phase system';
PRINT '3. Test the phase management functionality';
PRINT '4. Deploy updated application code';
PRINT '';
PRINT 'Database is now ready for the Competition Phase System!';
PRINT '========================================';

-- Final verification (safe)
PRINT 'Final verification:';

SELECT 'Competitions' as TableName, COUNT(*) as RecordCount FROM Competitions;
SELECT 'CompetitionTeams' as TableName, COUNT(*) as RecordCount FROM CompetitionTeams;
SELECT 'CompetitionPhaseLobbies' as TableName, COUNT(*) as RecordCount FROM CompetitionPhaseLobbies;
SELECT 'CompetitionPhaseLobbyTeams' as TableName, COUNT(*) as RecordCount FROM CompetitionPhaseLobbyTeams;
SELECT 'CompetitionTeamPhaseStats' as TableName, COUNT(*) as RecordCount FROM CompetitionTeamPhaseStats;

PRINT 'Deployment verification completed successfully!';
