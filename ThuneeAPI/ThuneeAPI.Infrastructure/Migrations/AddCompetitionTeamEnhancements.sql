-- Migration: Add Competition Team Enhancements
-- Description: Adds invite codes, scoring, and team invitation functionality

-- Add new columns to CompetitionTeams table
ALTER TABLE CompetitionTeams 
ADD InviteCode NVARCHAR(10) NOT NULL DEFAULT '',
    GamesPlayed INT NOT NULL DEFAULT 0,
    Points INT NOT NULL DEFAULT 0,
    BonusPoints INT NOT NULL DEFAULT 0,
    MaxGames INT NOT NULL DEFAULT 10,
    IsActive BIT NOT NULL DEFAULT 1,
    IsComplete BIT NOT NULL DEFAULT 0,
    CompletedAt DATETIME2 NULL;

-- Make Player2Id nullable (for teams waiting for partner)
ALTER TABLE CompetitionTeams 
ALTER COLUMN Player2Id UNIQUEIDENTIFIER NULL;

-- Add unique constraint on InviteCode
ALTER TABLE CompetitionTeams 
ADD CONSTRAINT UQ_CompetitionTeams_InviteCode UNIQUE (InviteCode);

-- Add MaxGamesPerTeam column to Competitions table
ALTER TABLE Competitions 
ADD MaxGamesPerTeam INT NOT NULL DEFAULT 10;

-- Create CompetitionTeamInvites table
CREATE TABLE CompetitionTeamInvites (
    Id UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    CompetitionId UNIQUEIDENTIFIER NOT NULL,
    TeamId UNIQUEIDENTIFIER NOT NULL,
    InviterId UNIQUEIDENTIFIER NOT NULL,
    InviteeId UNIQUEIDENTIFIER NULL,
    InviteCode NVARCHAR(10) NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'pending',
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ExpiresAt DATETIME2 NOT NULL DEFAULT DATEADD(day, 7, GETUTCDATE()),
    AcceptedAt DATETIME2 NULL,
    
    CONSTRAINT FK_CompetitionTeamInvites_Competition 
        FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id) ON DELETE CASCADE,
    CONSTRAINT FK_CompetitionTeamInvites_Team 
        FOREIGN KEY (TeamId) REFERENCES CompetitionTeams(Id) ON DELETE CASCADE,
    CONSTRAINT FK_CompetitionTeamInvites_Inviter 
        FOREIGN KEY (InviterId) REFERENCES Users(Id),
    CONSTRAINT FK_CompetitionTeamInvites_Invitee 
        FOREIGN KEY (InviteeId) REFERENCES Users(Id)
);

-- Add unique constraint on InviteCode for CompetitionTeamInvites
ALTER TABLE CompetitionTeamInvites 
ADD CONSTRAINT UQ_CompetitionTeamInvites_InviteCode UNIQUE (InviteCode);

-- Create indexes for better performance
CREATE INDEX IX_CompetitionTeams_CompetitionId_IsComplete ON CompetitionTeams(CompetitionId, IsComplete);
CREATE INDEX IX_CompetitionTeams_Player1Id ON CompetitionTeams(Player1Id);
CREATE INDEX IX_CompetitionTeams_Player2Id ON CompetitionTeams(Player2Id);
CREATE INDEX IX_CompetitionTeamInvites_InviteCode ON CompetitionTeamInvites(InviteCode);
CREATE INDEX IX_CompetitionTeamInvites_Status ON CompetitionTeamInvites(Status);

-- Update existing teams to have invite codes and be marked as complete
UPDATE CompetitionTeams 
SET InviteCode = LEFT(NEWID(), 8),
    IsComplete = CASE WHEN Player2Id IS NOT NULL THEN 1 ELSE 0 END,
    CompletedAt = CASE WHEN Player2Id IS NOT NULL THEN RegisteredAt ELSE NULL END
WHERE InviteCode = '';

-- Update existing competitions to have MaxGamesPerTeam
UPDATE Competitions 
SET MaxGamesPerTeam = 10 
WHERE MaxGamesPerTeam = 0;
