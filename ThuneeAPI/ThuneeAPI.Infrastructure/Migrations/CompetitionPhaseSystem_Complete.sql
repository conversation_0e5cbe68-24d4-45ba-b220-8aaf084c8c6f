-- =============================================
-- Competition Phase System - Complete Database Update
-- Description: Implements the full competition phase system with knockout rounds
-- Version: 1.0
-- Date: 2025-01-02
-- =============================================

BEGIN TRANSACTION;

BEGIN TRY
    PRINT 'Starting Competition Phase System Database Update...';

    -- =============================================
    -- 1. ADD PHASE MANAGEMENT COLUMNS TO EXISTING TABLES
    -- =============================================
    
    PRINT 'Adding phase management columns to Competitions table...';
    
    -- Add phase management columns to Competitions table
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'Phase')
    BEGIN
        ALTER TABLE Competitions 
        ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'PhaseEndDate')
    BEGIN
        ALTER TABLE Competitions 
        ADD PhaseEndDate DATETIME2 NULL;
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Competitions') AND name = 'MaxGamesPerPhase')
    BEGIN
        ALTER TABLE Competitions 
        ADD MaxGamesPerPhase INT NULL;
    END

    PRINT 'Adding phase tracking columns to CompetitionTeams table...';
    
    -- Add phase tracking columns to CompetitionTeams table
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'Phase')
    BEGIN
        ALTER TABLE CompetitionTeams 
        ADD Phase NVARCHAR(40) NOT NULL DEFAULT 'Leaderboard';
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'IsEliminated')
    BEGIN
        ALTER TABLE CompetitionTeams 
        ADD IsEliminated BIT NOT NULL DEFAULT 0;
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'AdvancedToNextPhase')
    BEGIN
        ALTER TABLE CompetitionTeams 
        ADD AdvancedToNextPhase BIT NOT NULL DEFAULT 0;
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionTeams') AND name = 'PhaseEliminatedAt')
    BEGIN
        ALTER TABLE CompetitionTeams 
        ADD PhaseEliminatedAt DATETIME2 NULL;
    END

    -- =============================================
    -- 2. CREATE NEW TABLES FOR PHASE SYSTEM
    -- =============================================
    
    PRINT 'Creating CompetitionPhaseLobbies table...';
    
    -- Create CompetitionPhaseLobbies table
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbies')
    BEGIN
        CREATE TABLE CompetitionPhaseLobbies (
            Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
            CompetitionId UNIQUEIDENTIFIER NOT NULL,
            Phase NVARCHAR(40) NOT NULL,
            LobbyCode NVARCHAR(12) NOT NULL,
            CreatedByAdminId UNIQUEIDENTIFIER NOT NULL,
            CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
            
            CONSTRAINT FK_CompetitionPhaseLobbies_Competition 
                FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
            CONSTRAINT FK_CompetitionPhaseLobbies_Admin 
                FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id)
        );
    END

    PRINT 'Creating CompetitionPhaseLobbyTeams table...';
    
    -- Create CompetitionPhaseLobbyTeams table
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbyTeams')
    BEGIN
        CREATE TABLE CompetitionPhaseLobbyTeams (
            Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
            LobbyId UNIQUEIDENTIFIER NOT NULL,
            CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
            IsWinner BIT NOT NULL DEFAULT 0,
            EliminatedAt DATETIME2 NULL,
            
            CONSTRAINT FK_CompetitionPhaseLobbyTeams_Lobby 
                FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id),
            CONSTRAINT FK_CompetitionPhaseLobbyTeams_Team 
                FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
        );
    END

    PRINT 'Creating CompetitionTeamPhaseStats table...';
    
    -- Create CompetitionTeamPhaseStats table
    IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionTeamPhaseStats')
    BEGIN
        CREATE TABLE CompetitionTeamPhaseStats (
            Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
            CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
            Phase NVARCHAR(40) NOT NULL,
            Points INT NOT NULL DEFAULT 0,
            BonusPoints INT NOT NULL DEFAULT 0,
            GamesPlayed INT NOT NULL DEFAULT 0,
            BallsWon INT NOT NULL DEFAULT 0,
            CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
            UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
            
            CONSTRAINT FK_CompetitionTeamPhaseStats_Team 
                FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id)
        );
    END

    -- =============================================
    -- 3. CREATE INDEXES FOR PERFORMANCE
    -- =============================================
    
    PRINT 'Creating indexes for performance...';
    
    -- Indexes for Competitions
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Competitions_Phase')
    BEGIN
        CREATE INDEX IX_Competitions_Phase ON Competitions(Phase);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Competitions_Status_Phase')
    BEGIN
        CREATE INDEX IX_Competitions_Status_Phase ON Competitions(Status, Phase);
    END

    -- Indexes for CompetitionTeams
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_Phase ON CompetitionTeams(Phase);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_IsEliminated')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_IsEliminated ON CompetitionTeams(IsEliminated);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_Competition_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_Competition_Phase ON CompetitionTeams(CompetitionId, Phase);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeams_Points_Ranking')
    BEGIN
        CREATE INDEX IX_CompetitionTeams_Points_Ranking ON CompetitionTeams(CompetitionId, Phase, Points DESC, BonusPoints DESC);
    END

    -- Indexes for CompetitionPhaseLobbies
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_Competition_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionPhaseLobbies_Competition_Phase ON CompetitionPhaseLobbies(CompetitionId, Phase);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_LobbyCode')
    BEGIN
        CREATE INDEX IX_CompetitionPhaseLobbies_LobbyCode ON CompetitionPhaseLobbies(LobbyCode);
    END

    -- Indexes for CompetitionPhaseLobbyTeams
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbyTeams_Lobby')
    BEGIN
        CREATE INDEX IX_CompetitionPhaseLobbyTeams_Lobby ON CompetitionPhaseLobbyTeams(LobbyId);
    END
    
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbyTeams_Team')
    BEGIN
        CREATE INDEX IX_CompetitionPhaseLobbyTeams_Team ON CompetitionPhaseLobbyTeams(CompetitionTeamId);
    END

    -- Indexes for CompetitionTeamPhaseStats
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionTeamPhaseStats_Team_Phase')
    BEGIN
        CREATE INDEX IX_CompetitionTeamPhaseStats_Team_Phase ON CompetitionTeamPhaseStats(CompetitionTeamId, Phase);
    END

    -- =============================================
    -- 4. ADD UNIQUE CONSTRAINTS
    -- =============================================
    
    PRINT 'Adding unique constraints...';
    
    -- Unique constraint on lobby codes
    IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_CompetitionPhaseLobbies_LobbyCode')
    BEGIN
        ALTER TABLE CompetitionPhaseLobbies 
        ADD CONSTRAINT UQ_CompetitionPhaseLobbies_LobbyCode UNIQUE (LobbyCode);
    END
    
    -- Unique constraint on team phase stats
    IF NOT EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ_CompetitionTeamPhaseStats_Team_Phase')
    BEGIN
        ALTER TABLE CompetitionTeamPhaseStats 
        ADD CONSTRAINT UQ_CompetitionTeamPhaseStats_Team_Phase UNIQUE (CompetitionTeamId, Phase);
    END

    -- =============================================
    -- 5. UPDATE EXISTING DATA
    -- =============================================
    
    PRINT 'Updating existing competition data...';
    
    -- Update existing competitions to have default phase values
    UPDATE Competitions 
    SET Phase = 'Leaderboard', 
        PhaseEndDate = DATEADD(DAY, 30, GETUTCDATE()),
        MaxGamesPerPhase = 10
    WHERE Phase IS NULL OR Phase = '' OR Phase = 'Leaderboard';

    PRINT 'Updating existing competition team data...';
    
    -- Update existing competition teams to have default phase values
    UPDATE CompetitionTeams 
    SET Phase = 'Leaderboard',
        IsEliminated = 0,
        AdvancedToNextPhase = 0
    WHERE Phase IS NULL OR Phase = '' OR Phase = 'Leaderboard';

    PRINT 'Database schema updates completed successfully.';

    COMMIT TRANSACTION;
    PRINT 'Transaction committed successfully.';

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    PRINT 'Error occurred. Transaction rolled back.';
    PRINT 'Error Message: ' + ERROR_MESSAGE();
    PRINT 'Error Number: ' + CAST(ERROR_NUMBER() AS VARCHAR);
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR);
    THROW;
END CATCH;

PRINT 'Competition Phase System Database Update completed successfully!';
