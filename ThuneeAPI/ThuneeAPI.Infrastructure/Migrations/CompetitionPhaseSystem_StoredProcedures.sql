-- =============================================
-- Competition Phase System - Stored Procedures
-- Description: All stored procedures for competition phase management
-- Version: 1.0
-- Date: 2025-01-02
-- =============================================

PRINT 'Creating Competition Phase System Stored Procedures...';

-- =============================================
-- 1. PHASE MANAGEMENT PROCEDURES
-- =============================================

-- Stored procedure to advance competition phase
PRINT 'Creating SP_AdvanceCompetitionPhase...';
GO
CREATE OR ALTER PROCEDURE SP_AdvanceCompetitionPhase
    @CompetitionId UNIQUEIDENTIFIER,
    @NewPhase NVARCHAR(40),
    @PhaseEndDate DATETIME2 = NULL,
    @MaxGamesPerPhase INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        UPDATE Competitions 
        SET Phase = @NewPhase,
            PhaseEndDate = @PhaseEndDate,
            MaxGamesPerPhase = @MaxGamesPerPhase,
            UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;
        
        -- Return updated competition
        SELECT * FROM Competitions WHERE Id = @CompetitionId;
        
        PRINT 'Competition phase advanced successfully to: ' + @NewPhase;
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_AdvanceCompetitionPhase: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to eliminate teams
PRINT 'Creating SP_EliminateTeams...';
GO
CREATE OR ALTER PROCEDURE SP_EliminateTeams
    @TeamIds NVARCHAR(MAX) -- Comma-separated list of team IDs
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @EliminatedCount INT = 0;
        
        UPDATE CompetitionTeams 
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE Id IN (
            SELECT CAST(value AS UNIQUEIDENTIFIER)
            FROM STRING_SPLIT(@TeamIds, ',')
            WHERE value != '' AND value IS NOT NULL
        );
        
        SET @EliminatedCount = @@ROWCOUNT;
        
        PRINT 'Successfully eliminated ' + CAST(@EliminatedCount AS VARCHAR) + ' teams.';
        
        -- Return eliminated teams
        SELECT * FROM CompetitionTeams 
        WHERE Id IN (
            SELECT CAST(value AS UNIQUEIDENTIFIER)
            FROM STRING_SPLIT(@TeamIds, ',')
            WHERE value != '' AND value IS NOT NULL
        );
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_EliminateTeams: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to advance teams to next phase
PRINT 'Creating SP_AdvanceTeamsToNextPhase...';
GO
CREATE OR ALTER PROCEDURE SP_AdvanceTeamsToNextPhase
    @TeamIds NVARCHAR(MAX), -- Comma-separated list of team IDs
    @NextPhase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @AdvancedCount INT = 0;
        
        UPDATE CompetitionTeams 
        SET Phase = @NextPhase,
            AdvancedToNextPhase = 1
        WHERE Id IN (
            SELECT CAST(value AS UNIQUEIDENTIFIER)
            FROM STRING_SPLIT(@TeamIds, ',')
            WHERE value != '' AND value IS NOT NULL
        );
        
        SET @AdvancedCount = @@ROWCOUNT;
        
        PRINT 'Successfully advanced ' + CAST(@AdvancedCount AS VARCHAR) + ' teams to ' + @NextPhase + ' phase.';
        
        -- Return advanced teams
        SELECT * FROM CompetitionTeams 
        WHERE Id IN (
            SELECT CAST(value AS UNIQUEIDENTIFIER)
            FROM STRING_SPLIT(@TeamIds, ',')
            WHERE value != '' AND value IS NOT NULL
        );
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_AdvanceTeamsToNextPhase: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 2. LOBBY MANAGEMENT PROCEDURES
-- =============================================

-- Stored procedure to create phase lobby
PRINT 'Creating SP_CreatePhaseLobby...';
GO
CREATE OR ALTER PROCEDURE SP_CreatePhaseLobby
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @LobbyCode NVARCHAR(12),
    @CreatedByAdminId UNIQUEIDENTIFIER,
    @TeamIds NVARCHAR(MAX) -- Comma-separated list of team IDs
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @LobbyId UNIQUEIDENTIFIER = NEWID();
        DECLARE @TeamCount INT = 0;
        
        -- Validate team count
        SELECT @TeamCount = COUNT(*)
        FROM STRING_SPLIT(@TeamIds, ',')
        WHERE value != '' AND value IS NOT NULL;
        
        IF @TeamCount != 2
        BEGIN
            RAISERROR('Exactly 2 teams are required for a knockout lobby. Provided: %d', 16, 1, @TeamCount);
            RETURN;
        END
        
        -- Create the lobby
        INSERT INTO CompetitionPhaseLobbies (Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId)
        VALUES (@LobbyId, @CompetitionId, @Phase, @LobbyCode, @CreatedByAdminId);
        
        -- Add teams to the lobby
        INSERT INTO CompetitionPhaseLobbyTeams (LobbyId, CompetitionTeamId)
        SELECT @LobbyId, CAST(value AS UNIQUEIDENTIFIER)
        FROM STRING_SPLIT(@TeamIds, ',')
        WHERE value != '' AND value IS NOT NULL;
        
        PRINT 'Phase lobby created successfully with code: ' + @LobbyCode;
        
        -- Return the created lobby with teams and admin info
        SELECT 
            l.*,
            u.Username as CreatedByAdminUsername,
            u.Email as CreatedByAdminEmail
        FROM CompetitionPhaseLobbies l
        JOIN Users u ON l.CreatedByAdminId = u.Id
        WHERE l.Id = @LobbyId;
        
        -- Return lobby teams
        SELECT 
            lt.*,
            ct.TeamName,
            ct.Player1Id,
            ct.Player2Id,
            u1.Username as Player1Username,
            u1.Email as Player1Email,
            u2.Username as Player2Username,
            u2.Email as Player2Email
        FROM CompetitionPhaseLobbyTeams lt
        JOIN CompetitionTeams ct ON lt.CompetitionTeamId = ct.Id
        JOIN Users u1 ON ct.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
        WHERE lt.LobbyId = @LobbyId;
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_CreatePhaseLobby: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to set lobby winner
PRINT 'Creating SP_SetLobbyWinner...';
GO
CREATE OR ALTER PROCEDURE SP_SetLobbyWinner
    @LobbyId UNIQUEIDENTIFIER,
    @WinnerTeamId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Reset all teams in lobby to not winner
        UPDATE CompetitionPhaseLobbyTeams 
        SET IsWinner = 0
        WHERE LobbyId = @LobbyId;
        
        -- Set the winner
        UPDATE CompetitionPhaseLobbyTeams 
        SET IsWinner = 1
        WHERE LobbyId = @LobbyId AND CompetitionTeamId = @WinnerTeamId;
        
        -- Set eliminated timestamp for losers
        UPDATE CompetitionPhaseLobbyTeams 
        SET EliminatedAt = GETUTCDATE()
        WHERE LobbyId = @LobbyId AND CompetitionTeamId != @WinnerTeamId;
        
        PRINT 'Lobby winner set successfully.';
        
        -- Return updated lobby teams
        SELECT 
            lt.*,
            ct.TeamName
        FROM CompetitionPhaseLobbyTeams lt
        JOIN CompetitionTeams ct ON lt.CompetitionTeamId = ct.Id
        WHERE lt.LobbyId = @LobbyId;
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_SetLobbyWinner: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 3. TEAM AND RANKING PROCEDURES
-- =============================================

-- Stored procedure to get phase teams with rankings
PRINT 'Creating SP_GetPhaseTeamsWithRankings...';
GO
CREATE OR ALTER PROCEDURE SP_GetPhaseTeamsWithRankings
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        SELECT 
            ct.*,
            u1.Username as Player1Username,
            u1.Email as Player1Email,
            u2.Username as Player2Username,
            u2.Email as Player2Email,
            (ct.Points + ct.BonusPoints) as TotalPoints,
            ROW_NUMBER() OVER (
                ORDER BY (ct.Points + ct.BonusPoints) DESC, 
                         ct.Points DESC, 
                         ct.GamesPlayed ASC,
                         ct.RegisteredAt ASC
            ) as Rank
        FROM CompetitionTeams ct
        JOIN Users u1 ON ct.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
        WHERE ct.CompetitionId = @CompetitionId 
          AND ct.Phase = @Phase 
          AND ct.IsEliminated = 0
        ORDER BY TotalPoints DESC, ct.Points DESC, ct.GamesPlayed ASC, ct.RegisteredAt ASC;
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_GetPhaseTeamsWithRankings: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to get eligible teams for next phase
PRINT 'Creating SP_GetEligibleTeamsForNextPhase...';
GO
CREATE OR ALTER PROCEDURE SP_GetEligibleTeamsForNextPhase
    @CompetitionId UNIQUEIDENTIFIER,
    @CurrentPhase NVARCHAR(40),
    @AdvancingCount INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        SELECT TOP (@AdvancingCount)
            ct.*,
            u1.Username as Player1Username,
            u1.Email as Player1Email,
            u2.Username as Player2Username,
            u2.Email as Player2Email,
            (ct.Points + ct.BonusPoints) as TotalPoints,
            ROW_NUMBER() OVER (
                ORDER BY (ct.Points + ct.BonusPoints) DESC, 
                         ct.Points DESC, 
                         ct.GamesPlayed ASC,
                         ct.RegisteredAt ASC
            ) as Rank
        FROM CompetitionTeams ct
        JOIN Users u1 ON ct.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
        WHERE ct.CompetitionId = @CompetitionId 
          AND ct.Phase = @CurrentPhase 
          AND ct.IsEliminated = 0
        ORDER BY TotalPoints DESC, ct.Points DESC, ct.GamesPlayed ASC, ct.RegisteredAt ASC;
        
    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_GetEligibleTeamsForNextPhase: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 4. PHASE STATISTICS PROCEDURES
-- =============================================

-- Stored procedure to create or update phase stats
PRINT 'Creating SP_CreateOrUpdatePhaseStats...';
GO
CREATE OR ALTER PROCEDURE SP_CreateOrUpdatePhaseStats
    @CompetitionTeamId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @Points INT = 0,
    @BonusPoints INT = 0,
    @GamesPlayed INT = 0,
    @BallsWon INT = 0
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Check if stats already exist
        IF EXISTS (SELECT 1 FROM CompetitionTeamPhaseStats WHERE CompetitionTeamId = @CompetitionTeamId AND Phase = @Phase)
        BEGIN
            -- Update existing stats
            UPDATE CompetitionTeamPhaseStats
            SET Points = Points + @Points,
                BonusPoints = BonusPoints + @BonusPoints,
                GamesPlayed = GamesPlayed + @GamesPlayed,
                BallsWon = BallsWon + @BallsWon,
                UpdatedAt = GETUTCDATE()
            WHERE CompetitionTeamId = @CompetitionTeamId AND Phase = @Phase;

            PRINT 'Phase stats updated successfully.';
        END
        ELSE
        BEGIN
            -- Create new stats
            INSERT INTO CompetitionTeamPhaseStats (CompetitionTeamId, Phase, Points, BonusPoints, GamesPlayed, BallsWon)
            VALUES (@CompetitionTeamId, @Phase, @Points, @BonusPoints, @GamesPlayed, @BallsWon);

            PRINT 'Phase stats created successfully.';
        END

        -- Return updated stats
        SELECT * FROM CompetitionTeamPhaseStats
        WHERE CompetitionTeamId = @CompetitionTeamId AND Phase = @Phase;

    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_CreateOrUpdatePhaseStats: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to get team phase stats
PRINT 'Creating SP_GetTeamPhaseStats...';
GO
CREATE OR ALTER PROCEDURE SP_GetTeamPhaseStats
    @CompetitionTeamId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        SELECT
            ps.*,
            ct.TeamName,
            c.Name as CompetitionName
        FROM CompetitionTeamPhaseStats ps
        JOIN CompetitionTeams ct ON ps.CompetitionTeamId = ct.Id
        JOIN Competitions c ON ct.CompetitionId = c.Id
        WHERE ps.CompetitionTeamId = @CompetitionTeamId
        ORDER BY ps.CreatedAt ASC;

    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_GetTeamPhaseStats: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 5. LOBBY QUERY PROCEDURES
-- =============================================

-- Stored procedure to get phase lobbies
PRINT 'Creating SP_GetPhaseLobbies...';
GO
CREATE OR ALTER PROCEDURE SP_GetPhaseLobbies
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        SELECT
            l.*,
            u.Username as CreatedByAdminUsername,
            u.Email as CreatedByAdminEmail,
            COUNT(lt.Id) as TeamCount,
            SUM(CASE WHEN lt.IsWinner = 1 THEN 1 ELSE 0 END) as WinnerCount
        FROM CompetitionPhaseLobbies l
        JOIN Users u ON l.CreatedByAdminId = u.Id
        LEFT JOIN CompetitionPhaseLobbyTeams lt ON l.Id = lt.LobbyId
        WHERE l.CompetitionId = @CompetitionId
          AND (@Phase IS NULL OR l.Phase = @Phase)
        GROUP BY l.Id, l.CompetitionId, l.Phase, l.LobbyCode, l.CreatedByAdminId, l.CreatedAt,
                 u.Username, u.Email
        ORDER BY l.CreatedAt DESC;

    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_GetPhaseLobbies: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- Stored procedure to get lobby by code
PRINT 'Creating SP_GetLobbyByCode...';
GO
CREATE OR ALTER PROCEDURE SP_GetLobbyByCode
    @LobbyCode NVARCHAR(12)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Get lobby details
        SELECT
            l.*,
            u.Username as CreatedByAdminUsername,
            c.Name as CompetitionName
        FROM CompetitionPhaseLobbies l
        JOIN Users u ON l.CreatedByAdminId = u.Id
        JOIN Competitions c ON l.CompetitionId = c.Id
        WHERE l.LobbyCode = @LobbyCode;

        -- Get lobby teams
        SELECT
            lt.*,
            ct.TeamName,
            ct.Player1Id,
            ct.Player2Id,
            u1.Username as Player1Username,
            u1.Email as Player1Email,
            u2.Username as Player2Username,
            u2.Email as Player2Email
        FROM CompetitionPhaseLobbyTeams lt
        JOIN CompetitionTeams ct ON lt.CompetitionTeamId = ct.Id
        JOIN Users u1 ON ct.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
        JOIN CompetitionPhaseLobbies l ON lt.LobbyId = l.Id
        WHERE l.LobbyCode = @LobbyCode;

    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_GetLobbyByCode: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 6. COMPETITION MANAGEMENT PROCEDURES
-- =============================================

-- Stored procedure to process phase end automatically
PRINT 'Creating SP_ProcessPhaseEnd...';
GO
CREATE OR ALTER PROCEDURE SP_ProcessPhaseEnd
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        DECLARE @CurrentPhase NVARCHAR(40);
        DECLARE @NextPhase NVARCHAR(40);
        DECLARE @AdvancingCount INT;
        DECLARE @EligibleTeamIds NVARCHAR(MAX) = '';
        DECLARE @EliminatedTeamIds NVARCHAR(MAX) = '';

        -- Get current phase
        SELECT @CurrentPhase = Phase FROM Competitions WHERE Id = @CompetitionId;

        -- Determine next phase and advancing count
        SET @NextPhase = CASE @CurrentPhase
            WHEN 'Leaderboard' THEN 'Top32'
            WHEN 'Top32' THEN 'Top16'
            WHEN 'Top16' THEN 'Top8'
            WHEN 'Top8' THEN 'Top4'
            WHEN 'Top4' THEN 'Final'
            WHEN 'Final' THEN 'Completed'
            ELSE @CurrentPhase
        END;

        SET @AdvancingCount = CASE @CurrentPhase
            WHEN 'Leaderboard' THEN 32
            WHEN 'Top32' THEN 16
            WHEN 'Top16' THEN 8
            WHEN 'Top8' THEN 4
            WHEN 'Top4' THEN 2
            ELSE 0
        END;

        -- Get eligible teams
        SELECT @EligibleTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
        FROM (
            SELECT TOP (@AdvancingCount) ct.Id
            FROM CompetitionTeams ct
            WHERE ct.CompetitionId = @CompetitionId
              AND ct.Phase = @CurrentPhase
              AND ct.IsEliminated = 0
            ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.Points DESC, ct.GamesPlayed ASC, ct.RegisteredAt ASC
        ) EligibleTeams;

        -- Get eliminated teams
        SELECT @EliminatedTeamIds = STRING_AGG(CAST(Id AS NVARCHAR(36)), ',')
        FROM CompetitionTeams ct
        WHERE ct.CompetitionId = @CompetitionId
          AND ct.Phase = @CurrentPhase
          AND ct.IsEliminated = 0
          AND ct.Id NOT IN (
              SELECT TOP (@AdvancingCount) Id
              FROM CompetitionTeams
              WHERE CompetitionId = @CompetitionId
                AND Phase = @CurrentPhase
                AND IsEliminated = 0
              ORDER BY (Points + BonusPoints) DESC, Points DESC, GamesPlayed ASC, RegisteredAt ASC
          );

        -- Eliminate teams
        IF @EliminatedTeamIds != ''
        BEGIN
            EXEC SP_EliminateTeams @EliminatedTeamIds;
        END

        -- Advance teams
        IF @EligibleTeamIds != ''
        BEGIN
            EXEC SP_AdvanceTeamsToNextPhase @EligibleTeamIds, @NextPhase;
        END

        -- Advance competition phase
        DECLARE @PhaseEndDate DATETIME2 = CASE @NextPhase
            WHEN 'Top32' THEN DATEADD(DAY, 7, GETUTCDATE())
            WHEN 'Top16' THEN DATEADD(DAY, 3, GETUTCDATE())
            WHEN 'Top8' THEN DATEADD(DAY, 2, GETUTCDATE())
            WHEN 'Top4' THEN DATEADD(DAY, 1, GETUTCDATE())
            WHEN 'Final' THEN DATEADD(HOUR, 12, GETUTCDATE())
            ELSE NULL
        END;

        EXEC SP_AdvanceCompetitionPhase @CompetitionId, @NextPhase, @PhaseEndDate, 10;

        PRINT 'Phase end processed successfully. Advanced to: ' + @NextPhase;

        -- Return summary
        SELECT
            @CurrentPhase as PreviousPhase,
            @NextPhase as NewPhase,
            @AdvancingCount as TeamsAdvanced,
            LEN(@EliminatedTeamIds) - LEN(REPLACE(@EliminatedTeamIds, ',', '')) + 1 as TeamsEliminated;

    END TRY
    BEGIN CATCH
        PRINT 'Error in SP_ProcessPhaseEnd: ' + ERROR_MESSAGE();
        THROW;
    END CATCH
END;
GO

PRINT 'All Competition Phase System Stored Procedures created successfully!';
