using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace ThuneeAPI.Infrastructure.Data;

/// <summary>
/// SQL Server implementation of the database connection factory
/// </summary>
public class SqlConnectionFactory : IDbConnectionFactory
{
    private readonly string _connectionString;

    public SqlConnectionFactory(IConfiguration configuration)
    {
        _connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? throw new ArgumentNullException(nameof(configuration), "Connection string 'DefaultConnection' not found");
    }

    /// <summary>
    /// Creates a new SQL Server database connection
    /// </summary>
    /// <returns>A new SqlConnection instance</returns>
    public IDbConnection CreateConnection()
    {
        return new SqlConnection(_connectionString);
    }

    /// <summary>
    /// Creates a new SQL Server database connection asynchronously
    /// </summary>
    /// <returns>A new SqlConnection instance</returns>
    public async Task<IDbConnection> CreateConnectionAsync()
    {
        var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        return connection;
    }
}
