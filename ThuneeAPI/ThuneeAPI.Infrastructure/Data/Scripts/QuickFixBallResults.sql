-- Quick Fix Script for Ball Result API Errors
-- This script ensures all required database objects exist for ball result functionality

PRINT '=== QUICK FIX FOR BALL RESULT ERRORS ===';
PRINT '';

-- Step 1: Ensure GameBalls table exists with basic structure
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'GameBalls')
BEGIN
    PRINT 'Creating GameBalls table...';
    
    CREATE TABLE GameBalls (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        GameId UNIQUEIDENTIFIER NOT NULL,
        BallNumber INT NOT NULL,
        WinnerTeam INT NOT NULL,
        Team1Score INT NOT NULL DEFAULT 0,
        Team2Score INT NOT NULL DEFAULT 0,
        CompletedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        
        -- Enhanced fields (will be added if missing)
        Team1BallsWon INT NOT NULL DEFAULT 0,
        Team2BallsWon INT NOT NULL DEFAULT 0,
        TrumpSuit NVARCHAR(10) NULL,
        HasThuneeDouble BIT NOT NULL DEFAULT 0,
        HasKhanka BIT NOT NULL DEFAULT 0,
        SpecialCallType NVARCHAR(20) NULL,
        SpecialCallResult NVARCHAR(20) NULL,
        Team1Player1Id UNIQUEIDENTIFIER NULL,
        Team1Player2Id UNIQUEIDENTIFIER NULL,
        Team2Player1Id UNIQUEIDENTIFIER NULL,
        Team2Player2Id UNIQUEIDENTIFIER NULL,
        Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1',
        Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2',
        CompetitionId UNIQUEIDENTIFIER NULL,
        
        -- Foreign key to Games table
        CONSTRAINT FK_GameBalls_Games FOREIGN KEY (GameId) REFERENCES Games(Id)
    );
    
    PRINT '✅ GameBalls table created successfully';
END
ELSE
BEGIN
    PRINT '✅ GameBalls table already exists';
END

-- Step 2: Add missing columns to GameBalls table (if any)
PRINT 'Checking for missing columns in GameBalls table...';

-- Add Team1BallsWon if missing
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1BallsWon')
BEGIN
    ALTER TABLE GameBalls ADD Team1BallsWon INT NOT NULL DEFAULT 0;
    PRINT '✅ Added Team1BallsWon column';
END

-- Add Team2BallsWon if missing
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2BallsWon')
BEGIN
    ALTER TABLE GameBalls ADD Team2BallsWon INT NOT NULL DEFAULT 0;
    PRINT '✅ Added Team2BallsWon column';
END

-- Add CompetitionId if missing
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'CompetitionId')
BEGIN
    ALTER TABLE GameBalls ADD CompetitionId UNIQUEIDENTIFIER NULL;
    PRINT '✅ Added CompetitionId column';
END

-- Add Team1Name if missing
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Name')
BEGIN
    ALTER TABLE GameBalls ADD Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1';
    PRINT '✅ Added Team1Name column';
END

-- Add Team2Name if missing
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Name')
BEGIN
    ALTER TABLE GameBalls ADD Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2';
    PRINT '✅ Added Team2Name column';
END

-- Add other essential columns
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'TrumpSuit')
BEGIN
    ALTER TABLE GameBalls ADD TrumpSuit NVARCHAR(10) NULL;
    PRINT '✅ Added TrumpSuit column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasThuneeDouble')
BEGIN
    ALTER TABLE GameBalls ADD HasThuneeDouble BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added HasThuneeDouble column';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasKhanka')
BEGIN
    ALTER TABLE GameBalls ADD HasKhanka BIT NOT NULL DEFAULT 0;
    PRINT '✅ Added HasKhanka column';
END

-- Step 3: Ensure SP_RecordBallResult stored procedure exists
PRINT 'Checking SP_RecordBallResult stored procedure...';

IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_RecordBallResult')
BEGIN
    PRINT 'Creating SP_RecordBallResult stored procedure...';
    
    EXEC('
    CREATE PROCEDURE SP_RecordBallResult
        @GameId UNIQUEIDENTIFIER,
        @CompetitionId UNIQUEIDENTIFIER = NULL,
        @BallNumber INT,
        @WinnerTeam INT,
        @Team1Score INT,
        @Team2Score INT,
        @Team1BallsWon INT,
        @Team2BallsWon INT,
        @Team1Player1Id UNIQUEIDENTIFIER = NULL,
        @Team1Player2Id UNIQUEIDENTIFIER = NULL,
        @Team2Player1Id UNIQUEIDENTIFIER = NULL,
        @Team2Player2Id UNIQUEIDENTIFIER = NULL,
        @Team1Name NVARCHAR(50) = ''Team 1'',
        @Team2Name NVARCHAR(50) = ''Team 2'',
        @TrumpSuit NVARCHAR(10) = NULL,
        @HasThuneeDouble BIT = 0,
        @HasKhanka BIT = 0,
        @SpecialCallType NVARCHAR(20) = NULL,
        @SpecialCallResult NVARCHAR(20) = NULL
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @BallId UNIQUEIDENTIFIER = NEWID();
        DECLARE @CompletedAt DATETIME2 = GETUTCDATE();
        
        BEGIN TRY
            BEGIN TRANSACTION;
            
            -- Insert the ball result
            INSERT INTO GameBalls (
                Id, GameId, CompetitionId, BallNumber, Team1Score, Team2Score, WinnerTeam,
                Team1BallsWon, Team2BallsWon, Team1Player1Id, Team1Player2Id, Team2Player1Id, Team2Player2Id,
                Team1Name, Team2Name, TrumpSuit, HasThuneeDouble, HasKhanka, SpecialCallType, SpecialCallResult,
                CompletedAt, CreatedAt
            )
            VALUES (
                @BallId, @GameId, @CompetitionId, @BallNumber, @Team1Score, @Team2Score, @WinnerTeam,
                @Team1BallsWon, @Team2BallsWon, @Team1Player1Id, @Team1Player2Id, @Team2Player1Id, @Team2Player2Id,
                @Team1Name, @Team2Name, @TrumpSuit, @HasThuneeDouble, @HasKhanka, @SpecialCallType, @SpecialCallResult,
                @CompletedAt, GETUTCDATE()
            );
            
            -- Update the game with current ball and ball scores
            UPDATE Games 
            SET 
                CurrentBall = @BallNumber + 1,
                Team1BallsWon = @Team1BallsWon,
                Team2BallsWon = @Team2BallsWon,
                UpdatedAt = @CompletedAt
            WHERE Id = @GameId;
            
            COMMIT TRANSACTION;
            
            -- Return the ball ID
            SELECT @BallId AS BallId;
            
        END TRY
        BEGIN CATCH
            ROLLBACK TRANSACTION;
            THROW;
        END CATCH
    END
    ');
    
    PRINT '✅ SP_RecordBallResult stored procedure created successfully';
END
ELSE
BEGIN
    PRINT '✅ SP_RecordBallResult stored procedure already exists';
END

PRINT '';
PRINT '=== QUICK FIX COMPLETE ===';
PRINT '';
PRINT 'Database is now ready for ball result functionality.';
PRINT 'Please restart the API and test ball result saving.';
