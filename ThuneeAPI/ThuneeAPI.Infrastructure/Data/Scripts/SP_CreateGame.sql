-- Stored Procedure to create a new game
-- This procedure creates a new game record in the Games table

USE GoldRushThunee;
GO

CREATE OR ALTER PROCEDURE SP_CreateGame
    @Id UNIQUEIDENTIFIER,
    @LobbyCode NVARCHAR(10),
    @Team1Name NVARCHAR(50),
    @Team2Name NVARCHAR(50),
    @Team1Player1Id UNIQUEIDENTIFIER,
    @Team1Player2Id UNIQUEIDENTIFIER = NULL,
    @Team2Player1Id UNIQUEIDENTIFIER = NULL,
    @Team2Player2Id UNIQUEIDENTIFIER = NULL,
    @CompetitionId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if lobby code already exists
        IF EXISTS (SELECT 1 FROM Games WHERE LobbyCode = @LobbyCode)
        BEGIN
            ROLLBACK TRANSACTION;
            RAISERROR('Lobby code already exists', 16, 1);
            RETURN;
        END
        
        -- Insert the new game
        INSERT INTO Games (
            Id,
            LobbyCode,
            CompetitionId,
            Team1Player1Id,
            Team1Player2Id,
            Team2Player1Id,
            Team2Player2Id,
            Team1Name,
            Team2Name,
            Status,
            DealerId,
            TrumpSuit,
            CurrentBall,
            CurrentHand,
            Team1Score,
            Team2Score,
            Team1BallsWon,
            Team2BallsWon,
            WinnerTeam,
            StartedAt,
            CompletedAt,
            CreatedAt,
            UpdatedAt
        )
        VALUES (
            @Id,
            @LobbyCode,
            @CompetitionId,
            @Team1Player1Id,
            @Team1Player2Id,
            @Team2Player1Id,
            @Team2Player2Id,
            @Team1Name,
            @Team2Name,
            'waiting',
            NULL,
            NULL,
            1,
            1,
            0,
            0,
            0,
            0,
            NULL,
            NULL,
            NULL,
            GETUTCDATE(),
            GETUTCDATE()
        );
        
        COMMIT TRANSACTION;
        
        -- Return the created game
        SELECT 
            Id,
            LobbyCode,
            CompetitionId,
            Team1Player1Id,
            Team1Player2Id,
            Team2Player1Id,
            Team2Player2Id,
            Team1Name,
            Team2Name,
            Status,
            DealerId,
            TrumpSuit,
            CurrentBall,
            CurrentHand,
            Team1Score,
            Team2Score,
            Team1BallsWon,
            Team2BallsWon,
            WinnerTeam,
            StartedAt,
            CompletedAt,
            CreatedAt,
            UpdatedAt
        FROM Games 
        WHERE Id = @Id;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
GO
