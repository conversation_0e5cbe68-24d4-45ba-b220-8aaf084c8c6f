-- Database Schema Verification Script for Ball Result Functionality
-- Run this script to verify that all required tables, columns, and stored procedures exist

PRINT '=== BALL RESULT SCHEMA VERIFICATION ===';
PRINT '';

-- Check if GameBalls table exists
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'GameBalls')
BEGIN
    PRINT '✅ GameBalls table exists';
    
    -- Check required columns
    DECLARE @MissingColumns TABLE (ColumnName NVARCHAR(50));
    
    -- List of required columns
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1BallsWon')
        INSERT INTO @MissingColumns VALUES ('Team1BallsWon');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2BallsWon')
        INSERT INTO @MissingColumns VALUES ('Team2BallsWon');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'TrumpSuit')
        INSERT INTO @MissingColumns VALUES ('TrumpSuit');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasThuneeDouble')
        INSERT INTO @MissingColumns VALUES ('HasThuneeDouble');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasKhanka')
        INSERT INTO @MissingColumns VALUES ('HasKhanka');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'SpecialCallType')
        INSERT INTO @MissingColumns VALUES ('SpecialCallType');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'SpecialCallResult')
        INSERT INTO @MissingColumns VALUES ('SpecialCallResult');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Player1Id')
        INSERT INTO @MissingColumns VALUES ('Team1Player1Id');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Player2Id')
        INSERT INTO @MissingColumns VALUES ('Team1Player2Id');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Player1Id')
        INSERT INTO @MissingColumns VALUES ('Team2Player1Id');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Player2Id')
        INSERT INTO @MissingColumns VALUES ('Team2Player2Id');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Name')
        INSERT INTO @MissingColumns VALUES ('Team1Name');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Name')
        INSERT INTO @MissingColumns VALUES ('Team2Name');
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'CompetitionId')
        INSERT INTO @MissingColumns VALUES ('CompetitionId');
    
    -- Report missing columns
    IF EXISTS (SELECT * FROM @MissingColumns)
    BEGIN
        PRINT '❌ Missing columns in GameBalls table:';
        SELECT '   - ' + ColumnName FROM @MissingColumns;
        PRINT '   👉 Run Migration_AddBallResultFields.sql to add missing columns';
    END
    ELSE
    BEGIN
        PRINT '✅ All required columns exist in GameBalls table';
    END
END
ELSE
BEGIN
    PRINT '❌ GameBalls table does not exist';
    PRINT '   👉 Create the GameBalls table first';
END

PRINT '';

-- Check if GameHands table has GameBallId column
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'GameHands')
BEGIN
    PRINT '✅ GameHands table exists';
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameHands]') AND name = 'GameBallId')
    BEGIN
        PRINT '✅ GameBallId column exists in GameHands table';
    END
    ELSE
    BEGIN
        PRINT '❌ GameBallId column missing in GameHands table';
        PRINT '   👉 Run Migration_AddBallResultFields.sql to add GameBallId column';
    END
END
ELSE
BEGIN
    PRINT '❌ GameHands table does not exist';
END

PRINT '';

-- Check if SP_RecordBallResult stored procedure exists
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_RecordBallResult')
BEGIN
    PRINT '✅ SP_RecordBallResult stored procedure exists';
END
ELSE
BEGIN
    PRINT '❌ SP_RecordBallResult stored procedure does not exist';
    PRINT '   👉 Run SP_RecordBallResult.sql to create the stored procedure';
END

-- Check if SP_RecordHandResult stored procedure exists
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_RecordHandResult')
BEGIN
    PRINT '✅ SP_RecordHandResult stored procedure exists';
END
ELSE
BEGIN
    PRINT '❌ SP_RecordHandResult stored procedure does not exist';
    PRINT '   👉 Create SP_RecordHandResult stored procedure';
END

PRINT '';

-- Check Games table structure
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Games')
BEGIN
    PRINT '✅ Games table exists';
    
    -- Check for essential columns
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Games]') AND name = 'LobbyCode')
    BEGIN
        PRINT '✅ LobbyCode column exists in Games table';
    END
    ELSE
    BEGIN
        PRINT '❌ LobbyCode column missing in Games table';
    END
END
ELSE
BEGIN
    PRINT '❌ Games table does not exist';
END

PRINT '';
PRINT '=== VERIFICATION COMPLETE ===';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. If any items show ❌, run the suggested scripts';
PRINT '2. Restart the API after database changes';
PRINT '3. Test ball result saving functionality';
