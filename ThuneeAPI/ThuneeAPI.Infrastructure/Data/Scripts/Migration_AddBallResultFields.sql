-- Migration script to add new fields to GameBalls table for enhanced ball tracking
-- This script adds fields for tracking special calls, ball scores, trump suit, team players, and competition linking

-- Check if the columns already exist before adding them
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1BallsWon')
BEGIN
    ALTER TABLE GameBalls ADD Team1BallsWon INT NOT NULL DEFAULT 0;
    PRINT 'Added Team1BallsWon column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team1BallsWon column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2BallsWon')
BEGIN
    ALTER TABLE GameBalls ADD Team2BallsWon INT NOT NULL DEFAULT 0;
    PRINT 'Added Team2BallsWon column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team2BallsWon column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'TrumpSuit')
BEGIN
    ALTER TABLE GameBalls ADD TrumpSuit NVARCHAR(10) NULL;
    PRINT 'Added TrumpSuit column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'TrumpSuit column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasThuneeDouble')
BEGIN
    ALTER TABLE GameBalls ADD HasThuneeDouble BIT NOT NULL DEFAULT 0;
    PRINT 'Added HasThuneeDouble column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'HasThuneeDouble column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'HasKhanka')
BEGIN
    ALTER TABLE GameBalls ADD HasKhanka BIT NOT NULL DEFAULT 0;
    PRINT 'Added HasKhanka column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'HasKhanka column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'SpecialCallType')
BEGIN
    ALTER TABLE GameBalls ADD SpecialCallType NVARCHAR(20) NULL;
    PRINT 'Added SpecialCallType column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'SpecialCallType column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'SpecialCallResult')
BEGIN
    ALTER TABLE GameBalls ADD SpecialCallResult NVARCHAR(20) NULL;
    PRINT 'Added SpecialCallResult column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'SpecialCallResult column already exists in GameBalls table';
END

-- Add Competition linking
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'CompetitionId')
BEGIN
    ALTER TABLE GameBalls ADD CompetitionId UNIQUEIDENTIFIER NULL;
    PRINT 'Added CompetitionId column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'CompetitionId column already exists in GameBalls table';
END

-- Add Team 1 Players
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Player1Id')
BEGIN
    ALTER TABLE GameBalls ADD Team1Player1Id UNIQUEIDENTIFIER NULL;
    PRINT 'Added Team1Player1Id column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team1Player1Id column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Player2Id')
BEGIN
    ALTER TABLE GameBalls ADD Team1Player2Id UNIQUEIDENTIFIER NULL;
    PRINT 'Added Team1Player2Id column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team1Player2Id column already exists in GameBalls table';
END

-- Add Team 2 Players
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Player1Id')
BEGIN
    ALTER TABLE GameBalls ADD Team2Player1Id UNIQUEIDENTIFIER NULL;
    PRINT 'Added Team2Player1Id column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team2Player1Id column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Player2Id')
BEGIN
    ALTER TABLE GameBalls ADD Team2Player2Id UNIQUEIDENTIFIER NULL;
    PRINT 'Added Team2Player2Id column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team2Player2Id column already exists in GameBalls table';
END

-- Add Team Names
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team1Name')
BEGIN
    ALTER TABLE GameBalls ADD Team1Name NVARCHAR(50) NOT NULL DEFAULT 'Team 1';
    PRINT 'Added Team1Name column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team1Name column already exists in GameBalls table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameBalls]') AND name = 'Team2Name')
BEGIN
    ALTER TABLE GameBalls ADD Team2Name NVARCHAR(50) NOT NULL DEFAULT 'Team 2';
    PRINT 'Added Team2Name column to GameBalls table';
END
ELSE
BEGIN
    PRINT 'Team2Name column already exists in GameBalls table';
END

-- Add GameBallId column to GameHands table to link hands to balls
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[GameHands]') AND name = 'GameBallId')
BEGIN
    ALTER TABLE GameHands ADD GameBallId UNIQUEIDENTIFIER NULL;
    PRINT 'Added GameBallId column to GameHands table';
    
    -- Add foreign key constraint
    ALTER TABLE GameHands 
    ADD CONSTRAINT FK_GameHands_GameBalls 
    FOREIGN KEY (GameBallId) REFERENCES GameBalls(Id);
    PRINT 'Added foreign key constraint FK_GameHands_GameBalls';
END
ELSE
BEGIN
    PRINT 'GameBallId column already exists in GameHands table';
END

-- Add foreign key constraints for GameBalls table
-- Competition foreign key
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_GameBalls_Competitions')
BEGIN
    ALTER TABLE GameBalls
    ADD CONSTRAINT FK_GameBalls_Competitions
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id);
    PRINT 'Added foreign key constraint FK_GameBalls_Competitions';
END
ELSE
BEGIN
    PRINT 'Foreign key constraint FK_GameBalls_Competitions already exists';
END

-- Team player foreign keys
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_GameBalls_Team1Player1')
BEGIN
    ALTER TABLE GameBalls
    ADD CONSTRAINT FK_GameBalls_Team1Player1
    FOREIGN KEY (Team1Player1Id) REFERENCES Users(Id);
    PRINT 'Added foreign key constraint FK_GameBalls_Team1Player1';
END
ELSE
BEGIN
    PRINT 'Foreign key constraint FK_GameBalls_Team1Player1 already exists';
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_GameBalls_Team1Player2')
BEGIN
    ALTER TABLE GameBalls
    ADD CONSTRAINT FK_GameBalls_Team1Player2
    FOREIGN KEY (Team1Player2Id) REFERENCES Users(Id);
    PRINT 'Added foreign key constraint FK_GameBalls_Team1Player2';
END
ELSE
BEGIN
    PRINT 'Foreign key constraint FK_GameBalls_Team1Player2 already exists';
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_GameBalls_Team2Player1')
BEGIN
    ALTER TABLE GameBalls
    ADD CONSTRAINT FK_GameBalls_Team2Player1
    FOREIGN KEY (Team2Player1Id) REFERENCES Users(Id);
    PRINT 'Added foreign key constraint FK_GameBalls_Team2Player1';
END
ELSE
BEGIN
    PRINT 'Foreign key constraint FK_GameBalls_Team2Player1 already exists';
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_GameBalls_Team2Player2')
BEGIN
    ALTER TABLE GameBalls
    ADD CONSTRAINT FK_GameBalls_Team2Player2
    FOREIGN KEY (Team2Player2Id) REFERENCES Users(Id);
    PRINT 'Added foreign key constraint FK_GameBalls_Team2Player2';
END
ELSE
BEGIN
    PRINT 'Foreign key constraint FK_GameBalls_Team2Player2 already exists';
END

PRINT 'Migration completed successfully';
