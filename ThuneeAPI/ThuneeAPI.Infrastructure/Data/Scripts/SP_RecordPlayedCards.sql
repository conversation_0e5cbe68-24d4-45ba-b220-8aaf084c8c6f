-- Stored Procedure to record played cards for a hand
-- This procedure saves individual card plays to the PlayedCards table

USE GoldRushThunee;
GO

CREATE OR ALTER PROCEDURE SP_RecordPlayedCards
    @GameHandId UNIQUEIDENTIFIER,
    @Played<PERSON>ards<PERSON>son NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Delete existing played cards for this hand to avoid duplicates
        DELETE FROM PlayedCards WHERE GameHandId = @GameHandId;
        
        -- Parse JSON and insert played cards
        INSERT INTO PlayedCards (Id, GameHandId, PlayerId, CardSuit, CardValue, PlayOrder, PlayedAt)
        SELECT
            NEWID() AS Id,
            @GameHandId AS GameHandId,
            JSON_VALUE(value, '$.PlayerId') AS PlayerId,
            JSON_VALUE(value, '$.CardSuit') AS CardSuit,
            JSON_VALUE(value, '$.CardValue') AS CardValue,
            CAST(JSON_VALUE(value, '$.PlayOrder') AS INT) AS PlayOrder,
            GETUTCDATE() AS PlayedAt
        FROM OPENJSON(@PlayedCardsJson)
        WHERE JSON_VALUE(value, '$.PlayerId') IS NOT NULL
          AND JSON_VALUE(value, '$.CardSuit') IS NOT NULL
          AND JSON_VALUE(value, '$.CardValue') IS NOT NULL
          AND JSON_VALUE(value, '$.PlayOrder') IS NOT NULL;
        
        COMMIT TRANSACTION;
        
        -- Return the count of cards inserted
        SELECT @@ROWCOUNT AS CardsInserted;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
GO
