-- Updated Stored Procedure to record hand result with duplicate handling
-- This procedure handles cases where the same hand might be recorded multiple times

USE GoldRushThunee;
GO

CREATE OR ALTER PROCEDURE SP_RecordHandResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @HandId UNIQUEIDENTIFIER;

    -- Check if hand already exists
    SELECT @HandId = Id
    FROM GameHands
    WHERE GameId = @GameId
      AND BallNumber = @BallNumber
      AND HandNumber = @HandNumber;

    IF @HandId IS NULL
    BEGIN
        -- Insert new hand
        SET @HandId = NEWID();
        INSERT INTO GameHands (Id, GameId, BallN<PERSON>ber, HandNumber, WinnerPlayerId, Points, CompletedAt)
        VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, GETUTCDATE());
    END
    ELSE
    BEGIN
        -- Update existing hand
        UPDATE GameHands
        SET WinnerPlayerId = @WinnerPlayerId,
            Points = @Points,
            CompletedAt = GETUTCDATE()
        WHERE Id = @HandId;

        -- Delete existing played cards for this hand to avoid duplicates
        DELETE FROM PlayedCards WHERE GameHandId = @HandId;
    END

    SELECT @HandId AS HandId;
END
