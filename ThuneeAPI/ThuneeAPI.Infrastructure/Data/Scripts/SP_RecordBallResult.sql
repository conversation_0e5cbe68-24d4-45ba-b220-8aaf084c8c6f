-- Stored Procedure to record ball result
-- This procedure saves ball data including all hands and special calls

USE GoldRushThunee;
GO

CREATE OR ALTER PROCEDURE SP_RecordBallResult
    @GameId UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER = NULL,
    @BallNumber INT,
    @WinnerTeam INT,
    @Team1Score INT,
    @Team2Score INT,
    @Team1BallsWon INT,
    @Team2BallsWon INT,
    @Team1Player1Id UNIQUEIDENTIFIER = NULL,
    @Team1Player2Id UNIQUEIDENTIFIER = NULL,
    @Team2Player1Id UNIQUEIDENTIFIER = NULL,
    @Team2Player2Id UNIQUEIDENTIFIER = NULL,
    @Team1Name NVARCHAR(50) = 'Team 1',
    @Team2Name NVARCHAR(50) = 'Team 2',
    @TrumpSuit NVARCHAR(10) = NULL,
    @HasThuneeDouble BIT = 0,
    @HasKhanka BIT = 0,
    @SpecialCallType NVARCHAR(20) = NULL,
    @SpecialCallResult NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @BallId UNIQUEIDENTIFIER;
    DECLARE @CompletedAt DATETIME2 = GETUTCDATE();

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Check if ball already exists
        SELECT @BallId = Id
        FROM GameBalls
        WHERE GameId = @GameId AND BallNumber = @BallNumber;

        IF @BallId IS NULL
        BEGIN
            -- Insert new ball result
            SET @BallId = NEWID();
            INSERT INTO GameBalls (
                Id,
                GameId,
                CompetitionId,
                BallNumber,
                Team1Score,
                Team2Score,
                WinnerTeam,
                Team1BallsWon,
                Team2BallsWon,
                Team1Player1Id,
                Team1Player2Id,
                Team2Player1Id,
                Team2Player2Id,
                Team1Name,
                Team2Name,
                TrumpSuit,
                HasThuneeDouble,
                HasKhanka,
                SpecialCallType,
                SpecialCallResult,
                CompletedAt,
                CreatedAt
            )
            VALUES (
                @BallId,
                @GameId,
                @CompetitionId,
                @BallNumber,
                @Team1Score,
                @Team2Score,
                @WinnerTeam,
                @Team1BallsWon,
                @Team2BallsWon,
                @Team1Player1Id,
                @Team1Player2Id,
                @Team2Player1Id,
                @Team2Player2Id,
                @Team1Name,
                @Team2Name,
                @TrumpSuit,
                @HasThuneeDouble,
                @HasKhanka,
                @SpecialCallType,
                @SpecialCallResult,
                @CompletedAt,
                GETUTCDATE()
            );
        END
        ELSE
        BEGIN
            -- Update existing ball result
            UPDATE GameBalls
            SET Team1Score = @Team1Score,
                Team2Score = @Team2Score,
                WinnerTeam = @WinnerTeam,
                Team1BallsWon = @Team1BallsWon,
                Team2BallsWon = @Team2BallsWon,
                TrumpSuit = @TrumpSuit,
                HasThuneeDouble = @HasThuneeDouble,
                HasKhanka = @HasKhanka,
                SpecialCallType = @SpecialCallType,
                SpecialCallResult = @SpecialCallResult,
                CompletedAt = @CompletedAt
            WHERE Id = @BallId;
        END
        
        -- Update the game with current ball and ball scores
        UPDATE Games 
        SET 
            CurrentBall = @BallNumber + 1,
            Team1BallsWon = @Team1BallsWon,
            Team2BallsWon = @Team2BallsWon,
            UpdatedAt = @CompletedAt
        WHERE Id = @GameId;
        
        -- Update any existing hands for this ball to link to the ball record
        UPDATE GameHands 
        SET GameBallId = @BallId
        WHERE GameId = @GameId AND BallNumber = @BallNumber;
        
        COMMIT TRANSACTION;
        
        -- Return the ball ID
        SELECT @BallId AS BallId;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END;
