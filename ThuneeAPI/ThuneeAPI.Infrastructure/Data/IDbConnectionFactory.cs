using System.Data;

namespace ThuneeAPI.Infrastructure.Data;

/// <summary>
/// Factory interface for creating database connections
/// </summary>
public interface IDbConnectionFactory
{
    /// <summary>
    /// Creates a new database connection
    /// </summary>
    /// <returns>A new database connection instance</returns>
    IDbConnection CreateConnection();
    
    /// <summary>
    /// Creates a new database connection asynchronously
    /// </summary>
    /// <returns>A new database connection instance</returns>
    Task<IDbConnection> CreateConnectionAsync();
}
