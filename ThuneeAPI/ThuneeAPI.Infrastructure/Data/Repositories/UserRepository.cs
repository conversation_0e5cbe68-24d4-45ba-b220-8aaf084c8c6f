using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for User entity operations using Dapper
/// </summary>
public class UserRepository : BaseRepository, IUserRepository
{
    public UserRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<User> CreateAsync(User user)
    {
        var parameters = new
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            PasswordHash = user.PasswordHash,
            IsVerified = user.IsVerified,
            IsActive = user.IsActive,
            IsAdmin = user.IsAdmin
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<User>("SP_CreateUser", parameters);
        return result ?? throw new InvalidOperationException("Failed to create user");
    }

    public async Task<User?> GetByIdAsync(Guid id)
    {
        try
        {
            var parameters = new { Id = id };
            return await ExecuteStoredProcedureFirstOrDefaultAsync<User>("SP_GetUserById", parameters);
        }
        catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Message.Contains("PROCEDURE"))
        {
            // Fallback to direct SQL if stored procedure doesn't exist
            var sql = "SELECT * FROM Users WHERE Id = @Id AND IsActive = 1";
            var parameters = new { Id = id };
            var users = await ExecuteQueryAsync<User>(sql, parameters);
            return users.FirstOrDefault();
        }
    }

    public async Task<User?> GetByUsernameAsync(string username)
    {
        try
        {
            var parameters = new { Username = username };
            return await ExecuteStoredProcedureFirstOrDefaultAsync<User>("SP_GetUserByUsername", parameters);
        }
        catch (Microsoft.Data.SqlClient.SqlException ex) when (ex.Message.Contains("PROCEDURE"))
        {
            // Fallback to direct SQL if stored procedure doesn't exist
            var sql = "SELECT * FROM Users WHERE Username = @Username AND IsActive = 1";
            var parameters = new { Username = username };
            var users = await ExecuteQueryAsync<User>(sql, parameters);
            return users.FirstOrDefault();
        }
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        // For now, we'll use a simple query since we don't have a specific stored procedure for email lookup
        var sql = "SELECT * FROM Users WHERE Email = @Email AND IsActive = 1";
        var parameters = new { Email = email };
        var users = await ExecuteQueryAsync<User>(sql, parameters);
        return users.FirstOrDefault();
    }

    public async Task<User?> GetByUsernameOrEmailAsync(string usernameOrEmail)
    {
        var sql = "SELECT * FROM Users WHERE (Username = @UsernameOrEmail OR Email = @UsernameOrEmail) AND IsActive = 1";
        var parameters = new { UsernameOrEmail = usernameOrEmail };
        var users = await ExecuteQueryAsync<User>(sql, parameters);
        return users.FirstOrDefault();
    }

    public async Task UpdateLastLoginAsync(Guid userId)
    {
        var parameters = new { Id = userId };
        await ExecuteStoredProcedureAsync("SP_UpdateUserLastLogin", parameters);
    }

    public async Task UpdateAsync(User user)
    {
        var sql = @"
            UPDATE Users 
            SET Username = @Username,
                Email = @Email,
                PasswordHash = @PasswordHash,
                IsVerified = @IsVerified,
                IsActive = @IsActive,
                IsAdmin = @IsAdmin,
                UpdatedAt = GETUTCDATE()
            WHERE Id = @Id";

        var parameters = new
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            PasswordHash = user.PasswordHash,
            IsVerified = user.IsVerified,
            IsActive = user.IsActive,
            IsAdmin = user.IsAdmin
        };

        await ExecuteCommandAsync(sql, parameters);
    }

    public async Task<bool> UsernameExistsAsync(string username)
    {
        var sql = "SELECT COUNT(1) FROM Users WHERE Username = @Username";
        var parameters = new { Username = username };
        var users = await ExecuteQueryAsync<int>(sql, parameters);
        return users.First() > 0;
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        var sql = "SELECT COUNT(1) FROM Users WHERE Email = @Email";
        var parameters = new { Email = email };
        var users = await ExecuteQueryAsync<int>(sql, parameters);
        return users.First() > 0;
    }

    public async Task<IEnumerable<User>> GetAllAsync(int pageNumber = 1, int pageSize = 20)
    {
        var offset = (pageNumber - 1) * pageSize;
        var sql = @"
            SELECT * FROM Users 
            WHERE IsActive = 1
            ORDER BY CreatedAt DESC
            OFFSET @Offset ROWS
            FETCH NEXT @PageSize ROWS ONLY";

        var parameters = new { Offset = offset, PageSize = pageSize };
        return await ExecuteQueryAsync<User>(sql, parameters);
    }
}
