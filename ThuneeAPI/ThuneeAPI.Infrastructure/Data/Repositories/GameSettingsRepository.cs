using Dapper;
using System.Data;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for game settings operations
/// </summary>
public class GameSettingsRepository : BaseRepository, IGameSettingsRepository
{
    public GameSettingsRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    /// <summary>
    /// Get the current game settings
    /// </summary>
    /// <returns>Game settings or null if not found</returns>
    public async Task<GameSettings?> GetGameSettingsAsync()
    {
        using var connection = await _connectionFactory.CreateConnectionAsync();
        
        const string sql = @"
            SELECT 
                Id,
                PlayTimeframeOptions,
                DefaultPlayTimeframe,
                TrumperThuneeCallingDuration,
                FirstRemainingThuneeCallingDuration,
                LastRemainingThuneeCallingDuration,
                VotingTimeLimit,
                TrumpDisplayDuration,
                CardDealingSpeed,
                TimerUpdateInterval,
                <PERSON>At,
                UpdatedAt,
                UpdatedBy
            FROM GameSettings 
            WHERE Id = 1";

        return await connection.QueryFirstOrDefaultAsync<GameSettings>(sql);
    }

    /// <summary>
    /// Update game settings
    /// </summary>
    /// <param name="settings">Updated settings</param>
    /// <returns>Updated game settings</returns>
    public async Task<GameSettings> UpdateGameSettingsAsync(GameSettings settings)
    {
        using var connection = await _connectionFactory.CreateConnectionAsync();
        
        const string sql = @"
            UPDATE GameSettings 
            SET 
                PlayTimeframeOptions = @PlayTimeframeOptions,
                DefaultPlayTimeframe = @DefaultPlayTimeframe,
                TrumperThuneeCallingDuration = @TrumperThuneeCallingDuration,
                FirstRemainingThuneeCallingDuration = @FirstRemainingThuneeCallingDuration,
                LastRemainingThuneeCallingDuration = @LastRemainingThuneeCallingDuration,
                VotingTimeLimit = @VotingTimeLimit,
                TrumpDisplayDuration = @TrumpDisplayDuration,
                CardDealingSpeed = @CardDealingSpeed,
                TimerUpdateInterval = @TimerUpdateInterval,
                UpdatedAt = @UpdatedAt,
                UpdatedBy = @UpdatedBy
            WHERE Id = 1";

        await connection.ExecuteAsync(sql, settings);
        
        // Return the updated settings
        return await GetGameSettingsAsync() ?? throw new InvalidOperationException("Failed to retrieve updated settings");
    }

    /// <summary>
    /// Create initial game settings
    /// </summary>
    /// <param name="settings">Initial settings</param>
    /// <returns>Created game settings</returns>
    public async Task<GameSettings> CreateGameSettingsAsync(GameSettings settings)
    {
        using var connection = await _connectionFactory.CreateConnectionAsync();
        
        const string sql = @"
            INSERT INTO GameSettings (
                Id,
                PlayTimeframeOptions,
                DefaultPlayTimeframe,
                TrumperThuneeCallingDuration,
                FirstRemainingThuneeCallingDuration,
                LastRemainingThuneeCallingDuration,
                VotingTimeLimit,
                TrumpDisplayDuration,
                CardDealingSpeed,
                TimerUpdateInterval,
                CreatedAt,
                UpdatedAt,
                UpdatedBy
            ) VALUES (
                @Id,
                @PlayTimeframeOptions,
                @DefaultPlayTimeframe,
                @TrumperThuneeCallingDuration,
                @FirstRemainingThuneeCallingDuration,
                @LastRemainingThuneeCallingDuration,
                @VotingTimeLimit,
                @TrumpDisplayDuration,
                @CardDealingSpeed,
                @TimerUpdateInterval,
                @CreatedAt,
                @UpdatedAt,
                @UpdatedBy
            )";

        await connection.ExecuteAsync(sql, settings);
        
        return await GetGameSettingsAsync() ?? throw new InvalidOperationException("Failed to retrieve created settings");
    }

    /// <summary>
    /// Reset game settings to defaults
    /// </summary>
    /// <returns>Reset game settings</returns>
    public async Task<GameSettings> ResetGameSettingsAsync()
    {
        var defaultSettings = new GameSettings
        {
            Id = 1,
            PlayTimeframeOptions = "[3,4,5,6,60]",
            DefaultPlayTimeframe = 3,
            TrumperThuneeCallingDuration = 5,
            FirstRemainingThuneeCallingDuration = 3,
            LastRemainingThuneeCallingDuration = 2,
            VotingTimeLimit = 15,
            TrumpDisplayDuration = 10,
            CardDealingSpeed = 300,
            TimerUpdateInterval = 100,
            CardFaceBaseUrl = "/CardFace",
            CardBackImageUrl = "/CardFace/card-back.svg",
            CustomCardFaceMappings = null,
            UpdatedAt = DateTime.UtcNow
        };

        return await UpdateGameSettingsAsync(defaultSettings);
    }
}
