using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for Game entity operations using Dapper
/// </summary>
public class GameRepository : BaseRepository, IGameRepository
{
    public GameRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<Game> CreateAsync(Game game)
    {
        var parameters = new
        {
            Id = game.Id,
            LobbyCode = game.LobbyCode,
            Team1Name = game.Team1Name,
            Team2Name = game.Team2Name,
            Team1Player1Id = game.Team1Player1Id,
            Team1Player2Id = game.Team1Player2Id,
            Team2Player1Id = game.Team2Player1Id,
            Team2Player2Id = game.Team2Player2Id,
            CompetitionId = game.CompetitionId
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<Game>("SP_CreateGame", parameters);
        return result ?? throw new InvalidOperationException("Failed to create game");
    }

    public async Task<Game?> GetByIdAsync(Guid id)
    {
        var sql = "SELECT * FROM Games WHERE Id = @Id";
        var parameters = new { Id = id };
        var games = await ExecuteQueryAsync<Game>(sql, parameters);
        return games.FirstOrDefault();
    }

    public async Task<Game?> GetByLobbyCodeAsync(string lobbyCode)
    {
        var parameters = new { LobbyCode = lobbyCode };
        return await ExecuteStoredProcedureFirstOrDefaultAsync<Game>("SP_GetGameByLobbyCode", parameters);
    }

    public async Task UpdateAsync(Game game)
    {
        var sql = @"
            UPDATE Games 
            SET LobbyCode = @LobbyCode,
                Team1Name = @Team1Name,
                Team2Name = @Team2Name,
                Team1Player1Id = @Team1Player1Id,
                Team1Player2Id = @Team1Player2Id,
                Team2Player1Id = @Team2Player1Id,
                Team2Player2Id = @Team2Player2Id,
                Status = @Status,
                CurrentBall = @CurrentBall,
                CurrentHand = @CurrentHand,
                Team1Score = @Team1Score,
                Team2Score = @Team2Score,
                Team1BallsWon = @Team1BallsWon,
                Team2BallsWon = @Team2BallsWon,
                TrumpSuit = @TrumpSuit,
                CompetitionId = @CompetitionId,
                UpdatedAt = GETUTCDATE()
            WHERE Id = @Id";

        var parameters = new
        {
            Id = game.Id,
            LobbyCode = game.LobbyCode,
            Team1Name = game.Team1Name,
            Team2Name = game.Team2Name,
            Team1Player1Id = game.Team1Player1Id,
            Team1Player2Id = game.Team1Player2Id,
            Team2Player1Id = game.Team2Player1Id,
            Team2Player2Id = game.Team2Player2Id,
            Status = game.Status,
            CurrentBall = game.CurrentBall,
            CurrentHand = game.CurrentHand,
            Team1Score = game.Team1Score,
            Team2Score = game.Team2Score,
            Team1BallsWon = game.Team1BallsWon,
            Team2BallsWon = game.Team2BallsWon,
            TrumpSuit = game.TrumpSuit,
            CompetitionId = game.CompetitionId
        };

        await ExecuteCommandAsync(sql, parameters);
    }

    public async Task<Game?> JoinGameAsync(string lobbyCode, Guid playerId)
    {
        var parameters = new { LobbyCode = lobbyCode, PlayerId = playerId };
        return await ExecuteStoredProcedureFirstOrDefaultAsync<Game>("SP_JoinGame", parameters);
    }

    public async Task<Guid> RecordHandResultAsync(Guid gameId, int ballNumber, int handNumber, Guid winnerPlayerId, int points)
    {
        var parameters = new
        {
            GameId = gameId,
            BallNumber = ballNumber,
            HandNumber = handNumber,
            WinnerPlayerId = winnerPlayerId,
            Points = points
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<dynamic>("SP_RecordHandResult", parameters);
        return result?.HandId ?? throw new InvalidOperationException("Failed to record hand result");
    }

    public async Task<bool> LobbyCodeExistsAsync(string lobbyCode)
    {
        var sql = "SELECT COUNT(1) FROM Games WHERE LobbyCode = @LobbyCode";
        var parameters = new { LobbyCode = lobbyCode };
        var count = await ExecuteQueryAsync<int>(sql, parameters);
        return count.First() > 0;
    }

    public async Task<IEnumerable<Game>> GetByCompetitionIdAsync(Guid competitionId)
    {
        var sql = "SELECT * FROM Games WHERE CompetitionId = @CompetitionId ORDER BY CreatedAt DESC";
        var parameters = new { CompetitionId = competitionId };
        return await ExecuteQueryAsync<Game>(sql, parameters);
    }

    public async Task<IEnumerable<Game>> GetByPlayerIdAsync(Guid playerId)
    {
        var sql = @"
            SELECT * FROM Games 
            WHERE Team1Player1Id = @PlayerId 
               OR Team1Player2Id = @PlayerId 
               OR Team2Player1Id = @PlayerId 
               OR Team2Player2Id = @PlayerId
            ORDER BY CreatedAt DESC";
        var parameters = new { PlayerId = playerId };
        return await ExecuteQueryAsync<Game>(sql, parameters);
    }

    public async Task<IEnumerable<Game>> GetActiveGamesAsync()
    {
        var sql = "SELECT * FROM Games WHERE Status IN ('waiting', 'in-progress') ORDER BY CreatedAt DESC";
        return await ExecuteQueryAsync<Game>(sql);
    }

    public async Task DeleteAsync(Guid id)
    {
        var sql = "DELETE FROM Games WHERE Id = @Id";
        var parameters = new { Id = id };
        await ExecuteCommandAsync(sql, parameters);
    }
}
