using Dapper;
using System.Data;
using ThuneeAPI.Infrastructure.Helpers;

namespace ThuneeAPI.Infrastructure.Data;

/// <summary>
/// Base repository class providing common database operations using Dapper
/// </summary>
public abstract class BaseRepository
{
    protected readonly IDbConnectionFactory _connectionFactory;

    protected BaseRepository(IDbConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
    }

    /// <summary>
    /// Executes a stored procedure and returns the result
    /// </summary>
    /// <typeparam name="T">The type to map the result to</typeparam>
    /// <param name="storedProcedureName">Name of the stored procedure resource</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>Collection of mapped results</returns>
    protected async Task<IEnumerable<T>> ExecuteStoredProcedureAsync<T>(string storedProcedureName, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        var procedureName = StoredProcedureHelper.GetStoredProcedure(storedProcedureName);
        return await connection.QueryAsync<T>(procedureName, parameters, commandType: CommandType.StoredProcedure);
    }

    /// <summary>
    /// Executes a stored procedure and returns the first result or default
    /// </summary>
    /// <typeparam name="T">The type to map the result to</typeparam>
    /// <param name="storedProcedureName">Name of the stored procedure resource</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>First mapped result or default value</returns>
    protected async Task<T?> ExecuteStoredProcedureFirstOrDefaultAsync<T>(string storedProcedureName, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        var procedureName = StoredProcedureHelper.GetStoredProcedure(storedProcedureName);
        return await connection.QueryFirstOrDefaultAsync<T>(procedureName, parameters, commandType: CommandType.StoredProcedure);
    }

    /// <summary>
    /// Executes a stored procedure and returns a single result
    /// </summary>
    /// <typeparam name="T">The type to map the result to</typeparam>
    /// <param name="storedProcedureName">Name of the stored procedure resource</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>Single mapped result</returns>
    protected async Task<T> ExecuteStoredProcedureSingleAsync<T>(string storedProcedureName, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        var procedureName = StoredProcedureHelper.GetStoredProcedure(storedProcedureName);
        return await connection.QuerySingleAsync<T>(procedureName, parameters, commandType: CommandType.StoredProcedure);
    }

    /// <summary>
    /// Executes a stored procedure without returning results
    /// </summary>
    /// <param name="storedProcedureName">Name of the stored procedure resource</param>
    /// <param name="parameters">Parameters for the stored procedure</param>
    /// <returns>Number of affected rows</returns>
    protected async Task<int> ExecuteStoredProcedureAsync(string storedProcedureName, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        var procedureName = StoredProcedureHelper.GetStoredProcedure(storedProcedureName);
        return await connection.ExecuteAsync(procedureName, parameters, commandType: CommandType.StoredProcedure);
    }

    /// <summary>
    /// Executes a raw SQL query and returns the result
    /// </summary>
    /// <typeparam name="T">The type to map the result to</typeparam>
    /// <param name="sql">SQL query to execute</param>
    /// <param name="parameters">Parameters for the query</param>
    /// <returns>Collection of mapped results</returns>
    protected async Task<IEnumerable<T>> ExecuteQueryAsync<T>(string sql, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync<T>(sql, parameters);
    }

    /// <summary>
    /// Executes a raw SQL command without returning results
    /// </summary>
    /// <param name="sql">SQL command to execute</param>
    /// <param name="parameters">Parameters for the command</param>
    /// <returns>Number of affected rows</returns>
    protected async Task<int> ExecuteCommandAsync(string sql, object? parameters = null)
    {
        using var connection = _connectionFactory.CreateConnection();
        var result = await connection.ExecuteAsync(sql, parameters);
        // Add logging to debug database operations
        Console.WriteLine($"[DEBUG] ExecuteCommandAsync: SQL executed, affected rows: {result}");
        Console.WriteLine($"[DEBUG] SQL: {sql}");
        if (parameters != null)
        {
            Console.WriteLine($"[DEBUG] Parameters: {System.Text.Json.JsonSerializer.Serialize(parameters)}");
        }
        return result;
    }

    /// <summary>
    /// Executes a SQL command or stored procedure without returning results
    /// </summary>
    /// <param name="sql">SQL command or stored procedure name to execute</param>
    /// <param name="parameters">Parameters for the command</param>
    /// <param name="isStoredProcedure">Whether this is a stored procedure call</param>
    /// <returns>Number of affected rows</returns>
    protected async Task<int> ExecuteCommandAsync(string sql, object? parameters = null, bool isStoredProcedure = false)
    {
        using var connection = _connectionFactory.CreateConnection();
        var commandType = isStoredProcedure ? System.Data.CommandType.StoredProcedure : System.Data.CommandType.Text;
        return await connection.ExecuteAsync(sql, parameters, commandType: commandType);
    }

    /// <summary>
    /// Executes multiple operations within a transaction
    /// </summary>
    /// <param name="operations">Function containing the operations to execute</param>
    /// <returns>Task representing the transaction</returns>
    protected async Task ExecuteInTransactionAsync(Func<IDbConnection, IDbTransaction, Task> operations)
    {
        using var connection = _connectionFactory.CreateConnection();
        connection.Open();
        using var transaction = connection.BeginTransaction();
        
        try
        {
            await operations(connection, transaction);
            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }
}
