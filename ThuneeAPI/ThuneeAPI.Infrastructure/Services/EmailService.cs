using Microsoft.Extensions.Configuration;
using System;
using System.Net.Mail;
using System.Net;
using System.Threading.Tasks;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Infrastructure.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string to, string subject, string body)
        {
            try
            {
                // Check if email settings are configured
                var host = _configuration["EmailSettings:Host"];
                var portStr = _configuration["EmailSettings:Port"]?.ToString();
                var useSslStr = _configuration["EmailSettings:UseSSL"]?.ToString();
                var username = _configuration["EmailSettings:Username"] ?? _configuration["EmailSettings:UserName"];
                var password = _configuration["EmailSettings:Password"];
                var from = _configuration["EmailSettings:From"] ?? _configuration["EmailSettings:from"];
                var displayName = _configuration["EmailSettings:DisplayName"] ?? _configuration["EmailSettings:displayName"];

                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(portStr) ||
                    string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) ||
                    string.IsNullOrEmpty(from))
                {
                    Console.WriteLine($"Email settings not configured properly. Host: {host}, Port: {portStr}, Username: {username}, From: {from}. Skipping email send.");
                    return;
                }

                if (!int.TryParse(portStr, out int port))
                {
                    Console.WriteLine($"Invalid port configuration: {portStr}. Skipping email send.");
                    return;
                }

                if (!bool.TryParse(useSslStr, out bool useSSL))
                {
                    useSSL = true; // Default to true for security
                }

                var smtpClient = new SmtpClient
                {
                    Host = host,
                    Port = port,
                    EnableSsl = useSSL,
                    Credentials = new NetworkCredential(username, password),
                    DeliveryMethod = SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Timeout = 30000 // 30 seconds timeout
                };

                Console.WriteLine($"Attempting to send email to {to} using SMTP {host}:{port} (SSL: {useSSL})");

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(from, displayName ?? "Thunee Competition"),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };
                mailMessage.To.Add(to);

                await smtpClient.SendMailAsync(mailMessage);
                Console.WriteLine($"Email sent successfully to {to}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending email to {to}: {ex.Message}");
                // In production, you might want to log this properly or throw the exception
                // For now, we'll just log and continue to not break the registration flow
            }
        }

        public async Task SendWelcomeEmailAsync(string to, string username)
        {
            var subject = "Welcome to Thunee Competition!";
            var body = GetWelcomeEmailBody(username);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendTeamPairingEmailAsync(string to, string username, string teamName, string partnerName)
        {
            var subject = "Team Pairing Complete - Ready to Compete!";
            var body = GetTeamPairingEmailBody(username, teamName, partnerName);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendKnockoutLobbyEmailAsync(string to, string username, string teamName, string competitionName, string phase, string lobbyCode, string opponentTeamName)
        {
            var subject = $"Knockout Match Ready - {phase} Phase";
            var body = GetKnockoutLobbyEmailBody(username, teamName, competitionName, phase, lobbyCode, opponentTeamName);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendPhaseAdvancementEmailAsync(string to, string username, string teamName, string competitionName, string newPhase, bool isEliminated)
        {
            var subject = isEliminated ? $"Competition Update - {competitionName}" : $"Congratulations! Advanced to {newPhase}";
            var body = GetPhaseAdvancementEmailBody(username, teamName, competitionName, newPhase, isEliminated);
            await SendEmailAsync(to, subject, body);
        }

        private string GetWelcomeEmailBody(string username)
        {
            return $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <h2 style='color: #E1C760;'>Welcome to Thunee Competition, {username}!</h2>
                    <p>We're excited to have you join our community of skilled card game players.</p>
                    <p>Get ready to compete, climb the leaderboards, and win amazing prizes!</p>
                    <p>Best regards,<br>The Thunee Team</p>
                </div>";
        }

        private string GetTeamPairingEmailBody(string username, string teamName, string partnerName)
        {
            return $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <h2 style='color: #E1C760;'>Team Pairing Complete!</h2>
                    <p>Hello {username},</p>
                    <p>Great news! Your team <strong>{teamName}</strong> is now complete with your partner <strong>{partnerName}</strong>.</p>
                    <p>You're now ready to compete in tournaments and climb the leaderboards together!</p>
                    <p>Best of luck,<br>The Thunee Team</p>
                </div>";
        }

        private string GetKnockoutLobbyEmailBody(string username, string teamName, string competitionName, string phase, string lobbyCode, string opponentTeamName)
        {
            return $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <h2 style='color: #E1C760;'>Knockout Match Ready - {phase} Phase</h2>
                    <p>Hello {username},</p>
                    <p>Your team <strong>{teamName}</strong> has been matched for the {phase} phase of <strong>{competitionName}</strong>!</p>
                    <p><strong>Opponent:</strong> {opponentTeamName}</p>
                    <p><strong>Lobby Code:</strong> <span style='font-size: 18px; font-weight: bold; color: #E1C760;'>{lobbyCode}</span></p>
                    <p>Join the lobby now and show your skills!</p>
                    <p>Good luck,<br>The Thunee Team</p>
                </div>";
        }

        private string GetPhaseAdvancementEmailBody(string username, string teamName, string competitionName, string newPhase, bool isEliminated)
        {
            if (isEliminated)
            {
                return $@"
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                        <h2 style='color: #E1C760;'>Competition Update</h2>
                        <p>Hello {username},</p>
                        <p>Thank you for participating in <strong>{competitionName}</strong> with team <strong>{teamName}</strong>.</p>
                        <p>Unfortunately, your team did not advance to the {newPhase} phase this time.</p>
                        <p>Don't give up! Keep practicing and join our next competition.</p>
                        <p>Best regards,<br>The Thunee Team</p>
                    </div>";
            }
            else
            {
                return $@"
                    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                        <h2 style='color: #E1C760;'>Congratulations! Advanced to {newPhase}</h2>
                        <p>Hello {username},</p>
                        <p>Excellent news! Your team <strong>{teamName}</strong> has advanced to the <strong>{newPhase}</strong> phase of <strong>{competitionName}</strong>!</p>
                        <p>Keep up the great work and continue your journey to victory!</p>
                        <p>Best of luck,<br>The Thunee Team</p>
                    </div>";
            }
        }
    }
}
