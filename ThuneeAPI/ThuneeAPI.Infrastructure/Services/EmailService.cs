using Microsoft.Extensions.Configuration;
using System;
using System.Net.Mail;
using System.Net;
using System.Threading.Tasks;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Infrastructure.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;

        public EmailService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public async Task SendEmailAsync(string to, string subject, string body)
        {
            try
            {
                // Check if email settings are configured
                var host = _configuration["EmailSettings:Host"];
                var portStr = _configuration["EmailSettings:Port"]?.ToString();
                var useSslStr = _configuration["EmailSettings:UseSSL"]?.ToString();
                var username = _configuration["EmailSettings:Username"] ?? _configuration["EmailSettings:UserName"];
                var password = _configuration["EmailSettings:Password"];
                var from = _configuration["EmailSettings:From"] ?? _configuration["EmailSettings:from"];
                var displayName = _configuration["EmailSettings:DisplayName"] ?? _configuration["EmailSettings:displayName"];

                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(portStr) ||
                    string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password) ||
                    string.IsNullOrEmpty(from))
                {
                    Console.WriteLine($"Email settings not configured properly. Host: {host}, Port: {portStr}, Username: {username}, From: {from}. Skipping email send.");
                    return;
                }

                if (!int.TryParse(portStr, out int port))
                {
                    Console.WriteLine($"Invalid port configuration: {portStr}. Skipping email send.");
                    return;
                }

                if (!bool.TryParse(useSslStr, out bool useSSL))
                {
                    useSSL = true; // Default to true for security
                }

                var smtpClient = new SmtpClient
                {
                    Host = host,
                    Port = port,
                    EnableSsl = useSSL,
                    Credentials = new NetworkCredential(username, password),
                    DeliveryMethod = SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Timeout = 30000 // 30 seconds timeout
                };

                // For Gmail, we need specific settings
                if (host.Contains("gmail.com"))
                {
                    smtpClient.EnableSsl = true;
                    if (port == 465)
                    {
                        // Port 465 requires SSL from the start
                        smtpClient.Port = 465;
                    }
                    else if (port == 587)
                    {
                        // Port 587 uses STARTTLS
                        smtpClient.Port = 587;
                        smtpClient.EnableSsl = true;
                    }
                }

                Console.WriteLine($"Attempting to send email to {to} using SMTP {host}:{port} (SSL: {useSSL})");

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(from, displayName ?? "Thunee Competition"),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };
                mailMessage.To.Add(to);

                await smtpClient.SendMailAsync(mailMessage);
                Console.WriteLine($"Email sent successfully to {to}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending email to {to}: {ex.Message}");
                // In production, you might want to log this properly or throw the exception
                // For now, we'll just log and continue to not break the registration flow
            }
        }

        public async Task SendWelcomeEmailAsync(string to, string username)
        {
            var subject = "Welcome to Thunee Competition!";
            var body = GetWelcomeEmailBody(username);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendTeamPairingEmailAsync(string to, string username, string teamName, string partnerName)
        {
            var subject = "Team Pairing Complete - Ready to Compete!";
            var body = GetTeamPairingEmailBody(username, teamName, partnerName);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendKnockoutLobbyEmailAsync(string to, string username, string teamName, string competitionName, string phase, string lobbyCode, string opponentTeamName)
        {
            var subject = $"Knockout Match Ready - {phase} Phase";
            var body = GetKnockoutLobbyEmailBody(username, teamName, competitionName, phase, lobbyCode, opponentTeamName);
            await SendEmailAsync(to, subject, body);
        }

        public async Task SendPhaseAdvancementEmailAsync(string to, string username, string teamName, string competitionName, string newPhase, bool isEliminated)
        {
            var subject = isEliminated ? $"Competition Update - {competitionName}" : $"Congratulations! Advanced to {newPhase}";
            var body = GetPhaseAdvancementEmailBody(username, teamName, competitionName, newPhase, isEliminated);
            await SendEmailAsync(to, subject, body);
        }

        private static string GetWelcomeEmailBody(string username)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background: linear-gradient(135deg, #E1C760, #D4AF37); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1 style='color: #000; margin: 0; font-size: 28px;'>Welcome to Thunee!</h1>
                        </div>
                        
                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                            <h2 style='color: #E1C760; margin-top: 0;'>Hello {username}!</h2>
                            
                            <p>Welcome to the exciting world of Thunee competitions! We're thrilled to have you join our community of skilled card game players.</p>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                <h3 style='color: #333; margin-top: 0;'>What's Next?</h3>
                                <ul style='color: #666;'>
                                    <li>Find a partner and create or join a competition team</li>
                                    <li>Participate in exciting tournaments and challenges</li>
                                    <li>Climb the leaderboards and earn prizes</li>
                                    <li>Connect with fellow Thunee enthusiasts</li>
                                </ul>
                            </div>
                            
                            <p>Ready to start your Thunee journey? Log in to your account and explore the available competitions!</p>
                            
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='#' style='background: #E1C760; color: #000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>Start Playing</a>
                            </div>
                            
                            <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                If you have any questions, feel free to reach out to our support team. Good luck and have fun!
                            </p>
                        </div>
                        
                        <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                            <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private static string GetTeamPairingEmailBody(string username, string teamName, string partnerName)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background: linear-gradient(135deg, #E1C760, #D4AF37); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1 style='color: #000; margin: 0; font-size: 28px;'>Team Complete!</h1>
                        </div>
                        
                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                            <h2 style='color: #E1C760; margin-top: 0;'>Congratulations {username}!</h2>
                            
                            <p>Great news! Your team <strong>{teamName}</strong> is now complete and ready to compete!</p>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                <h3 style='color: #333; margin-top: 0;'>Team Details</h3>
                                <p style='margin: 10px 0;'><strong>Team Name:</strong> {teamName}</p>
                                <p style='margin: 10px 0;'><strong>Your Partner:</strong> {partnerName}</p>
                                <p style='margin: 10px 0;'><strong>Status:</strong> Ready to compete!</p>
                            </div>
                            
                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>
                                <h3 style='color: #333; margin-top: 0;'>Ready to Play?</h3>
                                <ul style='color: #666;'>
                                    <li>Create a game lobby and invite your opponents</li>
                                    <li>Practice your strategies with your partner</li>
                                    <li>Compete in tournaments and climb the leaderboards</li>
                                    <li>Earn points and win exciting prizes</li>
                                </ul>
                            </div>
                            
                            <p>Your team is now eligible to participate in all available competitions. Good luck and may the best team win!</p>
                            
                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='#' style='background: #E1C760; color: #000; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>Start Competing</a>
                            </div>
                            
                            <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                Best of luck to you and {partnerName}! Show everyone what {teamName} is made of!
                            </p>
                        </div>
                        
                        <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                            <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private string GetKnockoutLobbyEmailBody(string username, string teamName, string competitionName, string phase, string lobbyCode, string opponentTeamName)
        {
            return $@"
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset='utf-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Knockout Match Ready - {phase} Phase</title>
                </head>
                <body style='margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;'>
                    <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff;'>
                        <div style='background: linear-gradient(135deg, #E1C760 0%, #D4AF37 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                            <h1 style='color: #000; margin: 0; font-size: 28px; font-weight: bold;'>🏆 KNOCKOUT MATCH READY!</h1>
                            <p style='color: #333; margin: 10px 0 0 0; font-size: 16px;'>{competitionName} - {phase} Phase</p>
                        </div>

                        <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                            <h2 style='color: #E1C760; margin-top: 0;'>Ready for Battle, {username}!</h2>

                            <p>Your team <strong>{teamName}</strong> has advanced to the <strong>{phase}</strong> phase! Your knockout match lobby is now ready.</p>

                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                <h3 style='color: #333; margin-top: 0;'>🎯 Match Details</h3>
                                <p style='margin: 10px 0;'><strong>Competition:</strong> {competitionName}</p>
                                <p style='margin: 10px 0;'><strong>Phase:</strong> {phase}</p>
                                <p style='margin: 10px 0;'><strong>Your Team:</strong> {teamName}</p>
                                <p style='margin: 10px 0;'><strong>Opponent:</strong> {opponentTeamName}</p>
                            </div>

                            <div style='background: #E1C760; color: #000; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;'>
                                <h3 style='margin-top: 0; font-size: 18px;'>🔑 LOBBY CODE</h3>
                                <div style='font-size: 24px; font-weight: bold; letter-spacing: 2px; margin: 10px 0;'>{lobbyCode}</div>
                                <p style='margin-bottom: 0; font-size: 14px;'>Use this code to join your knockout match</p>
                            </div>

                            <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;'>
                                <h3 style='color: #dc3545; margin-top: 0;'>⚠️ Important - Knockout Rules</h3>
                                <ul style='color: #666; margin: 10px 0;'>
                                    <li><strong>Single Elimination:</strong> This is a knockout match - winner advances, loser is eliminated</li>
                                    <li><strong>Join Promptly:</strong> Both teams must join the lobby to start the match</li>
                                    <li><strong>No Second Chances:</strong> Make sure your team is ready before joining</li>
                                    <li><strong>Good Sportsmanship:</strong> Play fair and respect your opponents</li>
                                </ul>
                            </div>

                            <p>This is your moment to shine! You've made it to the {phase} phase - now it's time to prove your team has what it takes to advance further.</p>

                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='#' style='background: #E1C760; color: #000; padding: 15px 40px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;'>Join Match Lobby</a>
                            </div>

                            <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                Best of luck to team {teamName}! May the best team advance to the next round. Show them what you're made of!
                            </p>
                        </div>

                        <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                            <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private string GetPhaseAdvancementEmailBody(string username, string teamName, string competitionName, string newPhase, bool isEliminated)
        {
            if (isEliminated)
            {
                return $@"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset='utf-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>Competition Update - {competitionName}</title>
                    </head>
                    <body style='margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;'>
                        <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff;'>
                            <div style='background: linear-gradient(135deg, #6c757d 0%, #495057 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                                <h1 style='color: #fff; margin: 0; font-size: 28px; font-weight: bold;'>Competition Update</h1>
                                <p style='color: #f8f9fa; margin: 10px 0 0 0; font-size: 16px;'>{competitionName}</p>
                            </div>

                            <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                                <h2 style='color: #6c757d; margin-top: 0;'>Thank You for Competing, {username}!</h2>

                                <p>We want to thank team <strong>{teamName}</strong> for your participation in {competitionName}. While your tournament journey has come to an end, your effort and sportsmanship have been exemplary.</p>

                                <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #6c757d;'>
                                    <h3 style='color: #333; margin-top: 0;'>📊 Tournament Summary</h3>
                                    <p style='margin: 10px 0;'><strong>Competition:</strong> {competitionName}</p>
                                    <p style='margin: 10px 0;'><strong>Team:</strong> {teamName}</p>
                                    <p style='margin: 10px 0;'><strong>Status:</strong> Eliminated from current phase</p>
                                </div>

                                <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>
                                    <h3 style='color: #28a745; margin-top: 0;'>🎯 What's Next?</h3>
                                    <ul style='color: #666; margin: 10px 0;'>
                                        <li>Keep an eye out for new competitions and tournaments</li>
                                        <li>Practice your skills for the next challenge</li>
                                        <li>Stay connected with the Thunee community</li>
                                        <li>Learn from this experience and come back stronger</li>
                                    </ul>
                                </div>

                                <p>Every great player has faced setbacks - it's how you bounce back that defines your journey. We look forward to seeing team {teamName} compete again soon!</p>

                                <div style='text-align: center; margin: 30px 0;'>
                                    <a href='#' style='background: #28a745; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;'>View Upcoming Competitions</a>
                                </div>

                                <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                    Thank you for being part of the Thunee competition community. Your participation makes our tournaments better for everyone!
                                </p>
                            </div>

                            <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                                <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                            </div>
                        </div>
                    </body>
                    </html>";
            }
            else
            {
                return $@"
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset='utf-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>Congratulations! Advanced to {newPhase}</title>
                    </head>
                    <body style='margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;'>
                        <div style='max-width: 600px; margin: 0 auto; background-color: #ffffff;'>
                            <div style='background: linear-gradient(135deg, #28a745 0%, #20c997 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
                                <h1 style='color: #fff; margin: 0; font-size: 28px; font-weight: bold;'>🎉 CONGRATULATIONS!</h1>
                                <p style='color: #f8f9fa; margin: 10px 0 0 0; font-size: 16px;'>You've Advanced to {newPhase}!</p>
                            </div>

                            <div style='background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #ddd;'>
                                <h2 style='color: #28a745; margin-top: 0;'>Outstanding Performance, {username}!</h2>

                                <p>Fantastic news! Team <strong>{teamName}</strong> has successfully advanced to the <strong>{newPhase}</strong> phase in {competitionName}!</p>

                                <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>
                                    <h3 style='color: #333; margin-top: 0;'>🏆 Advancement Details</h3>
                                    <p style='margin: 10px 0;'><strong>Competition:</strong> {competitionName}</p>
                                    <p style='margin: 10px 0;'><strong>Team:</strong> {teamName}</p>
                                    <p style='margin: 10px 0;'><strong>New Phase:</strong> {newPhase}</p>
                                    <p style='margin: 10px 0;'><strong>Status:</strong> ✅ Advanced Successfully</p>
                                </div>

                                <div style='background: #fff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #E1C760;'>
                                    <h3 style='color: #E1C760; margin-top: 0;'>🎯 What Happens Next?</h3>
                                    <ul style='color: #666; margin: 10px 0;'>
                                        <li>You'll receive match details when your next opponent is determined</li>
                                        <li>Keep practicing and stay sharp for the upcoming challenges</li>
                                        <li>Watch for lobby codes and match notifications</li>
                                        <li>Prepare for even tougher competition ahead</li>
                                    </ul>
                                </div>

                                <p>You're getting closer to the championship! The {newPhase} phase will bring new challenges and stronger opponents. Keep up the excellent teamwork!</p>

                                <div style='text-align: center; margin: 30px 0;'>
                                    <a href='#' style='background: #E1C760; color: #000; padding: 15px 40px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;'>View Tournament Bracket</a>
                                </div>

                                <p style='color: #666; font-size: 14px; margin-top: 30px;'>
                                    Congratulations once again to team {teamName}! You've earned your place in the {newPhase}. Keep pushing forward!
                                </p>
                            </div>

                            <div style='text-align: center; padding: 20px; color: #666; font-size: 12px;'>
                                <p>© 2025 Thunee Competition Platform. All rights reserved.</p>
                            </div>
                        </div>
                    </body>
                    </html>";
            }
        }
    }
}
