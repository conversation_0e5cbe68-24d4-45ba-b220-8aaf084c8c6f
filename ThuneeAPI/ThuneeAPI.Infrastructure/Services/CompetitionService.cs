using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using System.Linq;

namespace ThuneeAPI.Infrastructure.Services;

public class CompetitionService : ICompetitionService
{
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IUserRepository _userRepository;

    public CompetitionService(ICompetitionRepository competitionRepository, ICompetitionTeamRepository competitionTeamRepository, IUserRepository userRepository)
    {
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _userRepository = userRepository;
    }

    public async Task<List<CompetitionDto>> GetCompetitionsAsync()
    {
        var competitions = await _competitionRepository.GetAllAsync();
        return competitions.Select(MapToCompetitionDto).ToList();
    }

    public async Task<CompetitionDto> GetCompetitionByIdAsync(Guid id)
    {
        var competition = await _competitionRepository.GetByIdAsync(id);

        if (competition == null)
            throw new ArgumentException("Competition not found");

        return MapToCompetitionDto(competition);
    }

    public async Task<CompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto)
    {
        var competition = new Competition
        {
            Id = Guid.NewGuid(),
            Name = createDto.Name,
            Description = createDto.Description,
            StartDate = createDto.StartDate,
            EndDate = createDto.EndDate,
            MaxTeams = createDto.MaxTeams,
            EntryFee = createDto.EntryFee,
            PrizeFirst = createDto.PrizeFirst,
            PrizeSecond = createDto.PrizeSecond,
            PrizeThird = createDto.PrizeThird,
            TotalPrizePool = createDto.TotalPrizePool,
            Rules = createDto.Rules,
            IsPublic = createDto.IsPublic,
            AllowSpectators = createDto.AllowSpectators,
            Status = "upcoming",
            CurrentTeams = 0,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var createdCompetition = await _competitionRepository.CreateAsync(competition);
        return MapToCompetitionDto(createdCompetition);
    }

    public async Task<CompetitionDto> UpdateCompetitionAsync(Guid id, UpdateCompetitionDto updateDto)
    {
        var competition = await _competitionRepository.GetByIdAsync(id);

        if (competition == null)
            throw new ArgumentException("Competition not found");

        // Update fields if provided
        if (!string.IsNullOrEmpty(updateDto.Name))
            competition.Name = updateDto.Name;

        if (!string.IsNullOrEmpty(updateDto.Description))
            competition.Description = updateDto.Description;

        if (updateDto.StartDate.HasValue)
            competition.StartDate = updateDto.StartDate.Value;

        if (updateDto.EndDate.HasValue)
            competition.EndDate = updateDto.EndDate.Value;

        if (updateDto.MaxTeams.HasValue)
            competition.MaxTeams = updateDto.MaxTeams.Value;

        if (updateDto.EntryFee.HasValue)
            competition.EntryFee = updateDto.EntryFee.Value;

        if (!string.IsNullOrEmpty(updateDto.PrizeFirst))
            competition.PrizeFirst = updateDto.PrizeFirst;

        if (!string.IsNullOrEmpty(updateDto.PrizeSecond))
            competition.PrizeSecond = updateDto.PrizeSecond;

        if (!string.IsNullOrEmpty(updateDto.PrizeThird))
            competition.PrizeThird = updateDto.PrizeThird;

        if (updateDto.TotalPrizePool.HasValue)
            competition.TotalPrizePool = updateDto.TotalPrizePool.Value;

        if (!string.IsNullOrEmpty(updateDto.Rules))
            competition.Rules = updateDto.Rules;

        if (updateDto.IsPublic.HasValue)
            competition.IsPublic = updateDto.IsPublic.Value;

        if (updateDto.AllowSpectators.HasValue)
            competition.AllowSpectators = updateDto.AllowSpectators.Value;

        competition.UpdatedAt = DateTime.UtcNow;
        await _competitionRepository.UpdateAsync(competition);

        return MapToCompetitionDto(competition);
    }

    public async Task<bool> DeleteCompetitionAsync(Guid id)
    {
        var competition = await _competitionRepository.GetByIdAsync(id);

        if (competition == null)
            return false;

        await _competitionRepository.DeleteAsync(id);
        return true;
    }

    public async Task<CompetitionTeamDto> CreateCompetitionTeamAsync(Guid competitionId, CreateCompetitionTeamDto createDto, Guid playerId)
    {
        var competition = await _competitionRepository.GetByIdAsync(competitionId);

        if (competition == null)
            throw new ArgumentException("Competition not found");

        if (competition.Status != "upcoming" && competition.Status != "active")
            throw new InvalidOperationException("Competition is not accepting new teams");

        if (competition.CurrentTeams >= competition.MaxTeams)
            throw new InvalidOperationException("Competition is full");

        // Check if player is already in this competition
        var existingTeam = await _competitionTeamRepository.GetByPlayerAndCompetitionAsync(competitionId, playerId);
        if (existingTeam != null)
            throw new InvalidOperationException("Player is already registered in this competition");

        var player = await _userRepository.GetByIdAsync(playerId);
        if (player == null)
            throw new ArgumentException("Player not found");

        // Create the competition team entity
        var competitionTeam = new CompetitionTeam
        {
            Id = Guid.NewGuid(),
            CompetitionId = competitionId,
            TeamName = createDto.TeamName,
            Player1Id = playerId,
            Player2Id = null,
            InviteCode = GenerateInviteCode(),
            GamesPlayed = 0,
            Points = 0,
            BonusPoints = 0,
            MaxGames = (int)competition.MaxGamesPerTeam,
            IsActive = true,
            IsComplete = false,
            RegisteredAt = DateTime.UtcNow
        };

        // Save to database using stored procedure
        var createdTeam = await _competitionTeamRepository.CreateAsync(competitionTeam);

        // Map to DTO and return
        return MapToCompetitionTeamDto(createdTeam, player, null);
    }

    public async Task<CompetitionTeamDto> JoinCompetitionTeamAsync(JoinCompetitionTeamDto joinDto, Guid playerId)
    {
        var player2 = await _userRepository.GetByIdAsync(playerId);
        if (player2 == null)
            throw new ArgumentException("Player not found");

        // Find the team by invite code
        var team = await _competitionTeamRepository.GetByInviteCodeAsync(joinDto.InviteCode);
        if (team == null)
            throw new ArgumentException("Invalid invite code");

        if (team.Player2Id != null)
            throw new InvalidOperationException("Team is already complete");

        // Check if player is already in this competition
        var existingTeam = await _competitionTeamRepository.GetByPlayerAndCompetitionAsync(team.CompetitionId, playerId);
        if (existingTeam != null)
            throw new InvalidOperationException("Player is already registered in this competition");

        // Join the team using stored procedure
        var updatedTeam = await _competitionTeamRepository.JoinTeamAsync(joinDto.InviteCode, playerId);

        // Get player information for mapping
        var player1 = await _userRepository.GetByIdAsync(updatedTeam.Player1Id);

        // Map to DTO and return
        return MapToCompetitionTeamDto(updatedTeam, player1!, player2);
    }

    public async Task<CompetitionTeamDto> JoinCompetitionAsync(JoinCompetitionDto joinDto, Guid player1Id)
    {
        var competition = await _competitionRepository.GetByIdAsync(joinDto.CompetitionId);

        if (competition == null)
            throw new ArgumentException("Competition not found");

        if (competition.Status != "upcoming" && competition.Status != "active")
            throw new InvalidOperationException("Competition is not accepting new teams");

        if (competition.CurrentTeams >= competition.MaxTeams)
            throw new InvalidOperationException("Competition is full");

        // TODO: Implement full team logic with CompetitionTeam repository
        var player1 = await _userRepository.GetByIdAsync(player1Id);
        var player2 = await _userRepository.GetByIdAsync(joinDto.Player2Id);

        return new CompetitionTeamDto
        {
            Id = Guid.NewGuid(),
            TeamName = joinDto.TeamName,
            Player1 = new PlayerDto { Id = player1!.Id, Username = player1.Username },
            Player2 = new PlayerDto { Id = player2!.Id, Username = player2.Username },
            InviteCode = GenerateInviteCode(),
            GamesPlayed = 0,
            Points = 0,
            BonusPoints = 0,
            MaxGames = 10,
            IsComplete = true,
            RegisteredAt = DateTime.UtcNow,
            CompletedAt = DateTime.UtcNow
        };
    }

    public async Task<List<CompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId)
    {
        var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);
        var teamDtos = new List<CompetitionTeamDto>();

        foreach (var team in teams)
        {
            var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
            var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

            teamDtos.Add(MapToCompetitionTeamDto(team, player1!, player2));
        }

        return teamDtos;
    }

    public async Task<List<CompetitionTeamDto>> GetAdvancedTeamsByCompetitionAsync(Guid competitionId)
    {
        var teams = await _competitionTeamRepository.GetAdvancedTeamsByCompetitionAsync(competitionId);
        var teamDtos = new List<CompetitionTeamDto>();

        foreach (var team in teams)
        {
            var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
            var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

            teamDtos.Add(MapToCompetitionTeamDto(team, player1!, player2));
        }

        return teamDtos;
    }

    public async Task<CompetitionLeaderboardResponseDto> GetCompetitionLeaderboardAsync(Guid competitionId, int page = 1, int pageSize = 20)
    {
        // TODO: Implement with CompetitionTeam repository
        await Task.CompletedTask; // Remove warning

        return new CompetitionLeaderboardResponseDto
        {
            Teams = new List<CompetitionLeaderboardEntryDto>(),
            Pagination = new PaginationDto
            {
                CurrentPage = page,
                TotalPages = 1,
                TotalItems = 0,
                ItemsPerPage = pageSize
            }
        };
    }

    public async Task<CompetitionTeamDto?> GetTeamByPlayerAsync(Guid competitionId, Guid playerId)
    {
        var team = await _competitionTeamRepository.GetByPlayerAndCompetitionAsync(competitionId, playerId);
        if (team == null)
            return null;

        var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
        var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

        return MapToCompetitionTeamDto(team, player1!, player2);
    }

    public async Task<CompetitionTeamDto?> GetTeamByIdAsync(Guid teamId)
    {
        var team = await _competitionTeamRepository.GetByIdAsync(teamId);
        if (team == null)
            return null;

        var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
        var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

        return MapToCompetitionTeamDto(team, player1!, player2);
    }

    public async Task<CompetitionStatusDto> GetCompetitionStatusAsync(Guid competitionId, Guid playerId)
    {
        var competition = await _competitionRepository.GetByIdAsync(competitionId);

        if (competition == null)
            throw new ArgumentException("Competition not found");

        var team = await GetTeamByPlayerAsync(competitionId, playerId);

        string status;
        bool canJoin = false;
        bool canResume = false;

        if (team == null)
        {
            status = "not_joined";
            canJoin = competition.Status == "active" && competition.CurrentTeams < competition.MaxTeams;
        }
        else if (!team.IsComplete)
        {
            status = "waiting_for_partner";
        }
        else if (team.GamesPlayed < team.MaxGames)
        {
            status = "ready_to_play";
            canResume = true;
        }
        else
        {
            status = "completed";
        }

        return new CompetitionStatusDto
        {
            CompetitionId = competitionId,
            CompetitionName = competition.Name,
            Team = team,
            HasTeam = team != null,
            CanJoin = canJoin,
            CanResume = canResume,
            Status = status
        };
    }

    public async Task<bool> IsUserInCompetitionAsync(Guid userId, Guid competitionId)
    {
        var team = await _competitionTeamRepository.GetByPlayerAndCompetitionAsync(competitionId, userId);
        return team != null;
    }

    public async Task ProcessGameResultAsync(CompetitionGameResultDto gameResult)
    {
        // TODO: Implement with CompetitionTeam repository
        await Task.CompletedTask; // Remove warning
    }

    private static string GenerateInviteCode()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var random = new Random();
        return new string(Enumerable.Repeat(chars, 8)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private static CompetitionDto MapToCompetitionDto(Competition competition)
    {
        return new CompetitionDto
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            Status = competition.Status,
            MaxTeams = competition.MaxTeams,
            CurrentTeams = competition.CurrentTeams,
            EntryFee = competition.EntryFee,
            Phase=competition.Phase,
            Prizes = new PrizesDto
            {
                First = competition.PrizeFirst,
                Second = competition.PrizeSecond,
                Third = competition.PrizeThird
            },
            TotalPrizePool = competition.TotalPrizePool,
            Rules = competition.Rules,
            IsPublic = competition.IsPublic,
            AllowSpectators = competition.AllowSpectators,
            CreatedAt = competition.CreatedAt
        };
    }

    private static CompetitionTeamDto MapToCompetitionTeamDto(CompetitionTeam team, User player1, User? player2)
    {
        return new CompetitionTeamDto
        {
            Id = team.Id,
            TeamName = team.TeamName,
            Player1 = new PlayerDto { Id = player1.Id, Username = player1.Username },
            Player2 = player2 != null ? new PlayerDto { Id = player2.Id, Username = player2.Username } : null,
            InviteCode = team.InviteCode,
            GamesPlayed = team.GamesPlayed,
            Points = team.Points,
            BonusPoints = team.BonusPoints,
            MaxGames = team.MaxGames,
            IsComplete = team.IsComplete,
            RegisteredAt = team.RegisteredAt,
            CompletedAt = team.CompletedAt,
            Phase = team.Phase,
            IsEliminated = team.IsEliminated,
            AdvancedToNextPhase = team.AdvancedToNextPhase,
            PhaseEliminatedAt = team.PhaseEliminatedAt
        };
    }
}
