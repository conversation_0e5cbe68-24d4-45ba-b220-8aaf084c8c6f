using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Services;

public class AdminService : IAdminService
{
    private readonly IUserRepository _userRepository;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly ICompetitionTeamRepository _competitionTeamRepository;
    private readonly IGameRepository _gameRepository;
    private readonly IEmailService _emailService;
    private readonly ILogger<AdminService> _logger;

    public AdminService(
        IUserRepository userRepository,
        ICompetitionRepository competitionRepository,
        ICompetitionTeamRepository competitionTeamRepository,
        IGameRepository gameRepository,
        IEmailService emailService,
        ILogger<AdminService> logger)
    {
        _userRepository = userRepository;
        _competitionRepository = competitionRepository;
        _competitionTeamRepository = competitionTeamRepository;
        _gameRepository = gameRepository;
        _emailService = emailService;
        _logger = logger;
    }

    // User Management
    public async Task<List<AdminUserDto>> GetAllUsersAsync()
    {
        try
        {
            var users = await _userRepository.GetAllAsync(1, 1000); // Get all users
            var adminUsers = users.Select(user => new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            }).ToList();

            return adminUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            throw;
        }
    }

    public async Task<AdminUserDto?> GetUserByIdAsync(Guid userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null) return null;

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<AdminUserDto> UpdateUserAsync(Guid userId, AdminUpdateUserDto updateDto)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            // Update fields if provided
            if (!string.IsNullOrEmpty(updateDto.Username))
                user.Username = updateDto.Username;
            if (!string.IsNullOrEmpty(updateDto.Email))
                user.Email = updateDto.Email;
            if (updateDto.IsVerified.HasValue)
                user.IsVerified = updateDto.IsVerified.Value;
            if (updateDto.IsActive.HasValue)
                user.IsActive = updateDto.IsActive.Value;
            if (updateDto.IsAdmin.HasValue)
                user.IsAdmin = updateDto.IsAdmin.Value;

            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> DeleteUserAsync(Guid userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                return false;

            // For now, we'll mark the user as inactive instead of deleting
            // since there might be foreign key constraints
            user.IsActive = false;
            user.UpdatedAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("Deactivated user: {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ChangeUserPasswordAsync(Guid userId, string newPassword)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing user password: {UserId}", userId);
            throw;
        }
    }

    // Competition Management
    public async Task<List<AdminCompetitionDto>> GetAllCompetitionsAsync()
    {
        try
        {
            var competitions = await _competitionRepository.GetAllAsync();
            var adminCompetitions = competitions.Select(comp => new AdminCompetitionDto
            {
                Id = comp.Id,
                Name = comp.Name,
                Description = comp.Description ?? "",
                StartDate = comp.StartDate,
                EndDate = comp.EndDate,
                Status = comp.Status,
                MaxTeams = comp.MaxTeams,
                CurrentTeams = comp.CurrentTeams,
                EntryFee = comp.EntryFee,
                PrizeFirst = decimal.TryParse(comp.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(comp.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(comp.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = comp.TotalPrizePool ?? 0,
                Rules = comp.Rules ?? "",
                IsPublic = comp.IsPublic,
                AllowSpectators = comp.AllowSpectators,
                MaxGamesPerTeam = comp.MaxGamesPerTeam,
                CreatedAt = comp.CreatedAt,
                UpdatedAt = comp.UpdatedAt
            }).ToList();

            return adminCompetitions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all competitions");
            throw;
        }
    }

    public async Task<AdminCompetitionDto?> GetCompetitionByIdAsync(Guid competitionId)
    {
        try
        {
            var competition = await _competitionRepository.GetByIdAsync(competitionId);
            if (competition == null) return null;

            return new AdminCompetitionDto
            {
                Id = competition.Id,
                Name = competition.Name,
                Description = competition.Description ?? "",
                StartDate = competition.StartDate,
                EndDate = competition.EndDate,
                Status = competition.Status,
                MaxTeams = competition.MaxTeams,
                CurrentTeams = competition.CurrentTeams,
                EntryFee = competition.EntryFee,
                PrizeFirst = decimal.TryParse(competition.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(competition.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(competition.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = competition.TotalPrizePool ?? 0,
                Rules = competition.Rules ?? "",
                IsPublic = competition.IsPublic,
                AllowSpectators = competition.AllowSpectators,
                MaxGamesPerTeam = competition.MaxGamesPerTeam,
                CreatedAt = competition.CreatedAt,
                UpdatedAt = competition.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition by ID: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto)
    {
        try
        {
            // Parse prize values from strings to decimals
            decimal.TryParse(createDto.PrizeFirst, out var prizeFirst);
            decimal.TryParse(createDto.PrizeSecond, out var prizeSecond);
            decimal.TryParse(createDto.PrizeThird, out var prizeThird);

            // Create the competition entity
            var competition = new Competition
            {
                Id = Guid.NewGuid(),
                Name = createDto.Name,
                Description = createDto.Description ?? "",
                StartDate = createDto.StartDate,
                EndDate = createDto.EndDate,
                Status = "Upcoming",
                MaxTeams = createDto.MaxTeams,
                CurrentTeams = 0,
                EntryFee = createDto.EntryFee,
                PrizeFirst = createDto.PrizeFirst, // Keep as string
                PrizeSecond = createDto.PrizeSecond, // Keep as string
                PrizeThird = createDto.PrizeThird, // Keep as string
                TotalPrizePool = prizeFirst + prizeSecond + prizeThird,
                Rules = createDto.Rules ?? "",
                IsPublic = createDto.IsPublic,
                AllowSpectators = createDto.AllowSpectators,
                MaxGamesPerTeam = 10, // Default value since not in DTO
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save to database
            var createdCompetition = await _competitionRepository.CreateAsync(competition);

            // Return as AdminCompetitionDto
            var result = new AdminCompetitionDto
            {
                Id = createdCompetition.Id,
                Name = createdCompetition.Name,
                Description = createdCompetition.Description ?? "",
                StartDate = createdCompetition.StartDate,
                EndDate = createdCompetition.EndDate,
                Status = createdCompetition.Status,
                MaxTeams = createdCompetition.MaxTeams,
                CurrentTeams = createdCompetition.CurrentTeams,
                EntryFee = createdCompetition.EntryFee,
                PrizeFirst = decimal.TryParse(createdCompetition.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(createdCompetition.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(createdCompetition.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = createdCompetition.TotalPrizePool ?? 0,
                Rules = createdCompetition.Rules ?? "",
                IsPublic = createdCompetition.IsPublic,
                AllowSpectators = createdCompetition.AllowSpectators,
                MaxGamesPerTeam = createdCompetition.MaxGamesPerTeam,
                CreatedAt = createdCompetition.CreatedAt,
                UpdatedAt = createdCompetition.UpdatedAt
            };

            _logger.LogInformation("Created competition: {CompetitionName}", createDto.Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating competition: {CompetitionName}", createDto.Name);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> UpdateCompetitionAsync(Guid competitionId, UpdateCompetitionDto updateDto)
    {
        try
        {
            // Get existing competition from database
            var existingCompetition = await _competitionRepository.GetByIdAsync(competitionId);
            if (existingCompetition == null)
            {
                throw new ArgumentException($"Competition with ID {competitionId} not found");
            }

            // Parse prize values from strings to decimals
            decimal.TryParse(existingCompetition.PrizeFirst?.Replace("$", ""), out var currentPrizeFirst);
            decimal.TryParse(existingCompetition.PrizeSecond?.Replace("$", ""), out var currentPrizeSecond);
            decimal.TryParse(existingCompetition.PrizeThird?.Replace("$", ""), out var currentPrizeThird);

            decimal prizeFirst = currentPrizeFirst;
            decimal prizeSecond = currentPrizeSecond;
            decimal prizeThird = currentPrizeThird;

            if (!string.IsNullOrEmpty(updateDto.PrizeFirst))
                decimal.TryParse(updateDto.PrizeFirst, out prizeFirst);
            if (!string.IsNullOrEmpty(updateDto.PrizeSecond))
                decimal.TryParse(updateDto.PrizeSecond, out prizeSecond);
            if (!string.IsNullOrEmpty(updateDto.PrizeThird))
                decimal.TryParse(updateDto.PrizeThird, out prizeThird);

            // Update competition properties
            existingCompetition.Name = updateDto.Name ?? existingCompetition.Name;
            existingCompetition.Description = updateDto.Description ?? existingCompetition.Description;
            existingCompetition.StartDate = updateDto.StartDate ?? existingCompetition.StartDate;
            existingCompetition.EndDate = updateDto.EndDate ?? existingCompetition.EndDate;
            existingCompetition.MaxTeams = updateDto.MaxTeams ?? existingCompetition.MaxTeams;
            existingCompetition.EntryFee = updateDto.EntryFee ?? existingCompetition.EntryFee;
            existingCompetition.PrizeFirst = updateDto.PrizeFirst ?? existingCompetition.PrizeFirst;
            existingCompetition.PrizeSecond = updateDto.PrizeSecond ?? existingCompetition.PrizeSecond;
            existingCompetition.PrizeThird = updateDto.PrizeThird ?? existingCompetition.PrizeThird;
            existingCompetition.TotalPrizePool = prizeFirst + prizeSecond + prizeThird;
            existingCompetition.IsPublic = updateDto.IsPublic ?? existingCompetition.IsPublic;
            existingCompetition.AllowSpectators = updateDto.AllowSpectators ?? existingCompetition.AllowSpectators;
            existingCompetition.UpdatedAt = DateTime.UtcNow;

            // Update in database
            await _competitionRepository.UpdateAsync(existingCompetition);

            // Return updated competition as DTO
            var updatedCompetition = new AdminCompetitionDto
            {
                Id = existingCompetition.Id,
                Name = existingCompetition.Name,
                Description = existingCompetition.Description ?? "",
                StartDate = existingCompetition.StartDate,
                EndDate = existingCompetition.EndDate,
                Status = existingCompetition.Status,
                MaxTeams = existingCompetition.MaxTeams,
                CurrentTeams = existingCompetition.CurrentTeams,
                EntryFee = existingCompetition.EntryFee,
                PrizeFirst = decimal.TryParse(existingCompetition.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(existingCompetition.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(existingCompetition.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = existingCompetition.TotalPrizePool ?? 0,
                Rules = updateDto.Rules ?? "",
                IsPublic = existingCompetition.IsPublic,
                AllowSpectators = existingCompetition.AllowSpectators,
                MaxGamesPerTeam = 10, // Default value since not in entity
                CreatedAt = existingCompetition.CreatedAt,
                UpdatedAt = existingCompetition.UpdatedAt
            };

            _logger.LogInformation("Updated competition: {CompetitionId}", competitionId);
            return updatedCompetition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionAsync(Guid competitionId)
    {
        try
        {
            await _competitionRepository.DeleteAsync(competitionId);
            _logger.LogInformation("Deleted competition: {CompetitionId}", competitionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    // Competition Teams Management
    public async Task<List<AdminCompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId)
    {
        try
        {
            // Get teams from the database
            var teams = await _competitionTeamRepository.GetByCompetitionIdAsync(competitionId);

            var adminTeams = new List<AdminCompetitionTeamDto>();

            foreach (var team in teams)
            {
                // Get player details
                var player1 = await _userRepository.GetByIdAsync(team.Player1Id);
                var player2 = team.Player2Id.HasValue ? await _userRepository.GetByIdAsync(team.Player2Id.Value) : null;

                var adminTeam = new AdminCompetitionTeamDto
                {
                    Id = team.Id,
                    CompetitionId = team.CompetitionId,
                    TeamName = team.TeamName,
                    Player1Id = team.Player1Id,
                    Player1Username = player1?.Username ?? "",
                    Player1Email = player1?.Email ?? "",
                    Player2Id = team.Player2Id ?? Guid.Empty,
                    Player2Username = player2?.Username ?? "",
                    Player2Email = player2?.Email ?? "",
                    InviteCode = team.InviteCode,
                    GamesPlayed = team.GamesPlayed,
                    Points = team.Points,
                    BonusPoints = team.BonusPoints,
                    MaxGames = 10, // Default value since not in entity
                    IsActive = team.IsActive,
                    IsComplete = team.IsComplete,
                    RegisteredAt = team.RegisteredAt,
                    CompletedAt = team.CompletedAt
                };

                adminTeams.Add(adminTeam);
            }

            return adminTeams;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition teams: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionTeamAsync(Guid teamId)
    {
        try
        {
            // Delete the team from the database
            await _competitionTeamRepository.DeleteAsync(teamId);
            _logger.LogInformation("Deleted competition team: {TeamId}", teamId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition team: {TeamId}", teamId);
            throw;
        }
    }

    // Competition Games Management
    public async Task<List<AdminGameDto>> GetCompetitionGamesAsync(Guid competitionId)
    {
        try
        {
            // Get games from the database for this competition
            var games = await _gameRepository.GetByCompetitionIdAsync(competitionId);
            var competition = await _competitionRepository.GetByIdAsync(competitionId);

            var adminGames = new List<AdminGameDto>();

            foreach (var game in games)
            {
                var adminGame = new AdminGameDto
                {
                    Id = game.Id,
                    LobbyCode = game.LobbyCode,
                    CompetitionId = game.CompetitionId,
                    CompetitionName = competition?.Name ?? "Unknown Competition",
                    Team1Name = game.Team1Name ?? "Team 1",
                    Team2Name = game.Team2Name ?? "Team 2",
                    Status = game.Status,
                    Team1Score = game.Team1Score,
                    Team2Score = game.Team2Score,
                    Team1BallsWon = game.Team1BallsWon,
                    Team2BallsWon = game.Team2BallsWon,
                    WinnerTeam = game.WinnerTeam,
                    StartedAt = game.StartedAt,
                    CompletedAt = game.CompletedAt,
                    CreatedAt = game.CreatedAt
                };

                adminGames.Add(adminGame);
            }

            return adminGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition games: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<List<AdminGameDto>> GetAllGamesAsync()
    {
        try
        {
            // Get all games from the database
            var games = await _gameRepository.GetAllAsync();
            var competitions = await _competitionRepository.GetAllAsync();
            var competitionDict = competitions.ToDictionary(c => c.Id, c => c.Name);

            var adminGames = new List<AdminGameDto>();

            foreach (var game in games)
            {
                var competitionName = game.CompetitionId.HasValue && competitionDict.ContainsKey(game.CompetitionId.Value)
                    ? competitionDict[game.CompetitionId.Value]
                    : "No Competition";

                var adminGame = new AdminGameDto
                {
                    Id = game.Id,
                    LobbyCode = game.LobbyCode,
                    CompetitionId = game.CompetitionId,
                    CompetitionName = competitionName,
                    Team1Name = game.Team1Name ?? "Team 1",
                    Team2Name = game.Team2Name ?? "Team 2",
                    Status = game.Status,
                    Team1Score = game.Team1Score,
                    Team2Score = game.Team2Score,
                    Team1BallsWon = game.Team1BallsWon,
                    Team2BallsWon = game.Team2BallsWon,
                    WinnerTeam = game.WinnerTeam,
                    StartedAt = game.StartedAt,
                    CompletedAt = game.CompletedAt,
                    CreatedAt = game.CreatedAt
                };

                adminGames.Add(adminGame);
            }

            return adminGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all games");
            throw;
        }
    }

    // Email Management
    public async Task<bool> SendCompetitionEmailAsync(Guid competitionId, CompetitionEmailDto emailDto)
    {
        try
        {
            // Get all players in the competition
            var teams = await GetCompetitionTeamsAsync(competitionId);
            var emails = new List<string>();

            foreach (var team in teams)
            {
                emails.Add(team.Player1Email);
                emails.Add(team.Player2Email);
            }

            // Remove duplicates
            emails = emails.Distinct().ToList();

            // Send emails to all players
            foreach (var email in emails)
            {
                await _emailService.SendEmailAsync(email, emailDto.Subject, emailDto.Message);
            }

            _logger.LogInformation("Sent competition email to {EmailCount} players in competition {CompetitionId}", 
                emails.Count, competitionId);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending competition email: {CompetitionId}", competitionId);
            throw;
        }
    }
}
