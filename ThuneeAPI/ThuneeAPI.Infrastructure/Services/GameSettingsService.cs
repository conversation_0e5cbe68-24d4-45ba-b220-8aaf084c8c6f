using System.Text.Json;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Services;

/// <summary>
/// Service implementation for game settings operations
/// </summary>
public class GameSettingsService : IGameSettingsService
{
    private readonly IGameSettingsRepository _gameSettingsRepository;

    public GameSettingsService(IGameSettingsRepository gameSettingsRepository)
    {
        _gameSettingsRepository = gameSettingsRepository;
    }

    /// <summary>
    /// Get the current game settings
    /// </summary>
    /// <returns>Game settings DTO</returns>
    public async Task<GameSettingsDto> GetGameSettingsAsync()
    {
        var settings = await _gameSettingsRepository.GetGameSettingsAsync();
        
        if (settings == null)
        {
            // Create default settings if none exist
            settings = await CreateDefaultSettingsAsync();
        }

        return MapToDto(settings);
    }

    /// <summary>
    /// Update game settings
    /// </summary>
    /// <param name="updateDto">Settings to update</param>
    /// <param name="updatedBy">User ID who is updating (optional)</param>
    /// <returns>Updated game settings DTO</returns>
    public async Task<GameSettingsDto> UpdateGameSettingsAsync(UpdateGameSettingsDto updateDto, Guid? updatedBy = null)
    {
        var existingSettings = await _gameSettingsRepository.GetGameSettingsAsync();
        
        if (existingSettings == null)
        {
            existingSettings = await CreateDefaultSettingsAsync();
        }

        // Update only the provided fields
        if (updateDto.PlayTimeframeOptions != null)
        {
            existingSettings.PlayTimeframeOptions = JsonSerializer.Serialize(updateDto.PlayTimeframeOptions);
        }
        
        if (updateDto.DefaultPlayTimeframe.HasValue)
        {
            existingSettings.DefaultPlayTimeframe = updateDto.DefaultPlayTimeframe.Value;
        }
        
        if (updateDto.TrumperThuneeCallingDuration.HasValue)
        {
            existingSettings.TrumperThuneeCallingDuration = updateDto.TrumperThuneeCallingDuration.Value;
        }
        
        if (updateDto.FirstRemainingThuneeCallingDuration.HasValue)
        {
            existingSettings.FirstRemainingThuneeCallingDuration = updateDto.FirstRemainingThuneeCallingDuration.Value;
        }
        
        if (updateDto.LastRemainingThuneeCallingDuration.HasValue)
        {
            existingSettings.LastRemainingThuneeCallingDuration = updateDto.LastRemainingThuneeCallingDuration.Value;
        }
        
        if (updateDto.VotingTimeLimit.HasValue)
        {
            existingSettings.VotingTimeLimit = updateDto.VotingTimeLimit.Value;
        }
        
        if (updateDto.TrumpDisplayDuration.HasValue)
        {
            existingSettings.TrumpDisplayDuration = updateDto.TrumpDisplayDuration.Value;
        }
        
        if (updateDto.CardDealingSpeed.HasValue)
        {
            existingSettings.CardDealingSpeed = updateDto.CardDealingSpeed.Value;
        }
        
        if (updateDto.TimerUpdateInterval.HasValue)
        {
            existingSettings.TimerUpdateInterval = updateDto.TimerUpdateInterval.Value;
        }

        if (!string.IsNullOrEmpty(updateDto.CardFaceBaseUrl))
        {
            existingSettings.CardFaceBaseUrl = updateDto.CardFaceBaseUrl;
        }

        if (!string.IsNullOrEmpty(updateDto.CardBackImageUrl))
        {
            existingSettings.CardBackImageUrl = updateDto.CardBackImageUrl;
        }

        if (updateDto.CustomCardFaceMappings != null)
        {
            existingSettings.CustomCardFaceMappings = JsonSerializer.Serialize(updateDto.CustomCardFaceMappings);
        }

        existingSettings.UpdatedAt = DateTime.UtcNow;
        existingSettings.UpdatedBy = updatedBy;

        var updatedSettings = await _gameSettingsRepository.UpdateGameSettingsAsync(existingSettings);
        return MapToDto(updatedSettings);
    }

    /// <summary>
    /// Reset game settings to defaults
    /// </summary>
    /// <param name="updatedBy">User ID who is resetting (optional)</param>
    /// <returns>Reset game settings DTO</returns>
    public async Task<GameSettingsDto> ResetGameSettingsAsync(Guid? updatedBy = null)
    {
        var resetSettings = await _gameSettingsRepository.ResetGameSettingsAsync();
        resetSettings.UpdatedBy = updatedBy;
        resetSettings.UpdatedAt = DateTime.UtcNow;
        
        var updatedSettings = await _gameSettingsRepository.UpdateGameSettingsAsync(resetSettings);
        return MapToDto(updatedSettings);
    }

    /// <summary>
    /// Create default settings if none exist
    /// </summary>
    /// <returns>Default game settings</returns>
    private async Task<GameSettings> CreateDefaultSettingsAsync()
    {
        var defaultSettings = new GameSettings
        {
            Id = 1,
            PlayTimeframeOptions = "[3,4,5,6,60]",
            DefaultPlayTimeframe = 3,
            TrumperThuneeCallingDuration = 5,
            FirstRemainingThuneeCallingDuration = 3,
            LastRemainingThuneeCallingDuration = 2,
            VotingTimeLimit = 15,
            TrumpDisplayDuration = 10,
            CardDealingSpeed = 300,
            TimerUpdateInterval = 100,
            CardFaceBaseUrl = "/CardFace",
            CardBackImageUrl = "/CardFace/card-back.svg",
            CustomCardFaceMappings = null,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        return await _gameSettingsRepository.CreateGameSettingsAsync(defaultSettings);
    }

    /// <summary>
    /// Map GameSettings entity to DTO
    /// </summary>
    /// <param name="settings">GameSettings entity</param>
    /// <returns>GameSettingsDto</returns>
    private static GameSettingsDto MapToDto(GameSettings settings)
    {
        var playTimeframeOptions = new List<int>();
        
        try
        {
            playTimeframeOptions = JsonSerializer.Deserialize<List<int>>(settings.PlayTimeframeOptions) ?? new List<int> { 3, 4, 5, 6, 60 };
        }
        catch (JsonException)
        {
            // Fallback to default if JSON is invalid
            playTimeframeOptions = new List<int> { 3, 4, 5, 6, 60 };
        }

        // Parse custom card face mappings
        Dictionary<string, string>? customCardFaceMappings = null;
        if (!string.IsNullOrEmpty(settings.CustomCardFaceMappings))
        {
            try
            {
                customCardFaceMappings = JsonSerializer.Deserialize<Dictionary<string, string>>(settings.CustomCardFaceMappings);
            }
            catch (JsonException)
            {
                // Ignore invalid JSON, use null
                customCardFaceMappings = null;
            }
        }

        return new GameSettingsDto
        {
            PlayTimeframeOptions = playTimeframeOptions,
            DefaultPlayTimeframe = settings.DefaultPlayTimeframe,
            TrumperThuneeCallingDuration = settings.TrumperThuneeCallingDuration,
            FirstRemainingThuneeCallingDuration = settings.FirstRemainingThuneeCallingDuration,
            LastRemainingThuneeCallingDuration = settings.LastRemainingThuneeCallingDuration,
            VotingTimeLimit = settings.VotingTimeLimit,
            TrumpDisplayDuration = settings.TrumpDisplayDuration,
            CardDealingSpeed = settings.CardDealingSpeed,
            TimerUpdateInterval = settings.TimerUpdateInterval,
            CardFaceBaseUrl = settings.CardFaceBaseUrl,
            CardBackImageUrl = settings.CardBackImageUrl,
            CustomCardFaceMappings = customCardFaceMappings
        };
    }
}
