using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Infrastructure.Services;

public class LeaderboardService : ILeaderboardService
{
    private readonly IUserRepository _userRepository;

    public LeaderboardService(IUserRepository userRepository)
    {
        _userRepository = userRepository;
    }

    public async Task<LeaderboardResponseDto> GetGlobalLeaderboardAsync(string timeFrame = "all", string sortBy = "score", int page = 1, int pageSize = 20)
    {
        // For now, we'll create mock data since we don't have user statistics yet
        // In a real implementation, this would query actual user statistics
        
        var users = await _userRepository.GetAllAsync(page, pageSize);
        var totalUsers = users.Count(); // Simplified for now

        var leaderboardEntries = users.Select((user, index) => new LeaderboardEntryDto
        {
            Id = Guid.NewGuid(),
            PlayerId = user.Id,
            PlayerName = user.Username,
            Score = GenerateMockScore(index, timeFrame),
            Rank = ((page - 1) * pageSize) + index + 1,
            GamesPlayed = GenerateMockGamesPlayed(timeFrame),
            GamesWon = GenerateMockGamesWon(timeFrame),
            WinRate = GenerateMockWinRate()
        }).ToList();

        // Sort by the requested criteria
        leaderboardEntries = sortBy.ToLower() switch
        {
            "score" => leaderboardEntries.OrderByDescending(e => e.Score).ToList(),
            "winrate" => leaderboardEntries.OrderByDescending(e => e.WinRate).ToList(),
            "gamesplayed" => leaderboardEntries.OrderByDescending(e => e.GamesPlayed).ToList(),
            _ => leaderboardEntries.OrderByDescending(e => e.Score).ToList()
        };

        // Update ranks after sorting
        for (int i = 0; i < leaderboardEntries.Count; i++)
        {
            leaderboardEntries[i].Rank = ((page - 1) * pageSize) + i + 1;
        }

        return new LeaderboardResponseDto
        {
            Data = leaderboardEntries,
            Pagination = new PaginationDto
            {
                CurrentPage = page,
                TotalPages = (int)Math.Ceiling((double)totalUsers / pageSize),
                TotalItems = totalUsers,
                ItemsPerPage = pageSize
            },
            Filters = new FilterDto
            {
                TimeFrame = timeFrame,
                SortBy = sortBy
            }
        };
    }

    public async Task<LeaderboardResponseDto> GetWeeklyLeaderboardAsync(int page = 1, int pageSize = 20)
    {
        return await GetGlobalLeaderboardAsync("weekly", "score", page, pageSize);
    }

    public async Task<LeaderboardResponseDto> GetMonthlyLeaderboardAsync(int page = 1, int pageSize = 20)
    {
        return await GetGlobalLeaderboardAsync("monthly", "score", page, pageSize);
    }

    public async Task UpdatePlayerStatsAsync(Guid playerId, int score, bool won)
    {
        // This would update player statistics in a real implementation
        // For now, we'll just log the update
        await Task.CompletedTask;
    }

    private static int GenerateMockScore(int index, string timeFrame)
    {
        var random = new Random(index + timeFrame.GetHashCode());
        return timeFrame.ToLower() switch
        {
            "weekly" => random.Next(100, 500),
            "monthly" => random.Next(500, 2000),
            _ => random.Next(1000, 10000)
        };
    }

    private static int GenerateMockGamesPlayed(string timeFrame)
    {
        var random = new Random();
        return timeFrame.ToLower() switch
        {
            "weekly" => random.Next(5, 20),
            "monthly" => random.Next(20, 100),
            _ => random.Next(50, 500)
        };
    }

    private static int GenerateMockGamesWon(string timeFrame)
    {
        var gamesPlayed = GenerateMockGamesPlayed(timeFrame);
        var random = new Random();
        return random.Next(gamesPlayed / 3, (int)(gamesPlayed * 0.8));
    }

    private static double GenerateMockWinRate()
    {
        var random = new Random();
        return Math.Round(random.NextDouble() * 40 + 30, 1); // 30-70% win rate
    }
}
