using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class User
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    [Required]
    [MaxLength(50)]
    public string Username { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string PasswordHash { get; set; } = string.Empty;
    
    public bool IsVerified { get; set; } = false;
    public bool IsActive { get; set; } = true;
    public bool IsAdmin { get; set; } = false;
    
    public DateTime? LastLoginAt { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual ICollection<Game> GamesAsTeam1Player1 { get; set; } = new List<Game>();
    public virtual ICollection<Game> GamesAsTeam1Player2 { get; set; } = new List<Game>();
    public virtual ICollection<Game> GamesAsTeam2Player1 { get; set; } = new List<Game>();
    public virtual ICollection<Game> GamesAsTeam2Player2 { get; set; } = new List<Game>();
    public virtual ICollection<CompetitionTeam> CompetitionTeams { get; set; } = new List<CompetitionTeam>();
    public virtual ICollection<GameHand> GameHandsWon { get; set; } = new List<GameHand>();
    public virtual ICollection<PlayedCard> PlayedCards { get; set; } = new List<PlayedCard>();
}
