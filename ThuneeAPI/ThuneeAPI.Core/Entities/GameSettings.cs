namespace ThuneeAPI.Core.Entities;

/// <summary>
/// Entity representing game settings configuration
/// </summary>
public class GameSettings
{
    /// <summary>
    /// Primary key - always 1 (singleton pattern)
    /// </summary>
    public int Id { get; set; } = 1;

    /// <summary>
    /// JSON string of available play timeframe options in seconds
    /// </summary>
    public string PlayTimeframeOptions { get; set; } = string.Empty;

    /// <summary>
    /// Default play timeframe in seconds
    /// </summary>
    public int DefaultPlayTimeframe { get; set; }

    /// <summary>
    /// Time for trumper to call <PERSON>hun<PERSON> in seconds
    /// </summary>
    public int TrumperThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for first remaining player to call <PERSON><PERSON><PERSON> in seconds
    /// </summary>
    public int FirstRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for last remaining player to call Thunee in seconds
    /// </summary>
    public int LastRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time limit for voting in seconds
    /// </summary>
    public int VotingTimeLimit { get; set; }

    /// <summary>
    /// Duration to display trump card in seconds
    /// </summary>
    public int TrumpDisplayDuration { get; set; }

    /// <summary>
    /// Card dealing animation speed in milliseconds
    /// </summary>
    public int CardDealingSpeed { get; set; }

    /// <summary>
    /// Timer update interval in milliseconds
    /// </summary>
    public int TimerUpdateInterval { get; set; }

    /// <summary>
    /// Base URL for card face images (without trailing slash)
    /// </summary>
    public string CardFaceBaseUrl { get; set; } = "/CardFace";

    /// <summary>
    /// URL for the card back image
    /// </summary>
    public string CardBackImageUrl { get; set; } = "/CardFace/card-back.svg";

    /// <summary>
    /// JSON string containing custom card face mappings (optional)
    /// Format: {"6H": "/custom/6H.svg", "JS": "/custom/JS.svg", ...}
    /// </summary>
    public string? CustomCardFaceMappings { get; set; }

    /// <summary>
    /// When the settings were created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the settings were last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// User ID who last updated the settings (nullable for system updates)
    /// </summary>
    public Guid? UpdatedBy { get; set; }
}
