using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class Game
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    [Required]
    [MaxLength(6)]
    public string LobbyCode { get; set; } = string.Empty;
    
    public Guid? CompetitionId { get; set; }
    
    // Team 1 Players
    public Guid? Team1Player1Id { get; set; }
    public Guid? Team1Player2Id { get; set; }

    // Team 2 Players
    public Guid? Team2Player1Id { get; set; }
    public Guid? Team2Player2Id { get; set; }
    
    [MaxLength(50)]
    public string Team1Name { get; set; } = "Team 1";
    
    [MaxLength(50)]
    public string Team2Name { get; set; } = "Team 2";
    
    [Required]
    [MaxLength(20)]
    public string Status { get; set; } = "waiting"; // waiting, in_progress, completed, abandoned
    
    public Guid? DealerId { get; set; }
    
    [MaxLength(10)]
    public string? TrumpSuit { get; set; } // hearts, diamonds, clubs, spades
    
    public int CurrentBall { get; set; } = 1;
    public int CurrentHand { get; set; } = 1;
    
    public int Team1Score { get; set; } = 0;
    public int Team2Score { get; set; } = 0;
    
    public int Team1BallsWon { get; set; } = 0;
    public int Team2BallsWon { get; set; } = 0;
    
    public int? WinnerTeam { get; set; } // 1 or 2
    
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Competition? Competition { get; set; }
    public virtual User? Team1Player1 { get; set; }
    public virtual User? Team1Player2 { get; set; }
    public virtual User? Team2Player1 { get; set; }
    public virtual User? Team2Player2 { get; set; }
    public virtual User? Dealer { get; set; }
    public virtual ICollection<GameBall> GameBalls { get; set; } = new List<GameBall>();
    public virtual ICollection<GameHand> GameHands { get; set; } = new List<GameHand>();
}
