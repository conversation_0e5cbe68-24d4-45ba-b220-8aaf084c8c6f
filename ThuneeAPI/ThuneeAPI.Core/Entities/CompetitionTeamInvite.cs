using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class CompetitionTeamInvite
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid CompetitionId { get; set; }
    public Guid TeamId { get; set; }
    public Guid InviterId { get; set; } // Player who created the team
    public Guid? InviteeId { get; set; } // Player being invited (null if not yet accepted)
    
    [MaxLength(10)]
    public string InviteCode { get; set; } = string.Empty;
    
    [MaxLength(20)]
    public string Status { get; set; } = "pending"; // pending, accepted, expired, cancelled
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddDays(7); // Expires in 7 days
    public DateTime? AcceptedAt { get; set; }
    
    // Navigation properties
    public virtual Competition Competition { get; set; } = null!;
    public virtual CompetitionTeam Team { get; set; } = null!;
    public virtual User Inviter { get; set; } = null!;
    public virtual User? Invitee { get; set; }
}
