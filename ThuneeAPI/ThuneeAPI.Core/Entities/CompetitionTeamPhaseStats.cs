using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class CompetitionTeamPhaseStats
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid CompetitionTeamId { get; set; }
    
    [MaxLength(40)]
    public string Phase { get; set; } = string.Empty; // Leaderboard, Top32, Top16, Top8, Top4, Final
    
    public int Points { get; set; } = 0;
    public int BonusPoints { get; set; } = 0;
    public int GamesPlayed { get; set; } = 0;
    public int BallsWon { get; set; } = 0;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual CompetitionTeam CompetitionTeam { get; set; } = null!;
}
