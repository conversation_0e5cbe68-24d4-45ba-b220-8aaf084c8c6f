using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class Competition
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(500)]
    public string? Description { get; set; }
    
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string Status { get; set; } = "upcoming"; // upcoming, active, completed, cancelled
    
    public int MaxTeams { get; set; } = 32;
    public int CurrentTeams { get; set; } = 0;
    
    public decimal EntryFee { get; set; } = 0.00m;
    
    [MaxLength(100)]
    public string? PrizeFirst { get; set; }
    
    [MaxLength(100)]
    public string? PrizeSecond { get; set; }
    
    [MaxLength(100)]
    public string? PrizeThird { get; set; }
    
    public decimal? TotalPrizePool { get; set; }
    
    [MaxLength(1000)]
    public string? Rules { get; set; }
    
    public bool IsPublic { get; set; } = true;
    public bool AllowSpectators { get; set; } = true;

    // Competition game settings
    public long MaxGamesPerTeam { get; set; } = 10; // Maximum games each team can play

    // Phase Management
    [MaxLength(40)]
    public string Phase { get; set; } = "Leaderboard"; // Leaderboard, Top32, Top16, Top8, Top4, Final

    public DateTime? PhaseEndDate { get; set; } // End date for current phase
    public long? MaxGamesPerPhase { get; set; } // Max games per team in current phase

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual ICollection<CompetitionTeam> Teams { get; set; } = new List<CompetitionTeam>();
    public virtual ICollection<Game> Games { get; set; } = new List<Game>();
}
