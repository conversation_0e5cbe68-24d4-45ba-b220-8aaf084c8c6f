using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class CompetitionPhaseLobby
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid CompetitionId { get; set; }
    
    [MaxLength(40)]
    public string Phase { get; set; } = string.Empty; // Top16, Top8, Top4, Final
    
    [MaxLength(12)]
    public string LobbyCode { get; set; } = string.Empty;
    
    public Guid CreatedByAdminId { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual Competition Competition { get; set; } = null!;
    public virtual User CreatedByAdmin { get; set; } = null!;
    public virtual ICollection<CompetitionPhaseLobbyTeam> LobbyTeams { get; set; } = new List<CompetitionPhaseLobbyTeam>();
}

public class CompetitionPhaseLobbyTeam
{
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public Guid LobbyId { get; set; }
    public Guid CompetitionTeamId { get; set; }
    
    public bool IsWinner { get; set; } = false;
    public DateTime? EliminatedAt { get; set; }
    
    // Navigation properties
    public virtual CompetitionPhaseLobby Lobby { get; set; } = null!;
    public virtual CompetitionTeam CompetitionTeam { get; set; } = null!;
}
