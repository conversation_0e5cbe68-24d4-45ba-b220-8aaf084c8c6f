using System.ComponentModel.DataAnnotations;

namespace ThuneeAPI.Core.Entities;

public class GameHand
{
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid GameId { get; set; }
    public Guid? GameBallId { get; set; } // Foreign key to GameBall

    public int BallNumber { get; set; }
    public int HandNumber { get; set; }

    public Guid WinnerPlayerId { get; set; }
    public int Points { get; set; } = 0;

    [MaxLength(10)]
    public string? TrumpSuit { get; set; } // hearts, diamonds, clubs, spades

    public DateTime CompletedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Game Game { get; set; } = null!;
    public virtual GameBall? GameBall { get; set; }
    public virtual User WinnerPlayer { get; set; } = null!;
    // PlayedCards navigation removed - no longer tracking individual cards
}

public class GameBall
{
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid GameId { get; set; }
    public Guid? CompetitionId { get; set; } // Link to competition

    public int BallNumber { get; set; }

    public int Team1Score { get; set; } = 0;
    public int Team2Score { get; set; } = 0;

    public int? WinnerTeam { get; set; } // 1 or 2

    public int Team1BallsWon { get; set; } = 0; // Total balls won by team 1 after this ball
    public int Team2BallsWon { get; set; } = 0; // Total balls won by team 2 after this ball

    // Team 1 Players
    public Guid? Team1Player1Id { get; set; }
    public Guid? Team1Player2Id { get; set; }

    // Team 2 Players
    public Guid? Team2Player1Id { get; set; }
    public Guid? Team2Player2Id { get; set; }

    [MaxLength(50)]
    public string Team1Name { get; set; } = "Team 1";

    [MaxLength(50)]
    public string Team2Name { get; set; } = "Team 2";

    [MaxLength(10)]
    public string? TrumpSuit { get; set; } // hearts, diamonds, clubs, spades

    public bool HasThuneeDouble { get; set; } = false;
    public bool HasKhanka { get; set; } = false;

    [MaxLength(20)]
    public string? SpecialCallType { get; set; } // "thunee", "khanka", "double", etc.

    [MaxLength(20)]
    public string? SpecialCallResult { get; set; } // "success", "failed", etc.

    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Game Game { get; set; } = null!;
    public virtual Competition? Competition { get; set; }
    public virtual User? Team1Player1 { get; set; }
    public virtual User? Team1Player2 { get; set; }
    public virtual User? Team2Player1 { get; set; }
    public virtual User? Team2Player2 { get; set; }
    public virtual ICollection<GameHand> Hands { get; set; } = new List<GameHand>();
}

// PlayedCard entity commented out - no longer tracking individual cards
// This entity will be removed once the PlayedCards table is dropped from the database
/*
public class PlayedCard
{
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid GameHandId { get; set; }
    public Guid PlayerId { get; set; }

    [Required]
    [MaxLength(10)]
    public string CardSuit { get; set; } = string.Empty; // hearts, diamonds, clubs, spades

    [Required]
    [MaxLength(2)]
    public string CardValue { get; set; } = string.Empty; // 9, 10, J, Q, K, A

    public int PlayOrder { get; set; } // 1-4

    public DateTime PlayedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual GameHand GameHand { get; set; } = null!;
    public virtual User Player { get; set; } = null!;
}
*/

public class CompetitionTeam
{
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid CompetitionId { get; set; }
    public Guid Player1Id { get; set; }
    public Guid? Player2Id { get; set; } // Nullable until partner joins

    [MaxLength(50)]
    public string TeamName { get; set; } = string.Empty;

    [MaxLength(10)]
    public string InviteCode { get; set; } = string.Empty; // Code for partner invitation

    public int GamesPlayed { get; set; } = 0;
    public int Points { get; set; } = 0; // Total points earned
    public int BonusPoints { get; set; } = 0; // Bonus points for 6+ ball difference wins
    public int MaxGames { get; set; } = 10; // Maximum games allowed (configurable)

    // Phase Management
    [MaxLength(40)]
    public string Phase { get; set; } = "Leaderboard"; // Current phase team is in
    public bool IsEliminated { get; set; } = false; // Whether team has been eliminated
    public bool AdvancedToNextPhase { get; set; } = false; // Whether team advanced to next phase
    public DateTime? PhaseEliminatedAt { get; set; } // When team was eliminated

    public bool IsActive { get; set; } = true;
    public bool IsComplete { get; set; } = false; // True when partner has joined

    public DateTime RegisteredAt { get; set; } = DateTime.UtcNow;
    public DateTime? CompletedAt { get; set; } // When partner joined

    // Navigation properties
    public virtual Competition Competition { get; set; } = null!;
    public virtual User Player1 { get; set; } = null!;
    public virtual User? Player2 { get; set; }
}
