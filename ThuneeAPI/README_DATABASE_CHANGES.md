# Competition Phase System - Database Changes

## Overview

This document outlines all database changes required to implement the Competition Phase System with knockout rounds and email notifications.

## 📋 Summary of Changes

### Modified Tables
- **Competitions** - Added phase management columns
- **CompetitionTeams** - Added phase tracking columns

### New Tables
- **CompetitionPhaseLobbies** - Knockout match lobbies
- **CompetitionPhaseLobbyTeams** - Teams in knockout lobbies  
- **CompetitionTeamPhaseStats** - Per-phase statistics

### New Indexes
- Performance indexes for phase queries
- Ranking indexes for leaderboards

### New Stored Procedures
- 12 stored procedures for phase management

## 🗃️ Detailed Schema Changes

### 1. Competitions Table - New Columns

| Column Name | Data Type | Max Length | Nullable | Default | Description |
|-------------|-----------|------------|----------|---------|-------------|
| Phase | nvarchar | 40 | No | 'Leaderboard' | Current competition phase |
| PhaseEndDate | datetime2 | 8 | Yes | NULL | End date for current phase |
| MaxGamesPerPhase | int | 4 | Yes | NULL | Max games per team in phase |

### 2. CompetitionTeams Table - New Columns

| Column Name | Data Type | Max Length | Nullable | Default | Description |
|-------------|-----------|------------|----------|---------|-------------|
| Phase | nvarchar | 40 | No | 'Leaderboard' | Current phase team is in |
| IsEliminated | bit | 1 | No | 0 | Whether team is eliminated |
| AdvancedToNextPhase | bit | 1 | No | 0 | Whether team advanced |
| PhaseEliminatedAt | datetime2 | 8 | Yes | NULL | When team was eliminated |

### 3. CompetitionPhaseLobbies Table - New Table

| Column Name | Data Type | Max Length | Nullable | Default | Description |
|-------------|-----------|------------|----------|---------|-------------|
| Id | uniqueidentifier | 16 | No | NEWID() | Primary Key |
| CompetitionId | uniqueidentifier | 16 | No | - | FK to Competitions(Id) |
| Phase | nvarchar | 40 | No | - | Phase (Top16, Top8, Top4, Final) |
| LobbyCode | nvarchar | 12 | No | - | Unique lobby code |
| CreatedByAdminId | uniqueidentifier | 16 | No | - | FK to Users(Id) |
| CreatedAt | datetime2 | 8 | No | GETUTCDATE() | Creation timestamp |

### 4. CompetitionPhaseLobbyTeams Table - New Table

| Column Name | Data Type | Max Length | Nullable | Default | Description |
|-------------|-----------|------------|----------|---------|-------------|
| Id | uniqueidentifier | 16 | No | NEWID() | Primary Key |
| LobbyId | uniqueidentifier | 16 | No | - | FK to CompetitionPhaseLobbies(Id) |
| CompetitionTeamId | uniqueidentifier | 16 | No | - | FK to CompetitionTeams(Id) |
| IsWinner | bit | 1 | No | 0 | Whether team won the match |
| EliminatedAt | datetime2 | 8 | Yes | NULL | When team was eliminated |

### 5. CompetitionTeamPhaseStats Table - New Table

| Column Name | Data Type | Max Length | Nullable | Default | Description |
|-------------|-----------|------------|----------|---------|-------------|
| Id | uniqueidentifier | 16 | No | NEWID() | Primary Key |
| CompetitionTeamId | uniqueidentifier | 16 | No | - | FK to CompetitionTeams(Id) |
| Phase | nvarchar | 40 | No | - | Phase name |
| Points | int | 4 | No | 0 | Points earned in phase |
| BonusPoints | int | 4 | No | 0 | Bonus points in phase |
| GamesPlayed | int | 4 | No | 0 | Games played in phase |
| BallsWon | int | 4 | No | 0 | Balls won in phase |
| CreatedAt | datetime2 | 8 | No | GETUTCDATE() | Creation timestamp |
| UpdatedAt | datetime2 | 8 | No | GETUTCDATE() | Last update timestamp |

## 🔗 Foreign Key Relationships

### New Foreign Keys

| Constraint Name | Parent Table | Parent Column | Child Table | Child Column |
|-----------------|--------------|---------------|-------------|--------------|
| FK_CompetitionPhaseLobbies_Competition | Competitions | Id | CompetitionPhaseLobbies | CompetitionId |
| FK_CompetitionPhaseLobbies_Admin | Users | Id | CompetitionPhaseLobbies | CreatedByAdminId |
| FK_CompetitionPhaseLobbyTeams_Lobby | CompetitionPhaseLobbies | Id | CompetitionPhaseLobbyTeams | LobbyId |
| FK_CompetitionPhaseLobbyTeams_Team | CompetitionTeams | Id | CompetitionPhaseLobbyTeams | CompetitionTeamId |
| FK_CompetitionTeamPhaseStats_Team | CompetitionTeams | Id | CompetitionTeamPhaseStats | CompetitionTeamId |

## 📊 Indexes for Performance

### New Indexes

| Index Name | Table | Columns | Purpose |
|------------|-------|---------|---------|
| IX_Competitions_Phase | Competitions | Phase | Phase filtering |
| IX_CompetitionTeams_Phase | CompetitionTeams | Phase | Phase filtering |
| IX_CompetitionTeams_IsEliminated | CompetitionTeams | IsEliminated | Elimination filtering |
| IX_CompetitionTeams_Competition_Phase | CompetitionTeams | CompetitionId, Phase | Competition phase queries |
| IX_CompetitionTeams_Points_Ranking | CompetitionTeams | CompetitionId, Phase, Points DESC, BonusPoints DESC | Leaderboard ranking |
| IX_CompetitionPhaseLobbies_Competition_Phase | CompetitionPhaseLobbies | CompetitionId, Phase | Lobby queries |
| IX_CompetitionPhaseLobbies_LobbyCode | CompetitionPhaseLobbies | LobbyCode | Lobby code lookup |
| IX_CompetitionTeamPhaseStats_Team_Phase | CompetitionTeamPhaseStats | CompetitionTeamId, Phase | Phase stats queries |

## 🔒 Unique Constraints

| Constraint Name | Table | Columns | Purpose |
|-----------------|-------|---------|---------|
| UQ_CompetitionPhaseLobbies_LobbyCode | CompetitionPhaseLobbies | LobbyCode | Ensure unique lobby codes |
| UQ_CompetitionTeamPhaseStats_Team_Phase | CompetitionTeamPhaseStats | CompetitionTeamId, Phase | One stats record per team per phase |

## 🏗️ Stored Procedures

### Phase Management
1. **SP_AdvanceCompetitionPhase** - Advance competition to next phase
2. **SP_EliminateTeams** - Eliminate teams from competition
3. **SP_AdvanceTeamsToNextPhase** - Advance teams to next phase
4. **SP_ProcessPhaseEnd** - Automatically process phase end

### Lobby Management
5. **SP_CreatePhaseLobby** - Create knockout lobby
6. **SP_SetLobbyWinner** - Set winner of knockout match
7. **SP_GetPhaseLobbies** - Get lobbies for phase
8. **SP_GetLobbyByCode** - Get lobby by code

### Team & Ranking
9. **SP_GetPhaseTeamsWithRankings** - Get ranked teams for phase
10. **SP_GetEligibleTeamsForNextPhase** - Get teams eligible for advancement

### Statistics
11. **SP_CreateOrUpdatePhaseStats** - Create/update phase statistics
12. **SP_GetTeamPhaseStats** - Get team's phase statistics

## 📥 Deployment Instructions

### Step 1: Run Schema Updates
```sql
-- Run the main deployment script
EXEC SQLCMD -i "DEPLOY_CompetitionPhaseSystem.sql"
```

### Step 2: Create Stored Procedures
```sql
-- Run the stored procedures script
EXEC SQLCMD -i "CompetitionPhaseSystem_StoredProcedures.sql"
```

### Step 3: Verify Deployment
```sql
-- Check that all tables exist
SELECT name FROM sys.tables WHERE name LIKE 'Competition%' ORDER BY name;

-- Check that all stored procedures exist
SELECT name FROM sys.procedures WHERE name LIKE 'SP_%Competition%' ORDER BY name;

-- Verify data updates
SELECT Phase, COUNT(*) FROM Competitions GROUP BY Phase;
SELECT Phase, COUNT(*) FROM CompetitionTeams GROUP BY Phase;
```

## 🔄 Data Migration

### Existing Data Updates
- All existing competitions set to 'Leaderboard' phase
- All existing teams set to 'Leaderboard' phase
- Default phase end dates set (30 days from deployment)
- Default max games per phase set to 10

### Phase Values
- **Leaderboard** - Initial phase for all teams
- **Top32** - First knockout qualification
- **Top16** - Round of 16 knockout
- **Top8** - Quarter-finals
- **Top4** - Semi-finals
- **Final** - Championship match
- **Completed** - Tournament finished

## 🧪 Testing Queries

### Test Phase System
```sql
-- Get competition with phase info
SELECT Id, Name, Phase, PhaseEndDate FROM Competitions;

-- Get teams by phase
SELECT TeamName, Phase, Points + BonusPoints as TotalPoints 
FROM CompetitionTeams 
WHERE CompetitionId = 'YOUR_COMPETITION_ID'
ORDER BY TotalPoints DESC;

-- Test stored procedure
EXEC SP_GetPhaseTeamsWithRankings 'YOUR_COMPETITION_ID', 'Leaderboard';
```

## 🚨 Rollback Plan

If rollback is needed:
```sql
-- Drop new tables
DROP TABLE CompetitionTeamPhaseStats;
DROP TABLE CompetitionPhaseLobbyTeams;
DROP TABLE CompetitionPhaseLobbies;

-- Remove new columns
ALTER TABLE CompetitionTeams DROP COLUMN PhaseEliminatedAt;
ALTER TABLE CompetitionTeams DROP COLUMN AdvancedToNextPhase;
ALTER TABLE CompetitionTeams DROP COLUMN IsEliminated;
ALTER TABLE CompetitionTeams DROP COLUMN Phase;

ALTER TABLE Competitions DROP COLUMN MaxGamesPerPhase;
ALTER TABLE Competitions DROP COLUMN PhaseEndDate;
ALTER TABLE Competitions DROP COLUMN Phase;

-- Drop stored procedures
DROP PROCEDURE SP_AdvanceCompetitionPhase;
-- ... (drop all SP_* procedures)
```

## ✅ Post-Deployment Checklist

- [ ] All tables created successfully
- [ ] All indexes created
- [ ] All foreign keys established
- [ ] All stored procedures created
- [ ] Existing data updated
- [ ] Test queries run successfully
- [ ] Application configuration updated
- [ ] Email service configured
- [ ] Phase management tested
- [ ] Knockout lobby creation tested
