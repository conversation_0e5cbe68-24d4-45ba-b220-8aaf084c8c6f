using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

public interface IAuthService
{
    Task<AuthResponseDto> RegisterAsync(RegisterUserDto registerDto);
    Task<AuthResponseDto> LoginAsync(LoginUserDto loginDto);
    Task<UserDto> GetCurrentUserAsync(Guid userId);
    Task<UserDto?> GetUserByIdAsync(Guid userId);
    Task<bool> ValidateTokenAsync(string token);
    Task LogoutAsync(Guid userId);
    string GenerateJwtToken(UserDto user);
    string GenerateRefreshToken();
}
