using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

public interface ICompetitionService
{
    Task<List<CompetitionDto>> GetCompetitionsAsync();
    Task<CompetitionDto> GetCompetitionByIdAsync(Guid id);
    Task<CompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto);
    Task<CompetitionDto> UpdateCompetitionAsync(Guid id, UpdateCompetitionDto updateDto);
    Task<bool> DeleteCompetitionAsync(Guid id);

    // Team management
    Task<CompetitionTeamDto> CreateCompetitionTeamAsync(Guid competitionId, CreateCompetitionTeamDto createDto, Guid playerId);
    Task<CompetitionTeamDto> JoinCompetitionTeamAsync(JoinCompetitionTeamDto joinDto, Guid playerId);
    Task<CompetitionTeamDto> JoinCompetitionAsync(JoinCompetitionDto joinDto, Guid player1Id);
    Task<CompetitionTeamDto?> GetTeamByPlayerAsync(Guid competitionId, Guid playerId);
    Task<CompetitionTeamDto?> GetTeamByIdAsync(Guid teamId);
    Task<List<CompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId);

    // Competition status and leaderboard
    Task<CompetitionStatusDto> GetCompetitionStatusAsync(Guid competitionId, Guid playerId);
    Task<CompetitionLeaderboardResponseDto> GetCompetitionLeaderboardAsync(Guid competitionId, int page = 1, int pageSize = 20);
    Task<bool> IsUserInCompetitionAsync(Guid userId, Guid competitionId);

    // Game result processing
    Task ProcessGameResultAsync(CompetitionGameResultDto gameResult);
}

public interface ILeaderboardService
{
    Task<LeaderboardResponseDto> GetGlobalLeaderboardAsync(string timeFrame = "all", string sortBy = "score", int page = 1, int pageSize = 20);
    Task<LeaderboardResponseDto> GetWeeklyLeaderboardAsync(int page = 1, int pageSize = 20);
    Task<LeaderboardResponseDto> GetMonthlyLeaderboardAsync(int page = 1, int pageSize = 20);
    Task UpdatePlayerStatsAsync(Guid playerId, int score, bool won);
}
