using System.Threading.Tasks;

namespace ThuneeAPI.Application.Interfaces
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string body);
        Task SendWelcomeEmailAsync(string to, string username);
        Task SendTeamPairingEmailAsync(string to, string username, string teamName, string partnerName);
        Task SendKnockoutLobbyEmailAsync(string to, string username, string teamName, string competitionName, string phase, string lobbyCode, string opponentTeamName);
        Task SendPhaseAdvancementEmailAsync(string to, string username, string teamName, string competitionName, string newPhase, bool isEliminated);
    }
}
