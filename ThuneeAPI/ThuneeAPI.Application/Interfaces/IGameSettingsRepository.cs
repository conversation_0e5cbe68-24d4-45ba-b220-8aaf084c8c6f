using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for game settings operations
/// </summary>
public interface IGameSettingsRepository
{
    /// <summary>
    /// Get the current game settings
    /// </summary>
    /// <returns>Game settings or null if not found</returns>
    Task<GameSettings?> GetGameSettingsAsync();

    /// <summary>
    /// Update game settings
    /// </summary>
    /// <param name="settings">Updated settings</param>
    /// <returns>Updated game settings</returns>
    Task<GameSettings> UpdateGameSettingsAsync(GameSettings settings);

    /// <summary>
    /// Create initial game settings
    /// </summary>
    /// <param name="settings">Initial settings</param>
    /// <returns>Created game settings</returns>
    Task<GameSettings> CreateGameSettingsAsync(GameSettings settings);

    /// <summary>
    /// Reset game settings to defaults
    /// </summary>
    /// <returns>Reset game settings</returns>
    Task<GameSettings> ResetGameSettingsAsync();
}
