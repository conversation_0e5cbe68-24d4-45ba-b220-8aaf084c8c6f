using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for Game entity operations
/// </summary>
public interface IGameRepository
{
    /// <summary>
    /// Creates a new game
    /// </summary>
    /// <param name="game">Game entity to create</param>
    /// <returns>Created game with generated ID</returns>
    Task<Game> CreateAsync(Game game);

    /// <summary>
    /// Gets a game by its ID
    /// </summary>
    /// <param name="id">Game ID</param>
    /// <returns>Game entity or null if not found</returns>
    Task<Game?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets a game by its lobby code
    /// </summary>
    /// <param name="lobbyCode">Lobby code to search for</param>
    /// <returns>Game entity or null if not found</returns>
    Task<Game?> GetByLobbyCodeAsync(string lobbyCode);

    /// <summary>
    /// Updates a game entity
    /// </summary>
    /// <param name="game">Game entity to update</param>
    /// <returns>Task representing the operation</returns>
    Task UpdateAsync(Game game);

    /// <summary>
    /// Joins a player to a game
    /// </summary>
    /// <param name="lobbyCode">Lobby code of the game</param>
    /// <param name="playerId">Player ID to join</param>
    /// <returns>Updated game entity</returns>
    Task<Game?> JoinGameAsync(string lobbyCode, Guid playerId);

    /// <summary>
    /// Records a hand result for a game
    /// </summary>
    /// <param name="gameId">Game ID</param>
    /// <param name="ballNumber">Ball number</param>
    /// <param name="handNumber">Hand number</param>
    /// <param name="winnerPlayerId">Winner player ID</param>
    /// <param name="points">Points scored</param>
    /// <returns>Hand ID</returns>
    Task<Guid> RecordHandResultAsync(Guid gameId, int ballNumber, int handNumber, Guid winnerPlayerId, int points);

    /// <summary>
    /// Records game completion with automatic team statistics updates
    /// </summary>
    /// <param name="gameId">Game ID</param>
    /// <param name="winnerTeam">Winner team (1 or 2)</param>
    /// <param name="team1FinalScore">Team 1 final score</param>
    /// <param name="team2FinalScore">Team 2 final score</param>
    /// <returns>Result message</returns>
    Task<string> RecordGameCompletionAsync(Guid gameId, int winnerTeam, int team1FinalScore, int team2FinalScore);

    /// <summary>
    /// Records a ball result for a game
    /// </summary>
    /// <param name="gameId">Game ID</param>
    /// <param name="competitionId">Competition ID</param>
    /// <param name="ballNumber">Ball number</param>
    /// <param name="winnerTeam">Winner team (1 or 2)</param>
    /// <param name="team1Score">Team 1 score for this ball</param>
    /// <param name="team2Score">Team 2 score for this ball</param>
    /// <param name="team1BallsWon">Total balls won by team 1</param>
    /// <param name="team2BallsWon">Total balls won by team 2</param>
    /// <param name="team1Player1Id">Team 1 Player 1 ID</param>
    /// <param name="team1Player2Id">Team 1 Player 2 ID</param>
    /// <param name="team2Player1Id">Team 2 Player 1 ID</param>
    /// <param name="team2Player2Id">Team 2 Player 2 ID</param>
    /// <param name="team1Name">Team 1 Name</param>
    /// <param name="team2Name">Team 2 Name</param>
    /// <param name="trumpSuit">Trump suit for this ball</param>
    /// <param name="hasThuneeDouble">Whether Thunee Double was called</param>
    /// <param name="hasKhanka">Whether Khanka was called</param>
    /// <param name="specialCallType">Type of special call</param>
    /// <param name="specialCallResult">Result of special call</param>
    /// <returns>Ball ID</returns>
    Task<Guid> RecordBallResultAsync(Guid gameId, Guid? competitionId, int ballNumber, int winnerTeam, int team1Score, int team2Score,
        int team1BallsWon, int team2BallsWon, Guid? team1Player1Id, Guid? team1Player2Id, Guid? team2Player1Id, Guid? team2Player2Id,
        string team1Name, string team2Name, string? trumpSuit, bool hasThuneeDouble, bool hasKhanka,
        string? specialCallType, string? specialCallResult);

    /// <summary>
    /// Checks if a lobby code exists
    /// </summary>
    /// <param name="lobbyCode">Lobby code to check</param>
    /// <returns>True if lobby code exists, false otherwise</returns>
    Task<bool> LobbyCodeExistsAsync(string lobbyCode);

    /// <summary>
    /// Gets games for a specific competition
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Collection of games</returns>
    Task<IEnumerable<Game>> GetByCompetitionIdAsync(Guid competitionId);

    /// <summary>
    /// Gets games for a specific player
    /// </summary>
    /// <param name="playerId">Player ID</param>
    /// <returns>Collection of games</returns>
    Task<IEnumerable<Game>> GetByPlayerIdAsync(Guid playerId);

    /// <summary>
    /// Gets active games (waiting or in-progress)
    /// </summary>
    /// <returns>Collection of active games</returns>
    Task<IEnumerable<Game>> GetActiveGamesAsync();

    /// <summary>
    /// Gets all games
    /// </summary>
    /// <returns>Collection of all games</returns>
    Task<IEnumerable<Game>> GetAllAsync();

    /// <summary>
    /// Deletes a game
    /// </summary>
    /// <param name="id">Game ID</param>
    /// <returns>Task representing the operation</returns>
    Task DeleteAsync(Guid id);
}
