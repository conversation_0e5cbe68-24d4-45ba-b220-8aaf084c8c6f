using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

public interface IAdminService
{
    // User Management
    Task<List<AdminUserDto>> GetAllUsersAsync();
    Task<AdminUserDto?> GetUserByIdAsync(Guid userId);
    Task<AdminUserDto> UpdateUserAsync(Guid userId, AdminUpdateUserDto updateDto);
    Task<bool> DeleteUserAsync(Guid userId);
    Task<bool> ChangeUserPasswordAsync(Guid userId, string newPassword);

    // Competition Management
    Task<List<AdminCompetitionDto>> GetAllCompetitionsAsync();
    Task<AdminCompetitionDto?> GetCompetitionByIdAsync(Guid competitionId);
    Task<AdminCompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto);
    Task<AdminCompetitionDto> UpdateCompetitionAsync(Guid competitionId, UpdateCompetitionDto updateDto);
    Task<bool> DeleteCompetitionAsync(Guid competitionId);

    // Competition Teams Management
    Task<List<AdminCompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId);
    Task<bool> DeleteCompetitionTeamAsync(Guid teamId);

    // Competition Games Management
    Task<List<AdminGameDto>> GetCompetitionGamesAsync(Guid competitionId);
    Task<List<AdminGameDto>> GetAllGamesAsync();

    // Email Management
    Task<bool> SendCompetitionEmailAsync(Guid competitionId, CompetitionEmailDto emailDto);
}
