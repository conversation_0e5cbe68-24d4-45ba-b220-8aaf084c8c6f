using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

public interface IGameService
{
    Task<GameDto> CreateGameAsync(CreateGameDto createGameDto, Guid hostPlayerId);
    Task<GameDto> GetGameByLobbyCodeAsync(string lobbyCode);
    Task<GameDto> JoinGameAsync(JoinGameDto joinGameDto, Guid playerId);
    Task<GameDto> StartGameAsync(string lobbyCode, Guid hostPlayerId);
    Task<GameDto> RecordHandResultAsync(RecordHandResultDto handResultDto);
    Task<GameDto> RecordGameResultAsync(GameResultDto gameResultDto);
    Task<List<GameDto>> GetUserGamesAsync(Guid userId, int page = 1, int pageSize = 20);
    Task<List<GameHandDto>> GetGameHandsAsync(string lobbyCode);
    Task<bool> IsPlayerInGameAsync(Guid playerId, string lobbyCode);
    Task<string> GenerateLobbyCodeAsync();
    Task<List<GameDto>> GetActiveGamesAsync();
    Task LeaveGameAsync(string lobbyCode, Guid playerId);
    Task SetPlayerReadyAsync(string lobbyCode, Guid playerId, bool ready);
}
