using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Repository interface for User entity operations
/// </summary>
public interface IUserRepository
{
    /// <summary>
    /// Creates a new user
    /// </summary>
    /// <param name="user">User entity to create</param>
    /// <returns>Created user with generated ID</returns>
    Task<User> CreateAsync(User user);

    /// <summary>
    /// Gets a user by their ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <returns>User entity or null if not found</returns>
    Task<User?> GetByIdAsync(Guid id);

    /// <summary>
    /// Gets a user by their username
    /// </summary>
    /// <param name="username">Username to search for</param>
    /// <returns>User entity or null if not found</returns>
    Task<User?> GetByUsernameAsync(string username);

    /// <summary>
    /// Gets a user by their email
    /// </summary>
    /// <param name="email">Email to search for</param>
    /// <returns>User entity or null if not found</returns>
    Task<User?> GetByEmailAsync(string email);

    /// <summary>
    /// Gets a user by username or email
    /// </summary>
    /// <param name="usernameOrEmail">Username or email to search for</param>
    /// <returns>User entity or null if not found</returns>
    Task<User?> GetByUsernameOrEmailAsync(string usernameOrEmail);

    /// <summary>
    /// Updates a user's last login timestamp
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Task representing the operation</returns>
    Task UpdateLastLoginAsync(Guid userId);

    /// <summary>
    /// Updates a user entity
    /// </summary>
    /// <param name="user">User entity to update</param>
    /// <returns>Task representing the operation</returns>
    Task UpdateAsync(User user);

    /// <summary>
    /// Checks if a username already exists
    /// </summary>
    /// <param name="username">Username to check</param>
    /// <returns>True if username exists, false otherwise</returns>
    Task<bool> UsernameExistsAsync(string username);

    /// <summary>
    /// Checks if an email already exists
    /// </summary>
    /// <param name="email">Email to check</param>
    /// <returns>True if email exists, false otherwise</returns>
    Task<bool> EmailExistsAsync(string email);

    /// <summary>
    /// Gets all users with pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page</param>
    /// <returns>Collection of users</returns>
    Task<IEnumerable<User>> GetAllAsync(int pageNumber = 1, int pageSize = 20);
}
