using ThuneeAPI.Application.DTOs;

namespace ThuneeAPI.Application.Interfaces;

/// <summary>
/// Service interface for game settings operations
/// </summary>
public interface IGameSettingsService
{
    /// <summary>
    /// Get the current game settings
    /// </summary>
    /// <returns>Game settings DTO</returns>
    Task<GameSettingsDto> GetGameSettingsAsync();

    /// <summary>
    /// Update game settings
    /// </summary>
    /// <param name="updateDto">Settings to update</param>
    /// <param name="updatedBy">User ID who is updating (optional)</param>
    /// <returns>Updated game settings DTO</returns>
    Task<GameSettingsDto> UpdateGameSettingsAsync(UpdateGameSettingsDto updateDto, Guid? updatedBy = null);

    /// <summary>
    /// Reset game settings to defaults
    /// </summary>
    /// <param name="updatedBy">User ID who is resetting (optional)</param>
    /// <returns>Reset game settings DTO</returns>
    Task<GameSettingsDto> ResetGameSettingsAsync(Guid? updatedBy = null);
}
