namespace ThuneeAPI.Application.DTOs;

public class GameDto
{
    public Guid Id { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
    public string Team1Name { get; set; } = string.Empty;
    public string Team2Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? TrumpSuit { get; set; }
    public int CurrentBall { get; set; }
    public int CurrentHand { get; set; }
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public int? WinnerTeam { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public List<PlayerDto> Team1Players { get; set; } = new();
    public List<PlayerDto> Team2Players { get; set; } = new();
}

public class PlayerDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
}

public class CreateGameDto
{
    public string? LobbyCode { get; set; } // Optional - if not provided, will be auto-generated
    public string Team1Name { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
}

public class JoinGameDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public string PlayerName { get; set; } = string.Empty;
}

// CardPlayDto and JordhiCallDto removed - no longer tracking individual cards or calls

public class SetPlayerReadyDto
{
    public bool Ready { get; set; }
}

public class GameHandDto
{
    public Guid Id { get; set; }
    public int BallNumber { get; set; }
    public int HandNumber { get; set; }
    public Guid WinnerPlayerId { get; set; }
    public string WinnerPlayerName { get; set; } = string.Empty;
    public int Points { get; set; }
    public string? TrumpSuit { get; set; }
    public DateTime CompletedAt { get; set; }
    // PlayedCards removed - no longer tracking individual cards
}

public class RecordHandResultDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public int BallNumber { get; set; }
    public int HandNumber { get; set; }
    public Guid WinnerPlayerId { get; set; }
    public int Points { get; set; }
    // PlayedCards removed - no longer tracking individual cards
}

public class GameResultDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public int WinnerTeam { get; set; }
    public int Team1FinalScore { get; set; }
    public int Team2FinalScore { get; set; }
}

public class RecordBallResultDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
    public int BallNumber { get; set; }
    public int WinnerTeam { get; set; } // 1 or 2
    public int Team1Score { get; set; } // Points scored by team 1 in this ball
    public int Team2Score { get; set; } // Points scored by team 2 in this ball
    public int Team1BallsWon { get; set; } // Total balls won by team 1 after this ball
    public int Team2BallsWon { get; set; } // Total balls won by team 2 after this ball

    // Team 1 Players
    public Guid? Team1Player1Id { get; set; }
    public Guid? Team1Player2Id { get; set; }

    // Team 2 Players
    public Guid? Team2Player1Id { get; set; }
    public Guid? Team2Player2Id { get; set; }

    public string Team1Name { get; set; } = "Team 1";
    public string Team2Name { get; set; } = "Team 2";

    public string? TrumpSuit { get; set; }
    public bool HasThuneeDouble { get; set; } = false;
    public bool HasKhanka { get; set; } = false;
    public string? SpecialCallType { get; set; } // "thunee", "khanka", "double", etc.
    public string? SpecialCallResult { get; set; } // "success", "failed", etc.
    public List<BallHandDto> Hands { get; set; } = new();
}

public class BallHandDto
{
    public int HandNumber { get; set; } // 1-6
    public Guid WinnerPlayerId { get; set; }
    public int WinnerTeam { get; set; } // 1 or 2
    public int Points { get; set; }
    // PlayedCards removed - no longer tracking individual cards
}

/// <summary>
/// DTO for game settings
/// </summary>
public class GameSettingsDto
{
    /// <summary>
    /// Available play timeframe options in seconds
    /// </summary>
    public List<int> PlayTimeframeOptions { get; set; } = new();

    /// <summary>
    /// Default play timeframe in seconds
    /// </summary>
    public int DefaultPlayTimeframe { get; set; }

    /// <summary>
    /// Time for trumper to call Thunee in seconds
    /// </summary>
    public int TrumperThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for first remaining player to call Thunee in seconds
    /// </summary>
    public int FirstRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for last remaining player to call Thunee in seconds
    /// </summary>
    public int LastRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time limit for voting in seconds
    /// </summary>
    public int VotingTimeLimit { get; set; }

    /// <summary>
    /// Duration to display trump card in seconds
    /// </summary>
    public int TrumpDisplayDuration { get; set; }

    /// <summary>
    /// Card dealing animation speed in milliseconds
    /// </summary>
    public int CardDealingSpeed { get; set; }

    /// <summary>
    /// Timer update interval in milliseconds
    /// </summary>
    public int TimerUpdateInterval { get; set; }

    /// <summary>
    /// Base URL for card face images (without trailing slash)
    /// </summary>
    public string CardFaceBaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// URL for the card back image
    /// </summary>
    public string CardBackImageUrl { get; set; } = string.Empty;

    /// <summary>
    /// Custom card face mappings (optional)
    /// </summary>
    public Dictionary<string, string>? CustomCardFaceMappings { get; set; }
}

/// <summary>
/// DTO for updating game settings
/// </summary>
public class UpdateGameSettingsDto
{
    /// <summary>
    /// Available play timeframe options in seconds
    /// </summary>
    public List<int>? PlayTimeframeOptions { get; set; }

    /// <summary>
    /// Default play timeframe in seconds
    /// </summary>
    public int? DefaultPlayTimeframe { get; set; }

    /// <summary>
    /// Time for trumper to call Thunee in seconds
    /// </summary>
    public int? TrumperThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for first remaining player to call Thunee in seconds
    /// </summary>
    public int? FirstRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time for last remaining player to call Thunee in seconds
    /// </summary>
    public int? LastRemainingThuneeCallingDuration { get; set; }

    /// <summary>
    /// Time limit for voting in seconds
    /// </summary>
    public int? VotingTimeLimit { get; set; }

    /// <summary>
    /// Duration to display trump card in seconds
    /// </summary>
    public int? TrumpDisplayDuration { get; set; }

    /// <summary>
    /// Card dealing animation speed in milliseconds
    /// </summary>
    public int? CardDealingSpeed { get; set; }

    /// <summary>
    /// Timer update interval in milliseconds
    /// </summary>
    public int? TimerUpdateInterval { get; set; }

    /// <summary>
    /// Base URL for card face images (without trailing slash)
    /// </summary>
    public string? CardFaceBaseUrl { get; set; }

    /// <summary>
    /// URL for the card back image
    /// </summary>
    public string? CardBackImageUrl { get; set; }

    /// <summary>
    /// Custom card face mappings (optional)
    /// </summary>
    public Dictionary<string, string>? CustomCardFaceMappings { get; set; }
}
