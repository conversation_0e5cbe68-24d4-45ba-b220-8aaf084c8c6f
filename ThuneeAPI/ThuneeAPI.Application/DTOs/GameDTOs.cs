namespace ThuneeAPI.Application.DTOs;

public class GameDto
{
    public Guid Id { get; set; }
    public string LobbyCode { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
    public string Team1Name { get; set; } = string.Empty;
    public string Team2Name { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? TrumpSuit { get; set; }
    public int CurrentBall { get; set; }
    public int CurrentHand { get; set; }
    public int Team1Score { get; set; }
    public int Team2Score { get; set; }
    public int Team1BallsWon { get; set; }
    public int Team2BallsWon { get; set; }
    public int? WinnerTeam { get; set; }
    public DateTime? StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public List<PlayerDto> Team1Players { get; set; } = new();
    public List<PlayerDto> Team2Players { get; set; } = new();
}

public class PlayerDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
}

public class CreateGameDto
{
    public string? LobbyCode { get; set; } // Optional - if not provided, will be auto-generated
    public string Team1Name { get; set; } = string.Empty;
    public Guid? CompetitionId { get; set; }
}

public class JoinGameDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public string PlayerName { get; set; } = string.Empty;
}

public class CardPlayDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public int HandNumber { get; set; }
    public int BallNumber { get; set; }
    public string PlayerId { get; set; } = string.Empty;
    public string Suit { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public int PlayOrder { get; set; }
    public DateTime Timestamp { get; set; }
}

public class JordhiCallDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public string PlayerId { get; set; } = string.Empty;
    public string PlayerName { get; set; } = string.Empty;
    public int PlayerTeam { get; set; }
    public int Value { get; set; }
    public int HandNumber { get; set; }
    public int BallNumber { get; set; }
    public bool IsValid { get; set; }
    public string? JordhiSuit { get; set; }
    public DateTime Timestamp { get; set; }
}

public class SetPlayerReadyDto
{
    public bool Ready { get; set; }
}

public class GameHandDto
{
    public Guid Id { get; set; }
    public int BallNumber { get; set; }
    public int HandNumber { get; set; }
    public Guid WinnerPlayerId { get; set; }
    public string WinnerPlayerName { get; set; } = string.Empty;
    public int Points { get; set; }
    public string? TrumpSuit { get; set; }
    public DateTime CompletedAt { get; set; }
    public List<PlayedCardDto> PlayedCards { get; set; } = new();
}

public class PlayedCardDto
{
    public Guid PlayerId { get; set; }
    public string PlayerName { get; set; } = string.Empty;
    public string CardSuit { get; set; } = string.Empty;
    public string CardValue { get; set; } = string.Empty;
    public int PlayOrder { get; set; }
    public DateTime PlayedAt { get; set; }
}

public class RecordHandResultDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public int BallNumber { get; set; }
    public int HandNumber { get; set; }
    public Guid WinnerPlayerId { get; set; }
    public int Points { get; set; }
    public List<PlayCardDto> PlayedCards { get; set; } = new();
}

public class PlayCardDto
{
    public Guid PlayerId { get; set; }
    public string CardSuit { get; set; } = string.Empty;
    public string CardValue { get; set; } = string.Empty;
    public int PlayOrder { get; set; }
}

public class GameResultDto
{
    public string LobbyCode { get; set; } = string.Empty;
    public int WinnerTeam { get; set; }
    public int Team1FinalScore { get; set; }
    public int Team2FinalScore { get; set; }
}
