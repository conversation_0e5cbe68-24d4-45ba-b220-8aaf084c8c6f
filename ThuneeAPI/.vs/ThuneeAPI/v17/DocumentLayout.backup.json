{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\services\\competitionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\services\\competitionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\dtos\\competitiondtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\dtos\\competitiondtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\competitionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\competitionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\data\\repositories\\competitionrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\competitionphasecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\competitionphasecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\gamescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\gamescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\leaderboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\leaderboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\icompetitionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\icompetitionservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\services\\competitionphaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\services\\competitionphaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\iuserrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\iuserrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.core\\entities\\gamehand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|solutionrelative:thuneeapi.core\\entities\\gamehand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.core\\entities\\game.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|solutionrelative:thuneeapi.core\\entities\\game.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.core\\entities\\competition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|solutionrelative:thuneeapi.core\\entities\\competition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\dtos\\userdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\dtos\\userdtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\dtos\\gamedtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\dtos\\gamedtos.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\mapping\\automapperprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:th<PERSON><PERSON><PERSON>\\mapping\\automapperprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\icompetitionphaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\icompetitionphaseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.infrastructure\\services\\gameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Infrastructure\\ThuneeAPI.Infrastructure.csproj|solutionrelative:thuneeapi.infrastructure\\services\\gameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\igameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\igameservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\interfaces\\igamerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\interfaces\\igamerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:thuneea<PERSON>\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{8B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI\\ThuneeAPI.csproj|solutionrelative:thuneeapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.core\\thuneeapi.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{1B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Core\\ThuneeAPI.Core.csproj|solutionrelative:thuneeapi.core\\thuneeapi.core.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|c:\\users\\<USER>\\source\\repos\\thunee-fe\\thuneeapi\\thuneeapi.application\\thuneeapi.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{3B7F7F7F-7F7F-7F7F-7F7F-7F7F7F7F7F7F}|ThuneeAPI.Application\\ThuneeAPI.Application.csproj|solutionrelative:thuneeapi.application\\thuneeapi.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "CompetitionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\CompetitionService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Services\\CompetitionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\CompetitionService.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Services\\CompetitionService.cs", "ViewState": "AgIAAHQBAAAAAAAAAAAmwIYBAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T07:33:18.203Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CompetitionsController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\CompetitionsController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\CompetitionsController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\CompetitionsController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\CompetitionsController.cs", "ViewState": "AgIAABIAAAAAAAAAAAAmwCAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:57:36.656Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "IUserRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IUserRepository.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\IUserRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IUserRepository.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\IUserRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:45.322Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "GameHand.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\GameHand.cs", "RelativeDocumentMoniker": "ThuneeAPI.Core\\Entities\\GameHand.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\GameHand.cs", "RelativeToolTip": "ThuneeAPI.Core\\Entities\\GameHand.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:43.374Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "Game.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\Game.cs", "RelativeDocumentMoniker": "ThuneeAPI.Core\\Entities\\Game.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\Game.cs", "RelativeToolTip": "ThuneeAPI.Core\\Entities\\Game.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:41.222Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "Competition.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\Competition.cs", "RelativeDocumentMoniker": "ThuneeAPI.Core\\Entities\\Competition.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\Entities\\Competition.cs", "RelativeToolTip": "ThuneeAPI.Core\\Entities\\Competition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:40.021Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "UserDTOs.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\UserDTOs.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\DTOs\\UserDTOs.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\UserDTOs.cs", "RelativeToolTip": "ThuneeAPI.Application\\DTOs\\UserDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:35.807Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "GameDTOs.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\GameDTOs.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\DTOs\\GameDTOs.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\GameDTOs.cs", "RelativeToolTip": "ThuneeAPI.Application\\DTOs\\GameDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:35.028Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "CompetitionDTOs.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\CompetitionDTOs.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\DTOs\\CompetitionDTOs.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\DTOs\\CompetitionDTOs.cs", "RelativeToolTip": "ThuneeAPI.Application\\DTOs\\CompetitionDTOs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:34.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "AutoMapperProfile.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Mapping\\AutoMapperProfile.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Mapping\\AutoMapperProfile.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Mapping\\AutoMapperProfile.cs", "RelativeToolTip": "ThuneeAPI\\Mapping\\AutoMapperProfile.cs", "ViewState": "AgIAADoAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:48:28.267Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "CompetitionPhaseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\CompetitionPhaseService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Services\\CompetitionPhaseService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\CompetitionPhaseService.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Services\\CompetitionPhaseService.cs", "ViewState": "AgIAAFkAAAAAAAAAAAAuwGUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T12:42:37.149Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "ICompetitionPhaseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\ICompetitionPhaseService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\ICompetitionPhaseService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\ICompetitionPhaseService.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\ICompetitionPhaseService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:36:07.274Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ICompetitionService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\ICompetitionService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\ICompetitionService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\ICompetitionService.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\ICompetitionService.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAuwA0AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:35:35.367Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CompetitionPhaseController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\CompetitionPhaseController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\CompetitionPhaseController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\CompetitionPhaseController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\CompetitionPhaseController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:29:31.457Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CompetitionRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Data\\Repositories\\CompetitionRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T11:29:24.685Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "GameService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\GameService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Infrastructure\\Services\\GameService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Infrastructure\\Services\\GameService.cs", "RelativeToolTip": "ThuneeAPI.Infrastructure\\Services\\GameService.cs", "ViewState": "AgIAAFsAAAAAAAAAAADwv2YAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T12:19:56.722Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "IGameService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameService.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\IGameService.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameService.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\IGameService.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAuwAoAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T10:49:40.76Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "IGameRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "RelativeDocumentMoniker": "ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "RelativeToolTip": "ThuneeAPI.Application\\Interfaces\\IGameRepository.cs", "ViewState": "AgIAACYAAAAAAAAAAAAUwDcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T10:49:39.78Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "ThuneeAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.Development.json", "RelativeToolTip": "ThuneeAPI\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-01T10:28:51.278Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.json", "RelativeDocumentMoniker": "ThuneeAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\appsettings.json", "RelativeToolTip": "ThuneeAPI\\appsettings.json", "ViewState": "AgIAAAcAAAAAAAAAAABhwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-01T10:28:46.136Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "GamesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\GamesController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\GamesController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\GamesController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\GamesController.cs", "ViewState": "AgIAAAQBAAAAAAAAAAAqwBIBAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T10:28:44.745Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "ThuneeAPI.Core.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "RelativeDocumentMoniker": "ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "RelativeToolTip": "ThuneeAPI.Core\\ThuneeAPI.Core.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T12:09:17.406Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "ThuneeAPI.Application.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "RelativeDocumentMoniker": "ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "RelativeToolTip": "ThuneeAPI.Application\\ThuneeAPI.Application.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-09T12:09:16.264Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "LeaderboardController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\LeaderboardController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\LeaderboardController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\LeaderboardController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\LeaderboardController.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAiwCYAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T12:08:45.638Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "ThuneeAPI\\Controllers\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Thunee-fe\\ThuneeAPI\\ThuneeAPI\\Controllers\\AuthController.cs", "RelativeToolTip": "ThuneeAPI\\Controllers\\AuthController.cs", "ViewState": "AgIAAB8BAAAAAAAAAAAuwAsBAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-04T11:21:47.418Z", "EditorCaption": ""}]}]}]}