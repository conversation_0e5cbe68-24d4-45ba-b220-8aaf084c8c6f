-- =============================================
-- KNOCKOUT COMPETITION SYSTEM - STORED PROCEDURES
-- =============================================

USE [GoldRushThunee]
GO

PRINT 'Creating Knockout Competition Stored Procedures...'

-- =============================================
-- SP_CreatePhaseLobby - Create a knockout phase lobby
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_CreatePhaseLobby')
    DROP PROCEDURE SP_CreatePhaseLobby
GO

CREATE PROCEDURE SP_CreatePhaseLobby
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40),
    @Team1Id UNIQUEIDENTIFIER,
    @Team2Id UNIQUEIDENTIFIER,
    @AdminId UNIQUEIDENTIFIER,
    @MatchScheduledAt DATETIME2 = NULL,
    @BestOfGames INT = 3,
    @RequiredWins INT = 2,
    @LobbyId UNIQUEIDENTIFIER OUTPUT,
    @LobbyCode NVARCHAR(24) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Generate unique lobby code
        DECLARE @CodeExists BIT = 1;
        WHILE @CodeExists = 1
        BEGIN
            SET @LobbyCode = 'KO' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6);
            IF NOT EXISTS (SELECT 1 FROM CompetitionPhaseLobbies WHERE LobbyCode = @LobbyCode)
                SET @CodeExists = 0;
        END
        
        -- Create lobby
        SET @LobbyId = NEWID();
        INSERT INTO CompetitionPhaseLobbies (
            Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId, 
            MatchScheduledAt, BestOfGames, RequiredWins, MatchStatus
        )
        VALUES (
            @LobbyId, @CompetitionId, @Phase, @LobbyCode, @AdminId,
            @MatchScheduledAt, @BestOfGames, @RequiredWins, 
            CASE WHEN @MatchScheduledAt IS NOT NULL THEN 'Scheduled' ELSE 'Pending' END
        );
        
        -- Add teams to lobby
        INSERT INTO CompetitionPhaseLobbyTeams (LobbyId, CompetitionTeamId, IsWinner)
        VALUES 
            (@LobbyId, @Team1Id, 0),
            (@LobbyId, @Team2Id, 0);
        
        COMMIT TRANSACTION;
        
        SELECT 
            cpl.*,
            ct1.TeamName as Team1Name,
            ct2.TeamName as Team2Name,
            u1.Username as Team1Player1,
            u2.Username as Team1Player2,
            u3.Username as Team2Player1,
            u4.Username as Team2Player2
        FROM CompetitionPhaseLobbies cpl
        INNER JOIN CompetitionPhaseLobbyTeams cplt1 ON cpl.Id = cplt1.LobbyId
        INNER JOIN CompetitionPhaseLobbyTeams cplt2 ON cpl.Id = cplt2.LobbyId AND cplt2.Id != cplt1.Id
        INNER JOIN CompetitionTeams ct1 ON cplt1.CompetitionTeamId = ct1.Id
        INNER JOIN CompetitionTeams ct2 ON cplt2.CompetitionTeamId = ct2.Id
        INNER JOIN Users u1 ON ct1.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct1.Player2Id = u2.Id
        LEFT JOIN Users u3 ON ct2.Player1Id = u3.Id
        LEFT JOIN Users u4 ON ct2.Player2Id = u4.Id
        WHERE cpl.Id = @LobbyId;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_GetPhaseLobbyDetails - Get lobby details with teams and games
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetPhaseLobbyDetails')
    DROP PROCEDURE SP_GetPhaseLobbyDetails
GO

CREATE PROCEDURE SP_GetPhaseLobbyDetails
    @LobbyId UNIQUEIDENTIFIER = NULL,
    @LobbyCode NVARCHAR(24) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Get lobby details
    SELECT 
        cpl.*,
        c.Name as CompetitionName,
        admin.Username as CreatedByAdmin
    FROM CompetitionPhaseLobbies cpl
    INNER JOIN Competitions c ON cpl.CompetitionId = c.Id
    INNER JOIN Users admin ON cpl.CreatedByAdminId = admin.Id
    WHERE (@LobbyId IS NOT NULL AND cpl.Id = @LobbyId)
       OR (@LobbyCode IS NOT NULL AND cpl.LobbyCode = @LobbyCode);
    
    -- Get teams in lobby
    SELECT 
        cplt.*,
        ct.TeamName,
        ct.Player1Id,
        ct.Player2Id,
        u1.Username as Player1Name,
        u2.Username as Player2Name
    FROM CompetitionPhaseLobbyTeams cplt
    INNER JOIN CompetitionTeams ct ON cplt.CompetitionTeamId = ct.Id
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    INNER JOIN CompetitionPhaseLobbies cpl ON cplt.LobbyId = cpl.Id
    WHERE (@LobbyId IS NOT NULL AND cpl.Id = @LobbyId)
       OR (@LobbyCode IS NOT NULL AND cpl.LobbyCode = @LobbyCode);
    
    -- Get games played in this lobby
    SELECT 
        g.*,
        CASE 
            WHEN g.WinnerTeam = 1 THEN g.Team1Name
            WHEN g.WinnerTeam = 2 THEN g.Team2Name
            ELSE NULL
        END as WinnerTeamName
    FROM Games g
    INNER JOIN CompetitionPhaseLobbies cpl ON g.PhaseLobbyId = cpl.Id
    WHERE (@LobbyId IS NOT NULL AND cpl.Id = @LobbyId)
       OR (@LobbyCode IS NOT NULL AND cpl.LobbyCode = @LobbyCode)
    ORDER BY g.CreatedAt;
END
GO

-- =============================================
-- SP_ProcessGameResult - Process game result and check for match completion
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_ProcessGameResult')
    DROP PROCEDURE SP_ProcessGameResult
GO

CREATE PROCEDURE SP_ProcessGameResult
    @GameId UNIQUEIDENTIFIER,
    @PhaseLobbyId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Update game with phase lobby reference
        UPDATE Games 
        SET PhaseLobbyId = @PhaseLobbyId
        WHERE Id = @GameId;
        
        -- Get lobby details
        DECLARE @BestOfGames INT, @RequiredWins INT;
        SELECT @BestOfGames = BestOfGames, @RequiredWins = RequiredWins
        FROM CompetitionPhaseLobbies
        WHERE Id = @PhaseLobbyId;
        
        -- Count wins for each team
        DECLARE @Team1Wins INT, @Team2Wins INT;
        SELECT 
            @Team1Wins = SUM(CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END),
            @Team2Wins = SUM(CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END)
        FROM Games
        WHERE PhaseLobbyId = @PhaseLobbyId AND WinnerTeam IS NOT NULL;
        
        -- Check if match is complete
        DECLARE @MatchComplete BIT = 0;
        DECLARE @WinnerTeamNumber INT = NULL;
        
        IF @Team1Wins >= @RequiredWins
        BEGIN
            SET @MatchComplete = 1;
            SET @WinnerTeamNumber = 1;
        END
        ELSE IF @Team2Wins >= @RequiredWins
        BEGIN
            SET @MatchComplete = 1;
            SET @WinnerTeamNumber = 2;
        END
        
        -- If match is complete, update lobby and teams
        IF @MatchComplete = 1
        BEGIN
            -- Update lobby status
            UPDATE CompetitionPhaseLobbies
            SET MatchStatus = 'Completed',
                CompletedAt = GETUTCDATE()
            WHERE Id = @PhaseLobbyId;
            
            -- Get team IDs in order they were added to lobby
            DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER;
            
            WITH OrderedTeams AS (
                SELECT 
                    CompetitionTeamId,
                    ROW_NUMBER() OVER (ORDER BY Id) as TeamOrder
                FROM CompetitionPhaseLobbyTeams
                WHERE LobbyId = @PhaseLobbyId
            )
            SELECT 
                @Team1Id = CASE WHEN TeamOrder = 1 THEN CompetitionTeamId END,
                @Team2Id = CASE WHEN TeamOrder = 2 THEN CompetitionTeamId END
            FROM OrderedTeams;
            
            -- Determine winner team ID
            DECLARE @WinnerTeamId UNIQUEIDENTIFIER, @LoserTeamId UNIQUEIDENTIFIER;
            IF @WinnerTeamNumber = 1
            BEGIN
                SET @WinnerTeamId = @Team1Id;
                SET @LoserTeamId = @Team2Id;
            END
            ELSE
            BEGIN
                SET @WinnerTeamId = @Team2Id;
                SET @LoserTeamId = @Team1Id;
            END
            
            -- Update winner
            UPDATE CompetitionPhaseLobbyTeams
            SET IsWinner = 1
            WHERE LobbyId = @PhaseLobbyId AND CompetitionTeamId = @WinnerTeamId;
            
            -- Update loser
            UPDATE CompetitionPhaseLobbyTeams
            SET EliminatedAt = GETUTCDATE()
            WHERE LobbyId = @PhaseLobbyId AND CompetitionTeamId = @LoserTeamId;
        END
        
        COMMIT TRANSACTION;
        
        -- Return match status
        SELECT 
            @MatchComplete as IsMatchComplete,
            @Team1Wins as Team1Wins,
            @Team2Wins as Team2Wins,
            @RequiredWins as RequiredWins,
            @WinnerTeamNumber as WinnerTeamNumber,
            @WinnerTeamId as WinnerTeamId;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_GetPhaseLobbies - Get all lobbies for a competition phase
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetPhaseLobbies')
    DROP PROCEDURE SP_GetPhaseLobbies
GO

CREATE PROCEDURE SP_GetPhaseLobbies
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        cpl.*,
        admin.Username as CreatedByAdmin,
        -- Count games played in this lobby
        ISNULL(game_counts.GamesPlayed, 0) as GamesPlayed,
        ISNULL(game_counts.Team1Wins, 0) as Team1Wins,
        ISNULL(game_counts.Team2Wins, 0) as Team2Wins,
        -- Get team names
        teams.Team1Name,
        teams.Team2Name,
        teams.Team1Id,
        teams.Team2Id
    FROM CompetitionPhaseLobbies cpl
    INNER JOIN Users admin ON cpl.CreatedByAdminId = admin.Id
    LEFT JOIN (
        SELECT 
            PhaseLobbyId,
            COUNT(*) as GamesPlayed,
            SUM(CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END) as Team1Wins,
            SUM(CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END) as Team2Wins
        FROM Games
        WHERE PhaseLobbyId IS NOT NULL AND WinnerTeam IS NOT NULL
        GROUP BY PhaseLobbyId
    ) game_counts ON cpl.Id = game_counts.PhaseLobbyId
    LEFT JOIN (
        SELECT 
            cplt.LobbyId,
            MAX(CASE WHEN rn = 1 THEN ct.TeamName END) as Team1Name,
            MAX(CASE WHEN rn = 2 THEN ct.TeamName END) as Team2Name,
            MAX(CASE WHEN rn = 1 THEN ct.Id END) as Team1Id,
            MAX(CASE WHEN rn = 2 THEN ct.Id END) as Team2Id
        FROM (
            SELECT 
                cplt.LobbyId,
                cplt.CompetitionTeamId,
                ROW_NUMBER() OVER (PARTITION BY cplt.LobbyId ORDER BY cplt.Id) as rn
            FROM CompetitionPhaseLobbyTeams cplt
        ) cplt
        INNER JOIN CompetitionTeams ct ON cplt.CompetitionTeamId = ct.Id
        GROUP BY cplt.LobbyId
    ) teams ON cpl.Id = teams.LobbyId
    WHERE cpl.CompetitionId = @CompetitionId
      AND (@Phase IS NULL OR cpl.Phase = @Phase)
    ORDER BY cpl.CreatedAt;
END
GO

-- =============================================
-- SP_GetPhaseWinners - Get winning teams from a completed phase
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetPhaseWinners')
    DROP PROCEDURE SP_GetPhaseWinners
GO

CREATE PROCEDURE SP_GetPhaseWinners
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT DISTINCT
        ct.Id,
        ct.TeamName,
        ct.Player1Id,
        ct.Player2Id,
        u1.Username as Player1Name,
        u2.Username as Player2Name,
        cpl.Phase,
        cpl.CompletedAt as PhaseCompletedAt
    FROM CompetitionPhaseLobbyTeams cplt
    INNER JOIN CompetitionPhaseLobbies cpl ON cplt.LobbyId = cpl.Id
    INNER JOIN CompetitionTeams ct ON cplt.CompetitionTeamId = ct.Id
    INNER JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE cpl.CompetitionId = @CompetitionId
      AND cpl.Phase = @Phase
      AND cplt.IsWinner = 1
      AND cpl.MatchStatus = 'Completed'
    ORDER BY ct.TeamName;
END
GO

-- =============================================
-- SP_AdvancePhase - Advance winning teams to next phase
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_AdvancePhase')
    DROP PROCEDURE SP_AdvancePhase
GO

CREATE PROCEDURE SP_AdvancePhase
    @CompetitionId UNIQUEIDENTIFIER,
    @CurrentPhase NVARCHAR(40),
    @NextPhase NVARCHAR(40)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        BEGIN TRANSACTION;

        -- Validate all matches in current phase are complete
        DECLARE @IncompleteMatches INT;
        SELECT @IncompleteMatches = COUNT(*)
        FROM CompetitionPhaseLobbies
        WHERE CompetitionId = @CompetitionId
          AND Phase = @CurrentPhase
          AND MatchStatus != 'Completed';

        IF @IncompleteMatches > 0
        BEGIN
            RAISERROR('Cannot advance phase. %d matches are still incomplete.', 16, 1, @IncompleteMatches);
            RETURN;
        END

        -- Get winner team IDs
        DECLARE @WinnerTeams TABLE (TeamId UNIQUEIDENTIFIER);
        INSERT INTO @WinnerTeams
        SELECT DISTINCT cplt.CompetitionTeamId
        FROM CompetitionPhaseLobbyTeams cplt
        INNER JOIN CompetitionPhaseLobbies cpl ON cplt.LobbyId = cpl.Id
        WHERE cpl.CompetitionId = @CompetitionId
          AND cpl.Phase = @CurrentPhase
          AND cplt.IsWinner = 1;

        -- Update competition phase
        UPDATE Competitions
        SET Phase = @NextPhase,
            UpdatedAt = GETUTCDATE()
        WHERE Id = @CompetitionId;

        -- Advance winning teams
        UPDATE CompetitionTeams
        SET Phase = @NextPhase,
            AdvancedToNextPhase = 1
        WHERE Id IN (SELECT TeamId FROM @WinnerTeams);

        -- Eliminate losing teams
        UPDATE CompetitionTeams
        SET IsEliminated = 1,
            PhaseEliminatedAt = GETUTCDATE()
        WHERE CompetitionId = @CompetitionId
          AND Phase = @CurrentPhase
          AND Id NOT IN (SELECT TeamId FROM @WinnerTeams);

        COMMIT TRANSACTION;

        -- Return advancement summary
        SELECT
            @CurrentPhase as FromPhase,
            @NextPhase as ToPhase,
            (SELECT COUNT(*) FROM @WinnerTeams) as TeamsAdvanced,
            GETUTCDATE() as AdvancedAt;

    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- =============================================
-- SP_GetAdminLobbyView - Get comprehensive lobby view for admin dashboard
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAdminLobbyView')
    DROP PROCEDURE SP_GetAdminLobbyView
GO

CREATE PROCEDURE SP_GetAdminLobbyView
    @CompetitionId UNIQUEIDENTIFIER,
    @Phase NVARCHAR(40) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        cpl.Id as LobbyId,
        cpl.LobbyCode,
        cpl.Phase,
        cpl.MatchStatus,
        cpl.MatchScheduledAt,
        cpl.BestOfGames,
        cpl.RequiredWins,
        cpl.CompletedAt,
        cpl.NotificationSentAt,
        admin.Username as CreatedByAdmin,

        -- Team 1 Info
        ct1.Id as Team1Id,
        ct1.TeamName as Team1Name,
        u1.Username as Team1Player1,
        u2.Username as Team1Player2,

        -- Team 2 Info
        ct2.Id as Team2Id,
        ct2.TeamName as Team2Name,
        u3.Username as Team2Player1,
        u4.Username as Team2Player2,

        -- Match Statistics
        ISNULL(stats.GamesPlayed, 0) as GamesPlayed,
        ISNULL(stats.Team1Wins, 0) as Team1Wins,
        ISNULL(stats.Team2Wins, 0) as Team2Wins,

        -- Winner Info
        CASE
            WHEN cplt1.IsWinner = 1 THEN ct1.TeamName
            WHEN cplt2.IsWinner = 1 THEN ct2.TeamName
            ELSE NULL
        END as WinnerTeamName,

        CASE
            WHEN cplt1.IsWinner = 1 THEN ct1.Id
            WHEN cplt2.IsWinner = 1 THEN ct2.Id
            ELSE NULL
        END as WinnerTeamId

    FROM CompetitionPhaseLobbies cpl
    INNER JOIN Users admin ON cpl.CreatedByAdminId = admin.Id

    -- Get teams (assuming 2 teams per lobby)
    INNER JOIN CompetitionPhaseLobbyTeams cplt1 ON cpl.Id = cplt1.LobbyId
    INNER JOIN CompetitionPhaseLobbyTeams cplt2 ON cpl.Id = cplt2.LobbyId AND cplt2.Id != cplt1.Id
    INNER JOIN CompetitionTeams ct1 ON cplt1.CompetitionTeamId = ct1.Id
    INNER JOIN CompetitionTeams ct2 ON cplt2.CompetitionTeamId = ct2.Id

    -- Get player names
    INNER JOIN Users u1 ON ct1.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct1.Player2Id = u2.Id
    LEFT JOIN Users u3 ON ct2.Player1Id = u3.Id
    LEFT JOIN Users u4 ON ct2.Player2Id = u4.Id

    -- Get game statistics
    LEFT JOIN (
        SELECT
            PhaseLobbyId,
            COUNT(*) as GamesPlayed,
            SUM(CASE WHEN WinnerTeam = 1 THEN 1 ELSE 0 END) as Team1Wins,
            SUM(CASE WHEN WinnerTeam = 2 THEN 1 ELSE 0 END) as Team2Wins
        FROM Games
        WHERE PhaseLobbyId IS NOT NULL AND WinnerTeam IS NOT NULL
        GROUP BY PhaseLobbyId
    ) stats ON cpl.Id = stats.PhaseLobbyId

    WHERE cpl.CompetitionId = @CompetitionId
      AND (@Phase IS NULL OR cpl.Phase = @Phase)
      AND cplt1.Id < cplt2.Id  -- Ensure we only get one row per lobby
    ORDER BY cpl.Phase, cpl.CreatedAt;
END
GO

PRINT 'Knockout Competition Stored Procedures Created Successfully!'
PRINT ''
PRINT 'Available Procedures:'
PRINT '- SP_CreatePhaseLobby: Create knockout lobbies with Best-of-N configuration'
PRINT '- SP_GetPhaseLobbyDetails: Get detailed lobby information with teams and games'
PRINT '- SP_ProcessGameResult: Process game results and determine match winners'
PRINT '- SP_GetPhaseLobbies: Get all lobbies for a competition phase with statistics'
PRINT '- SP_GetPhaseWinners: Get winning teams from completed phase'
PRINT '- SP_AdvancePhase: Advance teams to next phase after validation'
PRINT '- SP_GetAdminLobbyView: Comprehensive admin dashboard view of lobbies'
