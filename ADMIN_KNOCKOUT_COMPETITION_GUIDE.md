# 🏆 Admin Guide: Managing Knockout Competitions

## Overview
This comprehensive guide walks you through managing a Thunee competition from the initial leaderboard phase through all knockout stages to crowning a champion.

## 📋 Competition Lifecycle

### Phase 1: Leaderboard Phase (Initial Setup)
**Duration**: Typically 1-2 weeks
**Purpose**: Allow teams to accumulate points and determine Top 32 qualifiers

#### What Happens:
- Teams register and play games to earn points
- System tracks wins, losses, and bonus points
- Teams compete to reach the top 32 positions

#### Admin Actions Required:
1. **Monitor Registration**: Ensure teams are registering properly
2. **Track Progress**: Monitor team standings and game activity
3. **Set Phase End Date**: Determine when to close the leaderboard phase
4. **Prepare for Knockout**: Identify top 32 teams ready for elimination rounds

---

## 🎯 Starting the Knockout Phase

### Step 1: Advance to Top32 Phase

#### Prerequisites:
- ✅ At least 32 teams have completed their leaderboard games
- ✅ Final standings are confirmed
- ✅ Teams are ranked by total points (points + bonus points)

#### Process:
1. **Navigate to Competition Management**
   - Go to `/admin/competitions/{competitionId}/knockout`
   - Click on the "Overview" tab

2. **Verify Eligible Teams**
   - Review the "Eligible for Next Phase" count
   - Ensure you have exactly 32 teams ready to advance

3. **Start Knockout Phase**
   - Click "Start Knockout Phase (Top32)" button
   - System automatically:
     - Advances top 32 teams to Top32 phase
     - Eliminates remaining teams
     - Updates competition phase to "Top32"
     - Sends email notifications to all teams

#### What Teams Receive:
- **Advanced Teams**: Congratulations email with next phase information
- **Eliminated Teams**: Encouragement email thanking them for participation

---

## 🏟️ Managing Knockout Phases

### Phase 2: Top32 → Top16

#### Step 1: Create Knockout Lobbies
1. **Access Lobby Management**
   - Navigate to "Lobbies" tab
   - Click "Create Lobby" button

2. **Set Up Matches**
   - **Team Pairing**: Select 2 teams for head-to-head match
   - **Match Format**: Choose Best-of-N (recommended: Best of 3)
   - **Scheduling**: Set date/time (optional but recommended)
   - **Notifications**: Enable automatic email notifications

3. **Bulk Lobby Creation** (Recommended)
   - Use "Create Multiple Lobbies" for efficiency
   - System can create all 16 matches at once
   - Each match gets a unique lobby code (e.g., "KO123456")

#### Step 2: Match Scheduling
1. **Navigate to "Scheduling" Tab**
2. **Schedule Individual Matches**
   - Select match from unscheduled list
   - Set date and time
   - Add custom message (optional)
   - Send immediate notifications

#### Step 3: Monitor Match Progress
1. **Track Game Results**
   - Games are automatically processed when completed
   - System tracks wins for each team in Best-of-N series
   - Match winner determined when required wins reached

2. **Admin Dashboard View**
   - See all lobby codes for easy reference
   - Monitor match status (Pending → Scheduled → InProgress → Completed)
   - Track series progress (e.g., "2-1, First to 2")

#### Step 4: Phase Advancement
1. **Navigate to "Advancement" Tab**
2. **Verify All Matches Complete**
   - System shows completion percentage
   - Lists any incomplete matches
   - Prevents advancement until all matches finished

3. **Advance to Top16**
   - Click "Advance Phase" button
   - Confirm advancement in dialog
   - System automatically:
     - Moves 16 winners to Top16 phase
     - Eliminates 16 losing teams
     - Updates competition phase
     - Sends notifications to all teams

---

### Phase 3: Top16 → Top8

#### Process (Same as Top32 → Top16):
1. **Create 8 Lobbies** (16 teams → 8 matches)
2. **Recommended Format**: Best of 5 (First to win 3)
3. **Schedule Matches** with specific dates/times
4. **Monitor Progress** through admin dashboard
5. **Advance Phase** when all 8 matches complete

#### Key Differences:
- **Higher Stakes**: Consider Best of 5 format for more decisive matches
- **Closer Scheduling**: Matches may be scheduled closer together
- **Increased Attention**: More spectator interest, consider streaming

---

### Phase 4: Top8 → Top4 (Quarterfinals)

#### Process:
1. **Create 4 Lobbies** (8 teams → 4 matches)
2. **Recommended Format**: Best of 5 or Best of 7
3. **Premium Scheduling**: Consider weekend prime time slots
4. **Enhanced Notifications**: Add custom messages for semifinal advancement

---

### Phase 5: Top4 → Final (Semifinals)

#### Process:
1. **Create 2 Lobbies** (4 teams → 2 matches)
2. **Recommended Format**: Best of 7 (First to win 4)
3. **Special Scheduling**: Coordinate with all teams for optimal timing
4. **Build Excitement**: Custom messages about reaching the finals

---

### Phase 6: Final (Championship)

#### Process:
1. **Create 1 Lobby** (2 teams → 1 match)
2. **Championship Format**: Best of 7 or Best of 9
3. **Premium Experience**: 
   - Schedule during peak viewing time
   - Send special championship notifications
   - Consider live streaming or commentary

#### Championship Match Features:
- **Extended Format**: Longer series for definitive winner
- **Special Recognition**: Winner receives championship status
- **Final Notifications**: Victory announcements to all participants

---

## 🛠️ Admin Tools & Features

### Lobby Management
- **Unique Lobby Codes**: Each match gets a code like "KO123456"
- **Team Visibility**: See both team names and player names
- **Match Progress**: Real-time tracking of games and wins
- **Status Monitoring**: Track match progression through all stages

### Match Scheduling
- **Flexible Timing**: Set specific dates and times
- **Bulk Operations**: Schedule multiple matches at once
- **Reminder System**: Send reminders before matches
- **Custom Messages**: Add personalized notes to notifications

### Phase Advancement
- **Validation System**: Prevents advancement with incomplete matches
- **Automatic Processing**: Winners advance, losers eliminated automatically
- **Audit Trail**: Complete history of all phase changes
- **Notification System**: Automatic emails to all affected teams

### Notification System
- **Match Scheduled**: Sent when match is scheduled with lobby code
- **Match Reminder**: Sent 2 hours before scheduled time
- **Match Started**: Confirmation when match begins
- **Phase Advancement**: Congratulations/elimination notifications
- **Championship**: Special victory announcements

---

## 📧 Email Notifications

### Automatic Notifications Include:
- **Lobby Code**: Unique code for joining the match
- **Opponent Information**: Team names and player details
- **Match Format**: Best-of-N information
- **Scheduled Time**: Date and time for the match
- **Phase Information**: Current tournament stage
- **Instructions**: How to join and play

### Notification Types:
1. **Phase Advancement**: "Congratulations! You've advanced to Top16"
2. **Match Scheduled**: "Your Top16 match is scheduled for..."
3. **Match Reminder**: "Your match starts in 2 hours. Lobby code: KO123456"
4. **Elimination**: "Thank you for participating. Better luck next time!"
5. **Championship**: "Congratulations on winning the championship!"

---

## ⚡ Best Practices

### Timing Recommendations:
- **Top32**: Best of 3, 1-2 days between matches
- **Top16**: Best of 3 or 5, 2-3 days between matches  
- **Top8**: Best of 5, 3-4 days between matches
- **Top4**: Best of 5 or 7, 1 week between matches
- **Final**: Best of 7 or 9, special event scheduling

### Communication Tips:
- **Clear Scheduling**: Always provide specific dates and times
- **Advance Notice**: Give teams at least 24-48 hours notice
- **Custom Messages**: Add encouraging or exciting messages
- **Consistent Updates**: Keep all participants informed of progress

### Technical Considerations:
- **Monitor Lobby Codes**: Ensure teams have correct codes
- **Track Match Progress**: Verify games are being recorded properly
- **Backup Plans**: Have contingency for technical issues
- **Support Availability**: Be available during scheduled matches

---

## 🚨 Troubleshooting

### Common Issues:
1. **Teams Can't Find Lobby**: Verify lobby code is correct and shared
2. **Match Not Recording**: Check if game is linked to correct phase lobby
3. **Advancement Blocked**: Ensure all matches in phase are completed
4. **Notification Issues**: Verify email addresses and notification settings

### Emergency Procedures:
- **Manual Winner Setting**: Available for exceptional circumstances
- **Match Rescheduling**: Can update scheduled times as needed
- **Phase Rollback**: Contact technical support if needed
- **Communication Backup**: Direct contact with teams if emails fail

---

## 🏆 Success Metrics

### Track These KPIs:
- **Match Completion Rate**: Percentage of scheduled matches completed
- **On-Time Performance**: Matches starting at scheduled times
- **Participant Satisfaction**: Feedback from teams
- **Technical Issues**: Number of support requests
- **Engagement**: Spectator interest and participation

This guide ensures smooth operation of knockout competitions from start to finish, providing teams with a professional tournament experience while giving admins complete control over the process.
