# Sound Files Needed

The following sound files need to be added to this directory for the card animations to have audio:

## Required Files:
1. `card-shuffle.mp3` - Card shuffling sound
2. `card-cut.mp3` - Card cutting sound  
3. `card-deal.mp3` - Card dealing sound
4. `card-play.mp3` - Card playing sound
5. `card-remove.mp3` - Card removal sound

## Where to Find Free Card Sounds:

### Free Resources:
- **Freesound.org** - Search for "card shuffle", "card deal", "card flip"
- **Zapsplat.com** - Professional sound effects (free with registration)
- **BBC Sound Effects Library** - Free for personal use
- **YouTube Audio Library** - Free sounds you can download

### Search Terms:
- "card shuffle sound effect"
- "playing card deal sound"
- "card flip sound"
- "card snap sound"
- "poker card sounds"

### File Requirements:
- Format: MP3
- Duration: 1-3 seconds recommended
- Volume: Moderate (the code adjusts volume automatically)

## Current Status:
The application will work without these files, but will show console warnings. The sound functionality is already implemented and will work as soon as you add the MP3 files.

## Quick Setup:
1. Download 5 card-related sound effects
2. Rename them to match the filenames above
3. Place them in this `/public/sounds/` directory
4. Restart your development server
5. Test the sounds during card animations
