# WebRTC Video Call Testing Guide

## 🎥 Camera Access Issue - Single PC Testing

**Problem**: Only one browser tab can access the camera at a time on most systems.

**Solution**: You have several options for testing:

### Option 1: Virtual Cameras (Recommended)
1. **Install OBS Studio** (free): https://obsproject.com/
2. **Create Virtual Camera**:
   - Open OBS Studio
   - Go to `Tools` → `Virtual Camera`
   - Click `Start Virtual Camera`
   - This creates additional camera devices

3. **Test with Multiple Browsers**:
   - Browser 1: Use real camera
   - Browser 2: Use OBS Virtual Camera
   - Browser 3: Audio-only (will show avatar)
   - Browser 4: Audio-only (will show avatar)

### Option 2: Audio-Only Testing
The system now automatically falls back to audio-only if camera access fails:
- **Video Available**: Shows video stream
- **Audio Only**: Shows user avatar with green dot indicator
- **No Media**: Shows user avatar only

### Option 3: Different Devices
Test with:
- Desktop browser (camera access)
- Laptop browser (different camera)
- Mobile phone browser
- Tablet browser

## 🧪 Testing Steps

### 1. Start Services
```bash
# Terminal 1: Start API
cd ThuneeAPI/ThuneeAPI
dotnet run

# Terminal 2: Start Frontend
npm run dev
```

### 2. Open Multiple Browsers
- **Chrome**: http://localhost:5173
- **Firefox**: http://localhost:5173
- **Edge**: http://localhost:5173
- **Chrome Incognito**: http://localhost:5173

### 3. Login as Different Users
Create 4 test accounts or use existing ones:
- User1, User2, User3, User4

### 4. Join Same Game Lobby
All users should join the same lobby code.

### 5. Start Video Calls
1. **First user**: Click video call button → Should get camera access
2. **Second user**: Click video call button → May get camera access or fall back to audio
3. **Third user**: Click video call button → Likely audio-only
4. **Fourth user**: Click video call button → Likely audio-only

## 🔍 Expected Behavior

### What You Should See:
- **User with Camera**: Video stream visible to all others
- **Audio-Only Users**: Avatar with "(Audio Only)" label
- **All Users**: Should see each other in the video grid
- **Connection Status**: All users should show as connected

### Troubleshooting:
1. **Check Browser Console**: Look for WebRTC errors
2. **Use Test Component**: The "Video Call Connection Test" panel shows detailed logs
3. **Check Permissions**: Ensure microphone/camera permissions are granted
4. **Network Issues**: All browsers should connect to SignalR hub

## 🎯 Success Criteria

✅ **SignalR Connection**: All users connect to video hub
✅ **Peer Discovery**: Users see each other joining
✅ **Media Streams**: At least one user has video, others have audio
✅ **WebRTC Connections**: Direct peer-to-peer connections established
✅ **UI Updates**: Video grid shows all participants

## 🚨 Common Issues

### "Only one video showing"
- **Cause**: Camera access limitation
- **Solution**: Use virtual cameras or accept audio-only for some users

### "No connections at all"
- **Cause**: SignalR authentication or WebRTC signaling issues
- **Solution**: Check browser console and test component logs

### "Users not seeing each other"
- **Cause**: Room joining issues
- **Solution**: Ensure all users are in the same lobby

## 📱 Production Testing

For real-world testing:
1. **Different Devices**: Test with actual different devices
2. **Different Networks**: Test across different internet connections
3. **TURN Servers**: May need TURN servers for NAT traversal in production
4. **HTTPS**: Ensure HTTPS in production for camera access

## 🔧 Debug Tools

1. **Video Call Test Component**: Real-time connection logs
2. **Browser DevTools**: Network tab for WebSocket connections
3. **Console Logs**: Detailed WebRTC negotiation logs
4. **SignalR Hub Logs**: Server-side connection logs

The current implementation should now handle the camera access limitation gracefully by falling back to audio-only for users who can't access the camera.
