{"name": "thunee-fe", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@microsoft/signalr": "^8.0.0", "@types/node": "^20.11.30", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.10.1", "buffer": "^6.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "stream-browserify": "^3.0.0", "typescript": "^5.4.2", "vite": "^5.1.6"}}