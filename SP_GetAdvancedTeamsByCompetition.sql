-- Stored Procedure to get teams that have advanced to the next phase for a specific competition
-- This procedure is optimized for the admin phases page to show only teams with AdvancedToNextPhase = 1

IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetAdvancedTeamsByCompetition')
    DROP PROCEDURE SP_GetAdvancedTeamsByCompetition;
GO

CREATE PROCEDURE SP_GetAdvancedTeamsByCompetition
    @CompetitionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ct.Id,
        ct.CompetitionId,
        ct.TeamName,
        ct.Player1Id,
        ct.Player2Id,
        ct.InviteCode,
        ct.GamesPlayed,
        ct.Points,
        ct.BonusPoints,
        (ct.Points + ct.BonusPoints) AS TotalPoints,
        ct.MaxGames,
        ct.IsActive,
        ct.IsComplete,
        ct.RegisteredAt,
        ct.CompletedAt,
        ct.Phase,
        ct.IsEliminated,
        ct.AdvancedToNextPhase,
        ct.PhaseEliminatedAt,
        u1.Username AS Player1Username,
        u2.Username AS Player2Username
    FROM CompetitionTeams ct
    LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
    LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
    WHERE ct.CompetitionId = @CompetitionId
      AND ct.AdvancedToNextPhase = 1
      AND ct.IsEliminated = 0  -- Only non-eliminated teams
    ORDER BY (ct.Points + ct.BonusPoints) DESC,
             ct.Points DESC,
             ct.TeamName ASC;
END
GO

PRINT 'SP_GetAdvancedTeamsByCompetition stored procedure created successfully!'
