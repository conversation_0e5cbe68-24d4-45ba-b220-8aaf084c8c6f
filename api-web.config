<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore
        processPath=".\ThuneeAPI.exe"
        arguments=""
        stdoutLogEnabled="true"
        stdoutLogFile=".\logs\stdout"
        hostingModel="inprocess"
        requestTimeout="23:00:00"
        startupTimeLimit="120">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
          <environmentVariable name="ASPNETCORE_URLS" value="http://*:8080" />
        </environmentVariables>
      </aspNetCore>

      
      <!-- URL Rewrite for API routes -->
      <rewrite>
        <rules>
          <!-- Redirect root to API docs -->
          <rule name="Redirect to API docs" stopProcessing="true">
            <match url="^$" />
            <action type="Redirect" url="/api-docs" redirectType="Permanent" />
          </rule>
        </rules>
      </rewrite>
      
      <!-- Security settings -->
      <security>
        <requestFiltering>
          <requestLimits maxAllowedContentLength="52428800" /> <!-- 50MB -->
        </requestFiltering>
      </security>
      
      <!-- Compression -->
      <httpCompression>
        <dynamicTypes>
          <add mimeType="application/json" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="text/css" enabled="true" />
          <add mimeType="text/html" enabled="true" />
        </dynamicTypes>
        <staticTypes>
          <add mimeType="application/json" enabled="true" />
          <add mimeType="application/javascript" enabled="true" />
          <add mimeType="text/css" enabled="true" />
          <add mimeType="text/html" enabled="true" />
        </staticTypes>
      </httpCompression>
      
      <!-- Default documents -->
      <defaultDocument>
        <files>
          <clear />
          <add value="index.html" />
        </files>
      </defaultDocument>
      
      <!-- Static content -->
      <staticContent>
        <remove fileExtension=".json" />
        <mimeMap fileExtension=".json" mimeType="application/json" />
        <remove fileExtension=".woff" />
        <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
        <remove fileExtension=".woff2" />
        <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
      </staticContent>
      
      <!-- Error pages -->
      <httpErrors errorMode="Detailed" />
      
    </system.webServer>
  </location>
</configuration>
