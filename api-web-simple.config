<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore 
        processPath="dotnet" 
        arguments=".\ThuneeAPI.dll" 
        stdoutLogEnabled="true" 
        stdoutLogFile=".\logs\stdout" 
        hostingModel="inprocess" />
    </system.webServer>
  </location>
</configuration>
