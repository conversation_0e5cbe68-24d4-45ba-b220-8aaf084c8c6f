-- Create or update the competition leaderboard stored procedure
-- This works directly with CompetitionTeams table since CompetitionLeaderboard table may not exist

USE [GoldRushThunee]
GO

-- Drop existing procedure if it exists
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'SP_GetCompetitionLeaderboard')
    DROP PROCEDURE SP_GetCompetitionLeaderboard;
GO

CREATE PROCEDURE SP_GetCompetitionLeaderboard
    @CompetitionId UNIQUEIDENTIFIER,
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Get competition leaderboard directly from CompetitionTeams table
    WITH RankedTeams AS (
        SELECT 
            ct.Id AS TeamId,
            ct.TeamName,
            u1.Username AS Player1Name,
            u2.Username AS Player2Name,
            ct.GamesPlayed,
            ct.Points,
            ct.BonusPoints,
            (ct.Points + ct.BonusPoints) AS TotalPoints,
            ct.MaxGames,
            (ct.MaxGames - ct.GamesPlayed) AS RemainingGames,
            CASE 
                WHEN ct.GamesPlayed = 0 THEN 0
                ELSE ct.Points
            END AS GamesWon,
            CASE 
                WHEN ct.GamesPlayed = 0 THEN 0
                ELSE (ct.GamesPlayed - ct.Points)
            END AS GamesLost,
            CASE 
                WHEN ct.GamesPlayed = 0 THEN 0.0
                ELSE ROUND((CAST(ct.Points AS FLOAT) / CAST(ct.GamesPlayed AS FLOAT)) * 100, 2)
            END AS WinRate,
            CASE 
                WHEN ct.GamesPlayed >= ct.MaxGames THEN 'Completed'
                WHEN ct.IsComplete = 1 THEN 'Active'
                ELSE 'Waiting for Partner'
            END AS Status,
            ROW_NUMBER() OVER (ORDER BY (ct.Points + ct.BonusPoints) DESC, ct.Points DESC, ct.GamesPlayed ASC) AS Rank
        FROM CompetitionTeams ct
        INNER JOIN Users u1 ON ct.Player1Id = u1.Id
        LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
        WHERE ct.CompetitionId = @CompetitionId 
        AND ct.IsActive = 1
    )
    SELECT 
        Rank,
        TeamId,
        TeamName,
        Player1Name,
        Player2Name,
        GamesPlayed,
        Points,
        BonusPoints,
        TotalPoints,
        MaxGames,
        RemainingGames,
        GamesWon,
        GamesLost,
        WinRate,
        Status
    FROM RankedTeams
    ORDER BY Rank
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
    
    -- Also return total count for pagination
    SELECT COUNT(*) AS TotalTeams
    FROM CompetitionTeams ct
    WHERE ct.CompetitionId = @CompetitionId 
    AND ct.IsActive = 1;
END
GO

PRINT 'SP_GetCompetitionLeaderboard created successfully';
GO
