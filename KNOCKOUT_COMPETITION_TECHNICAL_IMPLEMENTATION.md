# Knockout Competition System - Technical Implementation Guide

## Overview
This document outlines the technical implementation requirements for the knockout competition system with configurable Best-of-N matches, admin-driven lobby creation, match scheduling, and automated phase advancement.

## 1. Database Schema Changes

### 1.1 CompetitionPhaseLobbies Table Enhancements
```sql
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN MatchScheduledAt DATETIME2 NULL;
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN BestOfGames INT NOT NULL DEFAULT 3;
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN RequiredWins INT NOT NULL DEFAULT 2;
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN MatchStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending' 
    CHECK (MatchStatus IN ('Pending', 'Scheduled', 'InProgress', 'Completed', 'Cancelled'));
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN NotificationSentAt DATETIME2 NULL;
ALTER TABLE CompetitionPhaseLobbies ADD COLUMN CompletedAt DATETIME2 NULL;
```

### 1.2 New Tables Required

#### CompetitionPhaseSettings
```sql
CREATE TABLE CompetitionPhaseSettings (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    CompetitionId UNIQUEIDENTIFIER NOT NULL,
    Phase NVARCHAR(40) NOT NULL,
    BestOfGames INT NOT NULL DEFAULT 3,
    RequiredWins INT NOT NULL DEFAULT 2,
    AutoAdvancement BIT NOT NULL DEFAULT 0,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
    UNIQUE(CompetitionId, Phase)
);
```

#### MatchNotifications
```sql
CREATE TABLE MatchNotifications (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    LobbyId UNIQUEIDENTIFIER NOT NULL,
    NotificationType NVARCHAR(20) NOT NULL CHECK (NotificationType IN ('MatchScheduled', 'MatchReminder', 'MatchStarted')),
    SentAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    EmailContent NVARCHAR(MAX) NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT 'Sent' CHECK (Status IN ('Pending', 'Sent', 'Failed')),
    FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id)
);
```

### 1.3 Games Table Enhancement
```sql
ALTER TABLE Games ADD COLUMN PhaseLobbyId UNIQUEIDENTIFIER NULL;
ALTER TABLE Games ADD CONSTRAINT FK_Games_PhaseLobby 
    FOREIGN KEY (PhaseLobbyId) REFERENCES CompetitionPhaseLobbies(Id);
```

## 2. API Layer Changes

### 2.1 New DTOs Required

#### CompetitionPhaseLobbyDto Enhancement
```csharp
public class CompetitionPhaseLobbyDto
{
    public Guid Id { get; set; }
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public string LobbyCode { get; set; } = string.Empty;
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public string MatchStatus { get; set; } = "Pending";
    public DateTime? NotificationSentAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public List<CompetitionPhaseLobbyTeamDto> Teams { get; set; } = new();
    public List<GameSummaryDto> Games { get; set; } = new();
    public CompetitionPhaseLobbyTeamDto? Winner { get; set; }
}
```

#### CreateCompetitionPhaseLobbyDto Enhancement
```csharp
public class CreateCompetitionPhaseLobbyDto
{
    public Guid CompetitionId { get; set; }
    public string Phase { get; set; } = string.Empty;
    public List<Guid> TeamIds { get; set; } = new();
    public DateTime? MatchScheduledAt { get; set; }
    public int BestOfGames { get; set; } = 3;
    public int RequiredWins { get; set; } = 2;
    public bool SendNotification { get; set; } = true;
}
```

#### MatchSchedulingDto
```csharp
public class MatchSchedulingDto
{
    public Guid LobbyId { get; set; }
    public DateTime ScheduledAt { get; set; }
    public string? CustomMessage { get; set; }
    public bool SendImmediateNotification { get; set; } = true;
}
```

### 2.2 New Controller Endpoints

#### CompetitionPhaseController Enhancements
```csharp
[HttpPost("{competitionId}/phases/{phase}/lobbies/bulk")]
public async Task<ActionResult> CreateMultipleLobbies(Guid competitionId, string phase, [FromBody] CreateMultipleLobbiesDto dto);

[HttpPut("lobbies/{lobbyId}/schedule")]
public async Task<ActionResult> ScheduleMatch(Guid lobbyId, [FromBody] MatchSchedulingDto dto);

[HttpPost("lobbies/{lobbyId}/notify")]
public async Task<ActionResult> SendMatchNotification(Guid lobbyId);

[HttpGet("{competitionId}/phases/{phase}/match-results")]
public async Task<ActionResult<List<MatchResultDto>>> GetPhaseMatchResults(Guid competitionId, string phase);

[HttpPost("{competitionId}/phases/{phase}/advance")]
public async Task<ActionResult> AdvancePhase(Guid competitionId, string phase);

[HttpGet("{competitionId}/phases/settings")]
public async Task<ActionResult<List<CompetitionPhaseSettingsDto>>> GetPhaseSettings(Guid competitionId);

[HttpPut("{competitionId}/phases/{phase}/settings")]
public async Task<ActionResult> UpdatePhaseSettings(Guid competitionId, string phase, [FromBody] UpdatePhaseSettingsDto dto);
```

### 2.3 Service Layer Enhancements

#### ICompetitionPhaseService Interface
```csharp
// Match Management
Task<List<CompetitionPhaseLobbyDto>> CreateMultipleLobbiesAsync(Guid competitionId, string phase, List<TeamPairingDto> pairings, Guid adminId);
Task ScheduleMatchAsync(Guid lobbyId, DateTime scheduledAt, string? customMessage = null);
Task SendMatchNotificationAsync(Guid lobbyId, string notificationType = "MatchScheduled");

// Game Tracking
Task ProcessGameResultAsync(Guid gameId, Guid phaseLobbyId);
Task<MatchResultDto> EvaluateMatchResultAsync(Guid lobbyId);
Task<bool> IsMatchCompleteAsync(Guid lobbyId);

// Phase Management
Task<List<CompetitionTeamDto>> GetPhaseWinnersAsync(Guid competitionId, string phase);
Task AdvancePhaseAsync(Guid competitionId, string currentPhase, string nextPhase);
Task<bool> CanAdvancePhaseAsync(Guid competitionId, string phase);

// Settings Management
Task<CompetitionPhaseSettingsDto> GetPhaseSettingsAsync(Guid competitionId, string phase);
Task UpdatePhaseSettingsAsync(Guid competitionId, string phase, UpdatePhaseSettingsDto settings);
```

## 3. Business Logic Implementation

### 3.1 Match Result Evaluation Logic
```csharp
public async Task<MatchResultDto> EvaluateMatchResultAsync(Guid lobbyId)
{
    var lobby = await GetPhaseLobbyAsync(lobbyId);
    var games = await GetLobbyGamesAsync(lobbyId);
    
    var team1Wins = games.Count(g => g.WinnerTeam == 1);
    var team2Wins = games.Count(g => g.WinnerTeam == 2);
    
    var isComplete = team1Wins >= lobby.RequiredWins || team2Wins >= lobby.RequiredWins;
    var winner = team1Wins >= lobby.RequiredWins ? lobby.Teams[0] : 
                 team2Wins >= lobby.RequiredWins ? lobby.Teams[1] : null;
    
    if (isComplete && winner != null)
    {
        await SetLobbyWinnerAsync(lobbyId, winner.CompetitionTeamId);
        await UpdateLobbyStatusAsync(lobbyId, "Completed");
    }
    
    return new MatchResultDto
    {
        LobbyId = lobbyId,
        Team1Wins = team1Wins,
        Team2Wins = team2Wins,
        IsComplete = isComplete,
        Winner = winner,
        RequiredWins = lobby.RequiredWins,
        TotalGames = games.Count
    };
}
```

### 3.2 Phase Advancement Logic
```csharp
public async Task AdvancePhaseAsync(Guid competitionId, string currentPhase, string nextPhase)
{
    // Validate all matches in current phase are complete
    var incompleteMatches = await GetIncompleteMatchesAsync(competitionId, currentPhase);
    if (incompleteMatches.Any())
        throw new InvalidOperationException($"Cannot advance phase. {incompleteMatches.Count} matches are incomplete.");
    
    // Get winners from current phase
    var winners = await GetPhaseWinnersAsync(competitionId, currentPhase);
    
    // Update competition phase
    await UpdateCompetitionPhaseAsync(competitionId, nextPhase);
    
    // Advance winning teams
    await AdvanceTeamsToNextPhaseAsync(competitionId, winners.Select(w => w.Id).ToList());
    
    // Eliminate losing teams
    var losers = await GetPhaseLoserTeamsAsync(competitionId, currentPhase);
    await EliminateTeamsAsync(competitionId, losers.Select(l => l.Id).ToList());
    
    // Log phase advancement
    await LogPhaseAdvancementAsync(competitionId, currentPhase, nextPhase, winners.Count);
}
```

## 4. Email Notification System

### 4.1 Email Service Interface
```csharp
public interface IMatchNotificationService
{
    Task SendMatchScheduledNotificationAsync(Guid lobbyId);
    Task SendMatchReminderNotificationAsync(Guid lobbyId, int hoursBeforeMatch = 2);
    Task SendMatchStartedNotificationAsync(Guid lobbyId);
    Task<string> GenerateMatchEmailContentAsync(Guid lobbyId, string notificationType);
}
```

### 4.2 Email Templates
- Match Scheduled Template
- Match Reminder Template (2 hours before)
- Match Started Template
- Phase Advancement Notification

## 5. Stored Procedures Required

### 5.1 Core Procedures
```sql
-- SP_CreatePhaseLobby
-- SP_ScheduleMatch
-- SP_EvaluateMatchResult
-- SP_AdvancePhase
-- SP_GetPhaseWinners
-- SP_GetIncompleteMatches
-- SP_UpdateLobbyStatus
-- SP_ProcessGameResult
```

## 6. Frontend Implementation

### 6.1 New Components Required
- `AdminLobbyCreation.tsx` - Bulk lobby creation interface
- `MatchScheduler.tsx` - Match scheduling interface
- `PhaseAdvancement.tsx` - Phase management interface
- `MatchResultsView.tsx` - Match results display
- `BestOfNConfiguration.tsx` - Match format configuration

### 6.2 Enhanced Components
- `CompetitionPhaseManagement.tsx` - Add scheduling and advancement features
- `CompetitionBracket.tsx` - Show match schedules and results
- `AdminDashboard.tsx` - Add phase management section

### 6.3 New Pages Required
- `/admin/competitions/{id}/phase-management` - Comprehensive phase management
- `/admin/competitions/{id}/match-scheduling` - Match scheduling interface
- `/admin/competitions/{id}/notifications` - Notification management

## 7. Configuration Management

### 7.1 Phase Configuration
- Default Best-of-N settings per phase
- Auto-advancement settings
- Notification timing settings
- Email template customization

### 7.2 Environment Variables
```
MATCH_NOTIFICATION_HOURS_BEFORE=2
AUTO_ADVANCEMENT_ENABLED=false
EMAIL_TEMPLATE_PATH=/templates/match-notifications/
PHASE_ADVANCEMENT_WEBHOOK_URL=
```

## 8. Testing Requirements

### 8.1 Unit Tests
- Match result evaluation logic
- Phase advancement validation
- Email notification generation
- Best-of-N configuration validation

### 8.2 Integration Tests
- End-to-end match flow
- Phase advancement workflow
- Email delivery testing
- Database transaction integrity

## 9. Performance Considerations

### 9.1 Database Optimization
- Index on Games.PhaseLobbyId
- Index on CompetitionPhaseLobbies.MatchScheduledAt
- Composite index on (CompetitionId, Phase, MatchStatus)

### 9.2 Caching Strategy
- Cache phase settings
- Cache active lobby information
- Cache match results for completed phases

## 10. Security Considerations

### 10.1 Admin Authorization
- Verify admin permissions for lobby creation
- Validate phase advancement permissions
- Audit trail for all admin actions

### 10.2 Data Validation
- Validate team pairings
- Validate match scheduling constraints
- Validate Best-of-N configuration limits

This technical implementation provides the foundation for a robust knockout competition system with full admin control and automated match management.
