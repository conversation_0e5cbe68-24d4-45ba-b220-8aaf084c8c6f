-- Create knockout competition tables if they don't exist
USE [GoldRushThunee]
GO

PRINT 'Creating knockout competition tables...'

-- CompetitionPhaseLobbies table (enhanced version)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbies')
BEGIN
    CREATE TABLE CompetitionPhaseLobbies (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        CompetitionId UNIQUEIDENTIFIER NOT NULL,
        Phase NVARCHAR(40) NOT NULL,
        LobbyCode NVARCHAR(24) NOT NULL UNIQUE,
        CreatedByAdminId UNIQUEIDENTIFIER NOT NULL,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        MatchScheduledAt DATETIME2 NULL,
        BestOfGames INT NOT NULL DEFAULT 3,
        RequiredWins INT NOT NULL DEFAULT 2,
        MatchStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- Pending, Scheduled, Active, Completed, Cancelled
        WinnerTeamId UNIQUEIDENTIFIER NULL,
        CompletedAt DATETIME2 NULL,
        
        CONSTRAINT FK_CompetitionPhaseLobbies_Competition 
            FOREIGN KEY (CompetitionId) REFERENCES Competitions(Id),
        CONSTRAINT FK_CompetitionPhaseLobbies_Admin 
            FOREIGN KEY (CreatedByAdminId) REFERENCES Users(Id),
        CONSTRAINT FK_CompetitionPhaseLobbies_WinnerTeam 
            FOREIGN KEY (WinnerTeamId) REFERENCES CompetitionTeams(Id)
    );
    PRINT '  ✓ Created CompetitionPhaseLobbies table';
END
ELSE
BEGIN
    -- Add missing columns if they don't exist
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'MatchScheduledAt')
        ALTER TABLE CompetitionPhaseLobbies ADD MatchScheduledAt DATETIME2 NULL;
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'BestOfGames')
        ALTER TABLE CompetitionPhaseLobbies ADD BestOfGames INT NOT NULL DEFAULT 3;
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'RequiredWins')
        ALTER TABLE CompetitionPhaseLobbies ADD RequiredWins INT NOT NULL DEFAULT 2;
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'MatchStatus')
        ALTER TABLE CompetitionPhaseLobbies ADD MatchStatus NVARCHAR(20) NOT NULL DEFAULT 'Pending';
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'WinnerTeamId')
        ALTER TABLE CompetitionPhaseLobbies ADD WinnerTeamId UNIQUEIDENTIFIER NULL;
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CompetitionPhaseLobbies') AND name = 'CompletedAt')
        ALTER TABLE CompetitionPhaseLobbies ADD CompletedAt DATETIME2 NULL;
    
    PRINT '  ✓ CompetitionPhaseLobbies table updated with missing columns';
END

-- CompetitionPhaseLobbyTeams table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseLobbyTeams')
BEGIN
    CREATE TABLE CompetitionPhaseLobbyTeams (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        LobbyId UNIQUEIDENTIFIER NOT NULL,
        CompetitionTeamId UNIQUEIDENTIFIER NOT NULL,
        IsWinner BIT NOT NULL DEFAULT 0,
        GamesWon INT NOT NULL DEFAULT 0,
        JoinedAt DATETIME2 NULL,
        
        CONSTRAINT FK_CompetitionPhaseLobbyTeams_Lobby 
            FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id) ON DELETE CASCADE,
        CONSTRAINT FK_CompetitionPhaseLobbyTeams_Team 
            FOREIGN KEY (CompetitionTeamId) REFERENCES CompetitionTeams(Id),
        CONSTRAINT UQ_CompetitionPhaseLobbyTeams_LobbyTeam 
            UNIQUE (LobbyId, CompetitionTeamId)
    );
    PRINT '  ✓ Created CompetitionPhaseLobbyTeams table';
END
ELSE
    PRINT '  ✓ CompetitionPhaseLobbyTeams table already exists';

-- CompetitionPhaseGames table (for tracking individual games within a match)
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CompetitionPhaseGames')
BEGIN
    CREATE TABLE CompetitionPhaseGames (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        LobbyId UNIQUEIDENTIFIER NOT NULL,
        GameNumber INT NOT NULL,
        WinnerTeamId UNIQUEIDENTIFIER NULL,
        StartedAt DATETIME2 NULL,
        CompletedAt DATETIME2 NULL,
        GameData NVARCHAR(MAX) NULL, -- JSON data for game details
        
        CONSTRAINT FK_CompetitionPhaseGames_Lobby 
            FOREIGN KEY (LobbyId) REFERENCES CompetitionPhaseLobbies(Id) ON DELETE CASCADE,
        CONSTRAINT FK_CompetitionPhaseGames_WinnerTeam 
            FOREIGN KEY (WinnerTeamId) REFERENCES CompetitionTeams(Id),
        CONSTRAINT UQ_CompetitionPhaseGames_LobbyGame 
            UNIQUE (LobbyId, GameNumber)
    );
    PRINT '  ✓ Created CompetitionPhaseGames table';
END
ELSE
    PRINT '  ✓ CompetitionPhaseGames table already exists';

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_Competition_Phase')
    CREATE INDEX IX_CompetitionPhaseLobbies_Competition_Phase ON CompetitionPhaseLobbies(CompetitionId, Phase);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_LobbyCode')
    CREATE INDEX IX_CompetitionPhaseLobbies_LobbyCode ON CompetitionPhaseLobbies(LobbyCode);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_CompetitionPhaseLobbies_MatchScheduledAt')
    CREATE INDEX IX_CompetitionPhaseLobbies_MatchScheduledAt ON CompetitionPhaseLobbies(MatchScheduledAt);

PRINT 'Knockout competition tables created successfully!'
PRINT ''

-- Show table status
SELECT 
    'CompetitionPhaseLobbies' AS TableName,
    COUNT(*) AS RecordCount
FROM CompetitionPhaseLobbies
UNION ALL
SELECT 
    'CompetitionPhaseLobbyTeams' AS TableName,
    COUNT(*) AS RecordCount
FROM CompetitionPhaseLobbyTeams
UNION ALL
SELECT 
    'CompetitionPhaseGames' AS TableName,
    COUNT(*) AS RecordCount
FROM CompetitionPhaseGames;
