import path from "path";
import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      // Add polyfills for browser compatibility
      stream: 'stream-browserify',
      buffer: 'buffer',
    },
  },
  define: {
    // Define global variables for browser environment
    global: 'globalThis',
    'process.env': {},
  },
  optimizeDeps: {
    esbuildOptions: {
      // Node.js global to browser globalThis
      define: {
        global: 'globalThis',
      },
    },
  },
  server: {
    port: 5173,
    host: true, // Allow external connections
    open: true, // Automatically open browser
    // Proxy removed - using direct API URLs from environment variables
  },
});
